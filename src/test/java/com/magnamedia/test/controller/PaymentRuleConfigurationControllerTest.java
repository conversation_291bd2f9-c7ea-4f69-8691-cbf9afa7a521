package com.magnamedia.test.controller;

import com.magnamedia.controller.OfficestaffPayrollController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.MonthlyPaymentRulesJob;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.newversion.*;
import com.magnamedia.test.PayrollMasterTestCase;
import com.magnamedia.test.mock.HousemaidMockService;
import com.magnamedia.test.mock.OfficeStaffMockService;
import mockit.Mock;
import mockit.MockUp;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.test.context.support.WithMockUser;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.*;

public class PaymentRuleConfigurationControllerTest extends PayrollMasterTestCase {

    @Autowired
    private MonthlyPaymentRuleService monthlyPaymentRuleService;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private OfficeStaffMockService officeStaffMockService;

    @Autowired
    private HousemaidMockService housemaidMockService;

    @Autowired
    private TransferFilesService transferFilesService;

    @Autowired
    private SendingTransactionService sendingTransactionService;

    @Autowired
    private AccountantToDoService accountantToDoService;

    @Autowired
    private OfficestaffPayrollController payrollController;

    @Before
    public void loadContext() {
        new MockUp<SelectQuery>() {
            @Mock
            protected EntityManager getEntityManager() {
                return entityManager;
            }
        };
    }

    @Test
    public void createMonthlyPaymentRules() {
        monthlyPaymentRuleService.createDefaultPaymentRuleConfigurations();
        MonthlyPaymentRulesJob monthlyPaymentRulesJob = new MonthlyPaymentRulesJob();
        monthlyPaymentRulesJob.run(null);


//        for(MonthlyPaymentRule rule: Setup.getRepository(MonthlyPaymentRuleRepository.class).findByFinished(false)) {
//            if(rule.getPaymentMethod() == PaymentRulePaymentMethod.LOCAL_TRANSFER) {
//                HousemaidPayrollLog log = new HousemaidPayrollLog(null, "Ali Saker Ali", "Syria, Damascus", "+************",
//                        1000d);
//                try {
//                    Attachment attachment = transferFilesService.generateLocalTransferFile(rule, Arrays.asList(log), new ArrayList<>());
//                    System.out.println(attachment.getPath());
//                }catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        }


    }

    @Test
    public void readConfirmationTransferFile() throws IOException, URISyntaxException {



        URL resource = PayrollGenerationLibrary.class.getResource("/transaction file.xlsx");
        File file = new File(resource.toURI());

        sendingTransactionService.extractTransactionNumbers(Storage.storeTemporary(file.getName(), new FileInputStream(file), "confirmation", false));

    }

    @Test
    public void readInternationalTransferFile() throws IOException, URISyntaxException {

        URL resource = PayrollGenerationLibrary.class.getResource("/International Transfer File of Jun.xlsx");
        File file = new File(resource.toURI());

        double amount = 0d;
        double charges = 0d;
        double total = 0d;

        int amountIndex = 7, chargesIndex = 8, totalIndex = 9;
        Workbook workbook = new XSSFWorkbook(new FileInputStream(file));

        Sheet firstSheet = workbook.getSheetAt(0);
        Iterator<Row> iterator = firstSheet.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if(count++ == 0) continue;
            Cell amountCell = row.getCell(amountIndex);
            Cell chargesCell = row.getCell(chargesIndex);
            Cell totalCell = row.getCell(totalIndex);

            if (!isCellEmpty(amountCell)) {
                amount += amountCell.getCellType() == CellType.STRING ? Double.parseDouble(amountCell.getStringCellValue()) :
                        amountCell.getNumericCellValue();
            }
            if (!isCellEmpty(chargesCell)) {
                charges += chargesCell.getCellType() == CellType.STRING ? Double.parseDouble(chargesCell.getStringCellValue()) :
                chargesCell.getNumericCellValue();
            }
            if (!isCellEmpty(totalCell)) {
                total += totalCell.getCellType() == CellType.STRING ? Double.parseDouble(totalCell.getStringCellValue()):
                totalCell.getNumericCellValue();
            }
        }

        System.out.println(String.format(" %s  %s %s", amount, charges, total));
    }

    public boolean isCellEmpty(final Cell cell) {
        if (cell == null) { // use row.getCell(x, Row.CREATE_NULL_AS_BLANK) to avoid null cells
            return true;
        }

        if (cell.getCellType() == CellType.BLANK) {
            return true;
        }

        if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().isEmpty()) {
            return true;
        }

        return false;
    }

    @Test
    @WithMockUser(value = "mm")
    public void singleOfficeStaffSalaries() {
        monthlyPaymentRuleService.createDefaultPaymentRuleConfigurations();

        OfficeStaff officeStaff = this.officeStaffMockService.getMockEntity();
        officeStaff.setName("Ali Saker Ali");
        officeStaff.setEmail("<EMAIL>");
        officeStaff.setStartingDate(DateUtil.addDays(DateUtil.addMonths(new Date(), -3), -7));
        this.officeStaffMockService.save(officeStaff);

        TransferDestination transferDestination = new TransferDestination();
        transferDestination.setOfficeStaff(officeStaff);
        transferDestination.setReceiveMoneyMethod(ReceiveMoneyMethod.BANK_TRANSFER);
        transferDestination.setSelfReceiver(true);
        Setup.getRepository(TransferDestinationRepository.class).save(transferDestination);

        officeStaff.setSelectedTransferDestination(transferDestination);

        MonthlyPaymentRule rule = accountantToDoService.createMonthlyRuleForSingleOfficeStaff(officeStaff);

        payrollController.decrementMonthlyRuleDates(officeStaff, rule);
        payrollController.decrementMonthlyRuleDates(officeStaff, rule);
        payrollController.decrementMonthlyRuleDates(officeStaff, rule);

        rule.setFinished(true);
        rule.setSingleOfficeStaff(true);
        Setup.getRepository(MonthlyPaymentRuleRepository.class).save(rule);

        PayrollAccountantTodo todo = accountantToDoService.createAccountantToDoForSingleOfficeStaff(rule, officeStaff);
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);

        OfficeStaffPayrollLog log = new OfficeStaffPayrollLog(officeStaff, "Ali Saker Ali", "Ali Saker arabic",
                "Syria - Damascus", officeStaff.getPhoneNumber(), 1000d, SalaryCurrency.USD.toString(), todo.getPayrollMonth(), 100.0, transferDestination.getCountry() != null ? transferDestination.getCountry().getName() : "");
        log.setPayrollAccountantTodo(todo);
        Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(log);

        payrollController.salaries(officeStaff, false);
    }


    @Test
    @WithMockUser(value = "mm")
    public void paySingleOfficeStaff() {
        monthlyPaymentRuleService.createDefaultPaymentRuleConfigurations();

        OfficeStaff officeStaff = this.officeStaffMockService.getMockEntity();
        officeStaff.setName("Ali Saker Ali");
        officeStaff.setEmail("<EMAIL>");
        officeStaff.setStartingDate(DateUtil.addDays(DateUtil.addMonths(new Date(), -3), -7));
        this.officeStaffMockService.save(officeStaff);

        TransferDestination transferDestination = new TransferDestination();
        transferDestination.setOfficeStaff(officeStaff);
        transferDestination.setReceiveMoneyMethod(ReceiveMoneyMethod.BANK_TRANSFER);
        transferDestination.setSelfReceiver(true);
        Setup.getRepository(TransferDestinationRepository.class).save(transferDestination);

        officeStaff.setSelectedTransferDestination(transferDestination);

        List<java.sql.Date> dates = new ArrayList<>();

        MonthlyPaymentRule rule = accountantToDoService.createMonthlyRuleForSingleOfficeStaff(officeStaff);

        payrollController.decrementMonthlyRuleDates(officeStaff, rule);
        dates.add(rule.getPayrollMonth());
        payrollController.decrementMonthlyRuleDates(officeStaff, rule);
        dates.add(rule.getPayrollMonth());
        payrollController.decrementMonthlyRuleDates(officeStaff, rule);

        rule.setFinished(true);
        rule.setSingleOfficeStaff(true);
        Setup.getRepository(MonthlyPaymentRuleRepository.class).save(rule);

        PayrollAccountantTodo todo = accountantToDoService.createAccountantToDoForSingleOfficeStaff(rule, officeStaff);
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);

        OfficeStaffPayrollLog log = new OfficeStaffPayrollLog(officeStaff, "Ali Saker Ali", "Ali Saker arabic",
                "Syria - Damascus", officeStaff.getPhoneNumber(), 1000d, SalaryCurrency.USD.toString(), todo.getPayrollMonth(), 100.0, transferDestination.getCountry() != null ? transferDestination.getCountry().getName() : "");
        log.setPayrollAccountantTodo(todo);
        Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(log);

        Collections.reverse(dates);
        payrollController.paySalaries(officeStaff, true, true, false, "", "", dates);
    }
}

