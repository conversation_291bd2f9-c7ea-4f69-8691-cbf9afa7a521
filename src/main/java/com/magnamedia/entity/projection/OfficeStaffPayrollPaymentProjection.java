package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface OfficeStaffPayrollPaymentProjection {

     @JsonSerialize(using = SalaryAmountSerializer.class)
     @Value("#{target.getPreviouslyUnpaidSalaries() != null ? " +
             " target.getTotalSalary() - target.getPreviouslyUnpaidSalaries()" +
             " : target.getTotalSalary()}")
     Double getSalary();

     @Value("#{target.getPayrollMonth()}")
     Date getDate();

     String getMonthYear();

     String getPaymentMethod();

     Boolean getWillBeIncluded();

     String getPaidOnDate();

     Boolean getCurrentlyExcludedFromPayroll();

     Boolean getForEmployeeLoan();

     Boolean getTransferred();

     Long getId();
}
