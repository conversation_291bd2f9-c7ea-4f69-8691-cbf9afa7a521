package com.magnamedia.entity.projection;

import com.magnamedia.helper.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 8/29/2020
 **/
public class HousemaidPayslipProjection {

    private String lang;

    private String housemaidName;

    private Map<String, Object> payslipHeader;

    private String payrollMonth;

    private List<Map<String, Object>> proratedInfo;

    private List<Map<String, Object>> additionsSection;

    private Map<String, Object> yourDeductionsThisMonthSection;

    private Map<String, Object> youReceivedThisMonthSection;

    private String whyDeductionSentence;

    private String clickHereToView;

    private List<Map<String, Object>> allDeductions;

    private Map<String, Object> loanYouPreviouslyHad;
    private Map<String, Object> loanRepayment;

    private Map<String, Object> totalDeductionsAndLoanRepayment;

    private Map<String, Object> weOnlyDeducted;

    private List<Map<String, Object>> deductionConclusion;

    private boolean linkedToYayaBot;

    private boolean payslipIsSent;

    public String getPayrollMonth() {
        return payrollMonth;
    }

    public void setPayrollMonth(Date payrollMonth) {
        this.payrollMonth = DateUtil.formatMonth(payrollMonth);
    }

    public List<Map<String, Object>> getProratedInfo() {
        return proratedInfo;
    }

    public void setProratedInfo(List<Map<String, Object>> proratedInfo) {
        this.proratedInfo = proratedInfo;
    }

    public List<Map<String, Object>> getAdditionsSection() {
        return additionsSection;
    }

    public void setAdditionsSection(List<Map<String, Object>> additionsSection) {
        this.additionsSection = additionsSection;
    }

    public Map<String, Object> getYourDeductionsThisMonthSection() {
        return yourDeductionsThisMonthSection;
    }

    public void setYourDeductionsThisMonthSection(Map<String, Object> yourDeductionsThisMonthSection) {
        this.yourDeductionsThisMonthSection = yourDeductionsThisMonthSection;
    }

    public Map<String, Object> getYouReceivedThisMonthSection() {
        return youReceivedThisMonthSection;
    }

    public void setYouReceivedThisMonthSection(Map<String, Object> youReceivedThisMonthSection) {
        this.youReceivedThisMonthSection = youReceivedThisMonthSection;
    }

    public List<Map<String, Object>> getAllDeductions() {
        return allDeductions;
    }

    public void setAllDeductions(List<Map<String, Object>> allDeductions) {
        this.allDeductions = allDeductions;
    }


    public Map<String, Object> getLoanYouPreviouslyHad() {
        return loanYouPreviouslyHad;
    }

    public void setLoanYouPreviouslyHad(Map<String, Object> loanYouPreviouslyHad) {
        this.loanYouPreviouslyHad = loanYouPreviouslyHad;
    }

    public Map<String, Object> getLoanRepayment() {
        return loanRepayment;
    }

    public void setLoanRepayment(Map<String, Object> loanRepayment) {
        this.loanRepayment = loanRepayment;
    }

    public Map<String, Object> getTotalDeductionsAndLoanRepayment() {
        return totalDeductionsAndLoanRepayment;
    }

    public void setTotalDeductionsAndLoanRepayment(Map<String, Object> totalDeductionsAndLoanRepayment) {
        this.totalDeductionsAndLoanRepayment = totalDeductionsAndLoanRepayment;
    }

    public Map<String, Object> getWeOnlyDeducted() {
        return weOnlyDeducted;
    }

    public void setWeOnlyDeducted(Map<String, Object> weOnlyDeducted) {
        this.weOnlyDeducted = weOnlyDeducted;
    }

    public List<Map<String, Object>> getDeductionConclusion() {
        return deductionConclusion;
    }

    public void setDeductionConclusion(List<Map<String, Object>> deductionConclusion) {
        this.deductionConclusion = deductionConclusion;
    }

    public boolean isLinkedToYayaBot() {
        return linkedToYayaBot;
    }

    public void setLinkedToYayaBot(boolean linkedToYayaBot) {
        this.linkedToYayaBot = linkedToYayaBot;
    }

    public boolean isPayslipIsSent() {
        return payslipIsSent;
    }

    public void setPayslipIsSent(boolean payslipIsSent) {
        this.payslipIsSent = payslipIsSent;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public Map<String, Object> getPayslipHeader() {
        return payslipHeader;
    }

    public void setPayslipHeader(Map<String, Object> payslipHeader) {
        this.payslipHeader = payslipHeader;
    }

    public String getWhyDeductionSentence() {
        return whyDeductionSentence;
    }

    public void setWhyDeductionSentence(String whyDeductionSentence) {
        this.whyDeductionSentence = whyDeductionSentence;
    }

    public String getClickHereToView() {
        return clickHereToView;
    }

    public void setClickHereToView(String clickHereToView) {
        this.clickHereToView = clickHereToView;
    }

    public String getHousemaidName() {
        return housemaidName;
    }

    public void setHousemaidName(String housemaidName) {
        this.housemaidName = housemaidName;
    }

}
