package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;
import com.magnamedia.extra.RepaymentStatus;
import com.magnamedia.module.type.PaymentStatus;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 5/21/2020
 **/
public interface RepaymentProjection {

    @JsonSerialize(using = SalaryAmountSerializer.class)
    public Double getAmount();

    public Date getRepaymentDate();

    public String getSalaryCurrency();

    public Boolean getPaidRepayment();

    public Boolean getDoNotDeductFromSalary();

    public RepaymentStatus getStatus();

    public Long getLastId();
}
