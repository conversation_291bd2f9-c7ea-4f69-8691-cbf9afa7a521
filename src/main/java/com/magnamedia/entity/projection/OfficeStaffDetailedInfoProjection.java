package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.serializer.IdLabelCodeNameListSerializer;
import com.magnamedia.module.type.OfficeStaffType;

import java.util.List;

public interface OfficeStaffDetailedInfoProjection {
    String getFirstName();

    String getLastName();

    String getEmail();

    @JsonSerialize(using = IdLabelSerializer.class)
    OfficeStaff getEmployeeManager();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getJobTitle();

    String getWorkingPattern();

    OfficeStaffType getEmployeeType();

    @JsonSerialize(using = IdLabelCodeNameListSerializer.class)
    List<PicklistItem> getDepartments();

    Attachment getProfilePictureAttachment();

    String getZohoExactJobTitle();
}