package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 5/21/2020
 **/
public interface EmployeeLoanProjection {

    public Long getId();

    public String getEmployeeName();

    public String getSalaryCurrency();

    public String getLoanType();

    @JsonSerialize(using = SalaryAmountSerializer.class)
    public Double getAmount();

    public Double getMonthlyRepaymentAmount();

    public Date getLoanDate();

    public Boolean getIsEditable();

    public Boolean getIsApproved();

    @JsonSerialize(using = SalaryAmountSerializer.class)
    public String getNotes();

    public Boolean getDoNotDeductFromSalary();
}
