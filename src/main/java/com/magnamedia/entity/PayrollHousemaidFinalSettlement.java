package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.TerminationMode;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> Al-masto
 * Created on 1/12/21
 */
@Entity @org.hibernate.envers.Audited
public class PayrollHousemaidFinalSettlement extends BaseEntity {

    @OneToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Enumerated(EnumType.STRING)
    private TerminationMode terminationMode;

    @Column
    private double prorated = 0;

    @Column(columnDefinition = "double default 0")
    private Double group1Salary = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double group2Salary = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double group4Salary = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double group5Salary = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double group6Salary = 0.0;




    @Column(columnDefinition = "int default 0")
    private Integer group1Days = 0;

    @Column(columnDefinition = "int default 0")
    private Integer group2Days = 0;

    @Column(columnDefinition = "int default 0")
    private Integer group4Days = 0;

    @Column(columnDefinition = "int default 0")
    private Integer group5Days = 0;

    @Column(columnDefinition = "int default 0")
    private Integer group6Days = 0;




    @Column
    private double vacations = 0;

    @Column
    private double gratuity = 0;

    @Column
    private double resignationFees = 0;

    @Column
    private double cashAdvance = 0;

    @Column
    @Deprecated
    @JsonIgnore
    private double renewalExpenses = 0;

    @Column
    private boolean payHerByCash = false;

    @Column
    private boolean revised = false;

    @Column
    private Boolean reviseSMSNeeded = false;

    @Column
    private double calculatedFinalSettlementValue = 0;

    @Column
    private Double revisedFinalSettlement;

    @Column
    @Deprecated
    @JsonIgnore
    private Double forgivenessAmountEnteredByExpertDelighter; // entered by expert delighter -- is filled only if the revisedFinalSettlement less than zero (the maid should pay us)

    @Column
    @Deprecated
    @JsonIgnore
    private Double forgivenessAmountCalculatedByERP; // used for fired maid

    @Column
    @Deprecated
    @JsonIgnore
    private Double forgivenessAmountCalculatedByERPNonRenewal; // used for non-renewal

    @Column
    @Deprecated
    @JsonIgnore
    private Double forgivenessAmountDecidedByManager;

    @Column
    @Deprecated
    @JsonIgnore
    private Double forgivenessAmountTawafuq;

    @Column
    private Double finalValue;

    @Column
    private Date finalSettlementCalculationDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean readyForPay = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean relatedToTodo = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollAccountantTodo payrollAccountantTodo;

    @Column
    @Enumerated(EnumType.STRING)
    private NoticePeriodServed noticePeriodServed = NoticePeriodServed.No;

    @Column(columnDefinition = "int default 0")
    private Integer daysServed = 0; //will be used in case noticePeriodServed = Partially

    @Column(columnDefinition = "int default 14")
    private Integer gratuityBasedDays = 14; //could be 14 by default or 21 from ERP

    @Column(columnDefinition = "double default 0")
    private double additions = 0;

    @Column(columnDefinition = "double default 0")
    private double additionsWithoutRaffleAndReferral = 0;

    @Column(columnDefinition = "double default 0")
    private double deductions = 0;

    @Column(columnDefinition = "double default 0")
    private double desiredFinalSettlementAmountDecidedByErp = 0.0; //this represent the desired FS amount that we want and the forgiveness amount will be calculated based on it

    @Column(columnDefinition = "double default 0")
    private double desiredFinalSettlementAmountDecidedByManager = 0.0; //this represent the desired FS amount that we want and the forgiveness amount will be calculated based on it entered by erp

    @Column(columnDefinition = "double default 0")
    private Double automaticForgivenessAmount = 0.0; // this will be automatically calculated and may be changed later

    public enum NoticePeriodServed {
        No,
        Yes,
        Partially;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public TerminationMode getTerminationMode() {
        return terminationMode;
    }

    public void setTerminationMode(TerminationMode terminationMode) {
        this.terminationMode = terminationMode;
    }

    public double getProrated() {
        return prorated;
    }

    public void setProrated(double prorated) {
        this.prorated = prorated;
    }

    public Double getGroup1Salary() {
        return group1Salary;
    }

    public void setGroup1Salary(Double group1Salary) {
        this.group1Salary = group1Salary;
    }

    public Double getGroup2Salary() {
        return group2Salary;
    }

    public void setGroup2Salary(Double group2Salary) {
        this.group2Salary = group2Salary;
    }

    public Integer getGroup1Days() {
        return group1Days;
    }

    public void setGroup1Days(Integer group1Days) {
        this.group1Days = group1Days;
    }

    public Integer getGroup2Days() {
        return group2Days;
    }

    public void setGroup2Days(Integer group2Days) {
        this.group2Days = group2Days;
    }

    public double getVacations() {
        return vacations;
    }

    public void setVacations(double vacations) {
        this.vacations = vacations;
    }

    public double getGratuity() {
        return gratuity;
    }

    public void setGratuity(double gratuity) {
        this.gratuity = gratuity;
    }

    public double getResignationFees() {
        return resignationFees;
    }

    public void setResignationFees(double resignationFees) {
        this.resignationFees = resignationFees;
    }

    public double getCashAdvance() {
        return cashAdvance;
    }

    public void setCashAdvance(double cashAdvance) {
        this.cashAdvance = cashAdvance;
    }

    @Deprecated
    public double getRenewalExpenses() {
        return renewalExpenses;
    }

    public void setRenewalExpenses(double renewalExpenses) {
        this.renewalExpenses = renewalExpenses;
    }

    public boolean isPayHerByCash() {
        return payHerByCash;
    }

    public void setPayHerByCash(boolean payHerByCash) {
        this.payHerByCash = payHerByCash;
    }

    public boolean isRevised() {
        return revised;
    }

    public void setRevised(boolean revised) {
        this.revised = revised;
    }

    public double getCalculatedFinalSettlementValue() {
        return calculatedFinalSettlementValue;
    }

    public void setCalculatedFinalSettlementValue(double calculatedFinalSettlementValue) {
        this.calculatedFinalSettlementValue = calculatedFinalSettlementValue;
    }

    @Deprecated
    public Double getForgivenessAmountEnteredByExpertDelighter() {
        return forgivenessAmountEnteredByExpertDelighter;
    }

    public void setForgivenessAmountEnteredByExpertDelighter(Double forgivenessAmountEnteredByExpertDelighter) {
        this.forgivenessAmountEnteredByExpertDelighter = forgivenessAmountEnteredByExpertDelighter;
    }

    @Deprecated
    public Double getForgivenessAmountCalculatedByERP() {
        return forgivenessAmountCalculatedByERP;
    }

    public void setForgivenessAmountCalculatedByERP(Double forgivenessAmountCalculatedByERP) {
        this.forgivenessAmountCalculatedByERP = forgivenessAmountCalculatedByERP;
    }

    public Double getRevisedFinalSettlement() {
        return revisedFinalSettlement;
    }

    public void setRevisedFinalSettlement(Double revisedFinalSettlement) {
        this.revisedFinalSettlement = revisedFinalSettlement;
    }

    public Double getFinalValue() {
        return finalValue;
    }

    public void setFinalValue(Double finalValue) {
        this.finalValue = finalValue;
    }

    @Deprecated
    public Double getForgivenessAmountCalculatedByERPNonRenewal() {
        return forgivenessAmountCalculatedByERPNonRenewal;
    }

    public void setForgivenessAmountCalculatedByERPNonRenewal(Double forgivenessAmountCalculatedByERPNonRenewal) {
        this.forgivenessAmountCalculatedByERPNonRenewal = forgivenessAmountCalculatedByERPNonRenewal;
    }

    public Boolean getReviseSMSNeeded() {
        return reviseSMSNeeded;
    }

    public void setReviseSMSNeeded(Boolean reviseSMSNeeded) {
        this.reviseSMSNeeded = reviseSMSNeeded;
    }

    @Deprecated
    public Double getForgivenessAmountDecidedByManager() {
        return forgivenessAmountDecidedByManager;
    }

    public void setForgivenessAmountDecidedByManager(Double forgivenessAmountDecidedByManager) {
        this.forgivenessAmountDecidedByManager = forgivenessAmountDecidedByManager;
    }

    @Deprecated
    public Double getForgivenessAmountTawafuq() {
        return forgivenessAmountTawafuq;
    }

    public void setForgivenessAmountTawafuq(Double forgivenessAmountTawafuq) {
        this.forgivenessAmountTawafuq = forgivenessAmountTawafuq;
    }

    public Date getFinalSettlementCalculationDate() {
        return finalSettlementCalculationDate;
    }

    public void setFinalSettlementCalculationDate(Date finalSettlementCalculationDate) {
        this.finalSettlementCalculationDate = finalSettlementCalculationDate;
    }

    public Boolean getReadyForPay() {
        return readyForPay;
    }

    public void setReadyForPay(Boolean readyForPay) {
        this.readyForPay = readyForPay;
    }

    public Boolean getRelatedToTodo() {
        return relatedToTodo;
    }

    public void setRelatedToTodo(Boolean relatedToTodo) {
        this.relatedToTodo = relatedToTodo;
    }

    public PayrollAccountantTodo getPayrollAccountantTodo() {
        return payrollAccountantTodo;
    }

    public void setPayrollAccountantTodo(PayrollAccountantTodo payrollAccountantTodo) {
        this.payrollAccountantTodo = payrollAccountantTodo;
    }

    public Double getGroup4Salary() {
        return group4Salary;
    }

    public void setGroup4Salary(Double group4Salary) {
        this.group4Salary = group4Salary;
    }

    public Integer getGroup4Days() {
        return group4Days;
    }

    public void setGroup4Days(Integer group4Days) {
        this.group4Days = group4Days;
    }

    public Double getGroup5Salary() {
        return group5Salary;
    }

    public void setGroup5Salary(Double group5Salary) {
        this.group5Salary = group5Salary;
    }

    public Double getGroup6Salary() {
        return group6Salary;
    }

    public void setGroup6Salary(Double group6Salary) {
        this.group6Salary = group6Salary;
    }

    public Integer getGroup5Days() {
        return group5Days;
    }

    public void setGroup5Days(Integer group5Days) {
        this.group5Days = group5Days;
    }

    public Integer getGroup6Days() {
        return group6Days;
    }

    public void setGroup6Days(Integer group6Days) {
        this.group6Days = group6Days;
    }

    public NoticePeriodServed getNoticePeriodServed() {
        return noticePeriodServed;
    }

    public void setNoticePeriodServed(NoticePeriodServed noticePeriodServed) {
        this.noticePeriodServed = noticePeriodServed;
    }

    public Integer getDaysServed() {
        return daysServed;
    }

    public void setDaysServed(Integer daysServed) {
        this.daysServed = daysServed;
    }

    public Integer getGratuityBasedDays() {
        return gratuityBasedDays;
    }

    public void setGratuityBasedDays(Integer gratuityBasedDays) {
        this.gratuityBasedDays = gratuityBasedDays;
    }

    public double getDesiredFinalSettlementAmountDecidedByErp() {
        return desiredFinalSettlementAmountDecidedByErp;
    }

    public void setDesiredFinalSettlementAmountDecidedByErp(double desiredFinalSettlementAmountDecidedByErp) {
        this.desiredFinalSettlementAmountDecidedByErp = desiredFinalSettlementAmountDecidedByErp;
    }

    public double getDesiredFinalSettlementAmountDecidedByManager() {
        return desiredFinalSettlementAmountDecidedByManager;
    }

    public void setDesiredFinalSettlementAmountDecidedByManager(double desiredFinalSettlementAmountDecidedByManager) {
        this.desiredFinalSettlementAmountDecidedByManager = desiredFinalSettlementAmountDecidedByManager;
    }

    public Double getAutomaticForgivenessAmount() {
        return automaticForgivenessAmount;
    }

    public void setAutomaticForgivenessAmount(Double automaticForgivenessAmount) {
        this.automaticForgivenessAmount = automaticForgivenessAmount;
    }

    public double getAdditions() {
        return additions;
    }

    public void setAdditions(double additions) {
        this.additions = additions;
    }

    public double getAdditionsWithoutRaffleAndReferral() {
        return additionsWithoutRaffleAndReferral;
    }

    public void setAdditionsWithoutRaffleAndReferral(double additionsWithoutRaffleAndReferral) {
        this.additionsWithoutRaffleAndReferral = additionsWithoutRaffleAndReferral;
    }

    public double getDeductions() {
        return deductions;
    }

    public void setDeductions(double deductions) {
        this.deductions = deductions;
    }
}
