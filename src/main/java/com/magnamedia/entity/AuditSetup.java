package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.helper.PicklistHelper;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity @org.hibernate.envers.Audited
public class AuditSetup extends BaseEntity {
    @Column(columnDefinition = "int(10) default 20")
    private Integer additionMarginPercentage; //Exception Margin Percentage for Addition according to salary

    @Column(columnDefinition = "int(10) default 20")
    private Integer bonusMarginPercentage; //Exception Margin Percentage for Bonus according to salary

    @Column(columnDefinition = "int(10) default 3")
    private Integer repeatedAdditionAndBonusLimit; //Repeated Addition or Bonus in {NUMBER} months

    @Column(columnDefinition = "int(10) default 1")
    private Integer terminationCompensationLimit; //Termination Compensation Limit for overseas employees should not be more than {NUMBER} month salary

    @Column(columnDefinition = "int(10) default 1")
    private Integer LoanAmountLimit; //Loan added should not be more than {NUMBER} month salary

    @Column
    @OneToMany(fetch = FetchType.LAZY)
    private List<PicklistItem> excludedJobTitles;

    @Column(columnDefinition = "varchar(255) default '0'")
    private String uniquePaymentCode = "0";

    public int getAdditionMarginPercentage() {
        return additionMarginPercentage;
    }

    public void setAdditionMarginPercentage(int additionMarginPercentage) {
        this.additionMarginPercentage = additionMarginPercentage;
    }

    public int getBonusMarginPercentage() {
        return bonusMarginPercentage;
    }

    public void setBonusMarginPercentage(int bonusMarginPercentage) {
        this.bonusMarginPercentage = bonusMarginPercentage;
    }

    public int getRepeatedAdditionAndBonusLimit() {
        return repeatedAdditionAndBonusLimit;
    }

    public void setRepeatedAdditionAndBonusLimit(int repeatedAdditionAndBonusLimit) {
        this.repeatedAdditionAndBonusLimit = repeatedAdditionAndBonusLimit;
    }

    public int getTerminationCompensationLimit() {
        return terminationCompensationLimit;
    }

    public void setTerminationCompensationLimit(int terminationCompensationLimit) {
        this.terminationCompensationLimit = terminationCompensationLimit;
    }

    public int getLoanAmountLimit() {
        return LoanAmountLimit;
    }

    public void setLoanAmountLimit(int loanAmountLimit) {
        LoanAmountLimit = loanAmountLimit;
    }

    public List<PicklistItem> getExcludedJobTitles() {
        if(excludedJobTitles == null)
            excludedJobTitles = new ArrayList<>();
        return excludedJobTitles;
    }

    public void setExcludedJobTitles(List<PicklistItem> excludedJobTitles) {
        this.excludedJobTitles = excludedJobTitles;
    }

    public String getUniquePaymentCode() {
        return uniquePaymentCode;
    }
    public void setUniquePaymentCode(String uniquePaymentCode) {
        this.uniquePaymentCode = uniquePaymentCode;
    }
}
