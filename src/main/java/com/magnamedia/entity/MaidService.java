package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 27, 2019
 * Jirra ACC-580
 */
@Entity @org.hibernate.envers.Audited
public class MaidService extends BaseEntity implements Serializable {

    public enum Type {
        ATM_CARD_RELOCATION,
        APPLY_FOR_NEW_EMIRATES_ID,
        RESEND_IMMIGRATION_CANCELLATION_PAPER,
        ARAMEX_PACKAGE_DELIVERED_TO_OLD_CLIENT,
        APPLY_FOR_OEC,
        FORGIVE_MAID_FROM_A_LOAN_AMOUNT,
        PICKUP_LUGGAGE,
        EXCLUDE_CONTENT_FROM_ARAMEX_SHIPMENT,
        ADD_ARAMEX_SHIPMENT,
        CANCEL_ARAMEX_SHIPMENT,
        START_VISA_RENEW,
        COVID_19_TESTS,
        OEC_REQUEST,
        RESET_RENEWAL_JOURNEY,
        DELETE_FINAL_SETTLEMENT,
        TRIGGER_NON_RENEWAL_FINAL_SETTLEMENT,
        APPLY_FOR_UNPAID_LEAVE_APPLICATION,
        MODIFY_PERSONAL_INFORMATION,
        GET_ANSARI_SALARY_STATMENT,
        UPDATE_BANK_INFO
    }

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;
    

    @Column
    private Boolean repeatEID;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Type type;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Boolean getRepeatEID() {
        return repeatEID;
    }

    public void setRepeatEID(Boolean repeatEID) {
        this.repeatEID = repeatEID;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }
    
    
}