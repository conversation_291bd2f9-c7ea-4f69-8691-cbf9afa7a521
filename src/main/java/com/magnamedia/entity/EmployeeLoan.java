package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.AfterUpdate;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.repository.AuditorActionRepository;
import com.magnamedia.repository.EmployeeLoanRepository;
import com.magnamedia.repository.RepaymentRepository;
import org.hibernate.envers.NotAudited;
import org.joda.time.LocalDate;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity @org.hibernate.envers.Audited
@Table(
        indexes = {
                @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class EmployeeLoan extends AbstractEmployeeLoan {

    //Jirra ACC-1863
    @Column(columnDefinition = "boolean default false")
    private boolean skipLoanRepaymentForCurrentPayroll;


    @Column(columnDefinition = "boolean default false")
    @NotNull
    private Boolean isEditable = false;

    @OneToMany(mappedBy = "employeeLoan",fetch = FetchType.LAZY)
    private List<Repayment> repaymentList= new ArrayList<>();

    @Column(columnDefinition = "boolean default true")
    private Boolean isShown = true;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmedAmountByAuditor = false;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmedRepetitiveSkippedByAuditor = false;

    @Transient
    private boolean logActionRequired = false;

    @OneToOne(fetch = FetchType.LAZY)
    @NotAudited
    @JsonSerialize(using = IdLabelSerializer.class)
    private ExpenseRequestTodo expenseRequestTodo;

    public boolean isLogActionRequired() {
        return logActionRequired;
    }

    public void setLogActionRequired(boolean logActionRequired) {
        this.logActionRequired = logActionRequired;
    }

    public EmployeeLoan() {
        repaymentList= new ArrayList<>();
    }

    public EmployeeLoan(AbstractEmployeeLoan employeeLoan, Boolean isEditable) {
        super(employeeLoan);
        repaymentList= new ArrayList<>();
        this.isEditable = isEditable;
    }

    public Boolean getEditable() {
        return isEditable;
    }

    public void setEditable(Boolean editable) {
        isEditable = editable;
    }

    @JsonIgnore
    public List<Repayment> getRepaymentList() {
        return repaymentList;
    }

    public void setRepaymentList(List<Repayment> repaymentList) {
        this.repaymentList = repaymentList;
    }

    public Boolean getShown() {
        return isShown;
    }

    public void setShown(Boolean shown) {
        isShown = shown;
    }

    public boolean isConfirmedAmountByAuditor() {
        return confirmedAmountByAuditor;
    }

    public void setConfirmedAmountByAuditor(boolean confirmedAmountByAuditor) {
        this.confirmedAmountByAuditor = confirmedAmountByAuditor;
    }

    @JsonIgnore
    public String getAmountWIthCurrency(){
        String lastSalarytWithCurrency = "";
        Double amount = getAmount();

        if (getOfficeStaff() != null && getOfficeStaff().getSalaryCurrency() != null){
            lastSalarytWithCurrency += getOfficeStaff().getSalaryCurrency().name() + " ";
        }

        if (amount != null){
            lastSalarytWithCurrency += NumberFormatter.formatNumber(amount);
        }else{
            lastSalarytWithCurrency = "0";
        }


        return lastSalarytWithCurrency;
    }

    @JsonIgnore
    public String getHousemaidRepaymentAmountWithCurrency(){
        String repaymentAmount = "AED ";

        if (getHousemaid() != null){
            if (getHousemaid().getDefaulMonthlyRepayment() == null){
                repaymentAmount += NumberFormatter.formatNumber(getHousemaid().getLoanBalance());
            }else{
                repaymentAmount += NumberFormatter.formatNumber(Math.min(getHousemaid().getDefaulMonthlyRepayment(), getHousemaid().getLoanBalance()));
            }
        }else
        {
            repaymentAmount += "0";
        }

        return repaymentAmount;
    }

    public boolean isSkipLoanRepaymentForCurrentPayroll() {
        return skipLoanRepaymentForCurrentPayroll;
    }

    public void setSkipLoanRepaymentForCurrentPayroll(boolean skipLoanRepaymentForCurrentPayroll) {
        this.skipLoanRepaymentForCurrentPayroll = skipLoanRepaymentForCurrentPayroll;
    }

    public boolean isConfirmedRepetitiveSkippedByAuditor() {
        return confirmedRepetitiveSkippedByAuditor;
    }

    public void setConfirmedRepetitiveSkippedByAuditor(boolean confirmedRepetitiveSkippedByAuditor) {
        this.confirmedRepetitiveSkippedByAuditor = confirmedRepetitiveSkippedByAuditor;
    }

    @JsonIgnore
    public ExpenseRequestTodo getExpenseRequestTodo() {
        return expenseRequestTodo;
    }

    public void setExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo) {
        this.expenseRequestTodo = expenseRequestTodo;
    }

    @AfterInsert
    @AfterUpdate
    public void afterCreateAndUpdate(){
        if(isLogActionRequired() && this.getHousemaid() != null && CurrentRequest.getUser() != null && CurrentRequest.getUser().hasPosition("payroll_auditor")){
            AuditorActionRepository auditorActionRepository = Setup.getRepository(AuditorActionRepository.class);
            AuditorAction auditorAction = new AuditorAction(this.getHousemaid(), CurrentRequest.getUser(), this.getAmount(),
                    this.getVersion() == 0 ? AuditorAction.ActionType.ADDING : AuditorAction.ActionType.EDITING,
                    AuditorAction.Source.LOAN, this.getNotes());
            auditorActionRepository.save(auditorAction);
        }

    }

    @BeforeDelete
    public void beforeDelete(){
        if(isLogActionRequired() && this.getHousemaid() != null && CurrentRequest.getUser() != null && CurrentRequest.getUser().hasPosition("payroll_auditor")){
            AuditorActionRepository auditorActionRepository = Setup.getRepository(AuditorActionRepository.class);
            AuditorAction auditorAction = new AuditorAction(this.getHousemaid(), CurrentRequest.getUser(),this.getAmount(),
                    AuditorAction.ActionType.DELETING, AuditorAction.Source.LOAN, this.getNotes());
            auditorActionRepository.save(auditorAction);
        }
    }
}
