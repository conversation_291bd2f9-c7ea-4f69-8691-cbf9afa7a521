package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterUpdate;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.BaseEntityWithAdditionalInfo;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.core.type.Nationality;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.magnamedia.extra.AnsariPaymentMethod.*;

/**
 * <AUTHOR>
 */
@Entity @org.hibernate.envers.Audited
@Searchable(showunName = "Housemaids", order = 1, permissionCode = "HousemaidList")
public class Housemaid extends BaseEntityWithAdditionalInfo {

    @Transient
    public static final Set<HousemaidStatus> rejectedStatuses = new HashSet<>(
            Arrays.asList(HousemaidStatus.EMPLOYEMENT_TERMINATED, HousemaidStatus.WAITING_TO_FILE_ABSCONDING,
                    HousemaidStatus.VISA_UNSUCCESSFUL, HousemaidStatus.REJECTED, HousemaidStatus.UNREACHABLE,
                    HousemaidStatus.UNREACHABLE_AFTER_EXIT, HousemaidStatus.PASSED_EXIT));


    //Jirra ACC-737
    @Transient
    public static final Set<HousemaidStatus> inOfficeStatus
            = new HashSet<>(
            Arrays.asList(
                    HousemaidStatus.AVAILABLE,
                    HousemaidStatus.PENDING_FOR_DISCIPLINE
            ));
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    PicklistItem freedomSource;
    @Column
    private String firstName;
    @Column
    private String middleName;
    @Column
    private String lastName;
    @Column
    @Label
    private String name;
    @Column
    private Date visaCancellationDate;
    @Transient
    private Double loanBalance;
    @Transient
    private Double unpaidDeductionBalance;
    @Transient
    private Double lostLoans;
    @Transient
    private Double forgiveness;
    @Transient
    private Double repayments;
    //Jirra ACC-449
    @NotAudited
    @Column(name = "BIRTHDATE")
    @Temporal(TemporalType.DATE)
    private Date birthdate;
    @Column
    @ColumnDefault("false")
    private Boolean excludedFromPayroll = false;

    @JsonIgnore
    @OneToOne(mappedBy = "housemaid", fetch = FetchType.LAZY)
    private HousemaidLastExcludeDetails housemaidLastExcludeDetails;
    @Transient
    private String excludedManuallyNotes;
    @Column
    private String insuranceNumber;
    @Column
    private java.sql.Date dateOfInsuranceEndorsement;

    @NotAudited
    @Column
    private java.sql.Date flightToExitdate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem location;

    //
    // @Transient
    // private WorkingDaysDetails workingDetails;
    @Transient
    private List<LoansInformation> loansInfo;
    @Column
    private Date passportExpirydate;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<ScheduledAnnualVacation> scheduledAnnualVacations;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<Repayment> repaymentsList;
    @NotAudited
    @Column
    private Double foodAllowance;
    @NotAudited
    @Column
    private Double housingAllowance;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<HousemaidPsid> housemaidPsids;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<FbPaySlipsLog> paySlipsLog;
    @NotAudited
    @Column
    private String phoneNumber;

    @NotAudited
    @Column(length = 30)
    private String whatsAppPhoneNumber;

    @NotAudited
    @Column(length = 30)
    private String normalizedWhatsAppPhoneNumber;

    @Enumerated(EnumType.STRING)
    @Transient
    private HousemaidStatus oldStatus;
    @Enumerated(EnumType.STRING)
    @Column
    private PendingStatus pendingStatus;
    //Jirra ACC-608
    @Enumerated(EnumType.STRING)
    @Column
    private HousemaidType housemaidType;

    // ACC-1862
    @Column
    private Integer workPaidVacationDays;

    //Jirra ACC-1093
    @Column
    private Double additionToBalanceDeductionLimit;

    //Jirra SMM-1405
    @NotAudited
    @Column
    private String normalizedPhoneNumber;

    //From Firas
    @NotAudited
    @Column
    private Date availableSince;

    @NotAudited
    @Column
    private Date availableCheckDate;

    //Jirra ACC-1793
    @Column(columnDefinition = "boolean default false")
    private Boolean isAfricanCandidate = false;

    //ACC-1515
    @Column(columnDefinition = "boolean default false")
    private Boolean isMaidsAt = false;
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private HousemaidStatus status;
    @Column
    private Date startDate;
    private Timestamp landedInDubaiDate;
    @Column(columnDefinition = "double default 0")
    private Double primarySalary = 0.0;
    @Column(columnDefinition = "double default 0")
    private Double monthlyLoan = 0.0;
    @Column(columnDefinition = "double default 0")
    private Double overTime = 0.0;
    @Column(columnDefinition = "double default 0")
    private Double holiday = 0.0;
    @Column(columnDefinition = "double default 0")
    private Double airfareFee = 0.0;
    @Column(columnDefinition = "double default 0")
    private Double accommodationSalary = 0.0;
    // @Column
    // private Double basicSalary;
//	@Formula("IFNULL(AIRFARE_FEE, 0) + IFNULL(HOLIDAY, 0)+ IFNULL(OVER_TIME, 0) + IFNULL(MONTHLY_LOAN,0) + IFNULL(PRIMARY_SALARY, 0)")
    @Column(columnDefinition = "double default 0")
    private Double basicSalary;
    @Column
    private Double oldRenewalBasicSalary;
    @Column(columnDefinition = "boolean default false")
    private Boolean isBeingPaid50PercentSalary = false;
    // @Column
    // private Double basicSalary;
    // @Column
    // private Double lostLoans;
    @Enumerated(EnumType.STRING)
    private HousemaidLiveplace living;
    @Column
    private Double defaulMonthlyRepayment;
    @Column
    private String agentId;
    @Column
    private String employeeAccountWithAgent;
    @Column
    private String ansariUniqueId;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem manager;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem payrollType;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FreedomOperator freedomOperator;
    @Enumerated(EnumType.STRING)
    private HousemaidReligion religion;
    @Column(insertable = false, updatable = false)
    @JsonIgnore
    private String facebookAcc;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem nationality;
    @ManyToOne(fetch = FetchType.LAZY)
    @NotAudited
    @JsonSerialize(using = IdJsonSerializer.class)
    private NewRequest visaNewRequest;
    @Column(insertable = false, updatable = false)
    private Timestamp rejectDate;
    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    @NotAudited
    private HousemaidAccommodation accommodation;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<Contract> contracts;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<HousemaidForgiveness> forgivenesses;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<WarningLetter> warningLetters;
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<PayrollManagerNote> managerNotes;
    @Column
    private boolean isAgency = false;
    @Column
    private Boolean freedomMaid = false;
    @Column
    private Boolean cleanExitMaid = Boolean.FALSE;
    @NotAudited
    @OneToMany(cascade = CascadeType.MERGE, mappedBy = "housemaid", fetch = FetchType.LAZY)
    private List<MatchingType> matchingTypes;
    @Column(columnDefinition = "tinyint(1) default 0")
    private Boolean notArabicSpeaker;
    @OneToMany(mappedBy = "housemaid", fetch = FetchType.LAZY)
    @JsonIgnore
    @NotAudited
    private List<HousemaidDocument> documents;
    @NotAudited
    @Column
    private Date pendingSince;
    @NotAudited
    @Column
    private Date pendingUntil;
    @Column
    private String passportNumber;
    //Jirra ACC-737
    @Transient
    private String initialedName;
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem reasonOfPending;
    //Jirra ACC-837
    @Transient
    private String eid;
    @Transient
    private Attachment forntEid;
    @Transient
    private Double previousStateBasicSalary;

    @NotAudited
    @Column
    private Date dateOfTermination;

    @Column
    private Date replacementSalaryStartDate;

    @Column
    private Date lastPayrollLockDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean withMolNumber = false;

    /**
    * <AUTHOR> Qazzaz
    * @reason PAY-125
    * start
    */
    @Column
    private Date lastChangeConfirmByAuditor;

    @Column
    private Date lastChangeAmountConfirmByAuditor;
    /**
    * end
    */

    @Column
    private java.sql.Date lastWithoutClientPayrollMonthConfirmed;

    @Column
    private Date lastZeroSalaryConfirmByAuditor;

    @Column
    private Date lastPaymentConfirmByAuditor;

    @Enumerated(EnumType.STRING)
    @Column(nullable = true)
    private CardStatus cardStatus= CardStatus.REQUEST_SENT_TO_ANSARI;

    @Column(columnDefinition = "boolean default false")
    private Boolean overTimeAddedBefore = false;

    @Column (columnDefinition = "boolean default false")
    private Boolean updateSalaryComponents = Boolean.FALSE;

    @Column
    private String urlToken;

    @NotAudited
    @Enumerated(EnumType.STRING)
    private TerminationMode modeOfTermination;

    @Column
    private Date firstAvailableDate;

    @Column
    private Date finalSettlementCalculationDate;

    @NotAudited
    @Formula("(SELECT w.TASK_MOVE_OUT_DATE FROM NEWREQUESTS n INNER JOIN WORKFLOWTASKHISTORYS w ON n.ID = w.TASK_ID WHERE n.ID = VISA_NEW_REQUEST_ID and w.TASK_NAME = 'Waiting for the maid to go to medical test and EID fingerprinting' AND w.TYPE = 'com.magnamedia.entity.NewRequest' ORDER BY w.TASK_MOVE_OUT_DATE IS null DESC, w.TASK_MOVE_OUT_DATE DESC LIMIT 1)")
    private Date finishedMedicalTest;

    @Column(columnDefinition = "boolean default false")
    private Boolean convertMvToCCFromProfile = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean travelAssist = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FreedomOperator assignedOperator;

    public Date getFinalSettlementCalculationDate() {
        return finalSettlementCalculationDate;
    }

    public void setFinalSettlementCalculationDate(Date finalSettlementCalculationDate) {
        this.finalSettlementCalculationDate = finalSettlementCalculationDate;
    }

    public Date getFirstAvailableDate() {
        return firstAvailableDate;
    }

    public void setFirstAvailableDate(Date firstAvailableDate) {
        this.firstAvailableDate = firstAvailableDate;
    }

    public TerminationMode getModeOfTermination() {
        return modeOfTermination;
    }

    public void setModeOfTermination(TerminationMode modeOfTermination) {
        this.modeOfTermination = modeOfTermination;
    }

    @Column
    private Date yayaLastLoginDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem yayaLanguage;

    @Column
    private Double lastCCSalary;
    @Column
    private Double lastMVSalary;

    @NotAudited
    @Formula("(EXISTS (select 1 from HOUSEMAIDDOCUMENTS h inner join ATTACHMENTS a on a.OWNER_TYPE = 'HousemaidDocument' and a.OWNER_ID = h.ID where h.HOUSEMAID_ID = ID and a.TAG in ('EMIRATES_ID_FRONT_SIDE', 'EMIRATES_ID_BACK_SIDE', 'EMIRATE_ID', 'eid_front_side', 'eid_front', 'eid_front', 'eid_back_side', 'eid_back') ))")
    private Boolean hasEidAttachments;

    @NotAudited
    @Formula("(EXISTS (select 1 from NEWREQUESTS n inner join ATTACHMENTS a on a.OWNER_TYPE = 'NewRequest' and a.OWNER_ID = n.ID where n.HOUSEMAID_ID = ID and a.TAG in ('EMIRATES_ID_FRONT_SIDE', 'EMIRATES_ID_BACK_SIDE', 'EMIRATE_ID', 'eid_front_side', 'eid_front', 'eid_front', 'eid_back_side', 'eid_back') ))")
    private Boolean visaRequestHasEidAttachments;

    @Enumerated(EnumType.STRING)
    @Column
    private HousemaidType oldHousemaidType;

    @Transient
    private String switchedMaid;


    //Jirra ACC-832
//    public Timestamp getInsuranceCancellationDate() {
//        NewRequest newRequest = this.getVisaNewRequest();
//        CancelRequest cancelRequest = newRequest == null ? null : newRequest.getCancelRequest();
//        return cancelRequest == null ? null : cancelRequest.findTaskMoveOutDate("Cancel Insurance");
//    }

    @Enumerated
    @Column
    private Gender gender;

    @Column(columnDefinition = "boolean default false")
    private Boolean liveOut = false;


    public List<HousemaidPsid> getHousemaidPsids() {
        return housemaidPsids;
    }

    public void setHousemaidPsids(List<HousemaidPsid> housemaidPsids) {
        this.housemaidPsids = housemaidPsids;
    }

    public Double getFoodAllowance() {
        return foodAllowance;
    }

    public void setFoodAllowance(Double foodAllowance) {
        this.foodAllowance = foodAllowance;
    }

    public Double getHousingAllowance() {
        return housingAllowance;
    }

    public void setHousingAllowance(Double housingAllowance) {
        this.housingAllowance = housingAllowance;
    }

    public Timestamp getLandedInDubaiDate() {
        return landedInDubaiDate;
    }

    public void setLandedInDubaiDate(Timestamp landedInDubaiDate) {
        this.landedInDubaiDate = landedInDubaiDate;
    }

    public Double getPreviousStateBasicSalary() {
        return previousStateBasicSalary;
    }

    public void setPreviousStateBasicSalary(Double previousStateBasicSalary) {
        this.previousStateBasicSalary = previousStateBasicSalary;
    }

    public Boolean getIsBeingPaid50PercentSalary() {
        if (isBeingPaid50PercentSalary == null) {
            return false;
        }
        return isBeingPaid50PercentSalary;
    }

    public void setIsBeingPaid50PercentSalary(Boolean isBeingPaid50PercentSalary) {
        this.isBeingPaid50PercentSalary = isBeingPaid50PercentSalary;
    }

    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    public List<HousemaidDocument> getDocuments() {
        return documents == null ? new ArrayList<>() : documents;
    }

    public void setDocuments(List<HousemaidDocument> documents) {
        this.documents = documents;
    }

    public List<MatchingType> getMatchingTypes() {
        return matchingTypes;
    }

    public void setMatchingTypes(List<MatchingType> matchingTypes) {
        this.matchingTypes = matchingTypes;
    }

    public Timestamp getRejectDate() {
        return rejectDate;
    }

    public void setRejectDate(Timestamp rejectDate) {
        this.rejectDate = rejectDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private void updateName() {
        this.name = "";
        if (this.firstName != null && !this.firstName.isEmpty()) {
            name += firstName + " ";
        }
        if (this.middleName != null && !this.middleName.isEmpty()) {
            name += middleName + " ";
        }
        if (this.lastName != null && !this.lastName.isEmpty()) {
            name += lastName + " ";
        }
        if (this.name != null) {
            this.name = this.name.trim();
        }

    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
        updateName();
    }

    @JsonIgnore
    public String getFormattedFirstName() {
        if (firstName != null && !"".equals(firstName))
            return StringHelper.enumToCapitalizedFirstLetter(firstName);
        return firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
        updateName();
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
        updateName();
    }

    public Date getPassportExpirydate() {
        return passportExpirydate;
    }

    public void setPassportExpirydate(Date passportExpirydate) {
        this.passportExpirydate = passportExpirydate;
    }

    public Date getVisaCancellationDate() {
        return visaCancellationDate;
    }

    public void setVisaCancellationDate(Date visaCancellationDate) {
        this.visaCancellationDate = visaCancellationDate;
    }

    public HousemaidStatus getStatus() {
        return status;
    }

    public void setStatus(HousemaidStatus status) {
        this.status = status;
        this.oldStatus = this.status;

        if (oldStatus != null
                && status != null
                && oldStatus.equals(HousemaidStatus.PENDING_FOR_DISCIPLINE)
                && pendingStatus != null
                && !oldStatus.equals(status)) {

            pendingStatus = null;
        }
    }

    public PendingStatus getPendingStatus() {
        return pendingStatus;
    }

    public void setPendingStatus(PendingStatus pendingStatus) {
        this.pendingStatus = pendingStatus;
    }


    public Double getLoanBalance() {
        // Set<HousemaidStatus> rejectedStatuses = new
        // HashSet<HousemaidStatus>(Arrays.asList(HousemaidStatus.REJECTED,
        // HousemaidStatus.UNREACHABLE, HousemaidStatus.UNREACHABLE_AFTER_EXIT,
        // HousemaidStatus.PASSED_EXIT));
        //if (loanBalance == null) {
        if (rejectedStatuses.contains(this.getStatus())) {
            this.loanBalance = 0.0;
        } else {
            EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);

            Double originalLoan = employeeLoanRepository.sumLoansByMaid(this);
            Double forgiveNess = getForgiveness();
            Double repayment = getRepayments();
            if (originalLoan == null) {
                originalLoan = 0.0;
            }
            if (forgiveNess == null) {
                forgiveNess = 0.0;
            }
            if (repayment == null) {
                repayment = 0.0;
            }
            this.loanBalance = originalLoan - forgiveNess - repayment;
        }
        //}
        this.loanBalance = Math.round(Double.valueOf(this.loanBalance) * 100) / 100.0;
        return this.loanBalance;
    }

    public Double getUnpaidDeductionBalance() {
        if (rejectedStatuses.contains(this.getStatus())) {
            this.unpaidDeductionBalance = 0.0;
        } else {
            UnpaidDeductionRepository unpaidDeductionRepository = Setup.getRepository(UnpaidDeductionRepository.class);
            UnpaidDeductionRepaymentRepository unpaidDeductionRepaymentRepository = Setup.getRepository(UnpaidDeductionRepaymentRepository.class);

            Double unpaidDeductions = unpaidDeductionRepository.sumUnpaidDeductionsByMaid(this);
            Double unpaidDeductionRepayments = unpaidDeductionRepaymentRepository.sumUnpaidDeductionRepaymentByMaid(this);

            if (unpaidDeductions == null) {
                unpaidDeductions = 0.0;
            }
            if (unpaidDeductionRepayments == null) {
                unpaidDeductionRepayments = 0.0;
            }
            this.unpaidDeductionBalance = unpaidDeductions - unpaidDeductionRepayments;
        }
        //}
        this.unpaidDeductionBalance = Math.round(Double.valueOf(this.unpaidDeductionBalance) * 100) / 100.0;
        return this.unpaidDeductionBalance;
    }

    public void setLoanBalance(Double loanBalance) {

        this.loanBalance = loanBalance;

    }

    public Double getLostLoans() {
        // Set<HousemaidStatus> rejectedStatuses = new
        // HashSet<HousemaidStatus>(Arrays.asList(HousemaidStatus.REJECTED,
        // HousemaidStatus.UNREACHABLE, HousemaidStatus.UNREACHABLE_AFTER_EXIT,
        // HousemaidStatus.PASSED_EXIT));
        EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);

        if (rejectedStatuses.contains(this.getStatus())) {
            Double originalLoan = employeeLoanRepository.sumLoansByMaid(this);
            Double forgiveNess = getForgiveness();
            Double repayment = getRepayments();
            if (originalLoan == null) {
                originalLoan = 0.0;
            }
            if (forgiveNess == null) {
                forgiveNess = 0.0;
            }
            if (repayment == null) {
                repayment = 0.0;
            }
            lostLoans = originalLoan - forgiveNess - repayment;
            return lostLoans;
        }
        return lostLoans = 0.0;
    }

    public void setLostLoans(Double lostLoans) {
        this.lostLoans = lostLoans;
    }

    public List<ScheduledAnnualVacation> getScheduledAnnualVacations() {
        return scheduledAnnualVacations;
    }

    public void setScheduledAnnualVacations(List<ScheduledAnnualVacation> scheduledAnnualVacations) {
        this.scheduledAnnualVacations = scheduledAnnualVacations;
    }

    public List<Repayment> getRepaymentsList() {
        return repaymentsList == null ? new ArrayList<>() : repaymentsList;
    }

    public void setRepaymentsList(List<Repayment> repaymentsList) {
        this.repaymentsList = repaymentsList;
    }

    public Date getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(Date birthdate) {
        this.birthdate = birthdate;
    }

    public List<LoansInformation> getLoansInfo() {
        List<LoansInformation> allLoansInfo = new ArrayList<LoansInformation>();
        EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);
        allLoansInfo = employeeLoanRepository.getMaidLoansInfo(this);

        return allLoansInfo;
    }

    public void setLoansInfo(List<LoansInformation> loansInfo) {

        this.loansInfo = loansInfo;
    }

    public Double getForgiveness() {
        forgiveness = 0.0;
        HousemaidForgivenessRepository housemaidForgivenessRepository = Setup
                .getRepository(HousemaidForgivenessRepository.class);
        forgiveness = housemaidForgivenessRepository.sumForgivenessByMaid(this);
        return forgiveness;
    }

    public void setForgiveness(Double forgiveness) {
        this.forgiveness = forgiveness;
    }

    //Jirra ACC-1085
    public Double getRepayments() {
        repayments = 0.0;
//		RepaymentRepository repaymentRepository = Setup.getRepository(
//			RepaymentRepository.class);
//		repayments = repaymentRepository.sumRepaymentsByOfficeStaff(this);
        for (Repayment r : this.getRepaymentsList())
            if (r.getPaidRepayment() != null && r.getPaidRepayment())
                repayments += r.getAmount();
        return repayments;
    }

    public void setRepayments(Double repayments) {
        this.repayments = repayments;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Double getOldRenewalBasicSalary() {
        return oldRenewalBasicSalary;
    }

    public void setOldRenewalBasicSalary(Double oldRenewalBasicSalary) {
        this.oldRenewalBasicSalary = oldRenewalBasicSalary;
    }

    public HousemaidLiveplace getLiving() {
        return living;
    }

    public void setLiving(HousemaidLiveplace living) {
        this.living = living;
    }

    // public Double getLostLoans() {
    // return lostLoans;
    // }
    //
    // public void setLostLoans(Double lostLoans) {
    // this.lostLoans = lostLoans;
    // }
    public Double getDefaulMonthlyRepayment() {
        return defaulMonthlyRepayment;
    }

    public void setDefaulMonthlyRepayment(Double defaulMonthlyRepayment) {
        this.defaulMonthlyRepayment = defaulMonthlyRepayment;
    }

    public String getAgentId() {
        return visaNewRequest != null ? visaNewRequest.getAgentId() : null;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getEmployeeAccountWithAgent() {
        return  visaNewRequest != null ? visaNewRequest.getEmployeeAccountWithAgent() : null;
    }

    public void setEmployeeAccountWithAgent(String employeeAccountWithAgent) {
        this.employeeAccountWithAgent = employeeAccountWithAgent;
    }

    public String getAnsariUniqueId() {
        return visaNewRequest!= null ? visaNewRequest.getEmployeeUniqueId() : null;
    }

    public void setAnsariUniqueId(String ansariUniqueId) {
        this.ansariUniqueId = ansariUniqueId;
    }

    public HousemaidReligion getReligion() {
        return religion;
    }

    public void setReligion(HousemaidReligion religion) {
        this.religion = religion;
    }

    public String getFacebookAcc() {
        return facebookAcc;
    }

    public void setFacebookAcc(String facebookAcc) {
        this.facebookAcc = facebookAcc;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public NewRequest getVisaNewRequest() {
        return visaNewRequest;
    }

    public void setVisaNewRequest(NewRequest visaNewRequest) {
        this.visaNewRequest = visaNewRequest;
    }

    public HousemaidAccommodation getAccommodation() {
        return accommodation;
    }

    public void setAccommodation(HousemaidAccommodation accommodation) {
        this.accommodation = accommodation;
    }

    public List<Contract> getContracts() {
        return contracts;
    }

    public void setContracts(List<Contract> contracts) {
        this.contracts = contracts;
    }

    public List<HousemaidForgiveness> getForgivenesses() {
        return forgivenesses;
    }

    public void setForgivenesses(List<HousemaidForgiveness> forgivenesses) {
        this.forgivenesses = forgivenesses;
    }

    public List<WarningLetter> getWarningLetters() {
        return warningLetters;
    }

    public void setWarningLetters(List<WarningLetter> warningLetters) {
        this.warningLetters = warningLetters;
    }

    public List<PayrollManagerNote> getManagerNotes() {
        return managerNotes;
    }

    public void setManagerNotes(List<PayrollManagerNote> managerNotes) {
        this.managerNotes = managerNotes;
    }

    public boolean isIsAgency() {
        return isAgency;
    }

    public void setIsAgency(boolean isAgency) {
        this.isAgency = isAgency;
    }

    public PicklistItem getFreedomSource() {
        return freedomSource;
    }

    public void setFreedomSource(PicklistItem freedomSource) {
        this.freedomSource = freedomSource;
    }

    public Boolean getFreedomMaid() {
        return freedomMaid;
    }

    public void setFreedomMaid(Boolean freedomMaid) {
        this.freedomMaid = freedomMaid;
    }

    public Boolean getCleanExitMaid() {
        return cleanExitMaid;
    }

    public void setCleanExitMaid(Boolean cleanExitMaid) {
        this.cleanExitMaid = cleanExitMaid;
    }

    public Double getPrimarySalary() {
        return primarySalary;
    }

    public void setPrimarySalary(Double primarySalary) {
        this.primarySalary = primarySalary;
    }

    public Double getMonthlyLoan() {
        return monthlyLoan;
    }

    public void setMonthlyLoan(Double monthlyLoan) {
        this.monthlyLoan = monthlyLoan;
    }

    public Double getOverTime() {
        return overTime;
    }

    public void setOverTime(Double overTime) {
        this.overTime = overTime;
    }

    public Double getHoliday() {
        return holiday;
    }

    public void setHoliday(Double holiday) {
        this.holiday = holiday;
    }

    public Double getAirfareFee() {
        return airfareFee;
    }

    public void setAirfareFee(Double airfareFee) {
        this.airfareFee = airfareFee;
    }

    public Double getAccommodationSalary() {
        return accommodationSalary;
    }

    public void setAccommodationSalary(Double accommodationSalary) {
        this.accommodationSalary = accommodationSalary;
    }

    @JsonIgnore
    public boolean isPhilippines() {
        return this.getNationality() != null
                && Nationality.PHILIPPINES.equalsIgnoreCase(this.getNationality().getCode());
    }

    public FreedomOperator getFreedomOperator() {
        return freedomOperator;
    }

    public void setFreedomOperator(FreedomOperator freedomOperator) {
        this.freedomOperator = freedomOperator;
    }

    public Boolean getExcludedFromPayroll() {
        return excludedFromPayroll;
    }

    public void setExcludedFromPayroll(Boolean excludedFromPayroll) {
        this.excludedFromPayroll = excludedFromPayroll;
    }

    public HousemaidLastExcludeDetails getHousemaidLastExcludeDetails() {
        return housemaidLastExcludeDetails;
    }

    public void setHousemaidLastExcludeDetails(HousemaidLastExcludeDetails housemaidLastExcludeDetails) {
        this.housemaidLastExcludeDetails = housemaidLastExcludeDetails;
    }

    public String getExcludedManuallyNotes() {
        return excludedManuallyNotes;
    }

    public void setExcludedManuallyNotes(String excludedManuallyNotes) {
        this.excludedManuallyNotes = excludedManuallyNotes;
    }

    public PicklistItem getManager() {
        return manager;
    }

    public void setManager(PicklistItem manager) {
        this.manager = manager;
    }

    public PicklistItem getPayrollType() {
        return payrollType;
    }

    public void setPayrollType(PicklistItem payrollType) {
        this.payrollType = payrollType;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public java.sql.Date getDateOfInsuranceEndorsement() {
        return dateOfInsuranceEndorsement;
    }

    public void setDateOfInsuranceEndorsement(java.sql.Date dateOfInsuranceEndorsement) {
        this.dateOfInsuranceEndorsement = dateOfInsuranceEndorsement;
    }

//	@Transient
//	private boolean salaryUpdatedFromJob = false;

//    public String getHousemaidStringSource() {
//        if (isIsAgency())
//            return "Agency";
//        if (getFreedomMaid()) {
//            String source = "UNKNOWN";
//            if (getFreedomOperator() != null)
//                source = getFreedomOperator().getName();
//            return "Freedom: " + source;
//        }
//
//        return "Clean Exit";
//    }

    @PostLoad
    public void setPreviousState() {
        previousStateBasicSalary = this.getBasicSalary();
        // copy fields
    }

    public Boolean getNotArabicSpeaker() {
        return notArabicSpeaker;
    }

    public void setNotArabicSpeaker(Boolean notArabicSpeaker) {
        this.notArabicSpeaker = notArabicSpeaker;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public HousemaidType getHousemaidType() {
        return housemaidType;
    }

    public void setHousemaidType(HousemaidType housemaidType) {
        this.housemaidType = housemaidType;
    }

    public Double getAdditionToBalanceDeductionLimit() {
        return additionToBalanceDeductionLimit;
    }

    public void setAdditionToBalanceDeductionLimit(Double additionToBalanceDeductionLimit) {
        this.additionToBalanceDeductionLimit = additionToBalanceDeductionLimit;
    }

    public String getNormalizedPhoneNumber() {
        return normalizedPhoneNumber;
    }

    public void setNormalizedPhoneNumber(String normalizedPhoneNumber) {
        this.normalizedPhoneNumber = normalizedPhoneNumber;
    }

    public Date getAvailableSince() {
        return availableSince;
    }

    public void setAvailableSince(Date availableSince) {
        this.availableSince = availableSince;
    }

    public Boolean getIsMaidsAt() {
        return isMaidsAt != null && isMaidsAt;
    }

    public void setIsMaidsAt(Boolean isMaidsAt) {
        this.isMaidsAt = isMaidsAt;
    }

    public Boolean getIsAfricanCandidate() {
        return isAfricanCandidate;
    }

    public void setIsAfricanCandidate(Boolean isAfricanCandidate) {
        this.isAfricanCandidate = isAfricanCandidate;
    }

    public Date getAvailableCheckDate() {
        if (availableCheckDate == null) {
            availableCheckDate = getAvailableSince();
        }
        return availableCheckDate;
    }

    public void setAvailableCheckDate(Date availableCheckDate) {
        this.availableCheckDate = availableCheckDate;
    }

    public Client getCurrentClient() {
        Predicate<Contract> byContractStatus = contract -> contract.getStatus() == ContractStatus.ACTIVE;
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(this).stream()
                .filter(byContractStatus).collect(Collectors.<Contract>toList());

        if (activeContracts.size() > 0) {
            return activeContracts.get(0).getClient();
        } else {
            return null;
        }
    }

    @JsonIgnore
    public Contract getActiveContract() {
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaidAndStatus(this, ContractStatus.ACTIVE);

        if (activeContracts.size() > 0) {
            return activeContracts.get(0);
        } else {
            return null;
        }
    }

    @JsonIgnore
    public boolean getNotifySalaryRelease() {
        Predicate<Contract> byContractStatus = contract -> contract.getStatus() == ContractStatus.ACTIVE;
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(this).stream()
                .filter(byContractStatus).collect(Collectors.<Contract>toList());

        if (activeContracts.size() > 0) {
            return activeContracts.get(0).getNotifySalaryRelease();
        } else {
            return false;
        }
    }

    public List<FbPaySlipsLog> getPaySlipsLog() {
        return paySlipsLog;
    }

    public void setPaySlipsLog(List<FbPaySlipsLog> paySlipsLog) {
        this.paySlipsLog = paySlipsLog;
    }

    public Date getPendingSince() {
        return pendingSince;
    }

    public void setPendingSince(Date pendingSince) {
        this.pendingSince = pendingSince;
    }

    public Date getPendingUntil() {
        return pendingUntil;
    }

    public void setPendingUntil(Date pendingUntil) {
        this.pendingUntil = pendingUntil;
    }

    public PicklistItem getReasonOfPending() {
        return reasonOfPending;
    }

    public void setReasonOfPending(PicklistItem reasonOfPending) {
        this.reasonOfPending = reasonOfPending;
    }

//    public String getEid() {
//        if (this.visaNewRequest != null)
//            return this.visaNewRequest.getEidApplicationNumber();
//        return eid;
//    }
//
//    public Attachment getForntEid() {
//        HousemaidDocumentRepository documentRepository =
//                Setup.getRepository(HousemaidDocumentRepository.class);
//        PicklistItem docType = PicklistHelper.getItem("HousemaidDocType", "eid_front_side");
//        List<HousemaidDocument> docs = documentRepository.findByHousemaidAndType(this, docType);
//        if (docs != null && !docs.isEmpty())
//            return docs.get(0).getAttachment("eid_front_side");
//        return null;
//    }

    //Jirra ACC-737
    public boolean isInOffice() {
        if (this.getStatus() == null) {
            return false;
        } else {
            return inOfficeStatus.contains(this.getStatus());
        }
    }

    public String getInitaledName() {

        if (name == null) {
            return "";
        }
        if (name.equals("")) {
            return "";
        }
        if (initialedName == null) {

            String[] splits = this.name.split(" ");
            if (splits.length > 0) {
                initialedName = ""
                        + splits[0];
                for (int i = 1; i < splits.length; i++) {
                    if (splits[i] != null && !splits[i].isEmpty())
                        initialedName += " " + splits[i].substring(0, 1).toUpperCase() + ".";
                }
            }

        }
        return initialedName;
    }

//    public Integer getWarningLettersCount() {
//        List<WarningLetter> letters = Setup.getRepository(WarningLetterRepository.class).findByHousemaid(this);
//        return letters.size();
//    }
//
//    public Integer getReplacementsCount() {
//        List<Replacement> reps = Setup.getRepository(ReplacementRepository.class).findByOldHousemaid(this);
//        return reps.size();
//    }
//
//    public Integer getFaultyReplacementsCount() {
//        List<Replacement> reps = Setup.getRepository(ReplacementRepository.class).findFaultyReplacementsByOldHousemaid(this);
//        return reps.size();
//    }
//
//    public Long getFailedInterviewsCount() {
//        List<FailedInterviewBalanceLog> failedInterviewBalanceLogs = Setup.getRepository(FailedInterviewBalanceLogRepository.class).findByHousemaidOrderById(this);
//
//        Long failedInterviewsBalance = 0L;
//        if (!failedInterviewBalanceLogs.isEmpty()) {
//            failedInterviewsBalance = failedInterviewBalanceLogs.get(failedInterviewBalanceLogs.size() - 1).getFailedInterviewBalance();
//        }
//        return failedInterviewsBalance;
//    }

    @BeforeUpdate
    private void preSave() {
        if ((this.basicSalary != null)
                && !this.basicSalary.equals(previousStateBasicSalary)) {
            throw new BusinessException("Total Salary is not updatable");
        }
        List<HousemaidStatus> statuses = Arrays.asList(HousemaidStatus.EMPLOYEMENT_TERMINATED,
                HousemaidStatus.UNREACHABLE_AFTER_EXIT, HousemaidStatus.REJECTED, HousemaidStatus.PASSED_EXIT);
//        Housemaid old = Setup.getApplicationContext().getBean(HousemaidRepository.class).getOne(this.getId());
//        if (old.getStatus() != this.status && statuses.contains(this.status)) {
//            this.visaCancellationDate = new LocalDate().toDate();
//        }
        // recalculate basic salary
        setBasicSalary(caculateBasicSalary());
        setPreviousStateBasicSalary(getBasicSalary());
    }

    public double caculateBasicSalary() {
        //(airfareFee == null ? 0 : airfareFee) +
        return (holiday == null ? 0 : holiday)
                + (overTime == null ? 0 : overTime) + (monthlyLoan == null ? 0 : monthlyLoan)
                + (primarySalary == null ? 0 : primarySalary);
    }

    public java.sql.Date getFlightToExitdate() {
        return flightToExitdate;
    }

    public void setFlightToExitdate(java.sql.Date flightToExitdate) {
        this.flightToExitdate = flightToExitdate;
    }

    public Date getDateOfTermination() {
        return dateOfTermination;
    }

    public void setDateOfTermination(Date dateOfTermination) {
        this.dateOfTermination = dateOfTermination;
    }

    public Date getLastPayrollLockDate() {
        return lastPayrollLockDate;
    }

    public void setLastPayrollLockDate(Date lastPayrollLockDate) {
        this.lastPayrollLockDate = lastPayrollLockDate;
    }

    public Boolean getWithMolNumber() {
        return withMolNumber;
    }

    public void setWithMolNumber(Boolean withMolNumber) {
        this.withMolNumber = withMolNumber;
    }

    @JsonIgnore
    public MaidType getPayrollAuditHousemaidType(){
        if (getHousemaidType() != null && getHousemaidType().equals(HousemaidType.MAID_VISA)){
            return MaidType.MAID_VISA;
        }else{
            return MaidType.MAIDS_CC;
        }
    }

    /**
    * <AUTHOR> Qazzaz
    * @reason PAY-125
    * start
    */
    public Date getLastChangeConfirmByAuditor() {
        return lastChangeConfirmByAuditor;
    }

    public void setLastChangeConfirmByAuditor(Date lastChangeConfirmByAuditor) {
        this.lastChangeConfirmByAuditor = lastChangeConfirmByAuditor;
    }

    public Date getLastChangeAmountConfirmByAuditor() {
        return lastChangeAmountConfirmByAuditor;
    }

    public void setLastChangeAmountConfirmByAuditor(Date lastChangeAmountConfirmByAuditor) {
        this.lastChangeAmountConfirmByAuditor = lastChangeAmountConfirmByAuditor;
    }

    /**
    * end
    */

    @JsonIgnore
    public String getSalaryWithCurrency(){
        String salaryWithCurrency = "";
        Double salary = getBasicSalary();

        if (salary != null){
            salaryWithCurrency = "AED "+ NumberFormatter.formatNumber(salary);
        }else{
            salaryWithCurrency = "AED 0";
        }

        return salaryWithCurrency;
    }

    // Used only to optimize payroll generation
    @Transient
    private HousemaidPayrollBean payrollBean;

    public HousemaidPayrollBean getPayrollBean() {
        return payrollBean;
    }

    public void setPayrollBean(HousemaidPayrollBean payrollBean) {
        this.payrollBean = payrollBean;
    }

    public java.sql.Date getLastWithoutClientPayrollMonthConfirmed() {
        return lastWithoutClientPayrollMonthConfirmed;
    }

    public void setLastWithoutClientPayrollMonthConfirmed(java.sql.Date lastWithoutClientPayrollMonthConfirmed) {
        this.lastWithoutClientPayrollMonthConfirmed = lastWithoutClientPayrollMonthConfirmed;
    }

    public Date getLastZeroSalaryConfirmByAuditor() {
        return lastZeroSalaryConfirmByAuditor;
    }

    public void setLastZeroSalaryConfirmByAuditor(Date lastZeroSalaryConfirmByAuditor) {
        this.lastZeroSalaryConfirmByAuditor = lastZeroSalaryConfirmByAuditor;
    }

    public Date getLastPaymentConfirmByAuditor() {
        return lastPaymentConfirmByAuditor;
    }

    public void setLastPaymentConfirmByAuditor(Date lastPaymentConfirmByAuditor) {
        this.lastPaymentConfirmByAuditor = lastPaymentConfirmByAuditor;
    }

    public boolean skipThisMonthRepayment() {
        SelectQuery<EmployeeLoan> maidLoansQuery = new SelectQuery(EmployeeLoan.class);
        maidLoansQuery.filterBy("housemaid", "=", this);
        maidLoansQuery.filterBy("skipLoanRepaymentForCurrentPayroll", "=", Boolean.TRUE);
        maidLoansQuery.filterBy("loanDate", ">=", ************************.getPayrollStartDate(new LocalDate()).toDate());
        maidLoansQuery.filterBy("loanDate", "<=", ************************.getPayrollEndDate(new LocalDate()).toDate());

        List<EmployeeLoan> maidLoans = maidLoansQuery.execute();

        return maidLoans != null && !maidLoans.isEmpty();
    }

    public Integer getWorkPaidVacationDays() {
        if(workPaidVacationDays == null)
            return 0;
        return workPaidVacationDays;
    }

    public void setWorkPaidVacationDays(Integer workPaidVacationDays) {
        this.workPaidVacationDays = workPaidVacationDays;
    }

    public Date getReplacementSalaryStartDate() {
        return replacementSalaryStartDate;
    }

    public void setReplacementSalaryStartDate(Date replacementSalaryStartDate) {
        this.replacementSalaryStartDate = replacementSalaryStartDate;
    }

    public String getWhatsAppPhoneNumber() {
        return whatsAppPhoneNumber;
    }

    public void setWhatsAppPhoneNumber(String whatsAppPhoneNumber) {
        this.whatsAppPhoneNumber = whatsAppPhoneNumber;
    }

    public String getNormalizedWhatsAppPhoneNumber() {
        return normalizedWhatsAppPhoneNumber;
    }

    public void setNormalizedWhatsAppPhoneNumber(String normalizedWhatsAppPhoneNumber) {
        this.normalizedWhatsAppPhoneNumber = normalizedWhatsAppPhoneNumber;
    }

    @JsonIgnore
    public String getRealStatus() {
        if(this.getStatus() != null) {
            if(this.getPendingStatus() == null || this.getStatus() != HousemaidStatus.PENDING_FOR_DISCIPLINE) return this.getStatus().toString();
            return this.getPendingStatus().toString();
        }
        return "";
    }

    @JsonIgnore
    public String getSkippedLoanRepayment(){

        if(this.getDefaulMonthlyRepayment() == null)
            return "AED " + NumberFormatter.formatNumber(this.getLoanBalance());
        else
            return "AED " + NumberFormatter.formatNumber(Math.min(this.getDefaulMonthlyRepayment(), this.getLoanBalance()));
    }

    @JsonIgnore
    public String getMaidName(){
        return this.getName();
    }

    public CardStatus getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(CardStatus cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Boolean getOverTimeAddedBefore() {
        return overTimeAddedBefore;
    }

    public void setOverTimeAddedBefore(Boolean overTimeAddedBefore) {
        this.overTimeAddedBefore = overTimeAddedBefore;
    }

    public Boolean getUpdateSalaryComponents() {
        return updateSalaryComponents;
    }

    public void setUpdateSalaryComponents(Boolean updateSalaryComponents) {
        this.updateSalaryComponents = updateSalaryComponents;
    }

    public String getUrlToken() {
        return urlToken;
    }

    public void setUrlToken(String urlToken) {
        this.urlToken = urlToken;
    }

    public Date getYayaLastLoginDate() {
        return yayaLastLoginDate;
    }

    public void setYayaLastLoginDate(Date yayaLastLoginDate) {
        this.yayaLastLoginDate = yayaLastLoginDate;
    }

    public PicklistItem getYayaLanguage() {
        return yayaLanguage;
    }

    public void setYayaLanguage(PicklistItem yayaLanguage) {
        this.yayaLanguage = yayaLanguage;
    }

    public Boolean getCanEditFromPayrollProfile(){
        SalariesAccess salariesAccess = Setup.getRepository(SalariesAccessRepository.class).findTopByUser(CurrentRequest.getUser());
        return salariesAccess != null && salariesAccess.getForHousemaids();
    }

    public String getYayaAppNotificationLang(){
        String code = "en";
        if (this.getYayaLanguage() != null){
            code = getYayaLanguage().getCode();
        }

        if(code.equals("en")) {
            return null;
        } else {
            return code;
        }
    }

    public Double getLastCCSalary() {
        return lastCCSalary;
    }

    public void setLastCCSalary(Double lastCCSalary) {
        this.lastCCSalary = lastCCSalary;
    }

    public Double getLastMVSalary() {
        return lastMVSalary;
    }

    public void setLastMVSalary(Double lastMVSalary) {
        this.lastMVSalary = lastMVSalary;
    }

    @JsonIgnore
    public RenewRequest getRenewRequest() {
        return Setup.getRepository(RenewVisaRequestRepository.class).findFirstOneByHousemaidAndCompletedOrderByCreationDateDesc(this, true);
    }

    @JsonIgnore
    public Boolean isFirstSalary(java.sql.Date payrollMonth) {
        return ((startDate != null && startDate.compareTo(new LocalDate(payrollMonth).plusMonths(-1).withDayOfMonth(27).toDate()) >= 0)
                    || (replacementSalaryStartDate != null && replacementSalaryStartDate.compareTo(new LocalDate(payrollMonth).plusMonths(-1).withDayOfMonth(27).toDate()) >= 0))
                && Setup.getRepository(HousemaidPayrollLogRepository.class).countByHousemaidAndPayrollMonth(this, DateUtil.addMonthsSql(payrollMonth, -1)) == 0;
    }

    public Date getNewStartDate() {
        return (getReplacementSalaryStartDate() != null ? getReplacementSalaryStartDate() : getStartDate());
    }

    public Boolean getHasEidAttachments() {
        return hasEidAttachments;
    }

    public void setHasEidAttachments(Boolean hasEidAttachments) {
        this.hasEidAttachments = hasEidAttachments;
    }

    public Boolean getVisaRequestHasEidAttachments() {
        return visaRequestHasEidAttachments;
    }

    public void setVisaRequestHasEidAttachments(Boolean visaRequestHasEidAttachments) {
        this.visaRequestHasEidAttachments = visaRequestHasEidAttachments;
    }

    public HousemaidType getOldHousemaidType() {
        return oldHousemaidType;
    }

    public void setOldHousemaidType(HousemaidType oldHousemaidType) {
        this.oldHousemaidType = oldHousemaidType;
    }

    public String getSwitchedMaid() {
        return switchedMaid;
    }

    public void setSwitchedMaid(String switchedMaid) {
        this.switchedMaid = switchedMaid;
    }

    public String calculateSwitchedMaid() {
        if(oldHousemaidType!=null && housemaidType!=null && !housemaidType.equals(oldHousemaidType)){
            if(housemaidType.equals(HousemaidType.MAID_VISA)) {
                return "CC switched to MV";
            }else if(oldHousemaidType.equals(HousemaidType.MAID_VISA)){
                return "MV switched to CC";
            }
        }

        HistorySelectQuery<Housemaid> historySelectQuery = new HistorySelectQuery<>(Housemaid.class);
        historySelectQuery.filterBy("id", "=", this.getId());
        historySelectQuery.filterByChanged("housemaidType");
//        historySelectQuery.setLimit(1);
        historySelectQuery.sortBy("lastModificationDate",false);
        List<Housemaid> housemaids=historySelectQuery.execute();
        if(housemaids != null && housemaids.size() > 1){
            if(!HousemaidType.MAID_VISA.equals(housemaids.get(0).getHousemaidType())){
                for (int i = 1 ; i < housemaids.size() ; i++){
                    if (HousemaidType.MAID_VISA.equals(housemaids.get(i).getHousemaidType())){
                        return "MV switched to CC";
                    }
                }
            }
            if(HousemaidType.MAID_VISA.equals(housemaids.get(0).getHousemaidType())){
                for (int i = 1 ; i < housemaids.size() ; i++){
                    if (!HousemaidType.MAID_VISA.equals(housemaids.get(i).getHousemaidType())){
                        return "CC switched to MV";
                    }
                }
            }
        }

        if (housemaids != null && !housemaids.isEmpty()) {
            Housemaid housemaid = housemaids.get(0);
            if (HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())) {
                List<Long> ids = Setup.getRepository(HousemaidRepository.class).
                        getHousemaidsRevisionForMaidByIdAndHousemaidTypeNotEqualAndModificationDateBefore(housemaid.getId(), housemaid.getLastModificationDate());
                if (ids != null && !ids.isEmpty()) {
                    return "CC switched to MV";
                }

            } else {
                List<Long> ids = Setup.getRepository(HousemaidRepository.class).
                        getHousemaidsRevisionForMaidByIdAndHousemaidTypeEqualAndModificationDateBefore(housemaid.getId(), housemaid.getLastModificationDate());
                if (ids != null && !ids.isEmpty()) {
                    return "MV switched to CC";
                }
            }
        }

        return "";


    }

    public Date getFinishedMedicalTest() {
        return finishedMedicalTest;
    }

    public PicklistItem getLocation() {
        return location;
    }

    public void setLocation(PicklistItem location) {
        this.location = location;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public Boolean getLiveOut() {
        if (liveOut == null){
            return false;
        }
        return liveOut;
    }

    public void setLiveOut(Boolean liveOut) {
        this.liveOut = liveOut;
    }


    public Boolean getConvertMvToCCFromProfile() {
        if (convertMvToCCFromProfile == null) {
            return false;
        }
        return convertMvToCCFromProfile;
    }

    public void setConvertMvToCCFromProfile(Boolean convertMvToCCFromProfile) {
        this.convertMvToCCFromProfile = convertMvToCCFromProfile;
    }

    public Boolean getTravelAssist() {
        return travelAssist;
    }

    public void setTravelAssist(Boolean travelAssist) {
        this.travelAssist = travelAssist;
    }

    public FreedomOperator getAssignedOperator() {
        return assignedOperator;
    }

    public void setAssignedOperator(FreedomOperator assignedOperator) {
        this.assignedOperator = assignedOperator;
    }

    public String getAnsariPaymentMethod() {
        if (this.getVisaNewRequest() != null
                && this.getVisaNewRequest().getEmployeeAccountWithAgent() != null)
        {
            String accountValue = this.getVisaNewRequest().getEmployeeAccountWithAgent().trim();

            if (accountValue.startsWith("AE")) {
                String accountDigits = accountValue.substring(2);
                // Check if it's Du Pay Card - AE format with 7th to 14th digits being "********"
                if ( accountDigits.length() >= 14) {
                    String digits7to14 = accountDigits.substring(6, 14); // 6-13 indices for 7th-14th position
                    if ("********".equals(digits7to14)) {
                        return DU_PAY_CARD.getLabel();
                    }
                }
                // If starts with AE but not Du Pay Card pattern, it's Bank Transfer
                return BANK_TRANSFER.getLabel();
            } else if (accountValue.startsWith("9")) {
                return PAYROLL_CARD.getLabel();
            } else {
                // Remove leading zeros
                accountValue = accountValue.replaceFirst("^0+", "");

                // Check the first non-zero value
                if (accountValue.startsWith("5")) {
                    return FAB_MASTER_CARD.getLabel();
                } else if (accountValue.startsWith("10")) {
                    return ANSARI_VISA_CARD.getLabel();
                } else if (accountValue.startsWith("19")) {
                    return OVER_THE_COUNTER.getLabel();
                }
            }
        }

        // If no criteria are met, return an empty string
        return "";
    }


}
