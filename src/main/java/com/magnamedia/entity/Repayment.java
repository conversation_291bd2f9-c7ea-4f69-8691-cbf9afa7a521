package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.*;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;
import com.magnamedia.extra.RepaymentStatus;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity @org.hibernate.envers.Audited
public class Repayment extends BaseEntity {

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    private Double amount;
    @Column
    private Date repaymentDate;
    @Column
    private Boolean exculdedFromPayroll;
    @Column(columnDefinition = "boolean default false")
    private Boolean paidRepayment = false;
    @Column
    @Lob
    private String description;
    @Column
    @Enumerated(EnumType.STRING)
    private RepaymentType type;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff officestaff;
    //Jirra ACC-645
    @Column
    private Double postponedAmount;
    @Column
    private Date postponedDate;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Repayment oldRepayment;
    //Jirra ACC-1085
    @Column(columnDefinition = "boolean default false")
    private boolean notFinal;
    //Jirra PAY-2
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdJsonSerializer.class)
    private EmployeeLoan employeeLoan;

    @Column(columnDefinition = "boolean default false")
    private boolean dontConsiderAsDeduction = false;

    @Enumerated(EnumType.STRING)
    private RepaymentStatus status = RepaymentStatus.Normal;

    @Column
    @Lob
    private String notes;

    public Repayment() {
    }

    public Repayment(Double amount, Date repaymentDate, EmployeeLoan employeeLoan) {
        this.amount = amount;
        this.repaymentDate = repaymentDate;
        this.employeeLoan = employeeLoan;
        this.officestaff = employeeLoan != null ? employeeLoan.getOfficeStaff() : null;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getRepaymentDate() {
        return repaymentDate;
    }

    public void setRepaymentDate(Date repaymentDate) {
        this.repaymentDate = repaymentDate;
    }

    public Boolean getExculdedFromPayroll() {
        return exculdedFromPayroll;
    }

    public void setExculdedFromPayroll(Boolean exculdedFromPayroll) {
        this.exculdedFromPayroll = exculdedFromPayroll;
    }

    public Boolean getPaidRepayment() {
        return paidRepayment;
    }

    public void setPaidRepayment(Boolean paidRepayment) {
        this.paidRepayment = paidRepayment;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public RepaymentType getType() {
        return type;
    }

    public void setType(RepaymentType type) {
        this.type = type;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public OfficeStaff getOfficestaff() {
        return officestaff;
    }

    public void setOfficestaff(OfficeStaff officestaff) {
        this.officestaff = officestaff;
    }

    public Double getPostponedAmount() {
        return postponedAmount;
    }

    public void setPostponedAmount(Double postponedAmount) {
        this.postponedAmount = postponedAmount;
    }

    public Date getPostponedDate() {
        return postponedDate;
    }

    public void setPostponedDate(Date postponedDate) {
        this.postponedDate = postponedDate;
    }

    public Repayment getOldRepayment() {
        return oldRepayment;
    }

    public void setOldRepayment(Repayment oldRepayment) {
        this.oldRepayment = oldRepayment;
    }

    public boolean isNotFinal() {
        return notFinal;
    }

    public void setNotFinal(boolean notFinal) {
        this.notFinal = notFinal;
    }

    public EmployeeLoan getEmployeeLoan() {
        return employeeLoan;
    }

    public void setEmployeeLoan(EmployeeLoan employeeLoan) {
        this.employeeLoan = employeeLoan;
    }
    public boolean isDontConsiderAsDeduction() {
        return dontConsiderAsDeduction;
    }

    public void setDontConsiderAsDeduction(boolean dontConsiderAsDeduction) {
        this.dontConsiderAsDeduction = dontConsiderAsDeduction;
    }

    @BeforeInsert
    @BeforeUpdate
    public void beforeSave() {
        if (exculdedFromPayroll != null && exculdedFromPayroll)
            paidRepayment = true;
    }

    public enum RepaymentType {
        CASH_ADVANCED,
        SMARTPHONE,
        PROCESSING_FEES

    }

    public RepaymentStatus getStatus() {
        return status;
    }

    public void setStatus(RepaymentStatus status) {
        this.status = status;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}
