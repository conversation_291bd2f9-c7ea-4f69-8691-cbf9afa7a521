package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.ContractClientSerializer;
import com.magnamedia.repository.ContractRepository;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/18/2022
 * coppied from sales SAL-3842 PAY-635
 **/

@Entity @org.hibernate.envers.Audited
public class CcMaidSwitchedToMv extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JsonSerialize(using = ContractClientSerializer.class)
    private Contract contract;

    @Column(nullable = false)
    Date switchDate;

    @Column
    Double lastCcSalary;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User requester;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = ContractClientSerializer.class)
    private Contract oldContract;

    @Column
    Double amountToBeTransferred;

    @Column(columnDefinition = "boolean default false")
    Boolean confirmed = false;

    @Column(columnDefinition = "double default 0")
    private Double additionsAmount;

    @Column(columnDefinition = "double default 0")
    private Double deductionsAmount;

    @Column(columnDefinition = "double default 0")
    private Double repaymentAmount;

    @Column(columnDefinition = "double default 0")
    private Double finalAmountToBeTransferred;

    @Column(columnDefinition = "boolean default false")
    private Boolean alreadyCalculated = false;

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Date getSwitchDate() {
        return switchDate;
    }

    public void setSwitchDate(Date switchDate) {
        this.switchDate = switchDate;
    }

    public Double getLastCcSalary() {
        if (lastCcSalary == null)
            lastCcSalary = 0.0;
        return lastCcSalary;
    }

    public void setLastCcSalary(Double lastCcSalary) {
        this.lastCcSalary = lastCcSalary;
    }

    public User getRequester() {
        return requester;
    }

    public void setRequester(User requester) {
        this.requester = requester;
    }

    public Contract getOldContract() {
        return oldContract;
    }

    public void setOldContract(Contract oldContract) {
        this.oldContract = oldContract;
    }

    public void setOldContract() {
        HistorySelectQuery<Contract> historySelectQuery = new HistorySelectQuery<>(Contract.class);
        historySelectQuery.filterBy("housemaid", "=", housemaid);
        historySelectQuery.filterBy("id", "<>", contract.getId());
        historySelectQuery.sortBy("lastModificationDate", false);
        historySelectQuery.setLimit(1);

        List<Contract> contracts = historySelectQuery.execute();

        if(!contracts.isEmpty()) {
            this.oldContract = Setup.getRepository(ContractRepository.class).findOne(contracts.get(0).getId());
        }
    }

    public Double getAmountToBeTransferred() {
        if (amountToBeTransferred == null)
            amountToBeTransferred = housemaid.getLastCCSalary();
        if (amountToBeTransferred == null)
            amountToBeTransferred = 0.0;
        return amountToBeTransferred;
    }

    public void setAmountToBeTransferred(Double amountTobeTransferred) {
        this.amountToBeTransferred = amountTobeTransferred;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public Date getSalaryStartDate() {
        return housemaid.getStartDate();
    }

    public Double getAdditionsAmount() {
        return additionsAmount;
    }

    public void setAdditionsAmount(Double additionsAmount) {
        this.additionsAmount = additionsAmount;
    }

    public Double getDeductionsAmount() {
        return deductionsAmount;
    }

    public void setDeductionsAmount(Double deductionsAmount) {
        this.deductionsAmount = deductionsAmount;
    }

    public Double getRepaymentAmount() {
        return repaymentAmount;
    }

    public void setRepaymentAmount(Double repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    public Double getFinalAmountToBeTransferred() {
        if (finalAmountToBeTransferred == null)
            finalAmountToBeTransferred = 0.0;
        return finalAmountToBeTransferred;
    }

    public void setFinalAmountToBeTransferred(Double finalAmountToBeTransferred) {
        this.finalAmountToBeTransferred = finalAmountToBeTransferred;
    }

    public Boolean getAlreadyCalculated() {
        return alreadyCalculated;
    }

    public void setAlreadyCalculated(Boolean alreadyCalculated) {
        this.alreadyCalculated = alreadyCalculated;
    }
}