package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.module.type.*;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/10/2020
 **/
@MappedSuperclass
public class AbstractPaymentRule extends BaseEntity {

    @ElementCollection(targetClass= PaymentRuleEmployeeType.class)
    @Enumerated(EnumType.STRING)
    @Column(name="employeeType")
    protected List<PaymentRuleEmployeeType> employeeTypeList;

    @ElementCollection(targetClass= MaidType.class)
    @Enumerated(EnumType.STRING)
    @Column(name="maidType")
    protected List<MaidType> housemaidTypeList;

    @Column
//    @Enumerated(EnumType.STRING)
    protected MolType molType;

    @Column
    @Enumerated(EnumType.STRING)
    protected PaymentRulePaymentMethod paymentMethod;

    @Column(columnDefinition = "varchar(255) default 'Ansari'")
    protected String moneyExchangeName;

    @Column(columnDefinition = "int(10) default 1")
    protected Integer dayOfMonth = 1;

    @Column(columnDefinition = "boolean default true")
    protected Boolean currentMonth;

    @Column(columnDefinition = "boolean default true")
    protected Boolean afterHoliday;

    @Column(columnDefinition = "int(10) default 0")
    protected int daysBeforeLock = 0;

    @Column
    @Enumerated(EnumType.STRING)
    protected PayrollType payrollType;

    @ManyToMany(fetch = FetchType.LAZY)
    protected List<PicklistItem> countries;

    @ElementCollection(targetClass= PaymentRuleMaidStatus.class)
    @Enumerated(EnumType.STRING)
    @Column(name="housemaidStatus")
    protected List<PaymentRuleMaidStatus> housemaidStatusList;

    public AbstractPaymentRule() {
        super();
    }

    public AbstractPaymentRule(AbstractPaymentRule rule) {
        if(rule.employeeTypeList != null) {
            this.employeeTypeList = new ArrayList<>(rule.employeeTypeList);
        }
        if(rule.housemaidTypeList != null) {
            this.housemaidTypeList = new ArrayList<>(rule.housemaidTypeList);
        }
        this.molType = rule.molType;
        this.paymentMethod = rule.paymentMethod;
        this.moneyExchangeName = rule.moneyExchangeName;
        this.dayOfMonth = rule.dayOfMonth;
        this.currentMonth = rule.currentMonth;
        this.afterHoliday = rule.afterHoliday;
        this.daysBeforeLock = rule.daysBeforeLock;
        this.payrollType = rule.payrollType;
        this.countries = new ArrayList<>(rule.countries);
        if(rule.getHousemaidStatusList() != null) {
            this.housemaidStatusList = new ArrayList<>(rule.getHousemaidStatusList());
        }
    }

    public List<PaymentRuleEmployeeType> getEmployeeTypeList() {
        if(employeeTypeList == null)
            employeeTypeList = new ArrayList<>();
        return employeeTypeList;
    }

    public void setEmployeeTypeList(List<PaymentRuleEmployeeType> employeeTypeList) {
        this.employeeTypeList = employeeTypeList;
    }

    public List<MaidType> getHousemaidTypeList() {
        if (housemaidTypeList == null)
            housemaidTypeList = new ArrayList<>();
        return housemaidTypeList;
    }

    public void setHousemaidTypeList(List<MaidType> housemaidTypeList) {
        this.housemaidTypeList = housemaidTypeList;
    }

    public MolType getMolType() {
        return molType;
    }

    public void setMolType(MolType molType) {
        this.molType = molType;
    }

    public PaymentRulePaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentRulePaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getMoneyExchangeName() {
        return moneyExchangeName;
    }

    public void setMoneyExchangeName(String moneyExchangeName) {
        this.moneyExchangeName = moneyExchangeName;
    }

    public Integer getDayOfMonth() {
        return dayOfMonth;
    }

    public void setDayOfMonth(Integer dayOfMonth) {
        this.dayOfMonth = dayOfMonth;
    }

    public Boolean getCurrentMonth() {
        return currentMonth;
    }

    public void setCurrentMonth(Boolean currentMonth) {
        this.currentMonth = currentMonth;
    }

    public Boolean getAfterHoliday() {
        return afterHoliday;
    }

    public void setAfterHoliday(Boolean afterHoliday) {
        this.afterHoliday = afterHoliday;
    }

    public int getDaysBeforeLock() {
        return daysBeforeLock;
    }

    public void setDaysBeforeLock(int daysBeforeLock) {
        this.daysBeforeLock = daysBeforeLock;
    }

    public PayrollType getPayrollType() {
        return payrollType;
    }

    public void setPayrollType(PayrollType payrollType) {
        this.payrollType = payrollType;
    }

    public List<PicklistItem> getCountries() {
        if(countries == null)
            countries = new ArrayList<>();
        return countries;
    }

    public void setCountries(List<PicklistItem> countries) {
        this.countries = countries;
    }

    public List<PaymentRuleMaidStatus> getHousemaidStatusList() {
        if(housemaidStatusList == null)
            housemaidStatusList = new ArrayList<>();
        return housemaidStatusList;
    }

    public void setHousemaidStatusList(List<PaymentRuleMaidStatus> housemaidStatusList) {
        this.housemaidStatusList = housemaidStatusList;
    }

    public enum MolType implements LabelValueEnum {
        WITH_MOL("With MOL number"),
        WITHOUT_MOL("Without MOL number"),
        WITH_AND_WITHOUT("With and Without MOL number");

        private final String label;

        MolType(String label) {
            this.label = label;
        }

        @Override
        public String getLabel() {
            return label;
        }
    }
}
