package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.repository.SalariesAccessRepository;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class SalaryAmountSerializer extends JsonSerializer<Object> {
    public static List<String> publicPagesCodes = Arrays.asList(
            "payroll__public_final-manager-approve",
            "payroll_new-staff-questionnaire",
            "payroll__public_inform-terminated-staff",
            "payroll__publicPage",
            "payroll_final-settlement__publicPage",
            "payroll_cc_included_maids",
            "payroll_mv_included_maids",
            "payroll__public_question_about_maid_salary"
            );

    @Override
    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();
        String pageCode = Setup.getCurrentHttpRequest() != null ? Setup.getCurrentHttpRequest().getHeader("pageCode") : "";
        boolean isPublicPage = publicPagesCodes.stream().anyMatch(x -> x.equalsIgnoreCase(pageCode));
        if (user == null || "guest".equals(user.getLoginName()) || isPublicPage || repository.countByUserAndForOfficeStaffsTrue(user) > 0) {
            jsonGenerator.writeObject(o);
        } else {
            jsonGenerator.writeString("****");
        }
    }
}
