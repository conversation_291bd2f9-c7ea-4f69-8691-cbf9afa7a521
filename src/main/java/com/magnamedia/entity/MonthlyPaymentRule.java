package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.type.MaidType;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRuleMaidStatus;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.PublicHolidayRepository;

import javax.persistence.*;
import java.sql.Date;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/10/2020
 **/
@Entity @org.hibernate.envers.Audited
public class MonthlyPaymentRule extends AbstractPaymentRule {

    @Column
    private Date paymentDate;

    @Column
    private Date lockDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean finished = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean auditingFinished = false;

    @Column
    private Date payrollMonth;

    @Column(columnDefinition = "boolean default false")
    private Boolean singleOfficeStaff = false;

    //PAY-612
    //used to be for one housemaid now it's related to all maids who have a final settlement ready to be paid
    //it is now for all monthly payment rules which are not for Main Payroll process (like CC switched to MV to-do and paying Final settlements)
    @Column(columnDefinition = "boolean default false")
    private Boolean singleHousemaid = false;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<ExcludedMVInfo> mustIncludedMVInfoList = new HashSet<>();

    @Column(columnDefinition = "boolean default false")
    private Boolean ccMaidsIncludedBefore = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean mvMaidsIncludedBefore = false;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Housemaid> excludedMaidsDueToMissingFields = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Housemaid> excludedMaidsDueToOverLap = new HashSet<>();

//    @ManyToMany(fetch = FetchType.LAZY)
//    @JsonIgnore
//    private Set<Housemaid> excludedMaidDueToEVisaIssue = new HashSet<>();

    @Column(columnDefinition = "boolean default false")
    private Boolean isPaymentDateUpdatedManually = false;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Housemaid> excludedDueToMedicalTest = new HashSet<>();

    public MonthlyPaymentRule() {
        super();
    }

    public MonthlyPaymentRule(AbstractPaymentRule abstractPaymentRule) {
        super(abstractPaymentRule);
        this.payrollMonth = new Date(org.joda.time.LocalDate.now().withDayOfMonth(1).toDate().getTime());
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public Date getLockDate() {
        return lockDate;
    }

    public void setLockDate(Date lockDate) {
        this.lockDate = lockDate;
    }

    public Boolean getFinished() {
        return finished;
    }

    public void setFinished(Boolean finished) {
        this.finished = finished;
    }

    public Boolean getAuditingFinished() {
        return auditingFinished;
    }

    public void setAuditingFinished(Boolean auditingFinished) {
        this.auditingFinished = auditingFinished;
    }

    public Date getPayrollMonth() {
        return payrollMonth;
    }

    public void setPayrollMonth(Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }

    public Set<ExcludedMVInfo> getMustIncludedMVInfoList() {
        if (mustIncludedMVInfoList == null)
            mustIncludedMVInfoList = new HashSet<>();
        return mustIncludedMVInfoList;
    }

    public void setMustIncludedMVInfoList(Set<ExcludedMVInfo> mustIncludedMVInfoList) {
        this.mustIncludedMVInfoList = mustIncludedMVInfoList;
    }

    public Boolean getCcMaidsIncludedBefore() {
        return ccMaidsIncludedBefore;
    }

    public void setCcMaidsIncludedBefore(Boolean maidsIncludedBefore) {
        this.ccMaidsIncludedBefore = maidsIncludedBefore;
    }

    public Boolean getMvMaidsIncludedBefore() {
        return mvMaidsIncludedBefore;
    }

    public void setMvMaidsIncludedBefore(Boolean mvMaidsIncludedBefore) {
        this.mvMaidsIncludedBefore = mvMaidsIncludedBefore;
    }

    public Set<Housemaid> getExcludedMaidsDueToMissingFields() {
        if(excludedMaidsDueToMissingFields == null)
            excludedMaidsDueToMissingFields = new HashSet<>();
        return excludedMaidsDueToMissingFields;
    }

    public void setExcludedMaidsDueToMissingFields(Set<Housemaid> excludedMaidsDueToMissingFields) {
        this.excludedMaidsDueToMissingFields = excludedMaidsDueToMissingFields;
    }

    public Set<Housemaid> getExcludedMaidsDueToOverLap() {
        if (excludedMaidsDueToOverLap == null)
            excludedMaidsDueToOverLap = new HashSet<>();
        return excludedMaidsDueToOverLap;
    }

    public void setExcludedMaidsDueToOverLap(Set<Housemaid> excludedMaidsDueToOverLap) {
        this.excludedMaidsDueToOverLap = excludedMaidsDueToOverLap;
    }

//    public Set<Housemaid> getExcludedMaidDueToEVisaIssue() {
//        if (excludedMaidDueToEVisaIssue == null)
//            excludedMaidDueToEVisaIssue = new HashSet<>();
//        return excludedMaidDueToEVisaIssue;
//    }

//    public void setExcludedMaidDueToEVisaIssue(Set<Housemaid> excludedMaidDueToEVisaIssue) {
//        this.excludedMaidDueToEVisaIssue = excludedMaidDueToEVisaIssue;
//    }

    public boolean isTargetingExpats() {
        return this.employeeTypeList.contains(PaymentRuleEmployeeType.EXPATS);
    }

    public boolean isTargetingEmiratis() {
        return this.employeeTypeList.contains(PaymentRuleEmployeeType.EMIRATI);
    }

    public boolean isTargetingOverseas() {
        return this.employeeTypeList.contains(PaymentRuleEmployeeType.OVERSEAS);
    }

    public boolean isTargetingOfficeStaff() {
        return isTargetingEmiratis()
                || isTargetingExpats()
                || isTargetingOverseas();
    }

    public boolean isTargetingHousemaid() {
        return this.employeeTypeList.contains(PaymentRuleEmployeeType.HOUSEMAIDS);
    }

    public boolean isTargetingMaidVisa() {
        return this.housemaidTypeList != null && this.housemaidTypeList.contains(MaidType.MAID_VISA);
    }

    public boolean isTargetingMaidCC() {
        return this.housemaidTypeList != null && this.housemaidTypeList.contains(MaidType.MAIDS_CC);
    }

    public boolean isTargetingMaidVisaOnly() {
        return this.housemaidTypeList != null
                && this.housemaidTypeList.contains(MaidType.MAID_VISA)
                && !this.housemaidTypeList.contains(MaidType.MAIDS_CC);
    }

    public boolean isTargetingMaidCCOnly() {
        return this.housemaidTypeList != null
                && this.housemaidTypeList.contains(MaidType.MAIDS_CC)
                && !this.housemaidTypeList.contains(MaidType.MAID_VISA);
    }

    public boolean isTargetingWithClientOnly() {
        return this.housemaidStatusList != null
                && this.housemaidStatusList.contains(PaymentRuleMaidStatus.WITH_CLIENT)
                && !this.housemaidStatusList.contains(PaymentRuleMaidStatus.IN_ACCOMMODATION);
    }
    public boolean isTargetingInAccommodationOnly() {
        return this.housemaidStatusList != null
                && this.housemaidStatusList.contains(PaymentRuleMaidStatus.IN_ACCOMMODATION)
                && !this.housemaidStatusList.contains(PaymentRuleMaidStatus.WITH_CLIENT);
    }

    public Boolean withOrWithoutMOL() {
        if (this.getMolType() == AbstractPaymentRule.MolType.WITH_MOL )
            return true;
        else if (this.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL)
            return false;
        else
            return null;
    }

    public boolean isSecondaryMonthlyRule(){
        return PayrollType.SECONDARY.equals(this.payrollType);
    }

    public boolean getDisableLockDate() {
        return new Date(System.currentTimeMillis()).after(this.getLockDate());
    }

    @PrePersist
    public void updatePaymentLockDate() {
        if(this.getSingleOfficeStaff() || this.getSingleHousemaid()) return;
        // update payment date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(this.getCreationDate() == null ? new java.util.Date() : this.getCreationDate());
        calendar.add(Calendar.MONTH, this.getCurrentMonth() ? 1 : 2);
        calendar.set(Calendar.DAY_OF_MONTH, 0);

        int finalDay = calendar.get(Calendar.DAY_OF_MONTH) > this.getDayOfMonth() ? this.getDayOfMonth() : calendar.get(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, finalDay);
        LocalDate localDate = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if (localDate.getDayOfWeek() == DayOfWeek.SUNDAY) {
            calendar.add(Calendar.DAY_OF_MONTH, this.getAfterHoliday() ? 1 : -1);
        }

        PublicHoliday holiday = Setup.getRepository(PublicHolidayRepository.class).findTopByStartDateLessThanEqualAndEndDateGreaterThanEqualOrderByIdDesc(calendar.getTime(), calendar.getTime());

        if (holiday != null) {
            if (this.getAfterHoliday()) {
                calendar.setTime(holiday.getEndDate());
            } else {
                calendar.setTime(holiday.getStartDate());
                calendar.add(Calendar.DAY_OF_MONTH, -1);
            }

            localDate = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (localDate.getDayOfWeek() == DayOfWeek.SUNDAY) {
                calendar.add(Calendar.DAY_OF_MONTH, this.getAfterHoliday() ? 1 : -1);
            }
        }
        paymentDate = new Date(calendar.getTime().getTime());


        // Compute lock date based on payment date
        calendar = Calendar.getInstance();
        calendar.setTime(this.getPaymentDate());
        calendar.add(Calendar.DAY_OF_MONTH, -1 * this.getDaysBeforeLock());
        lockDate = new Date(calendar.getTime().getTime());
    }

    public Boolean getSingleOfficeStaff() {
        return singleOfficeStaff;
    }

    public void setSingleOfficeStaff(Boolean singleOfficeStaff) {
        this.singleOfficeStaff = singleOfficeStaff;
    }

    public Boolean getSingleHousemaid() {
        return singleHousemaid;
    }

    public void setSingleHousemaid(Boolean singleHousemaid) {
        this.singleHousemaid = singleHousemaid;
    }

    @JsonIgnore
    public String getEmployeesTypes (){
        String types = "";
        List<PaymentRuleEmployeeType> employeeTypeList = this.getEmployeeTypeList();
        for (int i = 0; i < employeeTypeList.size(); i++) {
            types += StringHelper.enumToCapitalizedFirstLetter(employeeTypeList.get(i).name());
            if (i < employeeTypeList.size() -1)
                types += " - ";
        }
        return types;
    }

    public Boolean getPaymentDateUpdatedManually() {
        return isPaymentDateUpdatedManually;
    }

    public void setPaymentDateUpdatedManually(Boolean paymentDateUpdatedManually) {
        isPaymentDateUpdatedManually = paymentDateUpdatedManually;
    }

    public Set<Housemaid> getExcludedDueToMedicalTest() {
        if (excludedDueToMedicalTest == null) {
            return new HashSet<>();
        }
        return excludedDueToMedicalTest;
    }

    public void setExcludedDueToMedicalTest(Set<Housemaid> excludedDueToMedicalTest) {
        this.excludedDueToMedicalTest = excludedDueToMedicalTest;
    }
}
