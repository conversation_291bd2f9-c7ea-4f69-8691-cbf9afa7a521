package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterDelete;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.AfterUpdate;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.repository.AuditorActionRepository;

import javax.persistence.*;
import java.sql.Date;
import java.text.DecimalFormat;
import java.util.Calendar;

/**
 * <AUTHOR> <<EMAIL>>
 */

@Entity @org.hibernate.envers.Audited
@Table(
        indexes = {
                @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class PayrollManagerNote extends AbstractPayrollManagerNote {

    @Column(columnDefinition = "boolean default false")
    private boolean applied = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ScheduledAnnualVacation scheduledAnnualVacation;

    @Column(columnDefinition = "double default 0")
    private Double lastSalary = 0.0;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmedAmountByAuditor = false;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmedRepeatedByAuditor = false;

    @Column(columnDefinition = "boolean default false")
    private boolean paid = false;

    @Transient
    private boolean logActionRequired = false;

    @Column
    private Long referredMaidId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private PayrollManagerNote additionPayrollManagerNoteDeductionSource;

    @Column
    private Integer numberOfDaysWorkedAtOffice;

    @Column
    private Date paidOnPayrollMonth;

    public  boolean  getEnabledAction()
    {
        Calendar calendar = Calendar.getInstance();

        calendar.add(Calendar.MONTH,-5);
        if(calendar.getTime().before(this.getNoteDate()))
        return  true;
        else
            return  false;
    }

    public boolean isLogActionRequired() {
        return logActionRequired;
    }

    public void setLogActionRequired(boolean logActionRequired) {
        this.logActionRequired = logActionRequired;
    }

    public boolean isApplied() {
        return applied;
    }

    public void setApplied(boolean applied) {
        this.applied = applied;
    }

    public ScheduledAnnualVacation getScheduledAnnualVacation() {
        return scheduledAnnualVacation;
    }

    public void setScheduledAnnualVacation(ScheduledAnnualVacation scheduledAnnualVacation) {
        this.scheduledAnnualVacation = scheduledAnnualVacation;
    }

    public Double getLastSalary() {
        return lastSalary;
    }

    public void setLastSalary(Double lastSalary) {
        this.lastSalary = lastSalary;
    }

    public boolean isConfirmedAmountByAuditor() {
        return confirmedAmountByAuditor;
    }

    public void setConfirmedAmountByAuditor(boolean confirmedAmountByAuditor) {
        this.confirmedAmountByAuditor = confirmedAmountByAuditor;
    }

    public boolean isConfirmedRepeatedByAuditor() {
        return confirmedRepeatedByAuditor;
    }

    public void setConfirmedRepeatedByAuditor(boolean confirmedRepeatedByAuditor) {
        this.confirmedRepeatedByAuditor = confirmedRepeatedByAuditor;
    }

    @JsonIgnore
    public String getLastSalaryWIthCurrency(){
        String lastSalarytWithCurrency = "";
        Double amount = getLastSalary();
        DecimalFormat df = new DecimalFormat("0.#####");

        if (amount != null){
            lastSalarytWithCurrency = df.format(amount);
        }else{
            lastSalarytWithCurrency = "0";
        }

        if (getOfficeStaff() != null && getOfficeStaff().getSalaryCurrency() != null){
            lastSalarytWithCurrency+=" "+getOfficeStaff().getSalaryCurrency().name();
        }

        return lastSalarytWithCurrency;
    }

    public boolean isPaid() {
        return paid;
    }

    public void setPaid(boolean paid) {
        this.paid = paid;
    }

    public PayrollManagerNote getAdditionPayrollManagerNoteDeductionSource() {
        return additionPayrollManagerNoteDeductionSource;
    }

    public void setAdditionPayrollManagerNoteDeductionSource(PayrollManagerNote additionPayrollManagerNoteDeductionSource) {
        this.additionPayrollManagerNoteDeductionSource = additionPayrollManagerNoteDeductionSource;
    }

    public Date getPaidOnPayrollMonth() {
        return paidOnPayrollMonth;
    }

    public void setPaidOnPayrollMonth(Date paidOnPayrollMonth) {
        this.paidOnPayrollMonth = paidOnPayrollMonth;
    }

    @AfterInsert
    @AfterUpdate
    public void afterCreateAndUpdate(){
        if(isLogActionRequired() && this.getHousemaid() != null && CurrentRequest.getUser() != null && CurrentRequest.getUser().hasPosition("payroll_auditor")){
            AuditorActionRepository auditorActionRepository = Setup.getRepository(AuditorActionRepository.class);
            AuditorAction auditorAction = new AuditorAction(this.getHousemaid(), CurrentRequest.getUser(), this.getAmount(),
                    this.getVersion() == 0 ? AuditorAction.ActionType.ADDING : AuditorAction.ActionType.EDITING,
                    AuditorAction.Source.PAYROLL_MANAGER_NOTE, this.getNoteReasone());
            auditorActionRepository.save(auditorAction);
        }

    }

    @BeforeDelete
    public void beforeDelete(){
        if(isLogActionRequired() && this.getHousemaid() != null && CurrentRequest.getUser() != null && CurrentRequest.getUser().hasPosition("payroll_auditor")){
            AuditorActionRepository auditorActionRepository = Setup.getRepository(AuditorActionRepository.class);
            AuditorAction auditorAction = new AuditorAction(this.getHousemaid(), CurrentRequest.getUser(),this.getAmount(),
                    AuditorAction.ActionType.DELETING, AuditorAction.Source.PAYROLL_MANAGER_NOTE, this.getNoteReasone());
            auditorActionRepository.save(auditorAction);
        }
    }

    public Integer getNumberOfDaysWorkedAtOffice() {
        return numberOfDaysWorkedAtOffice;
    }

    public void setNumberOfDaysWorkedAtOffice(Integer numberOfDaysWorkedAtOffice) {
        this.numberOfDaysWorkedAtOffice = numberOfDaysWorkedAtOffice;
    }

    public Long getReferredMaidId() {
        return referredMaidId;
    }

    public void setReferredMaidId(Long referredMaidId) {
        this.referredMaidId = referredMaidId;
    }
}
