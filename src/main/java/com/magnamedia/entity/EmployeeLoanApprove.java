package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.helper.NumberFormatter;

import javax.persistence.*;
import java.util.Date;

@Entity
public class EmployeeLoanApprove extends AbstractEmployeeLoan {

    @Column(columnDefinition = "boolean default false")
    private Boolean isApproved = false;

    @Column
    private Date approveDate;

    @Column
    private Double updatedAmount;

    @Column
    private Double updatedRepaymentAmount;

    @Column
    private Double remainingAmount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdJsonSerializer.class)
    private EmployeeLoan originalLoan;

    // Todo: below field could be just Boolean, also delete it if not needed.
    @Column
    @Enumerated(EnumType.STRING)
    private LoanApproveType loanApproveType;

    @Column(columnDefinition = "boolean default false")
    private Boolean isActionTaken = false;

    @Column
    private Date updatedMonth;

    public Boolean getApproved() {
        return isApproved;
    }

    public void setApproved(Boolean approved) {
        isApproved = approved;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public Double getUpdatedAmount() {
        return updatedAmount;
    }

    public void setUpdatedAmount(Double updatedAmount) {
        this.updatedAmount = updatedAmount;
    }

    public Double getUpdatedRepaymentAmount() {
        return updatedRepaymentAmount;
    }

    public void setUpdatedRepaymentAmount(Double updatedRepaymentAmount) {
        this.updatedRepaymentAmount = updatedRepaymentAmount;
    }

    public EmployeeLoan getOriginalLoan() {
        return originalLoan;
    }

    public void setOriginalLoan(EmployeeLoan originalLoan) {
        this.originalLoan = originalLoan;
    }

    public LoanApproveType getLoanApproveType() {
        return loanApproveType;
    }

    public void setLoanApproveType(LoanApproveType loanApproveType) {
        this.loanApproveType = loanApproveType;
    }

    public Double getRemainingAmount() {
        return remainingAmount;
    }

    public void setRemainingAmount(Double remainingAmount) {
        this.remainingAmount = remainingAmount;
    }

    public Boolean getActionTaken() {
        return isActionTaken;
    }

    public void setActionTaken(Boolean actionTaken) {
        isActionTaken = actionTaken;
    }

    public Date getUpdatedMonth() {
        return updatedMonth;
    }

    public void setUpdatedMonth(Date updatedMonth) {
        this.updatedMonth = updatedMonth;
    }

    @JsonIgnore
    public String getAmountWithCurrency() {
        String amountWithCurrency = "";
        if (getOfficeStaff() != null && getOfficeStaff().getSalaryCurrency() != null) {
            amountWithCurrency = getOfficeStaff().getSalaryCurrency().name() + " " + NumberFormatter.formatNumber(getAmount());
        } else {
            amountWithCurrency = "AED " + NumberFormatter.formatNumber(getAmount());
        }

        return amountWithCurrency;
    }

    @JsonIgnore
    public String getUpdatedAmountWithCurrency() {
        String amountWithCurrency = "";
        if (getOfficeStaff() != null && getOfficeStaff().getSalaryCurrency() != null) {
            amountWithCurrency = getOfficeStaff().getSalaryCurrency().name() + " " + NumberFormatter.formatNumber(getUpdatedAmount());
        } else {
            amountWithCurrency = "AED " + NumberFormatter.formatNumber(getUpdatedAmount());
        }

        return amountWithCurrency;
    }

    @JsonIgnore
    public String getUpdatedRepaymentAmountWithCurrency() {
        String amountWithCurrency = "";
        if (getOfficeStaff() != null && getOfficeStaff().getSalaryCurrency() != null) {
            amountWithCurrency = getOfficeStaff().getSalaryCurrency().name() + " " + NumberFormatter.formatNumber(getUpdatedRepaymentAmount());
        } else {
            amountWithCurrency = "AED " + NumberFormatter.formatNumber(getUpdatedRepaymentAmount());
        }

        return amountWithCurrency;
    }

    @Override
    public Double getAmount() {
        Double amount = super.getAmount();
        if (amount == null && originalLoan != null) amount = originalLoan.getAmount();
        return amount;
    }

    @Override
    public Date getLoanDate() {
        Date loanDate = super.getLoanDate();
        if (loanDate == null && originalLoan != null) loanDate = originalLoan.getLoanDate();
        return loanDate;
    }

    @Override
    public LoanType getLoanType() {
        LoanType type = super.getLoanType();
        if (type == null && originalLoan != null) type = originalLoan.getLoanType();
        return type;
    }

    public enum LoanApproveType {
        NEW_LOAN,
        UPDATED_LOAN,
        DELETE_LOAN,
        EDIT_REPAYMENT
    }
}