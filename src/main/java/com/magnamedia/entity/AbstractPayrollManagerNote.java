package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.repository.SalariesAccessRepository;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;

@MappedSuperclass
public abstract class AbstractPayrollManagerNote extends BaseEntity {
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private Housemaid housemaid;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private OfficeStaff officeStaff;
    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    private Double amount;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollManagerNote oldNote;

    //    @Column
//    private Double postponedAmount;
//
//    //Jirra ACC-645
//    @Column
//    private Date postponedDate;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    @Lob
    private String noteReasone;
    @Column
    private Date noteDate;
    @Column
    private Boolean isRefund = false;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollManagerNote refundedNote;
    @Column
    @Enumerated(EnumType.STRING)
    private PayrollManagerNote.ManagerNoteType noteType;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem additionReason;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem purpose;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem deductionReason;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem fromManager;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private OfficeStaff employeeManager;

    //Jirra ACC-1085
    @Column(columnDefinition = "boolean default false")
    private boolean notFinal;

    /*
     * <AUTHOR> Qazzaz
     * @reason PAY-117
     * start
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem noShowType;
    /*
     * end
     */

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public PayrollManagerNote getOldNote() {
        return oldNote;
    }

//    public Double getPostponedAmount() {
//        return postponedAmount;
//    }
//
//    public void setPostponedAmount(Double postponedAmount) {
//        this.postponedAmount = postponedAmount;
//    }
//
//    public Date getPostponedDate() {
//        return postponedDate;
//    }
//
//    public void setPostponedDate(Date postponedDate) {
//        this.postponedDate = postponedDate;
//    }

    public void setOldNote(PayrollManagerNote oldNote) {
        this.oldNote = oldNote;
    }

    public Boolean getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Boolean isRefund) {
        this.isRefund = isRefund;
    }

    public PayrollManagerNote getRefundedNote() {
        return refundedNote;
    }

    public void setRefundedNote(PayrollManagerNote refundedNote) {
        this.refundedNote = refundedNote;
    }

    public String getNoteReasone() {
        return noteReasone;
    }

    public void setNoteReasone(String noteReasone) {
        this.noteReasone = noteReasone;
    }

    public PayrollManagerNote.ManagerNoteType getNoteType() {
        return noteType;
    }

    public void setNoteType(PayrollManagerNote.ManagerNoteType noteType) {
        this.noteType = noteType;
    }

    public PicklistItem getAdditionReason() {
        return additionReason;
    }

    public void setAdditionReason(PicklistItem additionReason) {
        this.additionReason = additionReason;
    }

    public PicklistItem getDeductionReason() {
        return deductionReason;
    }

    public void setDeductionReason(PicklistItem deductionReason) {
        this.deductionReason = deductionReason;
    }

    public PicklistItem getPurpose() {
        return purpose;
    }

    public void setPurpose(PicklistItem purpose) {
        this.purpose = purpose;
    }

    public PicklistItem getFromManager() {
        return fromManager;
    }

    public void setFromManager(PicklistItem fromManager) {
        this.fromManager = fromManager;
    }

    public Date getNoteDate() {
        return noteDate;
    }

    public void setNoteDate(Date noteDate) {
        this.noteDate = noteDate;
    }

    public boolean isNotFinal() {
        return notFinal;
    }

    public void setNotFinal(boolean notFinal) {
        this.notFinal = notFinal;
    }

    public enum ManagerNoteType {
        ADDITION,
        DEDUCTION,
        PENALTY_DEDUCTION,
        EXTRA_SHIFT,
        BONUS,
        REDUCTION,
        SALARY_RAISE
    }

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double basicSalary = 0d;

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double housing = 0d;

    @Column(columnDefinition = "double default 0")
    @NotNull
    private Double transportation = 0d;

    public Double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Double getHousing() {
        return housing;
    }

    public void setHousing(Double housing) {
        this.housing = housing;
    }

    public Double getTransportation() {
        return transportation;
    }

    public void setTransportation(Double transportation) {
        this.transportation = transportation;
    }

    public OfficeStaff getEmployeeManager() {
        return employeeManager;
    }

    public void setEmployeeManager(OfficeStaff employeeManager) {
        this.employeeManager = employeeManager;
    }

    public boolean getCanDeletedUpdated(){
        SalariesAccess salariesAccess = Setup.getRepository(SalariesAccessRepository.class).findTopByUser(CurrentRequest.getUser());
        return salariesAccess != null && salariesAccess.getForHousemaids();
    }

    /*
     * <AUTHOR> Qazzaz
     * @reason PAY-117
     * start
     */
    public PicklistItem getNoShowType() {
        return noShowType;
    }

    public void setNoShowType(PicklistItem noShowType) {
        this.noShowType = noShowType;
    }

    /*
     * end PAY-117
     * start
     */

    @JsonIgnore
    public String getAmountWIthCurrency(){
        String amountWithCurrency = "";
        Double amount = getAmount();

        if (getOfficeStaff() != null && getOfficeStaff().getSalaryCurrency() != null)
            amountWithCurrency+=getOfficeStaff().getSalaryCurrency().name() + " ";
        else
            amountWithCurrency+= "AED ";

        if (amount != null){
            amountWithCurrency += NumberFormatter.formatNumber(amount);
        }else{
            amountWithCurrency = "0";
        }

        return amountWithCurrency;
    }

}

