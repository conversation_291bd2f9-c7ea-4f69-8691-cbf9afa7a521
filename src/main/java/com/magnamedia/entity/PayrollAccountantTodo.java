package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.AuditSetupController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.serializer.SalaryAmountSerializer;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.HousemaidUnpaidStatus;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PayrollAccountantTodoType;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.HousemaidPayrollLogRepository;
import com.magnamedia.repository.OfficeStaffPayrollLogRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.PayrollAccountantTodoRepository;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollExceptionsReportService;
import com.magnamedia.service.payroll.generation.newversion.AuditFilesService;
import com.magnamedia.service.payroll.generation.newversion.TransferFilesService;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Qazzaz
 * @reason PAY-58
 */

@Entity @org.hibernate.envers.Audited
public class PayrollAccountantTodo extends WorkflowEntity {

    @Column
    private String label;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double amount = 0.0;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double charges = 0.0;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double vat = 0.0;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double total = 0.0;

    @Column
    private String dueOn;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column
    private Double remainingAmount;

    @Column(columnDefinition = "boolean default false")
    private Boolean paidByManager = false;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    private MonthlyPaymentRule monthlyPaymentRule;

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Housemaid> housemaids = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<Housemaid> includedHousemaids = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<OfficeStaff> officeStaffs = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<OfficeStaff> includedofficeStaffs = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<OfficeStaff> TerminatedOfficeStaffs = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<OfficeStaff> excludedOfficeStaffs = new HashSet<>();

    @Enumerated(EnumType.STRING)
    private SalaryCurrency currency;

    @Column
    private Date paymentDate;

    @Column
    private Date payrollMonth;

    @Column(columnDefinition = "boolean default false")
    private Boolean singleOfficeStaff = false;

    //PAY-612
    //used to be for one housemaid now it's related to all maids who have a final settlement ready to be paid
    //it is now for all monthly payment rules which are not for Main Payroll process (like CC switched to MV to-do and paying Final settlements)
    @Column(columnDefinition = "boolean default false")
    private Boolean singleHousemaid = false;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction managerAction = PayrollAccountantTodoManagerAction.PENDING;

    @Enumerated(EnumType.STRING)
    private PayrollAccountantTodoManagerAction ceoAction = PayrollAccountantTodoManagerAction.PENDING;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem itemType;

    @JsonSerialize(using = SalaryAmountSerializer.class)
    @Column(columnDefinition = "double default 0")
    private Double totalInAED = 0.0;

    @Column(columnDefinition = "int default 0")
    private Integer totalIncludedStaffs = 0;

    @Column(columnDefinition = "int default 0")
    private Integer totalIncludedMaids = 0;

    @Column(columnDefinition = "boolean default false")
    private Boolean openedToAccountants = false;

    @Column
    private java.util.Date openedToAccountantsDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean housemaidsPayslipsSent = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean bankTransferReportIsSent = false;

    @Column
    private String uniquePaymentCode;

    public PayrollAccountantTodo() {
        super(null);
    }

    public PayrollAccountantTodo(MonthlyPaymentRule rule, PayrollAccountantTodoType type, List<Housemaid> housemaids, List<Housemaid> includedHousemaids, List<OfficeStaff> officeStaffs, List<OfficeStaff> includedOfficeStaffs, List<OfficeStaff> terminatedOfficeStaffs, List<OfficeStaff> excludedOfficeStaffs) {
        super(type.toString());
        this.monthlyPaymentRule = rule;
        this.payrollMonth = rule.getPayrollMonth();
        this.housemaids.addAll(housemaids);
        this.includedHousemaids.addAll(includedHousemaids);
        this.officeStaffs.addAll(officeStaffs);
        this.includedofficeStaffs.addAll(includedOfficeStaffs);
        this.TerminatedOfficeStaffs.addAll(terminatedOfficeStaffs);
        this.excludedOfficeStaffs.addAll(excludedOfficeStaffs);
    }

    // Used for pension authority
    public PayrollAccountantTodo(PayrollAccountantTodoType type, List<OfficeStaff> officeStaffs) {
        super(type.toString());
        this.officeStaffs.addAll(officeStaffs);
        this.includedofficeStaffs.addAll(officeStaffs);
        this.paymentDate = new Date(System.currentTimeMillis());
        this.payrollMonth = new Date(System.currentTimeMillis());
        this.monthlyPaymentRule = null;
    }

    // Used for sending transactions
    public PayrollAccountantTodo(PayrollAccountantTodoType type, Date paymentDate, Date payrollMonth) {
        super(type.toString());
        this.paymentDate = new Date(paymentDate.getTime());
        this.payrollMonth = new Date(payrollMonth.getTime());
        this.setDueOn(DateUtil.formatClientFullDate(this.getPaymentDate()));
        this.label = "Sending Transaction Number of the Transfers Done for Overseas Staff";
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Override
    public List<FormField> getForm(String string) {
        return null;
    }

    @Override
    public String getLabel() {
        super.getLabel();
        return this.label;
    }

    public String getToDoLabel() {
        return this.label;
    }
    public void setLabel(String label) {
        this.label = label;
    }

    public Double getAmount() {
        return NumberFormatter.twoDecimalPoints(amount);
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getDueOn() {
        return dueOn;
    }

    public void setDueOn(String dueOn) {
        this.dueOn = dueOn;
    }

    public Boolean getPaidByManager() {
        return paidByManager;
    }

    public void setPaidByManager(Boolean paidByManager) {
        this.paidByManager = paidByManager;
    }

    public Double getRemainingAmount() {
        return remainingAmount = remainingAmount == null ? getAmount() :
                NumberFormatter.twoDecimalPoints(remainingAmount);
    }

    public void setRemainingAmount(Double remainingAmount) {
        this.remainingAmount = remainingAmount;
    }

    public Double getCharges() {
        return NumberFormatter.twoDecimalPoints(charges);
    }

    public void setCharges(Double charges) {
        this.charges = charges;
    }

    public Double getVat() {
        return vat;
    }

    public void setVat(Double vat) {
        this.vat = vat;
    }

    public Double getTotal() {
        if (total == null || total.equals(0.0))
            total = amount + charges + vat;
        return NumberFormatter.twoDecimalPoints(total);
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    @JsonIgnore
    public Date getPayrollMonth() {
        if(this.payrollMonth != null) return this.payrollMonth;
        return this.getMonthlyPaymentRule() != null ? this.getMonthlyPaymentRule().getPayrollMonth() : null;
    }

    public void setPayrollMonth(Date payrollMonth) {
        this.payrollMonth = payrollMonth;
    }

    public void setPaymentDate(java.sql.Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public Date getPaymentDate() {
        if(this.paymentDate != null) return this.paymentDate;
        return this.getMonthlyPaymentRule() != null ? this.getMonthlyPaymentRule().getPaymentDate() : null;
    }

    public MonthlyPaymentRule getMonthlyPaymentRule() {
        return monthlyPaymentRule;
    }

    public void setMonthlyPaymentRule(MonthlyPaymentRule monthlyPaymentRule) {
        this.monthlyPaymentRule = monthlyPaymentRule;
    }

    public Set<Housemaid> getHousemaids() {
        return housemaids;
    }

    public void setHousemaids(Set<Housemaid> housemaids) {
        this.housemaids = housemaids;
    }

    public Set<Housemaid> getIncludedHousemaids() {
        if (includedHousemaids == null)
            includedHousemaids = new HashSet<>();
        return includedHousemaids;
    }

    public void setIncludedHousemaids(Set<Housemaid> includedHousemaids) {
        this.includedHousemaids = includedHousemaids;
    }

    public Set<OfficeStaff> getOfficeStaffs() {
        return officeStaffs;
    }

    public void setOfficeStaffs(Set<OfficeStaff> officeStaffs) {
        this.officeStaffs = officeStaffs;
    }

    public Set<OfficeStaff> getIncludedofficeStaffs() {
        if (includedofficeStaffs == null)
            includedofficeStaffs = new HashSet<>();
        return includedofficeStaffs;
    }

    public void setIncludedofficeStaffs(Set<OfficeStaff> includedofficeStaffs) {
        this.includedofficeStaffs = includedofficeStaffs;
    }

    public Set<OfficeStaff> getTerminatedOfficeStaffs() {
        return TerminatedOfficeStaffs;
    }

    public void setTerminatedOfficeStaffs(Set<OfficeStaff> terminatedOfficeStaffs) {
        TerminatedOfficeStaffs = terminatedOfficeStaffs;
    }

    public Set<OfficeStaff> getExcludedOfficeStaffs() {
        return excludedOfficeStaffs;
    }

    public void setExcludedOfficeStaffs(Set<OfficeStaff> excludedOfficeStaffs) {
        this.excludedOfficeStaffs = excludedOfficeStaffs;
    }

    public PayrollAccountantTodoManagerAction getManagerAction() {
        return managerAction;
    }

    public void setManagerAction(PayrollAccountantTodoManagerAction managerAction) {
        this.managerAction = managerAction;
    }

    public PayrollAccountantTodoManagerAction getCeoAction() {
        return ceoAction;
    }

    public void setCeoAction(PayrollAccountantTodoManagerAction ceoAction) {
        this.ceoAction = ceoAction;
    }

    public PicklistItem getItemType() {
        return itemType;
    }

    public void setItemType(PicklistItem itemType) {
        this.itemType = itemType;
    }

    // TODO: 7/8/2021 Change Later and make this functaion special for Pension Authority
    public void build() {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        this.setDueOn(DateUtil.formatClientFullDate(this.getPaymentDate()));

        //used in calculation of charges
        int totalIncludedMaids = 0;
        int totalIncludedStaffs = 0;

//        DebugHelper.sendMail("<EMAIL>", String.format("Try to generate payroll logs for %s housemaids and %s officestaff",
//                this.housemaids.size(), this.officeStaffs.size()));

        // ---------- Office Staff -------------- //
        List<OfficeStaffPayrollLog> officeStaffPayrollLogs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).
                generatePayrollLogs(new ArrayList<>(this.getOfficeStaffs()), this, true);
        for(OfficeStaffPayrollLog log: officeStaffPayrollLogs) {
            log.setPayrollAccountantTodo(this);
        }
        Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(officeStaffPayrollLogs);
//        DebugHelper.sendMail("<EMAIL>", "after processing included staffs: ");

        //PAY-359 get Excluded Payroll Logs just for Payroll Detailed File
        List<OfficeStaffPayrollLog> terminatedOfficeStaffPayrollLogs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).
                generatePayrollLogs(new ArrayList<>(this.getTerminatedOfficeStaffs()), this, true);
//        DebugHelper.sendMail("<EMAIL>", "after processing excluded staffs: ");

        List<OfficeStaffPayrollLog> excludedOfficeStaffPayrollLogs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).
                generatePayrollLogs(new ArrayList<>(this.getExcludedOfficeStaffs()), this, true);
//        DebugHelper.sendMail("<EMAIL>", "after processing terminated staffs: ");

        // ----------- Housemaids ------------- //
        List<HousemaidPayrollLog> housemaidPayrollLogs = new ArrayList<>();
        if(monthlyPaymentRule != null) {
            housemaidPayrollLogs = monthlyPaymentRule.isSecondaryMonthlyRule() ?
                    Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).
                            generatePayrollLogsForSecondary(new ArrayList<>(this.getHousemaids()), this, true) :
                    Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).
                            generatePayrollLogs(new ArrayList<>(this.getHousemaids()), this, true);
        }
        for(HousemaidPayrollLog log: housemaidPayrollLogs) {
            log.setPayrollAccountantTodo(this);
        }
        Setup.getRepository(HousemaidPayrollLogRepository.class).save(housemaidPayrollLogs);

        List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonths(this, true);
        if(maidVisaLogsForPreviousMonths != null && !maidVisaLogsForPreviousMonths.isEmpty()){
            maidVisaLogsForPreviousMonths.sort(Comparator.comparing((HousemaidPayrollLog h1) -> h1.getHousemaid().getName()));
            Setup.getRepository(HousemaidPayrollLogRepository.class).save(maidVisaLogsForPreviousMonths);
        }

//        DebugHelper.sendMail("<EMAIL>", String.format("After generate payroll logs for %s housemaids and %s officestaff",
//                this.housemaids.size(), this.officeStaffs.size()));

        for(OfficeStaffPayrollLog log: officeStaffPayrollLogs) {
            if(log.getWillBeIncluded()) {
                this.amount += todoType == PayrollAccountantTodoType.PENSION_AUTHORITY ? log.getContributionAmount() : log.getTotalSalary();
                totalIncludedStaffs++;
            }
        }

        for(HousemaidPayrollLog log: housemaidPayrollLogs) {
            if(log.getWillBeIncluded()) {
                this.amount += log.getTotalSalary();
            }
        }
        for(HousemaidPayrollLog log: maidVisaLogsForPreviousMonths) {
                this.amount += log.getTotalSalary();
        }

        Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);

        Set<Long> includedHousemaidIds = housemaidPayrollLogs.stream().filter(x -> x.getWillBeIncluded()).map(x -> x.getHousemaid().getId()).collect(Collectors.toSet());
        includedHousemaidIds.addAll(maidVisaLogsForPreviousMonths.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toSet()));
//        DebugHelper.sendMail("<EMAIL>", "includedHousemaidIds: " + includedHousemaidIds);
        totalIncludedMaids = includedHousemaidIds.size();


        if(PayrollAccountantTodoType.valueOf(this.getTaskName()) == PayrollAccountantTodoType.PENSION_AUTHORITY) {
            this.label = String.format("Transfer Money to Pension Authority for (%s) Employees", officeStaffPayrollLogs.size());
            return;
        }

        StringBuilder employeeTypes = new StringBuilder();
        for (PaymentRuleEmployeeType paymentRuleEmployeeType : this.getMonthlyPaymentRule().getEmployeeTypeList()) {
            if (employeeTypes.length() != 0) {
                employeeTypes.append(" and ");
            }
            employeeTypes.append(paymentRuleEmployeeType.getLabel());
        }

//        DebugHelper.sendMail("<EMAIL>", "Before setting accountant todo type");

        AuditFilesService auditFilesService = Setup.getApplicationContext()
                .getBean(AuditFilesService.class);
        // PAY-247
        this.setStopped(true);

        //get Included Payroll logs only
        List<HousemaidPayrollLog> includedHousemaidPayrollLogs = housemaidPayrollLogs.stream().filter(x -> x.getWillBeIncluded()).collect(Collectors.toList());
        List<HousemaidPayrollLog> excludedHousemaidPayrollLogs = housemaidPayrollLogs.stream().filter(x -> !x.getWillBeIncluded()).collect(Collectors.toList());
        List<OfficeStaffPayrollLog> includedOfficeStaffPayrollLogs = officeStaffPayrollLogs.stream().filter(x -> x.getWillBeIncluded()).collect(Collectors.toList());
        int daysInPeriod = this.getPayrollMonth().toLocalDate().lengthOfMonth();
        java.util.Date start = new LocalDate(this.getPayrollMonth()).withDayOfMonth(1).toDate();
        java.util.Date end = new LocalDate(this.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate();

        String ansariInfo = " of " + monthlyPaymentRule.getEmployeesTypes()
                + (monthlyPaymentRule.getPayrollType() == null ? "" : " - " + monthlyPaymentRule.getPayrollType().getLabel())
                + " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());

        List<Housemaid> ccMaids = this.getIncludedHousemaids().stream().filter(x -> !x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
        List<Housemaid> visaMaids = this.getIncludedHousemaids().stream().filter(x -> x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
        List<OfficeStaff> staffs = new ArrayList<>(this.getIncludedofficeStaffs());
        switch (todoType) {
            case WPS:
                this.label = String.format("Transfer Money to WPS for %s", employeeTypes);
                this.charges = (totalIncludedStaffs + totalIncludedMaids) * Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ANSARI_CHARGES_RATE));
                this.vat = charges * 0.05d;

                try {
                    List<Attachment> attachments = new ArrayList<>();

                    Attachment wpsFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateWPSTransferFile(this.getMonthlyPaymentRule(), ansariInfo, includedHousemaidPayrollLogs, includedOfficeStaffPayrollLogs, maidVisaLogsForPreviousMonthsMap, totalIncludedMaids, daysInPeriod, start, end);
                    if(wpsFile != null) {
                        attachments.add(wpsFile);
                    }

                    if(!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = auditFilesService
                                .generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, this.getMonthlyPaymentRule(), todoType, true);
                        if(staffDetailedFile != null) attachments.add(staffDetailedFile);
                    }

                    if(!housemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = auditFilesService
                                .generateHousemaidFinalDetailedPayrollFile(includedHousemaidPayrollLogs, this.getMonthlyPaymentRule(), maidVisaLogsForPreviousMonthsMap, excludedHousemaidPayrollLogs, todoType);
                        if(housemaidsDetailedFile != null) attachments.add(housemaidsDetailedFile);
                    }

                    Attachment payrollExceptionReport = Setup.getApplicationContext().getBean(PayrollExceptionsReportService.class).generatePayrollExceptionsReport(ccMaids, visaMaids, staffs, monthlyPaymentRule, StringHelper.enumToCapitalizedFirstLetter(this.getTaskName()));
                    if (payrollExceptionReport != null) attachments.add(payrollExceptionReport);
//                    Attachment noEidFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
//                            generateNoEIDFile(this.getMonthlyPaymentRule(), housemaidPayrollLogs, officeStaffPayrollLogs);
//                    if(noEidFile != null) {
//                        attachments.add(noEidFile);
//                    }

                    this.setAttachments(attachments);
                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating wps file", false);
                }
                break;
            case BANK_TRANSFER:
                this.label = String.format("Transfer Money to (%s) employees' bank accounts", includedOfficeStaffPayrollLogs.size());
                try {
                    Attachment bankTransferFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateBankTransferFile(ansariInfo, includedOfficeStaffPayrollLogs);

                    List<Attachment> attachments = new ArrayList<>();

                    if (bankTransferFile != null) {
                        attachments.add(bankTransferFile);
                    }

                    if (!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = auditFilesService
                                .generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, this.getMonthlyPaymentRule(), todoType, true);
                        if (staffDetailedFile != null) attachments.add(staffDetailedFile);
                    }

                    if (!housemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = auditFilesService
                                .generateHousemaidFinalDetailedPayrollFile(includedHousemaidPayrollLogs, this.getMonthlyPaymentRule(), maidVisaLogsForPreviousMonthsMap, excludedHousemaidPayrollLogs, todoType);
                        if (housemaidsDetailedFile != null) attachments.add(housemaidsDetailedFile);
                    }

                    Attachment payrollExceptionReport = Setup.getApplicationContext().getBean(PayrollExceptionsReportService.class).generatePayrollExceptionsReport(ccMaids, visaMaids, staffs, monthlyPaymentRule, StringHelper.enumToCapitalizedFirstLetter(this.getTaskName()));
                    if (payrollExceptionReport != null) attachments.add(payrollExceptionReport);

                    this.setAttachments(attachments);
                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating bank transfer file", false);
                }

                break;
            case LOCAL_TRANSFER:
                this.label = String.format("Transfer Money to %s for %s",
                        this.getMonthlyPaymentRule().getMoneyExchangeName(), employeeTypes);
                try {
                    Attachment localFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateLocalTransferFile(this.getMonthlyPaymentRule(), ansariInfo, includedHousemaidPayrollLogs, includedOfficeStaffPayrollLogs, maidVisaLogsForPreviousMonthsMap);

                    List<Attachment> attachments = new ArrayList<>();

                    if(localFile != null) {
                        attachments.add(localFile);
                    }

                    if (!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = auditFilesService
                                .generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, this.getMonthlyPaymentRule(), todoType, true);
                        if (staffDetailedFile != null) attachments.add(staffDetailedFile);
                    }

                    if (!housemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = auditFilesService
                                .generateHousemaidFinalDetailedPayrollFile(includedHousemaidPayrollLogs, this.getMonthlyPaymentRule(), maidVisaLogsForPreviousMonthsMap, excludedHousemaidPayrollLogs, todoType);
                        if (housemaidsDetailedFile != null) attachments.add(housemaidsDetailedFile);
                    }

                    Attachment payrollExceptionReport = Setup.getApplicationContext().getBean(PayrollExceptionsReportService.class).generatePayrollExceptionsReport(ccMaids, visaMaids, staffs, monthlyPaymentRule, StringHelper.enumToCapitalizedFirstLetter(this.getTaskName()));
                    if (payrollExceptionReport != null) attachments.add(payrollExceptionReport);

                    this.setAttachments(attachments);
//                    //send email to Ansari
//                    Map<String, String> paramValues = new HashMap();
//                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
//                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_Local_Exchange_Template",
//                            paramValues, this.getAttachments());


                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating local transfer file", false);
                }
                break;
            case INTERNATIONAL_TRANSFER:
                this.label = String.format("Transfer Money to %s for %s",
                        this.getMonthlyPaymentRule().getMoneyExchangeName(), employeeTypes);

                String uniquePaymentCode = null;
                if(this.getUniquePaymentCode() != null){
                    uniquePaymentCode = this.getUniquePaymentCode();
                } else {
                    // create new unique payment code
                    uniquePaymentCode = Setup.getApplicationContext().getBean(AuditSetupController.class).getNewUniquePaymentCode();

                    // save in the PayrollAccountantTodo
                    this.setUniquePaymentCode(uniquePaymentCode);
                }
                ansariInfo += " - " + uniquePaymentCode;

                try {
                    Attachment internationalFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateInternationalTransferFile(this.getMonthlyPaymentRule(), ansariInfo, includedOfficeStaffPayrollLogs);

                    List<Attachment> attachments = new ArrayList<>();

                    if(internationalFile != null) {
                        attachments.add(internationalFile);
                    }

                    if (!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = auditFilesService
                                .generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, this.getMonthlyPaymentRule(), todoType, true);
                        if (staffDetailedFile != null) attachments.add(staffDetailedFile);
                    }

                    if (!housemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = auditFilesService
                                .generateHousemaidFinalDetailedPayrollFile(includedHousemaidPayrollLogs, this.getMonthlyPaymentRule(), maidVisaLogsForPreviousMonthsMap, excludedHousemaidPayrollLogs, todoType);
                        if (housemaidsDetailedFile != null) attachments.add(housemaidsDetailedFile);
                    }

                    Attachment payrollExceptionReport = Setup.getApplicationContext().getBean(PayrollExceptionsReportService.class).generatePayrollExceptionsReport(ccMaids, visaMaids, staffs, monthlyPaymentRule, StringHelper.enumToCapitalizedFirstLetter(this.getTaskName()));
                    if (payrollExceptionReport != null) attachments.add(payrollExceptionReport);

                    this.setAttachments(attachments);

//                    //send email to Ansari
//                    Map<String, String> paramValues = new HashMap();
//                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
//                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_International_Exchange_Template",
//                            paramValues, this.getAttachments());

                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating international transfer file", false);
                }
                break;
            case CASH:
                this.label = "Pay cash salaries";
                break;
            case SENDING_TRANSACTIONS:
                this.label = "Sending Transaction Number of the Transfers Done for Overseas Staff";
                break;
            case PENSION_AUTHORITY:
                this.label = String.format("Transfer Money to Pension Authority for (%s) Employees", officeStaffPayrollLogs.size());
                break;
        }

//        DebugHelper.sendMail("<EMAIL>", "After setting accountant todo type");

    }

    public void buildNew(){
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        this.setDueOn(DateUtil.formatClientFullDate(this.getPaymentDate()));
        DebugHelper.sendMail("<EMAIL>", String.format("Try to generate payroll logs for %s housemaids and %s officestaff",
                this.housemaids.size(), this.officeStaffs.size()));

        List<Long> terminatedStaffsIds = this.getTerminatedOfficeStaffs().stream().map(BaseEntity::getId).collect(Collectors.toList());
        List<Long> excludedStaffsIds = this.getExcludedOfficeStaffs().stream().map(BaseEntity::getId).collect(Collectors.toList());

        // ---------- Office Staff -------------- //
        List<OfficeStaffPayrollLog> officeStaffPayrollLogs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).
                getOrCreatePayrollLogs(new ArrayList<>(this.getOfficeStaffs()), monthlyPaymentRule, this, true);
//        DebugHelper.sendMail("<EMAIL>", "after processing included staffs: ");

        //get Terminated Payroll Logs just for Payroll Detailed File
        List<OfficeStaffPayrollLog> terminatedOfficeStaffPayrollLogs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).
                getOfficeStaffPayrollLogsBasedOnAll(terminatedStaffsIds, monthlyPaymentRule, null, this);
//        DebugHelper.sendMail("<EMAIL>", "after processing excluded staffs: ");

        //get Excluded Payroll Logs just for Payroll Detailed File
        List<OfficeStaffPayrollLog> excludedOfficeStaffPayrollLogs = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).
                getOfficeStaffPayrollLogsBasedOnAll(excludedStaffsIds, monthlyPaymentRule, null, this);
//        DebugHelper.sendMail("<EMAIL>", "after processing terminated staffs: ");

        // ----------- Housemaids ------------- //
        if(monthlyPaymentRule != null) {
            Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).
                            getOrCreatePayrollLogs(new ArrayList<>(this.getHousemaids()), monthlyPaymentRule, this, true);
        }

        // -------------- Old MV Maids Salaries -------------//
        Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getMVPayrollLogsForPreviousMonthsNew(monthlyPaymentRule,this, true);


        //---------------------------------------------------//
//        DebugHelper.sendMail("<EMAIL>", String.format("After generate payroll logs for %s housemaids and %s officestaff and amount before sum is %s",
//                this.housemaids.size(), this.officeStaffs.size(), this.amount));

        Double officeStaffsLogsAmount = Setup.getRepository(OfficeStaffPayrollLogRepository.class).sumByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonth(this, this.payrollMonth);
        officeStaffsLogsAmount = officeStaffsLogsAmount == null ? 0D : officeStaffsLogsAmount;
        this.amount += officeStaffsLogsAmount;

        Double housemaidsLogsAmount= Setup.getRepository(HousemaidPayrollLogRepository.class).sumByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonth(this, this.getPayrollMonth());
        housemaidsLogsAmount = housemaidsLogsAmount == null ? 0D : housemaidsLogsAmount;
        this.amount += housemaidsLogsAmount;

        Double maidVisaOldLogsAmount= Setup.getRepository(HousemaidPayrollLogRepository.class).sumByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthLessThanAndHousemaidUnpaidStatus(this, this.getPayrollMonth(), HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
        maidVisaOldLogsAmount = maidVisaOldLogsAmount == null ? 0D : maidVisaOldLogsAmount;
        this.amount += maidVisaOldLogsAmount;


//        DebugHelper.sendMail("<EMAIL>", "total amount is: " + this.amount + ", officeStaffsLogsAmount: " + officeStaffsLogsAmount + ", housemaidsLogsAmount: " + housemaidsLogsAmount + ", maidVisaOldLogsAmount: " + maidVisaOldLogsAmount);

        Set<Long> includedHousemaidIds = new HashSet<>(Setup.getRepository(HousemaidPayrollLogRepository.class).findDistinctHousemaidIdByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByHousemaidName(this, this.payrollMonth));
        Set<Long> includedOfficeStaffIds = new HashSet<>(Setup.getRepository(OfficeStaffPayrollLogRepository.class).findDistinctOfficeStaffIdByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByOfficeStaffName(this, this.payrollMonth));
        includedHousemaidIds.addAll(Setup.getRepository(HousemaidPayrollLogRepository.class).findDistinctHousemaidIdByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthLessThanAndHousemaidUnpaidStatusOrderByHousemaidName(this, this.payrollMonth, HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT));
//        DebugHelper.sendMail("<EMAIL>", "includedHousemaidIds: " + includedHousemaidIds + " , includedOfficeStaffIds: " + includedOfficeStaffIds);
        this.totalIncludedMaids = includedHousemaidIds.size();
        this.totalIncludedStaffs = includedOfficeStaffIds.size();

        // if task_name == international_transfer
        if(todoType.equals(PayrollAccountantTodoType.INTERNATIONAL_TRANSFER)){
            // create new unique payment code
            String newUniquePaymentCode = Setup.getApplicationContext().getBean(AuditSetupController.class).getNewUniquePaymentCode();

            // save in the PayrollAccountantTodo so it can be used to send emails
            this.setUniquePaymentCode(newUniquePaymentCode);
        }

//        DebugHelper.sendMail("<EMAIL>", "finally finish building accountant todo of Type: " + this.getTaskName());

        Setup.getRepository(PayrollAccountantTodoRepository.class).save(this);

    }

    public void buildForSingleOfficeStaff(OfficeStaff officeStaff, List<OfficeStaffPayrollLog> officeStaffPayrollLogs) {
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        this.setDueOn(DateUtil.formatClientFullDate(this.getPaymentDate()));

        for (OfficeStaffPayrollLog log : officeStaffPayrollLogs) {
            log.setPayrollAccountantTodo(this);
            log.setWillBeIncluded(true);
            this.amount += log.getTotalSalary();

            fillPayrollLogInfo(log);
        }

        if(todoType.equals(PayrollAccountantTodoType.BANK_TRANSFER)
                && officeStaff.getSelectedTransferDestination() != null && officeStaff.getSelectedTransferDestination().getIban() != null && officeStaff.getSelectedTransferDestination().getIban().toLowerCase().startsWith("ae")){
            String exchangeRateS = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_USD_TO_AED_EXCHANGE_RATE);
            Double exchangeRate = exchangeRateS == null ? 0.0 : Double.parseDouble(exchangeRateS);
            this.totalInAED = exchangeRate * this.amount;
        }
        Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(officeStaffPayrollLogs);

        String newUniquePaymentCode = null;
        if(todoType.equals(PayrollAccountantTodoType.INTERNATIONAL_TRANSFER)){

            // create new unique payment code
            newUniquePaymentCode = Setup.getApplicationContext().getBean(AuditSetupController.class).getNewUniquePaymentCode();

            // save in the PayrollAccountantTodo so it can be used to send emails
            this.setUniquePaymentCode(newUniquePaymentCode);
        }

        StringBuilder employeeTypes = new StringBuilder();
        for (PaymentRuleEmployeeType paymentRuleEmployeeType : this.getMonthlyPaymentRule().getEmployeeTypeList()) {
            if (employeeTypes.length() != 0) {
                employeeTypes.append(" and ");
            }
            employeeTypes.append(paymentRuleEmployeeType.getLabel());
        }

        OfficeStaffPayrollLog accumulativeLog = officeStaffPayrollLogs.get(0);
        Double originalAmount = accumulativeLog.getTotalSalary();
        accumulativeLog.setTotalSalary(this.getAmount());

        String ansariInfo = " of " + monthlyPaymentRule.getEmployeesTypes()
                + (monthlyPaymentRule.getPayrollType() == null ? "" : " - " + monthlyPaymentRule.getPayrollType().getLabel())
                + " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth())
                + " - " + (PayrollAccountantTodoType.WPS.equals(todoType) || PayrollAccountantTodoType.LOCAL_TRANSFER.equals(todoType) ? officeStaff.getName() : accumulativeLog.getFullNameEnglish());

        if(todoType.equals(PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) && newUniquePaymentCode != null){
            ansariInfo += " - " + newUniquePaymentCode;
        }

        switch (todoType) {
            case WPS:
                this.label = String.format("Transfer Money to WPS for %s - %s", employeeTypes, officeStaff.getName());
                try {
                    Attachment wpsFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateWPSTransferFile(monthlyPaymentRule, ansariInfo, new ArrayList<>(), Arrays.asList(accumulativeLog), null, 0, 0, null, null);
                    if (wpsFile != null) {
                        List<Attachment> attachments = new ArrayList<>();
                        attachments.add(wpsFile);
                        this.setAttachments(attachments);
                    }

                    Map<String, String> paramValues = new HashMap<>();
                    paramValues.put("payroll_month", DateUtil.formatSimpleMonthYear(this.getPayrollMonth()));
                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
                    paramValues.put("total_salaries", NumberFormatter.formatNumber(this.getAmount()));
                    paramValues.put("ansari_charges", NumberFormatter.formatNumber(this.getCharges() + this.getVat()));
                    paramValues.put("total", NumberFormatter.formatNumber(this.getTotal()));
                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_WPS_Template",
                            paramValues, this.getAttachments());

                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating wps file", false);
                }
                break;
            case BANK_TRANSFER:
                this.label = String.format("Transfer Money to %s's bank account", officeStaff.getName());
                try {
                    Attachment bankTransferFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateBankTransferFile(ansariInfo, Arrays.asList(accumulativeLog));
                    if (bankTransferFile != null) {
                        List<Attachment> attachments = new ArrayList<>();
                        attachments.add(bankTransferFile);
                        this.setAttachments(attachments);
                    }
                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating bank transfer file", false);
                }

                break;
            case LOCAL_TRANSFER:
                this.label = String.format("Transfer Money to %s for %s - %s",
                        this.getMonthlyPaymentRule().getMoneyExchangeName(), employeeTypes, officeStaff.getName());
                try {
                    Attachment localFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateLocalTransferFile(this.getMonthlyPaymentRule(), ansariInfo, new ArrayList<>(), Arrays.asList(accumulativeLog), new HashMap<Long, List<HousemaidPayrollLog>>());
                    if (localFile != null) {
                        List<Attachment> attachments = new ArrayList<>();
                        attachments.add(localFile);
                        this.setAttachments(attachments);
                    }

                    //send email to Ansari
                    Map<String, String> paramValues = new HashMap();
                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_Local_Exchange_Template",
                            paramValues, this.getAttachments());


                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating local transfer file", false);
                }
                break;
            case INTERNATIONAL_TRANSFER:
                this.label = String.format("Transfer Money to %s for %s - %s",
                        this.getMonthlyPaymentRule().getMoneyExchangeName(), employeeTypes, officeStaff.getName());
                try {
                    Attachment internationalFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                            generateInternationalTransferFile(this.getMonthlyPaymentRule(), ansariInfo, Arrays.asList(accumulativeLog));
                    if (internationalFile != null) {
                        List<Attachment> attachments = new ArrayList<>();
                        attachments.add(internationalFile);
                        this.setAttachments(attachments);
                    }

                    //send email to Ansari
                    Map<String, String> paramValues = new HashMap();
                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
                    paramValues.put("unique_payment_code", newUniquePaymentCode);

                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_International_Exchange_Template",
                            paramValues, this.getAttachments());

                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating international transfer file", false);
                }
                break;
        }

        accumulativeLog.setTotalSalary(originalAmount);
    }

    public void buildForHousemaidFinalSettlements(List<HousemaidPayrollLog> housemaidPayrollLogs) {
        HousemaidPayrollLogRepository housemaidPayrollLogRepository = Setup.getRepository(HousemaidPayrollLogRepository.class);
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        this.setDueOn(DateUtil.formatClientFullDate(this.getPaymentDate()));
        for (HousemaidPayrollLog log : housemaidPayrollLogs) {
            log.setPayrollAccountantTodo(this);
            log.setWillBeIncluded(true);
            this.amount += log.getTotalSalary();
            housemaidPayrollLogRepository.save(log);
        }
        this.charges = housemaidPayrollLogs.size() * Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ANSARI_CHARGES_RATE));
        this.vat = charges * 0.05d;


        String ansariInfo = " of " + monthlyPaymentRule.getEmployeesTypes()
                + (monthlyPaymentRule.getPayrollType() == null ? "" : " - " + monthlyPaymentRule.getPayrollType().getLabel())
                + " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());

        this.label = "Transfer Money to WPS for the final settlement of Housemaids";
        try {
            Attachment wpsFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                    generateWPSTransferFile(this.getMonthlyPaymentRule(), ansariInfo, housemaidPayrollLogs, new ArrayList<>(), new HashMap<>(),housemaidPayrollLogs.size(), 0, null, null );
            if (wpsFile != null) {
                List<Attachment> attachments = new ArrayList<>();
                attachments.add(wpsFile);
                this.setAttachments(attachments);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating wps transfer file", false);
        }
    }

    public void buildForCcSwitchedToMv(List<HousemaidPayrollLog> housemaidPayrollLogs) {
        HousemaidPayrollLogRepository housemaidPayrollLogRepository = Setup.getRepository(HousemaidPayrollLogRepository.class);
        this.setDueOn(DateUtil.formatClientFullDate(this.getPaymentDate()));
        for (HousemaidPayrollLog log : housemaidPayrollLogs) {
            log.setPayrollAccountantTodo(this);
            log.setWillBeIncluded(true);
            this.amount += log.getTotalSalary();
            housemaidPayrollLogRepository.save(log);
        }
        this.charges = housemaidPayrollLogs.size() * Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ANSARI_CHARGES_RATE));
        this.vat = charges * 0.05d;


        String ansariInfo = " of " + monthlyPaymentRule.getEmployeesTypes()
                + (monthlyPaymentRule.getPayrollType() == null ? "" : " - " + monthlyPaymentRule.getPayrollType().getLabel())
                + " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());

        this.label = "Transfer money to WPS for maids switching to MV in the month of " + DateUtil.formatFullMonthFullYear(monthlyPaymentRule.getPayrollMonth());
        try {
            Attachment wpsFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                    generateWPSTransferFile(this.getMonthlyPaymentRule(), ansariInfo, housemaidPayrollLogs, new ArrayList<>(), new HashMap<>(),housemaidPayrollLogs.size(), 0, null, null );
            if (wpsFile != null) {
                List<Attachment> attachments = new ArrayList<>();
                attachments.add(wpsFile);
                this.setAttachments(attachments);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating wps transfer file", false);
        }
    }

    public void fillPayrollLogInfo(OfficeStaffPayrollLog log) {
        if (log != null) {
            OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class).findOne(log.getOfficeStaff().getId());
            if (staff != null){

                TransferDestination transferDestination = staff.getSelectedTransferDestination();
                NewRequest visaNewRequest = staff.getVisaNewRequest();
                Date payrollMonth = log.getPayrollMonth();
                switch (PayrollAccountantTodoType.valueOf(this.getTaskName())){
                    case BANK_TRANSFER:
                        log.setEmployeeName(staff.getName());
                        if(transferDestination != null) {
                            log.setIban(transferDestination.getIban());
                            log.setAccountHolderName(transferDestination.getAccountHolderName());
                            log.setAccountNumber(transferDestination.getAccountNumber());
                            log.setBankName(transferDestination.getBankName());
                            log.setSwiftCode(transferDestination.getSwiftCode());
                            log.setBeneficiaryAddress(transferDestination.getFullAddress());
                            log.setFullNameEnglish(transferDestination.getName());
                            log.setFullNameArabic(transferDestination.getFullNameInArabic());
                            log.setDestinationOfTransfer(transferDestination.getDetails());
                            log.setMobileNumber(transferDestination.getPhoneNumber());
                        }
                        log.setCurrency(staff.getSalaryCurrency() != null ? staff.getSalaryCurrency().toString() : "");
                        break;
                    case INTERNATIONAL_TRANSFER:
                        log.setEmployeeName(staff.getName());
                        if(transferDestination != null) {
                            log.setFullNameEnglish(transferDestination.getName());
                            log.setFullNameArabic(transferDestination.getFullNameInArabic());
                            log.setDestinationOfTransfer(transferDestination.getDetails());
                            log.setMobileNumber(transferDestination.getPhoneNumber());
                        }
                        log.setCurrency(staff.getSalaryCurrency() != null ? staff.getSalaryCurrency().toString() : "");
                        break;
                    case LOCAL_TRANSFER:
                        String countryName = staff.getCountry() != null ? staff.getCountry().getName() : "";
                        log.setReceiverName(staff.getName());
                        log.setDestinationOfTransfer(countryName);
                        log.setMobileNumber(staff.getPhoneNumber());
                        break;
                    case WPS:
                        log.setRecordType("EDR");
                        countryName = staff.getCountry() != null ? staff.getCountry().getName() : "";
                        log.setEmployeeName(staff.getName());
                        log.setReceiverName(staff.getName());
                        log.setDestinationOfTransfer(countryName);
                        log.setMobileNumber(staff.getPhoneNumber());
                        if (visaNewRequest != null){
                            log.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
                            log.setAgentId(visaNewRequest.getAgentId());
                            log.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
                        }
                        java.util.Date payStartDate = new LocalDate(payrollMonth).withDayOfMonth(1).toDate();
                        java.util.Date payEndDate = new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate();
                        int daysInPeriod = DateUtil.getDaysBetween(payStartDate, payEndDate) + 1;
                        log.setPayStartDate(payStartDate);
                        log.setPayEndDate(payEndDate);
                        log.setDaysInPeriod(daysInPeriod);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public SalaryCurrency getCurrency() {
        return currency;
    }

    public void setCurrency(SalaryCurrency currency) {
        this.currency = currency;
    }

    public String getMoneyExchangeName() {
        if(this.getMonthlyPaymentRule() != null) return this.getMonthlyPaymentRule().getMoneyExchangeName();

        return Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MONEY_EXCHANGE_NAME);
    }

    @JsonIgnore
    public int countUnCompletedStaffTransfer() {
        if (officeStaffs.isEmpty()) return 0;
        return Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                .countUnCompletedStaffTransfer(this);
    }

    @JsonIgnore
    public int countUnCompletedHousemaidTransfer() {
        if (housemaids.isEmpty()) return 0;
        return Setup.getRepository(HousemaidPayrollLogRepository.class)
                .countUnCompletedHousemaidTransfer(this);
    }

    public Boolean getSingleOfficeStaff() {
        return singleOfficeStaff;
    }

    public void setSingleOfficeStaff(Boolean singleOfficeStaff) {
        this.singleOfficeStaff = singleOfficeStaff;
    }

    public Boolean getSingleHousemaid() {
        return singleHousemaid;
    }

    public void setSingleHousemaid(Boolean singleHousemaid) {
        this.singleHousemaid = singleHousemaid;
    }

    @Column(columnDefinition = "boolean default false")
    private Boolean isActionTaken = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean firstActionTaken = false;

    public Boolean getActionTaken() {
        return isActionTaken;
    }

    public void setActionTaken(Boolean actionTaken) {
        isActionTaken = actionTaken;
    }

    public Boolean getFirstActionTaken() {
        return firstActionTaken;
    }

    public void setFirstActionTaken(Boolean firstActionTaken) {
        this.firstActionTaken = firstActionTaken;
    }

    @BeforeInsert
    @BeforeUpdate
    private void updateItemType() {
        if (this.getTaskName() == null || this.getTaskName().trim().equals(""))
            return;
        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(this.getTaskName());
        PicklistItem result = null;
        switch (todoType) {
            case WPS:
                result = PicklistHelper.getItem("accountant_todo_types", "wps_transfer");
                break;
            case BANK_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "office_staff_salary");
                break;
            case LOCAL_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "salary_local_transfer");
                break;
            case PENSION_AUTHORITY:
                result = PicklistHelper.getItem("accountant_todo_types", "pay_pension");
                break;
            case INTERNATIONAL_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "international_salaries_transfer");
                break;
            case CLIENT_REFUND_BANK_TRANSFER:
            case CLIENT_REFUND_MONEY_TRANSFER:
                result = PicklistHelper.getItem("accountant_todo_types", "refund_client");
                break;
        }
        this.setItemType(result);
    }

    public String getMonth() {
        return this.payrollMonth != null ? DateUtil.formatMonth(payrollMonth) : "";
    }

    public Double getTotalInAED() {
        return totalInAED;
    }

    public void setTotalInAED(Double totalInAED) {
        this.totalInAED = totalInAED;
    }

    public Integer getTotalIncludedStaffs() {
        if(totalIncludedStaffs == null)
            totalIncludedStaffs = 0;
        return totalIncludedStaffs;
    }

    public void setTotalIncludedStaffs(Integer totalIncludedStaffs) {
        this.totalIncludedStaffs = totalIncludedStaffs;
    }

    public Integer getTotalIncludedMaids() {
        if(totalIncludedMaids == null)
            totalIncludedMaids =0;
        return totalIncludedMaids;
    }

    public void setTotalIncludedMaids(Integer totalIncludedMaids) {
        this.totalIncludedMaids = totalIncludedMaids;
    }

    public Boolean getOpenedToAccountants() {
        return openedToAccountants;
    }

    public void setOpenedToAccountants(Boolean openedToAccountants) {
        this.openedToAccountants = openedToAccountants;
    }

    public java.util.Date getOpenedToAccountantsDate() {
        return openedToAccountantsDate;
    }

    public void setOpenedToAccountantsDate(java.util.Date openedToAccountantsDate) {
        this.openedToAccountantsDate = openedToAccountantsDate;
    }

    public Boolean getHousemaidsPayslipsSent() {
        return housemaidsPayslipsSent;
    }

    public void setHousemaidsPayslipsSent(Boolean housemaidsPayslipsSent) {
        this.housemaidsPayslipsSent = housemaidsPayslipsSent;
    }

    public Boolean getBankTransferReportIsSent() {
        return bankTransferReportIsSent;
    }

    public void setBankTransferReportIsSent(Boolean bankTransferReportIsSent) {
        this.bankTransferReportIsSent = bankTransferReportIsSent;
    }

    public String getUniquePaymentCode() {
        return uniquePaymentCode;
    }

    public void setUniquePaymentCode(String uniquePaymentCode) {
        this.uniquePaymentCode = uniquePaymentCode;
    }
}
