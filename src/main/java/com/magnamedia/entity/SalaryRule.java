package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import org.hibernate.envers.NotAudited;
import org.joda.time.LocalDate;

import javax.persistence.*;
import java.lang.reflect.Field;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on May 21, 2019
 * Jirra ACC-675
 */
@Entity @org.hibernate.envers.Audited
public class SalaryRule extends BaseEntity{

    @Column
    private String name;
    
    @Column(nullable = false, columnDefinition = "boolean default true")
    private Boolean isActive = true;
    
    @Column(columnDefinition="TEXT")
    private String filter;
    
    @Transient
    private SelectFilter selectFilter;
    
    @Column(columnDefinition = "TEXT")
    private String filterPlainText;
    
    @NotAudited
    @OneToMany(
            cascade = CascadeType.ALL,
            mappedBy = "salaryRule",
            fetch = FetchType.LAZY)
    private List<SalaryRuleDetails> salaryRuleDetailses;

    @Column(columnDefinition = "boolean default false")
    private Boolean holidayAutoCalculated = false;

    @Column(columnDefinition = "double default 0")
    private Double holidayMaximumValue = 0.0;

    @Column(columnDefinition = "int default 0")
    private Integer holidayPriority = 0;

    @Column(columnDefinition = "boolean default false")
    private Boolean cashAdvancedAutoCalculated = false;

    @Column(columnDefinition = "double default 0")
    private Double cashAdvancedMaximumValue = 0.0;

    @Column(columnDefinition = "int default 0")
    private Integer cashAdvancedPriority = 0;

    @Column(columnDefinition = "boolean default false")
    private Boolean overTimeAutoCalculated = false;

    @Column(columnDefinition = "double default 0")
    private Double overTimeMaximumValue = 0.0;

    @Column(columnDefinition = "int default 0")
    private Integer overTimePriority = 0;

    //PAY-495
    @Column(columnDefinition = "double default 0")
    private Double overTimeToAddLater = 0.0;

    @Lob
    @Column
    private String description;

        
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public SelectFilter getSelectFilter() {
        if (this.filter != null && (this.selectFilter == null || this.selectFilter.isEmpty()))
            this.selectFilter = SelectFilter.load(this.filter);
        return selectFilter;
    }

    public void setSelectFilter(SelectFilter selectFilter) {
        this.selectFilter = selectFilter;
    }

    public String getFilterPlainText() {
        return filterPlainText;
    }

    public void setFilterPlainText(String filterPlainText) {
        this.filterPlainText = filterPlainText;
    }

    public List<SalaryRuleDetails> getSalaryRuleDetailses() {
        return salaryRuleDetailses;
    }

    public void setSalaryRuleDetailses(List<SalaryRuleDetails> salaryRuleDetailses) {
        this.salaryRuleDetailses = salaryRuleDetailses;
    }

    public Boolean getHolidayAutoCalculated() {
        return holidayAutoCalculated;
    }

    public void setHolidayAutoCalculated(Boolean holidayAutoCalculated) {
        this.holidayAutoCalculated = holidayAutoCalculated;
    }

    public Double getHolidayMaximumValue() {
        return holidayMaximumValue;
    }

    public void setHolidayMaximumValue(Double holidayMaximumValue) {
        this.holidayMaximumValue = holidayMaximumValue;
    }

    public Integer getHolidayPriority() {
        return holidayPriority;
    }

    public void setHolidayPriority(Integer holidayPriority) {
        this.holidayPriority = holidayPriority;
    }

    public Boolean getCashAdvancedAutoCalculated() {
        return cashAdvancedAutoCalculated;
    }

    public void setCashAdvancedAutoCalculated(Boolean cashAdvancedAutoCalculated) {
        this.cashAdvancedAutoCalculated = cashAdvancedAutoCalculated;
    }

    public Double getCashAdvancedMaximumValue() {
        return cashAdvancedMaximumValue;
    }

    public void setCashAdvancedMaximumValue(Double cashAdvancedMaximumValue) {
        this.cashAdvancedMaximumValue = cashAdvancedMaximumValue;
    }

    public Integer getCashAdvancedPriority() {
        return cashAdvancedPriority;
    }

    public void setCashAdvancedPriority(Integer cashAdvancedPriority) {
        this.cashAdvancedPriority = cashAdvancedPriority;
    }

    public Boolean getOverTimeAutoCalculated() {
        return overTimeAutoCalculated;
    }

    public void setOverTimeAutoCalculated(Boolean overTimeAutoCalculated) {
        this.overTimeAutoCalculated = overTimeAutoCalculated;
    }

    public Double getOverTimeMaximumValue() {
        return overTimeMaximumValue;
    }

    public void setOverTimeMaximumValue(Double overTimeMaximumValue) {
        this.overTimeMaximumValue = overTimeMaximumValue;
    }

    public Integer getOverTimePriority() {
        return overTimePriority;
    }

    public void setOverTimePriority(Integer overTimePriority) {
        this.overTimePriority = overTimePriority;
    }

    public Double getOverTimeToAddLater() {
        return overTimeToAddLater;
    }

    public void setOverTimeToAddLater(Double overTimeToAddLater) {
        this.overTimeToAddLater = overTimeToAddLater;
    }

    @JsonIgnore
    public Boolean hasAutoCalculatedFields (){
        return this.holidayAutoCalculated || this.cashAdvancedAutoCalculated || this.overTimeAutoCalculated;
    }

    @JsonIgnore
    public Map<Integer, String> getPrioritizedFields (){
        Map<Integer, String> result = new HashMap<>();
        if (holidayAutoCalculated && holidayPriority != null && holidayPriority > 0)
            result.put(holidayPriority, "Holiday");
        if (cashAdvancedAutoCalculated && cashAdvancedPriority != null && cashAdvancedPriority > 0)
            result.put(cashAdvancedPriority, "CashAdvanced");
        if (overTimeAutoCalculated && overTimePriority != null && overTimePriority > 0)
            result.put(overTimePriority, "OverTime");

        return result;
    }


    public void apply(){
        List<Housemaid> housemaids =  this.getHousemaidsForApply();
        HousemaidRepository housemaidRepository =
                Setup.getRepository(HousemaidRepository.class);
        for (Housemaid h : housemaids){
            // this condition apply on select query level
//            if (h.getBasicSalary() == null || h.getBasicSalary() == 0D){
                try {

                    for (SalaryRuleDetails salaryRuleDetails : this.salaryRuleDetailses) {
                        try {
                            Field declaredField =
                                    Housemaid.class.getDeclaredField(
                                            salaryRuleDetails.getSalaryComponent().getName());
                            declaredField.setAccessible(true);
                            declaredField.set(h, salaryRuleDetails.getValue());
                        } catch (NoSuchFieldException
                                | SecurityException
                                | IllegalArgumentException
                                | IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    }
                    // PAY-299
                    if (h.getIsMaidsAt()) {
                        MaidsAtCandidate candidate = Setup.getRepository(MaidsAtCandidateRepository.class)
                                .findTopByHousemaid(h);
                        if (candidate != null && candidate.getMaidSalary() != null) {
                            h.setMonthlyLoan(candidate.getMaidSalary() - h.getPrimarySalary());
                        }
                    }

                    if(hasAutoCalculatedFields()){
                        Map<String, String> salesJobParameter;
                        ObjectMapper mapper = new ObjectMapper();
                        salesJobParameter = mapper.readValue(Setup.getParameter(Setup.getModule("sales"),
                                PayrollManagementModule.PARAMETER_SALES_APPS_GENERIC_JOB_CONFIG), HashMap.class);
                        String hoursS = salesJobParameter == null ? "" :
                                salesJobParameter.get("disable_update_prev_steps_after_hours");
                        Integer hours = hoursS == null || hoursS.isEmpty() ? 0 : Integer.valueOf(hoursS);

                        Contract activeContract = h.getActiveContract();
                        if(activeContract != null && DateUtil.getHoursBetween(activeContract.getCreationDate(), new java.util.Date()) > hours){
                            Map<Integer, String> prioritizedFields = getPrioritizedFields();
                            Double workerSalary = activeContract.getWorkerSalary() == null ? 0.0 : activeContract.getWorkerSalary();
                            for(int i = 1; i <= 3; i++){
                                String field = prioritizedFields.getOrDefault(i, "none");
                                Double fieldAmount = 0.0;
                                switch (field) {
                                    case "Holiday":
                                        fieldAmount = workerSalary
                                                - (h.getAirfareFee() == null ? 0.0 : h.getAirfareFee())
                                                - (h.getPrimarySalary() == null ? 0.0 : h.getPrimarySalary())
                                                - (h.getOverTime() == null ? 0.0 : h.getOverTime())
                                                - (h.getMonthlyLoan() == null ? 0.0 : h.getMonthlyLoan());
                                        if (this.holidayMaximumValue != null)
                                            h.setHoliday(Math.min(this.holidayMaximumValue, fieldAmount));
                                        else
                                            h.setHoliday(fieldAmount);
                                        break;
                                    case "CashAdvanced":
                                        fieldAmount = workerSalary
                                                - (h.getAirfareFee() == null ? 0.0 : h.getAirfareFee())
                                                - (h.getPrimarySalary() == null ? 0.0 : h.getPrimarySalary())
                                                - (h.getOverTime() == null ? 0.0 : h.getOverTime())
                                                - (h.getHoliday() == null ? 0.0 : h.getHoliday());
                                        if (this.cashAdvancedMaximumValue != null)
                                            h.setMonthlyLoan(Math.min(this.cashAdvancedMaximumValue, fieldAmount));
                                        else
                                            h.setMonthlyLoan(fieldAmount);
                                        break;
                                    case "OverTime":
                                        fieldAmount = workerSalary
                                                - (h.getAirfareFee() == null ? 0.0 : h.getAirfareFee())
                                                - (h.getPrimarySalary() == null ? 0.0 : h.getPrimarySalary())
                                                - (h.getHoliday() == null ? 0.0 : h.getHoliday())
                                                - (h.getMonthlyLoan() == null ? 0.0 : h.getMonthlyLoan());
                                        if (this.overTimeMaximumValue != null)
                                            h.setOverTime(Math.min(this.overTimeMaximumValue, fieldAmount));
                                        else
                                            h.setOverTime(fieldAmount);
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }else // skip and don't update
                            continue;
                    }

                    if(h.getUpdateSalaryComponents() != null && h.getUpdateSalaryComponents()) {
                        Double primarySalary = null;
                        Double otherAllowance = null;
                        Double renewalRaiseAmount = null;
                        Double monthlyLoan = 0.0;
                        Double overTime = h.getOverTime() != null ? h.getOverTime() : 0.0;
                        Double basicSalary = h.caculateBasicSalary();
                        RenewRequest renewRequest = Setup.getRepository(RenewVisaRequestRepository.class).findTopByHousemaidOrderByIdDesc(h);
                        if(renewRequest != null) {
                            primarySalary = renewRequest.getPrimarySalary();
                            otherAllowance = renewRequest.getOtherAllowance();
                            renewalRaiseAmount = renewRequest.getRenewalRaiseAmount();
                        }else if (h.getVisaNewRequest() != null){
                            NewRequest visaNewRequest = Setup.getRepository(NewVisaRequestRepository.class).findOne(h.getVisaNewRequest().getId());
                            primarySalary = visaNewRequest.getPrimarySalary();
                            otherAllowance = visaNewRequest.getOtherAllowance();
                        }
                        if(primarySalary != null && otherAllowance != null && basicSalary > 0) {
                            h.setPrimarySalary(primarySalary);
                            h.setHoliday(otherAllowance);
                            h.setAirfareFee(0.0);
                            monthlyLoan = basicSalary - primarySalary - overTime - otherAllowance;

                            //throw exception if monthly loan became less than Zero
//                            if(monthlyLoan < 0.0)
//                                throw new BusinessException("Exception: housemaid: #" + h.getId() + ", monthly loan became less than Zero. " + monthlyLoan);
                            h.setMonthlyLoan(monthlyLoan);
                        }

                        //check if we need to add the raise to the Cash Advanced amount
                        if (renewalRaiseAmount != null && renewalRaiseAmount > 0.0){
                            h.setMonthlyLoan(monthlyLoan + renewalRaiseAmount);
                        }

                        //set the flag to false again
                        h.setUpdateSalaryComponents(false);
                    }

                    Double basicSalary = h.caculateBasicSalary();
                    if (!Setup.getApplicationContext().getBean(PayrollGroupService.class).validateComponent(basicSalary, h)){
                        Logger.getLogger(SalaryRule.class.getName()).log(Level.SEVERE, "validateComponent return false Housemaid #" + h.getId());
                        continue;
                    }

                    housemaidRepository.save(h);

                }catch (Exception e) {
                    DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception While applying salary rule for maid #" + h.getId(), false);
                }
//            }
        }
    }
    
    public void reSet(){
        List<Housemaid> housemaids =  this.getHousemaids();
        HousemaidRepository housemaidRepository =
                Setup.getRepository(HousemaidRepository.class);
        for (Housemaid h : housemaids){
            for (SalaryRuleDetails salaryRuleDetails: this.salaryRuleDetailses){
                try{
                    Field declaredField =
                            Housemaid.class.getDeclaredField(
                                    salaryRuleDetails.getSalaryComponent().getName());
                    declaredField.setAccessible(true);
                    declaredField.set(h, 0D);
                } catch (NoSuchFieldException 
                        | SecurityException
                        | IllegalArgumentException 
                        | IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            housemaidRepository.save(h);
        }
    }
    
    @JsonIgnore
    public List<Housemaid> getHousemaids(){
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if (this.filter != null)
            query.filterBy(SelectFilter.load(this.filter));
        List<Housemaid> housemaids = query.execute();
        return housemaids!=null ? housemaids : new ArrayList<Housemaid>();
    }

    @JsonIgnore
    public List<Housemaid> getHousemaidsForApply(){
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if (this.filter != null)
            query.filterBy(SelectFilter.load(this.filter));
        query.filterBy(new SelectFilter("basicSalary", "IS NULL", null).or("basicSalary", "=", 0.0));
        List<Housemaid> housemaids = query.execute();
        return housemaids!=null ? housemaids : new ArrayList<Housemaid>();
    }

    @JsonIgnore
    public List<Housemaid> getHousemaids(List<Long> targetHousemaids){
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if (this.filter != null)
            query.filterBy(SelectFilter.load(this.filter));

        if(targetHousemaids != null && targetHousemaids.size() > 0)
            query.filterBy("id", "IN", targetHousemaids);

        List<Housemaid> housemaids = query.execute();
        return housemaids!=null ? housemaids : new ArrayList<Housemaid>();
    }

    @JsonIgnore
    public List<Housemaid> getHousemaidsWithStartDateBefore(Date beforeDate){
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if (this.filter != null)
            query.filterBy(SelectFilter.load(this.filter));
        query.filterBy("startDate", "<=", beforeDate);
        query.filterBy("overTimeAddedBefore", "=", false);
        List<Housemaid> housemaids = query.execute();
        return housemaids!=null ? housemaids : new ArrayList<Housemaid>();
    }

    @JsonIgnore
    public Double getTotalSalaryFromComponents(boolean withOverTimeAfterSixMonth){
        Double total = 0.0;
        if (salaryRuleDetailses != null)
            for (SalaryRuleDetails details : salaryRuleDetailses) {
                if (!"accommodationSalary".equalsIgnoreCase(details.getSalaryComponent().getCode()))
                    total += details.getValue() != null ? details.getValue() : 0.0;
            }
        if(withOverTimeAfterSixMonth)
            total += overTimeToAddLater == null ? 0.0 : overTimeToAddLater;
        return total;
    }
    
    @BeforeInsert
    @BeforeUpdate
    public void validate(){
        List<SalaryRuleDetails> salaryRuleDetailses1 =
                new ArrayList<SalaryRuleDetails>(this.salaryRuleDetailses);
        this.salaryRuleDetailses = new ArrayList<SalaryRuleDetails>();
        for (SalaryRuleDetails salaryRuleDetails : salaryRuleDetailses1){
            if (salaryRuleDetails.getId() != null){
                double value = salaryRuleDetails.getValue();
                salaryRuleDetails =
                        Setup.getRepository(SalaryRuleDetailsRepository.class)
                                .getOne(salaryRuleDetails.getId());
                salaryRuleDetails.setValue(value);
            }
            salaryRuleDetails.validate();
            salaryRuleDetails.setSalaryRule(this);
            this.salaryRuleDetailses.add(salaryRuleDetails);
        }
        this.filterPlainText =
                this.filter != null ?
                SelectFilter.load(this.filter).toString() : "";
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
