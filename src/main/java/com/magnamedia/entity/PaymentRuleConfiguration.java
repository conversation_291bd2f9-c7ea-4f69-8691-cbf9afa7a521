package com.magnamedia.entity;

import com.magnamedia.module.type.*;

import javax.persistence.Entity;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/10/2020
 **/
@Entity @org.hibernate.envers.Audited
public class PaymentRuleConfiguration extends AbstractPaymentRule {

    public PaymentRuleConfiguration(AbstractPaymentRule abstractPaymentRule) {
        super(abstractPaymentRule);
    }

    public PaymentRuleConfiguration() {
    }

    public PaymentRuleConfiguration(List<PaymentRuleEmployeeType> employeeTypeList, List<MaidType> housemaidTypeList, List<PaymentRuleMaidStatus> housemaidStatusList,
                                    MolType molType, String moneyExchangeName, PaymentRulePaymentMethod paymentMethod, Integer dayOfMonth, int daysBeforeLock,
                                    Boolean currentMonth, Boolean afterHoliday, PayrollType payrollType) {
        if(employeeTypeList != null) {
            this.employeeTypeList = new ArrayList<>(employeeTypeList);
        }
        if (housemaidTypeList != null) {
            this.housemaidTypeList = new ArrayList<>(housemaidTypeList);
        }
        if(housemaidStatusList != null) {
            this.housemaidStatusList = new ArrayList<>(housemaidStatusList);
        }
        this.molType = molType;
        this.paymentMethod = paymentMethod;
        this.moneyExchangeName = moneyExchangeName;
        this.dayOfMonth = dayOfMonth;
        this.daysBeforeLock = daysBeforeLock;
        this.currentMonth = currentMonth;
        this.afterHoliday = afterHoliday;
        this.payrollType = payrollType;
    }
}
