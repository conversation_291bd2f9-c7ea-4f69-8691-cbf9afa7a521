package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.projection.CheckListNoPaymentDetailsProjection;
import com.magnamedia.entity.projection.CheckListNoPaymentProjection;
import com.magnamedia.extra.payroll.init.HousemaidContractInfoProjection;
import com.magnamedia.extra.payroll.init.HousemaidFieldProjection;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.HousemaidType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ContractRepository extends BaseRepository<Contract> {

    //Jirra Jirra ACC-841
    List<Contract> findByHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    Contract findFirstOneByHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    //Jirra ACC-1087
    List<Contract> findByClientOrderByCreationDateDesc(Client client);

    public List<Contract> findByHousemaidAndStatus(Housemaid housemaid, ContractStatus status);

    //Jirra ACC-837
    //Jirra ACC-1241
    Contract findFirstOneByClientAndStatusInOrderByCreationDateDesc(Client client, ContractStatus[] statuses);

    @Query("SELECT c "
            + "FROM Contract c "
            + "WHERE "
            + "(SELECT count(l) from LiveInOutLog l where l.reason = com.magnamedia.module.type.LiveInOutLogReason.START_OF_CONTRACT and l.contract=c)>1")
    public List<Contract> findInvalidMoreThanOneLog();

//    @Query("SELECT c from Contract c where "
//            + "(SELECT l.contract from LiveInOutLog l where l.reason = com.magnamedia.module.type.LiveInOutLogReason.START_OF_CONTRACT and l.contract=c group by l.contract) IS NULL "
//            + "and c.status<> com.magnamedia.module.type.ContractStatus.PLANNED_RENEWAL")
//    public List<Contract> findInvalidNoStartLog();

    @Query("SELECT c "
            + "FROM Contract c "
            + "WHERE c.status =com.magnamedia.module.type.ContractStatus.ACTIVE AND c.creationDate IS NULL")
    public List<Contract> findActiveContractsWithNullCreationDate();

    @Query("SELECT coalesce(max(ch.id), 0) FROM Contract ch")
    Long getMaxId();

    public List<Contract> findByHousemaid(Housemaid housemaid);

    //jirra ACC-633
    public List<Contract> findByStatusAndVatRecordGenerated(
            ContractStatus status, Boolean vatRecordGenerated);

    //jirra ACC-633
    public List<Contract> findByStatusAndVatRecordGeneratedAndCreationDateLessThan(
            ContractStatus status, Boolean vatRecordGenerated, Date creationDate);

    //jirra ACC-1135
    public List<Contract> findByStatusInOrScheduledDateOfTerminationNotNull(List<ContractStatus> statuses);

    //Jirra ACC-1241
    @Query("SELECT C FROM Contract C "
            + "INNER JOIN Attachment A ON (A.ownerId = C.id AND A.ownerType = 'Contract' AND A.tag LIKE ?1) "
            + "where C.discountCode is not null AND C.client is not null AND C.refundSentToExpensify = false")
    public List<Contract> findByDiscountCodeNotNullAndClientNotNullAndAttachmentsTagEquals(String tag);

    //Jirra ACC-1435
    @Query(value = "SELECT cont FROM Contract cont WHERE cont.client = :client AND cont.status= 'ACTIVE'")
    public List<Contract> findActiveContractByClient(@Param("client") Client c);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidFieldProjection(housemaid.id, client.name) from Contract contract join contract.client client " +
            "join contract.housemaid housemaid where housemaid.id in ?1 and contract.status='ACTIVE'")
    List<HousemaidFieldProjection> findClientNames(List<Long> housemaids);

    @Query("select client.name from Contract contract join contract.client client " +
            "where contract.housemaid = ?1 and contract.status='ACTIVE' group by contract.housemaid")
    String findClientNameByHousemaid(Housemaid housemaid);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidContractInfoProjection(housemaid.id, contract.id, contractType.code) from Contract contract left join contract.contractProspectType contractType " +
            "join contract.housemaid housemaid where housemaid.id in ?1 and contract.status='ACTIVE'")
    List<HousemaidContractInfoProjection> findContractInfo(List<Long> housemaids);

    @Query("select contract.id from Contract contract " +
            "where contract.housemaid = ?1 and contract.status='ACTIVE' group by contract.housemaid")
    String findContractIdByHousemaid(Housemaid housemaid);

    @Query("select contractType.code from Contract contract left join contract.contractProspectType contractType " +
            "where contract.housemaid = ?1 and contract.status='ACTIVE' group by contract.housemaid")
    String findContractTypeByHousemaid(Housemaid housemaid);

    @Query ("SELECT CONCAT('No DD signed for ',?1) as status, 'NO_DD_SIGNED' as type, COUNT(c) as numberOfClients FROM Contract c inner join c.housemaid h where c.status = ?2 " +
                    "and h.id IN ?3 and h.housemaidType <> ?4")
    List<CheckListNoPaymentProjection> getNoDDSigned(String month, ContractStatus status, List<Long> targetHousemaids, HousemaidType housemaidType);

    @Query("SELECT h.name as maidName, cl.name as clientName FROM Contract c inner join c.housemaid h inner join c.client cl where c.status = ?1 " +
            "and h.id IN ?2 and h.housemaidType <> ?3 order by h.name")
    List<CheckListNoPaymentDetailsProjection> getNoDDSignedDetails(ContractStatus active, List<Long> targetForDDNotSignedHousemaidsIds, HousemaidType maidVisa);

    Contract findTop1ByClientOrderByCreationDateDesc(Client client);

}
