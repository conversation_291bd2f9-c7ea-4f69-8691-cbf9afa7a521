package com.magnamedia.repository;

import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.entity.OfficeStaff;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 16, 2017
 */
@Repository
public interface NewVisaRequestRepository extends VisaRequestRepository<NewRequest> {

    NewRequest findFirstByOfficeStaffOrderByCreationDateDesc(OfficeStaff officeStaff);

    NewRequest findFirstByHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    NewRequest findFirstByEmployeeUniqueIdAndHousemaidIsNotNullOrderByCreationDateDesc(String employeeUniqueId);
}
