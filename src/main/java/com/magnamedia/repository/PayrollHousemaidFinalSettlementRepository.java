package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PayrollHousemaidFinalSettlement;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * Created on 1/12/21
 */
@Repository
public interface PayrollHousemaidFinalSettlementRepository extends BaseRepository<PayrollHousemaidFinalSettlement> {
    boolean existsByHousemaid(Housemaid housemaid);
    PayrollHousemaidFinalSettlement findByHousemaidAndRevised(Housemaid housemaid, boolean revised);
    PayrollHousemaidFinalSettlement findTop1ByHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    PayrollHousemaidFinalSettlement findTopByHousemaidOrderByIdDesc(Housemaid housemaid);

    List<PayrollHousemaidFinalSettlement> findByReadyForPayTrueAndRelatedToTodoFalseOrderByHousemaid_Name();

}
