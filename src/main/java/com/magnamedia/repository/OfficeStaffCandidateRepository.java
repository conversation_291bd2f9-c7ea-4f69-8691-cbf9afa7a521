package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OfficeStaffCandidate;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 5/9/2020
 **/
@Repository
public interface OfficeStaffCandidateRepository extends BaseRepository<OfficeStaffCandidate> {
    public OfficeStaffCandidate findByApplicantId(String applicantId);

    @Query("select (count(o) > 0) from OfficeStaffCandidate o where o.rehiredId = ?1 and o.rehired = ?2 and o.rehireProcessCompleted = ?3")
    public Boolean existsByRehiredAndRehiredIdAnd(Long rehiredId, Boolean rehired, Boolean rehireProcessCompleted);
}
