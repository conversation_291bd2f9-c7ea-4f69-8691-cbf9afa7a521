/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AbstractPayrollManagerNote;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.entity.projection.CheckListAdditionByAdditionReasonProjection;
import com.magnamedia.entity.projection.CheckListAdditionProjection;
import com.magnamedia.entity.projection.payrollAudit.CumulativeSalaryAdditionsOrDeductionsProjection;
import com.magnamedia.entity.projection.payrollAudit.CumulativeSalaryChangesProjection;
import com.magnamedia.entity.projection.payrollAudit.PayrollManagerNoteProjection;
import com.magnamedia.entity.ScheduledAnnualVacation;
import com.magnamedia.entity.projection.v2.MaidsWereOnVacationProjection;
import com.magnamedia.extra.payroll.init.HousemaidAmountProjection;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Abbas <<EMAIL>>
 */
@Repository
public interface PayrollManagerNoteRepository extends BaseRepository<PayrollManagerNote> {
    List<PayrollManagerNote> findByHousemaid(Housemaid housemaid);

    @Query("select p from PayrollManagerNote p where p.housemaid = ?1 and (p.noteType <> ?2 or p.amount > 0.0)")
    List<PayrollManagerNote> findAllByHousemaidWithoutZeroDeductions(Housemaid housemaid, AbstractPayrollManagerNote.ManagerNoteType noteType);

    List<PayrollManagerNote> findByOfficeStaff(OfficeStaff officeStaff);

    List<PayrollManagerNoteProjection> findByOfficeStaffAndNoteTypeAndNoteDateLessThanEqualAndIdNotOrderByNoteDateDesc(OfficeStaff officeStaff, AbstractPayrollManagerNote.ManagerNoteType noteType, Date noDate, long id);

    PayrollManagerNote findTopByOfficeStaffAndNoteTypeAndNoteDateGreaterThanEqualAndNoteDateLessThanAndAdditionReason(OfficeStaff officeStaff, AbstractPayrollManagerNote.ManagerNoteType noteType, Date fromDate, Date toDate, PicklistItem additionReason);

    @Query("select p from PayrollManagerNote p where p.officeStaff = ?1 and p.noteType = ?2 and p.noteDate >= ?3 and p.noteDate < ?4 and p.additionReason = ?5 and p.noteReasone like concat('%', ?6, '%') order by p.id desc")
    List <PayrollManagerNote> findTopByOfficeStaffAndNoteTypeAndNoteDateAndAdditionReasonAndNoteReasone(OfficeStaff officeStaff, AbstractPayrollManagerNote.ManagerNoteType noteType, Date fromDate, Date toDate, PicklistItem additionReason, String noteReason);

    //Jirra ACC-1085
    List<PayrollManagerNote> findByNotFinal(boolean notFinal);

    List<PayrollManagerNote> findByNotFinalAndHousemaid(boolean notFinal, Housemaid housemaid);

    List<PayrollManagerNote> deleteByNotFinalAndHousemaidId(boolean notFinal, Long housemaid);

    @Modifying
    @Query("UPDATE PayrollManagerNote p SET p.notFinal = false WHERE p.housemaid = ?1 and p.notFinal = true")
    int updateNotFinalNotesByHousemaid(Housemaid housemaid);

    //Jirra ACC-1561
    List<PayrollManagerNote> findByHousemaidAndNoteDateAndAdditionReasonIn(Housemaid housemaid, Date noteDate, List<PicklistItem> additionReason);

    @Query("select p  " +
            "   from PayrollManagerNote p join p.officeStaff o " +
            "   where p.officeStaff = ?1 and p.confirmedAmountByAuditor= false and (p.amount/p.lastSalary)*100 > ?2 and cast(p.noteDate as date) >= ?3 and cast(p.noteDate as date) < ?4 and p.noteType = ?5 ")
    List<PayrollManagerNote> findByNoteTypeMarginPercentage(OfficeStaff officeStaff,
                                                            double percentage,
                                                            Date previousMonthLockDate,
                                                            Date currentMonthLockDate,
                                                            AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("select count(p)  " +
            "   from PayrollManagerNote p join p.officeStaff o " +
            "   where p.officeStaff = ?1 and p.confirmedAmountByAuditor= false and (p.amount/p.lastSalary)*100 > ?2 and cast(p.noteDate as date) >= ?3 and cast(p.noteDate as date) < ?4 and p.noteType = ?5 ")
    Integer findCountByNoteTypeMarginPercentage(OfficeStaff officeStaff,
                                                double percentage,
                                                Date previousMonthLockDate,
                                                Date currentMonthLockDate,
                                                AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("select p from PayrollManagerNote p join p.officeStaff o " +
            "where o = ?1 and p.confirmedRepeatedByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 order by p.noteDate desc ")
    List<PayrollManagerNote> findByNoteTypeRepetitiveInMonths(OfficeStaff officeStaff,
                                                              Date previousMonthLockDate,
                                                              Date currentMonthLockDate,
                                                              AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("select 1 as numOfficeStaff from PayrollManagerNote p join p.officeStaff o " +
            "where o = ?1 and p.confirmedRepeatedByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 " +
            "group by o.id having count(o.id) > 1")
    Integer findCountByNoteTypeRepetitiveInMonths(OfficeStaff officeStaff,
                                                  Date previousMonthLockDate,
                                                  Date currentMonthLockDate,
                                                  AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("SELECT COUNT(p) FROM PayrollManagerNote p WHERE p.housemaid IN ?1 AND p.noteDate >= ?2 AND p.noteDate < ?3 AND p.noteType = ?4 AND p.additionReason = ?5 AND p.confirmedAmountByAuditor = false")
    Integer countByNotConfirmedGoogleReviews(List<Housemaid> housemaidList, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReason);

    @Query("SELECT COUNT(p) FROM PayrollManagerNote p WHERE p.housemaid IN ?1 AND p.noteDate >= ?2 AND p.noteDate < ?3 AND p.noteType = ?4 AND p.additionReason = ?5")
    Integer countAllGoogleReviews(List<Housemaid> housemaidList, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReason);

    /**
     * <AUTHOR> Qazzaz
     * @reason PAY-125
     * start
     */
    @Query("select h from PayrollManagerNote p join p.housemaid h inner join p.additionReason r " +
            "where h IN ?1 and p.confirmedRepeatedByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 and r.code NOT IN ?5 " +
            "group by h having count(p.id) > 1")
    List<Housemaid> findHousemaidsByNoteTypeRepetitiveAdditionsInMonths(List<Housemaid> housemaid,
                                                                        Date previousMonthLockDate,
                                                                        Date currentMonthLockDate,
                                                                        AbstractPayrollManagerNote.ManagerNoteType noteType,
                                                                        List<String> unwantedAdditionReasons
    );

    @Query("select p from PayrollManagerNote p join p.housemaid h inner join p.additionReason r " +
            "where h = ?1 and p.confirmedRepeatedByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 and r.code NOT IN ?5 order By p.noteDate desc"
    )
    List<PayrollManagerNote> findNotesByNoteTypeRepetitiveAdditionsInMonths(Housemaid housemaid,
                                                                            Date previousMonthLockDate,
                                                                            Date currentMonthLockDate,
                                                                            AbstractPayrollManagerNote.ManagerNoteType noteType,
                                                                            List<String> unwantedAdditionReasons
    );

    /*
     * end
     */
    @Query(nativeQuery = true,
            value = "select ar.NAME as additionType," +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2 THEN p.Amount " +
                    "              else 0 " +
                    "         end) as currentMonthAmount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3 THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4 THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2 THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3 THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4 THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount " +
                    "from PAYROLLMANAGERNOTES p " +
                    "inner join PICKLISTS_ITEMS ar on ar.ID = p.ADDITION_REASON_ID " +
                    "where p.HOUSEMAID_ID is not null and p.HOUSEMAID_ID in ?1 and p.NOTE_TYPE = 'ADDITION' and p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?2 " +
                    "GROUP BY ar.NAME")
    List<CumulativeSalaryAdditionsOrDeductionsProjection> getHousemaidCumulativeSalaryAdditionsReport(List<Long> housemaidIds, Date currentLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query(nativeQuery = true,
            value = "select ar.NAME as additionType," +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2 THEN p.Amount " +
                    "              else 0 " +
                    "         end) as currentMonthAmount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3 THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4 THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2 THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3 THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4 THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount " +
                    "from PAYROLLMANAGERNOTES p " +
                    "inner join PICKLISTS_ITEMS ar on ar.ID = p.DEDUCTION_REASON_ID " +
                    "where p.HOUSEMAID_ID is not null and p.HOUSEMAID_ID in ?1 and p.NOTE_TYPE = 'DEDUCTION' and p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?2 " +
                    "GROUP BY ar.NAME")
    List<CumulativeSalaryAdditionsOrDeductionsProjection> getHousemaidCumulativeSalaryDeductionsReport(List<Long> housemaidIds, Date currentLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query("SELECT SUM(p.amount) FROM PayrollManagerNote p WHERE p.housemaid IN ?1 AND cast(p.noteDate as date) >= ?2 AND cast(p.noteDate as date) < ?3 AND p.noteType = ?4 AND p.additionReason = ?5")
    public Double sumHousemaidNoteAmountByType(List<Housemaid> housemaidList, Date lastPayrollLockDate, Date currentMonthLockDate, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReason);

    @Query("SELECT COUNT(p) FROM PayrollManagerNote p WHERE p.housemaid IN ?1 AND cast(p.noteDate as date) >= ?2 AND cast(p.noteDate as date) < ?3 AND p.noteType = ?4 AND p.additionReason = ?5")
    public Integer countHousemaidNoteByType(List<Housemaid> housemaidList, Date lastPayrollLockDate, Date currentMonthLockDate, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReason);

    @Query(nativeQuery = true,
            value = "select p.NOTE_TYPE as changeType," +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2 and o.SALARY_CURRENCY = 'USD' THEN p.Amount " +
                    "              else 0 " +
                    "         end) as currentMonthAmountUSD, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3 and o.SALARY_CURRENCY = 'USD' THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmountUSD, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4 and o.SALARY_CURRENCY = 'USD' THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmountUSD, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2  THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3   THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4  THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?3 and p.NOTE_DATE < ?2 and o.SALARY_CURRENCY = 'AED' THEN p.Amount " +
                    "              else 0 " +
                    "         end) as currentMonthAmountAED, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?4 and p.NOTE_DATE < ?3 and o.SALARY_CURRENCY = 'AED' THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmountAED, " +
                    "     sum(case WHEN p.NOTE_DATE >= ?5 and p.NOTE_DATE < ?4 and o.SALARY_CURRENCY = 'AED' THEN p.Amount " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmountAED " +
                    "from PAYROLLMANAGERNOTES p " +
                    "inner join OFFICESTAFFS o on o.ID = p.OFFICE_STAFF_ID " +
                    "where p.OFFICE_STAFF_ID is not null and p.OFFICE_STAFF_ID in ?1 and p.NOTE_DATE >= ?5  and p.NOTE_DATE < ?2 " +
                    "GROUP BY p.NOTE_TYPE")
    List<CumulativeSalaryChangesProjection> getOfficeStaffCumulativeSalaryChangesReport(List<Long> officeStaffIds, Date currentLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);


    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (w.amount)) FROM PayrollManagerNote w inner join w.housemaid h inner join w.additionReason p " +
            "where h.id in ?1 and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 and p.code = ?5 group by h.id")
    List<HousemaidAmountProjection> findCapAdditions(List<Long> housemaids, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type, String code);

    @Query("SELECT SUM(w.amount) from PayrollManagerNote w inner join w.additionReason p where w.housemaid = ?1 and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 and p.code = ?5 group by w.housemaid")
    Double findCapAdditionsByHousemaid(Housemaid housemaid, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type, String code);


    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (w.amount)) FROM PayrollManagerNote w inner join w.housemaid h where h.id in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 and w.deductionReason in ?5 group by h.id")
    List<HousemaidAmountProjection> housemaidExceedingInterviewDeductions(List<Long> housemaids, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type,
                                                                          List<PicklistItem> items);

    @Query("SELECT SUM (w.amount) FROM PayrollManagerNote w where w.housemaid = ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 and w.deductionReason in ?5 group by w.housemaid")
    Double housemaidExceedingInterviewDeductionsByHousemaid(Housemaid housemaid, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type,
                                                                          List<PicklistItem> items);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (w.amount)) FROM PayrollManagerNote w inner join w.housemaid h where h.id in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 " +
            "and w.additionReason not in ?5 group by h.id")
    List<HousemaidAmountProjection> housemaidNoteTypeSum(List<Long> housemaids, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type, List<PicklistItem> items);

    @Query("SELECT SUM (w.amount) FROM PayrollManagerNote w where w.housemaid = ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 " +
            "and w.additionReason not in ?5 group by w.housemaid")
    Double housemaidNoteTypeSumByHousemaid(Housemaid housemaid, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type, List<PicklistItem> items);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (w.amount)) FROM PayrollManagerNote w inner join w.housemaid h where h.id in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 and w.deductionReason not in ?5 group by h.id")
    List<HousemaidAmountProjection> housemaidDeductionsNotIn(List<Long> housemaids, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type,
                                                             List<PicklistItem> items);

    @Query("SELECT SUM (w.amount) FROM PayrollManagerNote w where w.housemaid = ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 and w.deductionReason not in ?5 group by w.housemaid")
    Double housemaidDeductionsNotInByHousemaid(Housemaid housemaid, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type,
                                                             List<PicklistItem> items);

    @Query("SELECT SUM (w.amount) FROM PayrollManagerNote w where w.housemaid = ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 group by w.housemaid")
    Double housemaidAllDeductionsBetweenDateByHousemaid(Housemaid housemaid, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type);

    PayrollManagerNote findTopByScheduledAnnualVacationOrderByNoteDateDesc(ScheduledAnnualVacation vacation);

    List<PayrollManagerNote> findByScheduledAnnualVacation(ScheduledAnnualVacation vacation);

    /*
     * <AUTHOR> Qazzaz
     * @reason PAY-117
     */
    @Query("SELECT p"
            + " FROM PayrollManagerNote p"
            + " WHERE p.housemaid = ?1 AND p.noteType IN ?2")
    List<PayrollManagerNote> findByHousemaidAndNoteTypes(Housemaid housemaid, List<PayrollManagerNote.ManagerNoteType> noteTypes);

    @Query("SELECT p"
            + " FROM PayrollManagerNote p"
            + " WHERE p.housemaid = ?1 AND p.noteType IN ?2" +
            " AND (?3 is null or p.noteDate >= ?3)")
    List<PayrollManagerNote> findByHousemaidAndNoteTypesAndNoteDateAfter(Housemaid housemaid,
                                                                         List<PayrollManagerNote.ManagerNoteType> noteTypes,
                                                                         Date beforeSixMonthsDate);

    @Query("SELECT p FROM PayrollManagerNote p WHERE p.housemaid = ?1 AND ( (p.noteDate >= ?2 AND p.noteDate < ?3 and (p.paid = false or p.paidOnPayrollMonth = ?7)) OR (p.noteDate >= ?3 AND p.noteDate <= ?4 and p.paid = true and p.paidOnPayrollMonth = ?7) ) AND p.noteType = ?5 AND p.additionReason NOT IN ?6")
    List<PayrollManagerNote> getByHousemaidAndNoteTypeAndAdditionReasonNotIn(Housemaid housemaid, Date date1, Date date2, Date now, AbstractPayrollManagerNote.ManagerNoteType noteType, List<PicklistItem> additionReasons, java.sql.Date payrollMonth);

    @Query("SELECT p FROM PayrollManagerNote p WHERE p.housemaid = ?1 AND p.noteDate >= ?2 AND p.noteDate < ?3 AND p.noteType = ?4")
    List<PayrollManagerNote> getByHousemaidAndNoteType(Housemaid housemaid, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("select h from PayrollManagerNote p inner join p.housemaid h inner join p.additionReason r " +
            "where h IN ?1 and p.confirmedAmountByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 and r.code IN ?5 AND h.nationality = ?6 AND p.amount > ?7")
    List<Housemaid> getHousemaidsByNoteTypeAndAdditionReasonAndNationalityAndAmount(List<Housemaid> housemaids, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<String> wantedAdditionReasons, PicklistItem nationality, Double amount);

    @Query("select h from PayrollManagerNote p inner join p.housemaid h inner join p.additionReason r " +
            "where h IN ?1 and p.confirmedAmountByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 and r.code IN ?5 AND h.nationality NOT IN ?6 AND p.amount > ?7")
    List<Housemaid> getHousemaidsByNoteTypeAndAdditionReasonAndNationalityNotInAndAmount(List<Housemaid> housemaids, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<String> wantedAdditionReasons, List<PicklistItem> unwantedNationalities, Double amount);

    @Query("select p from PayrollManagerNote p inner join p.housemaid h inner join p.additionReason r " +
            "where h = ?1 and p.confirmedAmountByAuditor = false and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.noteType = ?4 and r.code IN ?5 AND p.amount > ?6")
    List<PayrollManagerNote> getManagerNoteByNoteTypeAndAdditionReasonAndAmount(Housemaid housemaid, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<String> wantedAdditionReasons, Double amount);

    @Query("SELECT a.name as managerNoteAddition, a.code as additionReasonCode, COUNT(p) as numberOfAdditions, SUM(p.amount) as amount FROM PayrollManagerNote p inner join p.additionReason a WHERE p.housemaid IN ?1 and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 group by a")
    List<CheckListAdditionProjection> getCheckListAdditions(List<Housemaid> housemaids, Date lastPayrollLockDate, Date currentPayrollLockDate);

    @Query("SELECT h.name as maidName, n.name as nationality, p.amount as amountOfAddition, p.noteReasone as description, p.noteDate as date FROM PayrollManagerNote p inner join p.housemaid h inner join h.nationality n WHERE p.housemaid IN ?1 and cast(p.noteDate as date) >= ?2 and cast(p.noteDate as date) < ?3 and p.additionReason = ?4 ")
    List<CheckListAdditionByAdditionReasonProjection> getCheckListAdditionsByAdditionReason(List<Housemaid> housemaids, Date lastPayrollLockDate, Date currentPayrollLockDate, PicklistItem additionReason);

    @Query("SELECT h.id FROM PayrollManagerNote p inner join p.housemaid h where p.noteDate >= ?1 AND p.noteDate < ?2 AND p.noteType = ?3 AND p.additionReason = ?4 and p.notFinal = false")
    List<Long> getHeldSalaryHousemaids(Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReason);

    @Query("SELECT h.id as id, h.name as housemaidName, p.amount as heldSalary, l.totalSalary as total FROM PayrollManagerNote p inner join p.housemaid h inner join HousemaidPayrollLog l on l.housemaid = h where p.noteDate >= ?1 AND p.noteDate < ?2 AND p.noteType = ?3 AND p.additionReason = ?4 and l.payrollMonth = ?5 and l.creationDate >= ?6 and h in ?7")
    List<MaidsWereOnVacationProjection> getHousemaidWereOnVacation(Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReason, Date payrollMonth, java.sql.Date currentLockDate, List<Housemaid> housemaids);

    @Query("SELECT p"
            + " FROM PayrollManagerNote p"
            + " WHERE p.housemaid = ?1 AND p.noteDate >= ?2 and p.noteDate < ?3 and p.noteType = ?4")
    List<PayrollManagerNote> findByHousemaidAndNoteType(Housemaid housemaid, Date from, Date to, PayrollManagerNote.ManagerNoteType noteType);

    @Query("SELECT h.id FROM PayrollManagerNote p inner join p.housemaid h where p.noteDate >= ?1 AND p.noteDate < ?2 AND p.noteType = ?3 AND p.additionReason IN ?4 and p.paid=false and p.amount > 0.0")
    List<Long> findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<PicklistItem> additionReasons);

    @Query("SELECT new com.magnamedia.extra.payroll.init.HousemaidAmountProjection(h.id, SUM (w.amount)) FROM PayrollManagerNote w inner join w.housemaid h where " +
            " w.noteDate >= ?1 and w.noteDate < ?2 and w.noteType = ?3 AND w.additionReason IN ?4 and w.paid=false group by h.id")
    List<HousemaidAmountProjection> findByNonPaidManagerNotes(Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<PicklistItem> additionReasons);

    @Query("SELECT SUM (w.amount) FROM PayrollManagerNote w where " +
            " w.creationDate >= ?1 and w.noteType = ?2 AND w.additionReason = ?3 and w.housemaid = ?4 group by w.housemaid ")
    Double findByPreviouslyHeldSalariesManagerNotesByHousemaid(Date date1, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem additionReasons, Housemaid housemaid);

    @Query("SELECT SUM (w.amount) FROM PayrollManagerNote w where " +
            " w.noteDate >= ?1 and w.noteDate < ?2 and w.noteType = ?3 AND w.additionReason IN ?4 and w.paid=false and w.housemaid = ?5 group by w.housemaid ")
    Double findByNonPaidManagerNotesByHousemaid(Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<PicklistItem> additionReasons, Housemaid housemaid);

    @Query("SELECT w FROM PayrollManagerNote w inner join w.housemaid h where " +
            " w.noteDate >= ?1 and w.noteDate < ?2 and w.noteType = ?3 AND w.additionReason IN ?4 and w.paid=false and h.id in ?5")
    List<PayrollManagerNote> findByNonPaidManagerNotes(Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType,
                                                       List<PicklistItem> additionReasons, List<Long> housemaids);

    @Query("SELECT w FROM PayrollManagerNote w inner join w.housemaid h where " +
            " w.noteDate >= ?1 and w.noteDate < ?2 AND w.additionReason NOT IN ?3 and w.paid=false and h.id in ?4")
    List<PayrollManagerNote> findByNonPaidManagerNotesNotIn(Date date1, Date date2,
                                                            List<PicklistItem> additionReasons, List<Long> housemaids);

    @Query("SELECT w FROM PayrollManagerNote w inner join w.officeStaff o where " +
            " w.noteDate >= ?1 and w.noteDate < ?2 AND w.paid=false and o.id in ?3")
    List<PayrollManagerNote> findByNonPaidManagerNotesForOfficeStaff(Date date1, Date date2, List<Long> officeStaffList);

    @Query("SELECT w FROM PayrollManagerNote w where " +
            " w.noteDate >= ?1 and w.noteDate < ?2 and w.paid=false and w.housemaid = ?3")
    List<PayrollManagerNote> findALLByNonPaidManagerNotes(Date date1, Date date2, Housemaid housemaid);

//    @Query("SELECT pmn FROM PayrollManagerNote pmn WHERE pmn.id > :id AND pmn.lastModificationDate >= :fromDate " +
//            " and pmn.lastModifier.position.code = 'payroll_auditor' ")
//    Page<PayrollManagerNote> findByLastModificationDateAfter(@Param("id") Long id,
//                                                             @Param("fromDate") Date fromDate,
//                                                             Pageable pageable);

    @Query(value = "SELECT HM.NAME AS housemaidName, PMNR.AMOUNT AS amount, US.NAME AS lastModifier " +
            " FROM PAYROLLMANAGERNOTES_REVISIONS AS PMNR " +
            " LEFT JOIN HOUSEMAIDS AS HM ON HM.ID = PMNR.HOUSEMAID_ID " +
            " LEFT JOIN USERS AS US ON US.ID = PMNR.LAST_MODIFIER " +
            " WHERE PMNR.LAST_MODIFICATION_DATE >= ?1 " +
            " AND PMNR.ID NOT IN (SELECT PMN.ID FROM PAYROLLMANAGERNOTES PMN)", nativeQuery = true)
    List<Map<String, Object>> getDeletedManagerNotesAfter(Date fromDate);

    PayrollManagerNote findTopByHousemaidAndAdditionReason(Housemaid maid,
                                        PicklistItem item);

    List<PayrollManagerNote> findByHousemaidAndNoteTypeAndNoteDateBetween(Housemaid housemaid, PayrollManagerNote.ManagerNoteType noteType,
                                                        Date fromDate, Date toDate);

    List<PayrollManagerNote> findByOfficeStaffAndNoteDateBetween(OfficeStaff staff, java.sql.Date payrollStart, java.sql.Date payrollEnd);

    @Query("SELECT h FROM PayrollManagerNote w inner join w.housemaid h where h in ?1 " +
            "and w.noteDate >= ?2 and ( (w.noteDate < ?3 and w.additionReason not in ?6) or (w.noteDate < ?4 and w.additionReason in ?6)) " +
            "and w.noteType = ?5 " +
            "group by h having sum(w.amount) > ?7")
    List<Housemaid> getSummationHousemaidNotesByTypeBiggerThan(List<Housemaid> housemaids, Date start, Date end, Date nowDate, AbstractPayrollManagerNote.ManagerNoteType type, List<PicklistItem> items, Double amount);

    @Query("SELECT w FROM PayrollManagerNote w inner join w.housemaid h where h in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 " +
            "and w.additionReason not in ?5 ")
    List<PayrollManagerNote> getHousemaidNotesByTypeBiggerThan(List<Housemaid> housemaids, Date start, Date end, AbstractPayrollManagerNote.ManagerNoteType type, List<PicklistItem> items);

    @Query("SELECT w FROM PayrollManagerNote w inner join w.housemaid h where h in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 AND w.additionReason IN ?5 ")
    List<PayrollManagerNote> getHousemaidImportantNotesByTypeBiggerThan(List<Housemaid> housemaids, Date date1, Date date2, AbstractPayrollManagerNote.ManagerNoteType noteType, List<PicklistItem> additionReasons);

    @Query("select h.id from PayrollManagerNote w inner join w.housemaid h where h in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4 group by h.id having count(h.id) > 1")
    List<Long> findHousemaidsWithRepetitiveAdditions(List<Housemaid> housemaids, Date payrollStart, Date payrollEnd, AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("select w from PayrollManagerNote w inner join w.housemaid h where h.id in ?1 " +
            "and w.noteDate >= ?2 and w.noteDate < ?3 and w.noteType = ?4")
    List<PayrollManagerNote> findHousemaidsWithRepetitiveAdditionsData(List<Long> housemaidsIds, Date payrollStart, Date payrollEnd, AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("SELECT SUM(p.amount) FROM PayrollManagerNote p WHERE p.housemaid = ?1 AND p.noteDate >= ?2 AND p.noteType = ?3 and p.paid = false")
    Double sumByHousemaidAndNoteType(Housemaid housemaid, Date date1, AbstractPayrollManagerNote.ManagerNoteType noteType);

    @Query("SELECT SUM(p.amount) FROM PayrollManagerNote p WHERE p.housemaid = ?1 AND p.noteDate >= ?2 AND p.noteType = ?3 and p.paid = false and p.additionReason <> ?4 and (p.additionReason <> ?5 or (p.additionReason = ?5 and p.purpose <> ?6))")
    Double sumByHousemaidAndNoteTypeWithoutRaffleAndReferral(Housemaid housemaid, Date date1, AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem raffle, PicklistItem bonus, PicklistItem referralPurpose);

    List<PayrollManagerNote> findByNoteTypeAndAdditionReasonAndCreationDateBetween(AbstractPayrollManagerNote.ManagerNoteType noteType, PicklistItem reason, Date from, Date to);

    @Query("SELECT p"
            + " FROM PayrollManagerNote p"
            + " WHERE p.housemaid = :housemaid"
            + " and p.additionReason = :additionReason"
            + " and p.noteReasone like concat('%', :noteReason, '%')"
            + " order by p.id desc")
    PayrollManagerNote findTopByHousemaidAndAdditionReasonAndNoteReasoneOrderByIdDesc(@Param("housemaid") Housemaid housemaid,
                                                                                      @Param("additionReason") PicklistItem additionReason,
                                                                                      @Param("noteReason") String noteReason);


    @Query("select p from PayrollManagerNote p " +
            "left join p.officeStaff o " +
            "where o.name = ?1 " +
            "and p.noteType = ?2 " +
            "order by p.creationDate desc")
    List<PayrollManagerNote> findByOfficeStaffNameAndChangeType(String name, PayrollManagerNote.ManagerNoteType noteType);

}
