package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PayrollRosterApproveRequest;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PayrollRosterApproveRequestRepository extends BaseRepository<PayrollRosterApproveRequest> {

    PayrollRosterApproveRequest findTopByManagerAndPayrollMonth(OfficeStaff manager, java.sql.Date payrollMonth);

    List<PayrollRosterApproveRequest> findByPayrollMonthAndFinalManagerFalseAndForEmarati(java.sql.Date payrollMonth, Boolean forEmarati);

    Integer countByPayrollMonthAndApprovedFalseAndForEmarati(java.sql.Date payrollMonth, Boolean forEmarati);

    Integer countByPayrollMonthAndApprovedFalse(java.sql.Date payrollMonth);

    Integer countByPayrollMonthAndFinalManagerAndFor<PERSON>marati(java.sql.Date payrollMonth, Boolean finalManager, Boolean forEmarati);
}
