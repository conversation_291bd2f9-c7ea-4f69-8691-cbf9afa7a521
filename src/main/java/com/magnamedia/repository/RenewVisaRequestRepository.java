package com.magnamedia.repository;

import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.RenewRequest;
import com.magnamedia.extra.payroll.init.HousemaidDateProjection;
import com.magnamedia.extra.payroll.init.HousemaidFieldProjection;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 16, 2017
 */
@Repository
public interface RenewVisaRequestRepository extends VisaRequestRepository<RenewRequest> {

    public List<RenewRequest> findByHousemaidAndCompletedAndLastMoveDateGreaterThanEqual
            (Housemaid housemaid, boolean completed, Date lastMoveDate);

    //Jirra 1384
    public RenewRequest findFirstOneByHousemaidAndCompletedOrderByCreationDateDesc
    (Housemaid housemaid, boolean completed);

    public List<RenewRequest> findByCompletedAndLastMoveDateGreaterThanEqualAndHousemaidIsNotNull
            (boolean completed, Date lastMoveDate);

    public List<RenewRequest> findByCompleted(boolean completed);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidDateProjection(h.id, renewRequest.lastMoveDate) from RenewRequest renewRequest join renewRequest.housemaid h " +
            "where h.id in ?1 and renewRequest.completed=true order by renewRequest.id desc")
    List<HousemaidDateProjection> findRenewalDate(List<Long> housemaids);

    @Query("select renewRequest.lastMoveDate from RenewRequest renewRequest " +
            "where renewRequest.housemaid = ?1 and renewRequest.completed=true order by renewRequest.id desc")
    List<Date> findRenewalDateByHousemaid(Housemaid housemaid);

    @Query("select new com.magnamedia.extra.payroll.init.HousemaidDateProjection(h.id, renewRequest.lastMoveDate) from RenewRequest renewRequest join renewRequest.housemaid h " +
            "where h.id = ?1 and renewRequest.completed=true order by renewRequest.id desc")
    List<HousemaidDateProjection> findRenewalDateForSingleHousemaid(Long housemaid);

    @Query("select h from RenewRequest r join r.housemaid h join r.taskHistorys history " +
            "where history.taskName like concat('%', ?2, '%') and history.taskMoveOutDate is not null and history.taskMoveOutDate >= ?3 and history.taskMoveOutDate < ?4 and " +
            "(r.maxRenewalRaise is not null and r.maxRenewalRaise > ?1) and r.stopped=false " +
            "and (h.lastChangeAmountConfirmByAuditor is null or h.lastChangeAmountConfirmByAuditor < ?4) and h in ?5 group by h")
    List<Housemaid> findRenewedHousemaids(Double raiseLimit, String taskName, Date from, Date to, List<Housemaid> housemaids);

    @Query("select r from RenewRequest r join r.taskHistorys history " +
            "where history.taskName like concat('%', ?2, '%') and history.taskMoveOutDate is not null and history.taskMoveOutDate >= ?3 and history.taskMoveOutDate < ?4 and " +
            "(r.maxRenewalRaise is not null and r.maxRenewalRaise > ?1) and r.stopped=false and r.housemaid=?5")
    List<RenewRequest> getRenewedHousemaidRequests(Double raiseLimit, String taskName, Date from, Date to, Housemaid housemaid);

    @Query("select h.id from RenewRequest r join r.housemaid h where r.completed=true and r.stopped=false")
    List<Long> findRenewedMaids();

    @Query("select r from RenewRequest r join r.housemaid h join r.taskHistorys history " +
            "where history.taskName like concat('%', ?1, '%') and history.taskMoveOutDate is not null and history.taskMoveOutDate >= ?2 and history.taskMoveOutDate < ?3 and " +
            "r.maxRenewalRaise is not null and r.maxRenewalRaise > 0 and r.stopped=false and h in ?4")
    List<RenewRequest> getCheckListRenewedMaids(String taskName, Date from, Date to, List<Housemaid> housemaids);

    @Query("select r.maxRenewalRaise from RenewRequest r where r.id = " +
            "(select max(r2.id) from RenewRequest r2 inner join r2.housemaid h where r2.completed=true" +
            " and r2.stopped=false and h.id=?1)")
    Double findSalaryRaiseOfLastCompletedRenewRequest(Long housemaidId);

    List<RenewRequest> findByHousemaid(Housemaid housemaid);

    RenewRequest findTopByHousemaidOrderByIdDesc(Housemaid housemaid);
}
