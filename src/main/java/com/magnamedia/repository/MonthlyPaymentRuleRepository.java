package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/10/2020
 **/
@Repository
public interface MonthlyPaymentRuleRepository extends BaseRepository<MonthlyPaymentRule> {

    List<MonthlyPaymentRule> findByFinished( Boolean finished);

    List<MonthlyPaymentRule> findByPayrollMonth(Date payrollMonth);

    List<MonthlyPaymentRule> findByPayrollMonthOrderByPaymentDateAsc(Date payrollMonth);

    List<MonthlyPaymentRule> findByLockDateAndPayrollType(Date lockDate, PayrollType payrollType);

    @Query("select m from MonthlyPaymentRule m where m.lockDate = ?1 and ?2 MEMBER OF m.employeeTypeList")
    List<MonthlyPaymentRule> findByLockDateAndEmployeeType(Date lockDate, PaymentRuleEmployeeType paymentRuleEmployeeType);

    List<MonthlyPaymentRule> findByPayrollMonthAndLockDateAndFinishedFalse(Date payrollMonth, Date lockDate);

    List<MonthlyPaymentRule> findByPayrollMonthAndPayrollType(Date payrollMonth, PayrollType payrollType);

    Integer countByPaymentDate(Date paymentDate);

    List<MonthlyPaymentRule> findByPaymentDate(Date paymentDate);

    List<MonthlyPaymentRule> findByPaymentDateAndPayrollTypeAndSingleOfficeStaff(Date paymentDate, PayrollType payrollType, Boolean singleOfficeStaff);

    MonthlyPaymentRule findTopByPayrollMonthAndPayrollTypeAndPaymentMethodAndLockDate(Date payrollMonth, PayrollType payrollType, PaymentRulePaymentMethod paymentMethod, Date lockDate);

    @Query("SELECT mpr FROM MonthlyPaymentRule mpr" +
            " where mpr.paymentDate >= :fromDate" +
            " AND (:payrollType IS NULL OR mpr.payrollType = :payrollType) " +
            " AND mpr.singleOfficeStaff = false and mpr.singleHousemaid = false " +
            " ORDER BY mpr.paymentDate asc")
    List<MonthlyPaymentRule> findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(
            @Param("fromDate") Date fromDate, @Param("payrollType") PayrollType payrollType);

    @Query("SELECT mpr FROM MonthlyPaymentRule mpr" +
            " where mpr.paymentDate >= :fromDate" +
            " AND (:payrollType IS NULL OR mpr.payrollType = :payrollType) " +
            " AND (:employeeType IS NULL OR :employeeType MEMBER OF mpr.employeeTypeList) " +
            " ORDER BY mpr.paymentDate asc")
    List<MonthlyPaymentRule> findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(
            @Param("fromDate") Date fromDate, @Param("payrollType") PayrollType payrollType, @Param("employeeType") PaymentRuleEmployeeType employeeType);

    @Query("SELECT mpr FROM MonthlyPaymentRule mpr" +
            " where mpr.paymentDate > :fromDate" +
            " AND (:payrollType IS NULL OR mpr.payrollType = :payrollType) " +
            " AND (:employeeType IS NULL OR :employeeType MEMBER OF mpr.employeeTypeList) " +
            " AND mpr.payrollMonth = :payrollMonth " +
            " ORDER BY mpr.paymentDate asc")
    List<MonthlyPaymentRule> findNextHousemaidRuleByPayrollTypeAndPayrollMonthAfterPaymentDateOrderByPaymentDateAsc(
            @Param("fromDate") Date fromDate, @Param("payrollType") PayrollType payrollType, @Param("employeeType") PaymentRuleEmployeeType employeeType, @Param("payrollMonth") Date payrollMonth);


    @Query("SELECT mpr FROM MonthlyPaymentRule mpr" +
            " where mpr.payrollMonth = ?1 " +
            " AND mpr.payrollType = ?2 " +
            " AND (?3 IS NULL OR ?3 MEMBER OF mpr.employeeTypeList) " +
            " AND mpr.paymentMethod in ?4 " +
            " ORDER BY mpr.paymentDate asc")
    List<MonthlyPaymentRule> findByPayrollMonthAndEmployeeTypeAndPaymentMethod(
            Date payrollMonth, PayrollType payrollType, PaymentRuleEmployeeType employeeType, List<PaymentRulePaymentMethod> paymentMethods);

    @Query("select rule from MonthlyPaymentRule rule where ?1 member of rule.employeeTypeList and rule.payrollMonth = ?2 and rule.paymentDate <= ?3 and rule.paymentMethod = ?4")
    List<MonthlyPaymentRule> getMonthlyRulesByEmployeeTypeAndPayrollMonthAndPaymentDateBefore(PaymentRuleEmployeeType paymentRuleEmployeeType, Date payrollMonth, Date currentDate, PaymentRulePaymentMethod paymentMethod);

    @Query("select rule from MonthlyPaymentRule rule where ?1 member of rule.employeeTypeList and rule.paymentDate <= ?2 and rule.paymentMethod = ?3 and rule.payrollType = ?4 order by rule.paymentDate desc")
    List<MonthlyPaymentRule> getMonthlyRulesByEmployeeTypeAndPaymentDateBeforeAndPaymentMethodAndPayrollType(PaymentRuleEmployeeType paymentRuleEmployeeType, Date date, PaymentRulePaymentMethod paymentMethod, PayrollType payrollType);

    @Query("select rule from MonthlyPaymentRule rule where ?2 member of rule.employeeTypeList and rule.paymentDate <= ?1 order by rule.paymentDate desc")
    List<MonthlyPaymentRule> findByPaymentDateLessThan(Date date, PaymentRuleEmployeeType paymentRuleEmployeeType);

//    @Query("select rule from MonthlyPaymentRule rule where ?2 member of rule.employeeTypeList and rule.paymentDate <= ?1 and rule.payrollType = ?3 order by rule.paymentDate desc")
//    List<MonthlyPaymentRule> findByPaymentDateLessThanAndPayrollType(Date date, PaymentRuleEmployeeType paymentRuleEmployeeType, PayrollType payrollType);

    @Query("SELECT RULE FROM MonthlyPaymentRule RULE WHERE RULE.payrollMonth=?1 AND ?2 MEMBER OF RULE.employeeTypeList ORDER BY RULE.paymentDate ASC")
    List<MonthlyPaymentRule> findByPayrollMonthAndPaymentRuleEmployeeType(Date payrollMonth, PaymentRuleEmployeeType paymentRuleEmployeeType);

    @Query("SELECT RULE FROM MonthlyPaymentRule RULE WHERE RULE.payrollMonth=?1 AND ?2 MEMBER OF RULE.employeeTypeList AND RULE.payrollType = ?3 AND RULE.lockDate = ?4 ORDER BY RULE.paymentDate ASC")
    List<MonthlyPaymentRule> findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(Date payrollMonth, PaymentRuleEmployeeType paymentRuleEmployeeType, PayrollType payrollType, Date lockDate);

    @Query("select m from MonthlyPaymentRule m where m.paymentDate <= ?1 and m.payrollType = ?2 and m.finished = true and ?3 MEMBER OF m.employeeTypeList order by m.paymentDate desc ")
    List<MonthlyPaymentRule> findLastPayroll(java.util.Date date, PayrollType payrollType, PaymentRuleEmployeeType employeeType);

    @Query("select rule from MonthlyPaymentRule rule where ?1 member of rule.employeeTypeList and rule.paymentDate > ?2 and rule.paymentMethod = ?3 order by rule.paymentDate")
    List<MonthlyPaymentRule> findNextMonthlyRuleByHousemaidType(PaymentRuleEmployeeType employeeType, Date today, PaymentRulePaymentMethod paymentMethod);

    @Query("select m from MonthlyPaymentRule m where ?1 member of m.employeeTypeList and m.finished = true and m.payrollType = 'PRIMARY' and m.paymentDate < ?2 and m.singleHousemaid = false and m.singleOfficeStaff = false order by m.id desc")
    List<MonthlyPaymentRule> findLastPrimaryPayroll(PaymentRuleEmployeeType employeeType, Date now);

    MonthlyPaymentRule findTop1ByLockDateAndSingleOfficeStaffFalseAndSingleHousemaidFalse(Date lockDate);

    @Query("select e.id from MonthlyPaymentRule m inner join m.excludedMaidsDueToMissingFields e where m = ?1")
    Set<Long> getExcludedMaidsDueToMissingFields(MonthlyPaymentRule monthlyPaymentRule);

    @Query("select e.id from MonthlyPaymentRule m inner join m.excludedMaidsDueToOverLap e where m = ?1")
    Set<Long> getExcludedMaidsDueToOverLap(MonthlyPaymentRule monthlyPaymentRule);

    @Query("select e.id from MonthlyPaymentRule m inner join m.excludedDueToMedicalTest e where m = ?1")
    Set<Long> getExcludedDueToMedicalTest(MonthlyPaymentRule monthlyPaymentRule);
}
