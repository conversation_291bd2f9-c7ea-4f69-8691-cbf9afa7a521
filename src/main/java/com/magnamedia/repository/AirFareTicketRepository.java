package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AirFareTicket;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.TravelDays;
import com.magnamedia.entity.projection.payrollAudit.CumulativeSalaryAdditionsOrDeductionsProjection;
import com.magnamedia.entity.projection.payrollAudit.CumulativeSalaryChangesProjection;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 28, 2020
 *         Jirra ACC-1227
 */

@Repository
public interface AirFareTicketRepository extends BaseRepository<AirFareTicket> {

    AirFareTicket findFirstByTravelDays(TravelDays travelDays);

    @Query(nativeQuery = true,
            value="select sum(case WHEN d.CHECK_DATE >= ?3 and d.CHECK_DATE < ?2 and o.SALARY_CURRENCY = 'USD' THEN t.TICKET_AMOUNT " +
                    "              else 0 " +
                    "         end) as currentMonthAmountUSD, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?4 and d.CHECK_DATE < ?3 and o.SALARY_CURRENCY = 'USD' THEN t.TICKET_AMOUNT " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmountUSD, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?5 and d.CHECK_DATE < ?4 and o.SALARY_CURRENCY = 'USD' THEN t.TICKET_AMOUNT " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmountUSD, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?3 and d.CHECK_DATE < ?2  THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?4 and d.CHECK_DATE < ?3  THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?5 and d.CHECK_DATE < ?4  THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?3 and d.CHECK_DATE < ?2 and o.SALARY_CURRENCY = 'AED' THEN t.TICKET_AMOUNT " +
                    "              else 0 " +
                    "         end) as currentMonthAmountAED, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?4 and d.CHECK_DATE < ?3 and o.SALARY_CURRENCY = 'AED' THEN t.TICKET_AMOUNT " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmountAED, " +
                    "     sum(case WHEN d.CHECK_DATE >= ?5 and d.CHECK_DATE < ?4 and o.SALARY_CURRENCY = 'AED' THEN t.TICKET_AMOUNT " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmountAED " +
                    "from  AIRFARETICKETS t " +
                    "inner join TRAVELDAYS d on d.ID = t.TRAVEL_DAYS_ID " +
                    "inner join OFFICESTAFFS o on o.ID = d.OFFICE_STAFF_ID " +
                    "where d.OFFICE_STAFF_ID in ?1 and d.CHECK_DATE >= ?5 and d.CHECK_DATE < ?2 " +
                    "GROUP BY o.ENTITY_TYPE")
    List<CumulativeSalaryChangesProjection> getOfficeStaffCumulativeSalaryChangesReport(List<Long> officeStaffIds, Date currentLockDate, Date prevOneMonthLockDate, Date prevTowLockDate, Date prevThreeLockDate);

    @Query("select sum(a.ticketAmount) from AirFareTicket a inner join a.travelDays t where t.officeStaff = ?3 and t.confirmed = true and t.checkDate >= ?1 and t.checkDate < ?2")
    Double getSumAirFareTickets(java.sql.Date payrollStart, java.sql.Date payrollEnd, OfficeStaff staff);
}
