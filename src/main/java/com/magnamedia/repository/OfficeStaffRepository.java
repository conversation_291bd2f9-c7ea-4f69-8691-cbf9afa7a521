/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.OfficeStaffBirthdayProjection;
import com.magnamedia.entity.projection.payrollAudit.HighestSalariesSameJobTitleProjection;
import com.magnamedia.entity.projection.v2.OfficeStaffList;
import com.magnamedia.entity.projection.v2.OfficeStaffRoster;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.ReceiveMoneyMethod;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Abbas <<EMAIL>>
 */
@Repository
public interface OfficeStaffRepository extends BaseRepository<OfficeStaff> {

    @Query("select o from OfficeStaff o where o.nationality=?1 and o.status=?2 and o.employeeType <> ?3")
    List<OfficeStaff> findByNationalityAndStatus(PicklistItem nationality, OfficeStaffStatus staffStatus, OfficeStaffType type);

    @Query("select o from OfficeStaff o " +
            "left join o.selectedTransferDestination t " +
            "where o.employeeType = ?1 " +
            "and t.receiveMoneyMethod = ?2 " +
            "and t.country = ?3")
    List<OfficeStaff> findByCountryAndTypeAndSalaryTransfer(OfficeStaffType officeStaffType,
                                                            ReceiveMoneyMethod receiveMoneyMethod,
                                                            PicklistItem country);


    @Query("SELECT o FROM OfficeStaff o WHERE ("
            + "(SELECT SUM (l.amount) FROM EmployeeLoan l WHERE l.officeStaff = o)"
            + "-(SELECT SUM (e.amount) FROM Repayment e WHERE e.officestaff = o AND e.paidRepayment=true)"
            + ")<-1")
    public List<OfficeStaff> findNegativeLoanBalance();

    @Query("SELECT o from OfficeStaff o left outer join o.manager left outer join o.team where o.status=com.magnamedia.module.type.OfficeStaffStatus.ACTIVE" +
            " order by o.manager.name asc, o.team.name asc")
    List<OfficeStaff> findActiveOfficeStaffOrdered();


    @Query("SELECT o from OfficeStaff o where o.status=com.magnamedia.module.type.OfficeStaffStatus.TERMINATED" +
            " order by o.terminationDate desc")
    List<OfficeStaff> findTerminatedOfficeStaffOrdered();


    @Query("select o.name as name, o.terminationDate as terminationDate, o.status as status, o.basicSalary as basicSalary, o.salaryCurrency as salaryCurrency, o.cityName as city, _manager.name as manager, _team.name as team, case when o.salaryCurrency='AED' then o.salary else 0 end as companySalaryAed, case when o.salaryCurrency='USD' then o.salary else 0 end as companySalaryUsd " +
            "from OfficeStaff o left join o.employeeManager as _manager left join o.team as _team where o.status= ?1 order by o.terminationDate")
    List<OfficeStaffRoster> findByStatusOrderByTerminationDate(OfficeStaffStatus status);


    @Query("select o.name as name, o.terminationDate as terminationDate, o.status as status, o.basicSalary as basicSalary, o.salaryCurrency as salaryCurrency, o.cityName as city, _manager.name as manager, _team.name as team, case when o.salaryCurrency='AED' then o.salary else 0 end as companySalaryAed, case when o.salaryCurrency='USD' then o.salary else 0 end as companySalaryUsd " +
            "from OfficeStaff o left join o.employeeManager as _manager left join o.team as _team where o.status= ?1 order by _manager.name")
    List<OfficeStaffRoster> findByStatusOrderByManager_Name(OfficeStaffStatus status);

    @Query("select o.name as name, o.terminationDate as terminationDate, o.status as status, o.basicSalary as basicSalary, o.salaryCurrency as salaryCurrency, o.cityName as city, _manager.name as manager, _team.name as team, case when o.salaryCurrency='AED' then o.salary else 0 end as companySalaryAed, case when o.salaryCurrency='USD' then o.salary else 0 end as companySalaryUsd " +
            "from OfficeStaff o left join o.employeeManager as _manager left join o.team as _team where o.terminationDate >= ?2 and o.status= ?1 order by o.terminationDate")
    List<OfficeStaffRoster> findByStatusAndTerminationDateAfterOrderByTerminationDate(OfficeStaffStatus status, Date start);

    @Query("select o.id as id, o.name as name, o.status as status, _jobTitle.name as jobTitle, o.employeeType as employeeType, o.startingDate as startingDate, _employeeManager.name as manager from OfficeStaff o left join o.jobTitle _jobTitle left join o.employeeManager _employeeManager " +
            "where (?1 is null or o.name like CONCAT('%',?1,'%')) and o.status in ?2 order by o.name")
    Page<OfficeStaffList> findOfficeStaffPayrollList(String name, List<OfficeStaffStatus> status, Pageable pageable);

    @Query("select o.id as id, o.name as name, o.status as status, _jobTitle.name as jobTitle, o.employeeType as employeeType, o.startingDate as startingDate, _employeeManager.name as manager from OfficeStaff o left join o.jobTitle _jobTitle left join o.employeeManager _employeeManager " +
            "where (o.name like CONCAT('%', ?1, '%', ?2, '%')) and o.status in ?3 order by o.name")
    Page<OfficeStaffList> findOfficeStaffPayrollList(String firstName, String lastName, List<OfficeStaffStatus> status, Pageable pageable);

//    @Query("select o.id as id, o.name as name, o.status as status, _team.name as team, o.employeeType as employeeType, o.startingDate as startingDate, _employeeManager.name as manager from OfficeStaff o left join o.team _team left join o.employeeManager _employeeManager where o.status = ?1")
//    Page<OfficeStaffList> findOfficeStaffPayrollList(OfficeStaffStatus status, Pageable pageable);


    int countByOfficeStaffCandidate(OfficeStaffCandidate candidate);

    OfficeStaff findFirstByOfficeStaffCandidate(OfficeStaffCandidate candidate);
    
    /*
     *@author: Amjad Qazzaz
     *@reason: PAY-53
     *start
     */
    List<OfficeStaff> findByEmployeeManager(OfficeStaff officestaff);
    /*
     *end
     */

    @Query("SELECT o from OfficeStaff o where o.status=com.magnamedia.module.type.OfficeStaffStatus.ACTIVE")
    List<OfficeStaff> findActiveOfficeStaff();

    List<OfficeStaff> findByNameContaining(String name);

    List<OfficeStaff> findByName(String name);

    List<OfficeStaff> findBySelectedTransferDestinationName(String name);

    @Query("SELECT o from OfficeStaff o where o.status=com.magnamedia.module.type.OfficeStaffStatus.ACTIVE and " +
            "?1 member of o.jobTitle.tags")
    List<OfficeStaff> findAllManagers(Tag manager);


    OfficeStaff findFirstByUser(User user);
    @Query("select  o from  OfficeStaff o where o.compensation > o.salary * ?2 " +
            "and o.confirmedTerminationCompensationByAuditor = false " +
            "and o = ?1 " +
            "and cast(o.terminationDate as date) >= ?3 and cast(o.terminationDate as date) < ?4")
    List<OfficeStaff> findByTerminationDateAndCompensationLimit(OfficeStaff officeStaff, double months, Date previousMonthLockDate, Date currentMonthLockDate);

    @Query("select  count(o) from  OfficeStaff o where o.compensation > o.salary * ?2 " +
            "and o.confirmedTerminationCompensationByAuditor = false " +
            "and o.status = 'TERMINATED' " +
            "and o = ?1 " +
            "and cast(o.terminationDate as date) >= ?3 and cast(o.terminationDate as date) < ?4")
    Integer findCountByTerminationDateAndCompensationLimit(OfficeStaff officeStaff, double months, Date previousMonthLockDate, Date currentMonthLockDate);

    @Query("select o from OfficeStaff o where o = ?1 and o.confirmedHighSalaryByAuditor = false and cast(o.startingDate as date) >= ?2 and cast(o.startingDate as date) < ?3 and o.salary > (select max(t.salary) from  OfficeStaff t where o.jobTitle = t.jobTitle and t.id <> o.id)")
    List<OfficeStaff> findByNewHireSalary(OfficeStaff officeStaff, Date previousMonthLockDate, Date currentMonthLockDate);

    @Query("select count(o) from OfficeStaff o where o = ?1 and o.confirmedHighSalaryByAuditor = false and cast(o.startingDate as date) >= ?2 and cast(o.startingDate as date) < ?3 and o.salary > (select max(t.salary) from  OfficeStaff t where o.employeeManager = t.employeeManager and t.id <> o.id)")
    Integer findCountByNewHireSalary(OfficeStaff officeStaff, Date previousMonthLockDate, Date currentMonthLockDate);

    List<HighestSalariesSameJobTitleProjection> findTop5ByJobTitleAndIdNotOrderBySalaryDesc(PicklistItem jobTitle, long id);

    List<OfficeStaff> findByIdInAndWithMolNumber(List<Long> officeStaffIds, Boolean molNumberStatus);

    @Query("SELECT o FROM OfficeStaff o INNER JOIN o.visaNewRequest v WHERE o.employeeType = ?2 AND TRIM(LEADING '0' FROM v.employeeUniqueId) IN ?1")
    List<OfficeStaff> findByUniqueIdAndEmployeeType(List<String> uniqueIdsList, OfficeStaffType employeeType);

    @Query("SELECT o FROM OfficeStaff o WHERE o IN ?1 AND o NOT IN ?2")
    List<OfficeStaff> findInFirstAndNotInSecond(List<OfficeStaff> firstList, List<OfficeStaff> secondList);

    @Query("SELECT o.id FROM OfficeStaff o INNER JOIN o.visaNewRequest v WHERE TRIM(LEADING '0' FROM v.employeeUniqueId) NOT IN ?1")
    List<Long> findNotInMOLFile(List<String> uniqueIds);

    @Modifying
    @Query("UPDATE OfficeStaff o SET o.withMolNumber = false WHERE o.id IN ?1")
    int updateNotInMOLFile(List<Long> notInFileStaffs);

    @Query("SELECT o.id FROM OfficeStaff o INNER JOIN o.visaNewRequest v WHERE TRIM(LEADING '0' FROM v.employeeUniqueId) IN ?1")
    List<Long> findInMOLFile(List<String> uniqueIds);

    @Modifying
    @Query("UPDATE OfficeStaff o SET o.withMolNumber = true WHERE o.id IN ?1")
    int updateInMOLFile(List<Long> inFileStaffs);

    @Query("SELECT o FROM OfficeStaff o WHERE o.status = com.magnamedia.module.type.OfficeStaffStatus.ACTIVE AND o.terminationDate IS NULL AND o.employeeType = ?1 AND o.excludedFromPayroll=true")
    List<OfficeStaff> findExcludedStaffs(OfficeStaffType employeeType);

    @Query("select count(0) from OfficeStaff o WHERE o in ?1 and o.withMolNumber = ?2")
    Integer countWithMolFile(List<OfficeStaff> targetList, Boolean withMolNumber);

    public OfficeStaff findFirstByName(String Name);

    @Query ("select o from OfficeStaff o where o.status = ?1 and o.employeeManager is not null and o.birthDate is not null and MONTH(o.birthDate) = MONTH(?2) and DAY(o.birthDate) = DAY(?2) ")
    public List<OfficeStaff> getBirthDayTodayStaffs (OfficeStaffStatus status, Date current);

    @Query("Select distinct o from OfficeStaff o left join OfficeStaffPayrollLog log on o = log.officeStaff and log.payrollMonth = ?5 left join o.finalSettlement fs left join PayrollManagerNote p on o = p.officeStaff " +
            "left join PaidOffDays pod on o = pod.officeStaff left join EmployeeLoan el on o = el.officeStaff " +
            "left join Repayment r on o = r.officestaff left join TravelDays td on o = td.officeStaff left join AirFareTicket at on at.travelDays = td " +
            "left join o.visaNewRequest visa left join o.selectedTransferDestination std " +
            "where o in ?1 " +
            "and ((log is null) or (" +
            "   (log.monthlyPaymentRule in ?2) and " +
            "   (?3 is NULL or log.payrollAuditTodo = ?3) and " +
            "   (?4 is NULL or log.payrollAccountantTodo = ?4) " +
            "   and (" +
            "   (o.lastModificationDate > log.creationDate) " +
            "   or (fs.lastModificationDate > log.creationDate) " +
            "   or (p.lastModificationDate > log.creationDate) " +
            "   or (pod.lastModificationDate > log.creationDate) " +
            "   or (el.lastModificationDate > log.creationDate) " +
            "   or (r.lastModificationDate > log.creationDate) " +
            "   or (td.lastModificationDate > log.creationDate) " +
            "   or (at.lastModificationDate > log.creationDate) " +
            "   or (visa.lastModificationDate > log.creationDate) " +
            "   or (std.lastModificationDate > log.creationDate) " +
            "   )" +
            ")) "
    )
    List<OfficeStaff> getChangedOfficeStaffs(List<OfficeStaff> officeStaffs, List<MonthlyPaymentRule> monthlyPaymentRules, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, java.sql.Date payrollMonth);


    @Query("Select distinct o from OfficeStaff o inner join OfficeStaffPayrollLog log on o = log.officeStaff and log.monthlyPaymentRule in ?2 and (?3 is NULL or log.payrollAuditTodo = ?3) and (?4 is NULL or log.payrollAccountantTodo = ?4) " +
            "left join OfficeStaffPayrollLog log2 on o = log2.officeStaff and log2.payrollMonth < ?5 " +
            "where o in ?1 and log2.lastModificationDate > log.creationDate")
    List<OfficeStaff> getChangedOfficeStaffsByOldLogs(List<OfficeStaff> officeStaffs, List<MonthlyPaymentRule> monthlyPaymentRules, PayrollAuditTodo auditTodo, PayrollAccountantTodo todo, java.sql.Date payrollMonth);

    @Query("select o from TransferDestination t " +
            "inner join t.officeStaff o " +
            "where t.iban = ?1 " +
            "and o.id != ?2 and o.status = ?3 and o.employeeType in ?4 ")
    List<OfficeStaff> findDuplicateIbanOfficeStaff(String iban, Long officeStaffId, OfficeStaffStatus status, List<OfficeStaffType> employeeTypes);

    @Query("select (count(o) > 0) from OfficeStaff o left join PayrollManagerNote p on (p.officeStaff.id = o.id) " +
            "left join EmployeeLoan l on (l.officeStaff.id = o.id ) " +
            "left join Repayment r on (r.officestaff.id = o.id ) " +
            "where o.id = ?1 and (p.lastModificationDate > ?2 or l.lastModificationDate > ?2 or r.lastModificationDate > ?2 ) ")
    boolean hasNoteOrLoanOrRepaymentAfterDate(Long officeStaffId, Date date);

    @Query("SELECT o.name as name, j.name as jobTitle, o.startingDate as startingDate, o.email as email, m.name as managerName " +
            "FROM OfficeStaff o " +
            "JOIN o.jobTitle j " +
            "JOIN o.employeeManager m " +
            "WHERE o.status = com.magnamedia.module.type.OfficeStaffStatus.ACTIVE " +
            "AND FUNCTION('DAY', o.birthDate) = :dayOfMonth " +
            "AND FUNCTION('MONTH', o.birthDate) = :month " +
            "ORDER BY o.name")
    List<OfficeStaffBirthdayProjection> findActiveOfficeStaffWithBirthday(int dayOfMonth, int month);

    List<OfficeStaffList> findByEmployeeType(OfficeStaffType employeeType);

    List<OfficeStaff> findByEmployeeTypeAndTerminationDateIsNull(OfficeStaffType overseasStaff);

    @Query("select o from OfficeStaff o where o.phoneNumber = :phoneNumber")
    List<OfficeStaff> findByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    List<OfficeStaff> findByEmployeeTypeIn(List<OfficeStaffType> employeeTypes);

    List<OfficeStaff> findByFullNameInArabic(String name);

    OfficeStaff findFirstByEmail(String email);

    Page<OfficeStaff> findByStatus(OfficeStaffStatus staffStatus, Pageable pageable);

    List<OfficeStaff> findByEmail(String email);

    @Query(nativeQuery = true, value = "SELECT d.DEPARTMENTS_ID as departmentId, COUNT(*) as staffCount " +
            "FROM OFFICESTAFFS o " +
            "INNER JOIN OFFICESTAFF_DEPARTMENTS d ON o.ID = d.OFFICE_STAFF_ID " +
            "WHERE o.STATUS = 'ACTIVE' " +
            "GROUP BY d.DEPARTMENTS_ID")
    List<Map<String, Object>> findActiveStaffCountPerDepartment();
}
