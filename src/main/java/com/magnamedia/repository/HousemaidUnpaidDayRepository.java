/*
 * *
 *  *
 *  * <AUTHOR> <<EMAIL>>
 *  * Created At ${DATE}
 *  *
 *  *
 *
 */

package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.HousemaidUnpaidDay;
import com.magnamedia.entity.projection.HousemaidUnpaidDayProjection;
import com.magnamedia.module.type.HousemaidType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/28/2022
 **/

@Repository
public interface HousemaidUnpaidDayRepository extends BaseRepository <HousemaidUnpaidDay> {

    List<HousemaidUnpaidDay> findByActionTakenAndForgivenAndHousemaid_IdIn(Boolean actionTaken, Boolean forgiven, List<Long> housemaidIds);

//    @Query("select h.id as id , h.housemaid.name as housemaidName, h.unpaidDate as unpaidDate,  case when h.forgivenessType = ?1 then 'With Client' else 'Available' end as status from HousemaidUnpaidDay h where h.actionTaken = true and h.forgiven = true order by h.unpaidDate desc, h.lastModificationDate desc")
//    Page<HousemaidUnpaidDayProjection> findAllForgivenRecords(PicklistItem withClientForgivenessType, Pageable pageable);

    @Query("select count(h) from HousemaidUnpaidDay h where h.housemaid = ?1 " +
            "and (h.actionTaken = false or (h.actionTaken = true and h.forgiven = false)) " +
            "and h.unpaidDate between ?2 and ?3")
    Integer findGroup3DaysForHousemaid(Housemaid housemaid, Date from, Date to);

    @Query("select count(h) from HousemaidUnpaidDay h where h.housemaid = ?1 " +
            "and h.actionTaken = true and h.forgiven = true " +
            "and h.unpaidDate between ?2 and ?3 " +
            "and h.forgivenessType = ?4")
    Integer findForgivenDaysForHousemaidByType(Housemaid housemaid, Date from, Date to, PicklistItem forgivenessType);

    @Query("select h from HousemaidUnpaidDay h where h.housemaid = ?1 and h.actionTaken = false and h.forgiven = false")
    List<HousemaidUnpaidDay> findUnForgivenDaysByHousemaid(Housemaid housemaid);

    @Query("select hud from HousemaidUnpaidDay hud " +
            "inner join hud.housemaid h " +
            "where hud.actionTaken = false " +
            "and hud.forgiven = false " +
            "and h.housemaidType = ?1")
    List<HousemaidUnpaidDay> findDismissMVMaids(HousemaidType housemaidType);

    Integer countByHousemaidAndUnpaidDate(Housemaid housemaid, Date date);

    List<HousemaidUnpaidDay> findByHousemaidAndUnpaidDate(Housemaid housemaid, Date date);

}
