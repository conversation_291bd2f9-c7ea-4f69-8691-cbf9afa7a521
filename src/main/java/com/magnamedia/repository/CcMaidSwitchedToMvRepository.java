package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CcMaidSwitchedToMv;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/18/2022
 **/

@Repository
public interface CcMaidSwitchedToMvRepository extends BaseRepository<CcMaidSwitchedToMv> {

    @Query("select c from CcMaidSwitchedToMv c inner join c.housemaid h where c.switchDate between ?1 and ?2 order by c.housemaid.name")
    List<CcMaidSwitchedToMv> getCcMaidSwitchedToMvByPayrollMonth(Date from, Date to);

    @Query("select count(c) from CcMaidSwitchedToMv c where c.confirmed = ?1 and c.switchDate between ?2 and ?3")
    int countByConfirmedAndSwitchDateBetween(<PERSON><PERSON><PERSON> confirmed, Date from, Date end);
}
