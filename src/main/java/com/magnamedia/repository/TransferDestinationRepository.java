package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.TransferDestination;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/2/2020
 **/
@Repository
public interface TransferDestinationRepository extends BaseRepository<TransferDestination> {

    @Query("select t from TransferDestination t " +
            "inner join t.officeStaff o on o.selectedTransferDestination.id = t.id " +
            "where ((o.selfReceiver = true and o.phoneNumber = :phoneNumber) " +
            "or (o.selfReceiver = false and t.phoneNumber = :phoneNumber))")
    List<TransferDestination> getByReceiverPhoneNumber(@Param("phoneNumber") String phoneNumber);

    @Query("select t from TransferDestination t " +
            "inner join t.officeStaff o on o.selectedTransferDestination.id = t.id " +
            "where t.selfReceiver = false and t.name = ?1 and o.status = com.magnamedia.module.type.OfficeStaffStatus.ACTIVE")
    List<TransferDestination> findByNameAndSelfReceiverFalseAndOfficeStaffIsActive(String name);

    @Query("select t from TransferDestination t " +
            "inner join t.officeStaff o on o.selectedTransferDestination.id = t.id " +
            "where t.selfReceiver = false and t.fullNameInArabic = ?1 and o.status = com.magnamedia.module.type.OfficeStaffStatus.ACTIVE")
    List<TransferDestination> findByFullNameInArabicAndSelfReceiverFalseAndOfficeStaffIsActive(String name);

    @Query("select t from TransferDestination t" +
            "    inner join  t.officeStaff o" +
            "    where o.employeeType = 'OVERSEAS_STAFF' and o.terminationDate is null" +
            "    and t.id = o.selectedTransferDestination.id")
    List<TransferDestination> findBankTransferDestinationsForActiveOverseasStaff();

    @Query("select t from TransferDestination t where t.swiftCode is not null and t.swiftCode != ''")
    List<TransferDestination> findAllWithSwiftCode();

    @Query("select t from TransferDestination t " +
            "inner join t.officeStaff o " +
            "where t.receiveMoneyMethod = com.magnamedia.module.type.ReceiveMoneyMethod.BANK_TRANSFER " +
            "and t.id = o.selectedTransferDestination.id " +
            "and t.swiftCode is not null and t.swiftCode != '' " +
            "and (t.branchCountryName is null or t.branchCountryName = '' " +
            "or t.branchCityName is null or t.branchCityName = '' " +
            "or t.bankName is null or t.bankName = '')")
    List<TransferDestination> findBankTransferDestinationsWithMissingBranchDetails();

}
