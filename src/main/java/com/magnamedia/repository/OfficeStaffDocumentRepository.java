package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.OfficeStaffCandidate;
import com.magnamedia.entity.OfficeStaffDocument;
import com.magnamedia.module.type.OfficeStaffDocumentType;

import java.util.List;

import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> <PERSON><PERSON>i <<EMAIL>>
 * Created at Nov 4, 2017
 */
@Repository
public interface OfficeStaffDocumentRepository extends BaseRepository<OfficeStaffDocument> {

    public List<OfficeStaffDocument> findByOfficeStaff(OfficeStaff id);

    public List<OfficeStaffDocument> findByOfficeStaffAndType(OfficeStaff id, OfficeStaffDocumentType type);

    public OfficeStaffDocument findTopByOfficeStaffAndTypeAndNameOrderByCreationDateDesc(OfficeStaff id, OfficeStaffDocumentType type, String name);

    public List<OfficeStaffDocument> findByOfficeStaffCandidateAndType(OfficeStaffCandidate id, OfficeStaffDocumentType type);

}