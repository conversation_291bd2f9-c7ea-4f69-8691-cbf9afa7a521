package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PayrollAccountantTodo;
import com.magnamedia.entity.PayrollAuditTodo;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.BankTransferProjection;
import com.magnamedia.entity.projection.PensionAuthorityProjection;
import com.magnamedia.entity.projection.payrollAudit.SalariesByTeamProjection;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.SalaryCurrency;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.sql.Date;
import java.util.List;

public interface OfficeStaffPayrollLogRepository extends BaseRepository<OfficeStaffPayrollLog> {

    OfficeStaffPayrollLog findFirstByOfficeStaffAndPayrollAccountantTodo(OfficeStaff officeStaff, PayrollAccountantTodo todo);

    List<OfficeStaffPayrollLog> findByPayrollAccountantTodoAndTransferredTrue(PayrollAccountantTodo todo);

    List<OfficeStaffPayrollLog> findByPayrollAccountantTodo_IdInAndTransferredTrue(List<Long> todosIds);

    @Query("select l.id from OfficeStaffPayrollLog l where l.payrollAccountantTodo = ?1 and l.transferred = ?2 and l.willBeIncluded = ?3")
    List<Long> findByPayrollAccountantTodoAndTransferredAndWillBeIncluded(PayrollAccountantTodo todo, Boolean transferred, Boolean willBeIncluded);

    @Query("select l.id from OfficeStaffPayrollLog l where l.payrollAccountantTodo = ?1 and l.transferred = ?2 and l.willBeIncluded = ?3 and l.previouslyUnpaidSalaries > 0.0")
    List<Long> findByPayrollAccountantTodoAndTransferredAndWillBeIncludedAndPreviouslyUnpaidSalaries(PayrollAccountantTodo todo, Boolean transferred, Boolean willBeIncluded);

    @Query("select l from OfficeStaffPayrollLog l where l.officeStaff.id in ?1 and l.transferred = ?2 and l.willBeIncluded = ?3 and l.forEmployeeLoan = ?4")
    List<OfficeStaffPayrollLog> findByTransferredAndWillBeIncludedAndOfficeStaffList(List<Long> staffs, Boolean transferred, Boolean willBeIncluded, Boolean forEmployeeLoan);

    @Query("select l from OfficeStaffPayrollLog l where l.officeStaff = ?1 and l.transferred = ?2 and l.willBeIncluded = ?3")
    List<OfficeStaffPayrollLog> findByTransferredAndWillBeIncludedAndOfficeStaff(OfficeStaff staff, Boolean transferred, Boolean willBeIncluded);

    List<OfficeStaffPayrollLog> findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByOfficeStaffName(PayrollAccountantTodo todo, Date payrollMonth);

    @Query("select sum(log.totalSalary) from OfficeStaffPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.willBeIncluded = true and log.payrollMonth = ?2")
    Double sumByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonth(PayrollAccountantTodo todo, Date payrollMonth);

    List<OfficeStaffPayrollLog> findByPayrollAccountantTodoAndTransferredFalseAndPayrollMonthAndOfficeStaffInOrderByOfficeStaffName(PayrollAccountantTodo todo, Date payrollMonth, List<OfficeStaff> staffs);

    @Query("select min(log.payrollMonth) from OfficeStaffPayrollLog log")
    java.sql.Date findMinimumLogDate();

    @Query("select log from OfficeStaffPayrollLog log left join log.payrollAccountantTodo todo where log.officeStaff = ?1 and " +
            "log.payrollMonth >= ?2 and log.payrollMonth <= ?3 and (log.transferred = true or (todo is not null and todo.completed=false))")
    List<OfficeStaffPayrollLog> findByOfficeStaffWithinMonthAndTransferred(OfficeStaff officeStaff, java.sql.Date from, java.sql.Date to);

    @Query("select log from OfficeStaffPayrollLog log left join log.payrollAccountantTodo todo where log.officeStaff = ?1 " +
            " order by log.payrollMonth" )
    List<OfficeStaffPayrollLog> findByOfficeStaffWithinMonth(OfficeStaff officeStaff);

    @Query("select log from OfficeStaffPayrollLog log left join log.payrollAccountantTodo todo where log.officeStaff = ?1 and " +
            " log.transferred = ?2 order by log.payrollMonth")
    List<OfficeStaffPayrollLog> findByOfficeStaffWithinMonthAndTransferredFalse(OfficeStaff officeStaff, boolean transferred);


    @Query("select count(log) from OfficeStaffPayrollLog log where log.payrollAccountantTodo = ?1 and " +
            "log.transferred = false and log.willBeIncluded = true")
    int countUnCompletedStaffTransfer(PayrollAccountantTodo accountantTodo);

    @Query("SELECT SUM(o.totalSalary) FROM OfficeStaffPayrollLog o INNER JOIN o.officeStaff s WHERE s.salaryCurrency = ?1 AND o.payrollMonth = ?2 AND s.employeeType = ?3")
    Double sumTotalSalaryByMonthAndEmployeeTypeAndCurrency(SalaryCurrency salaryCurrency, java.sql.Date payrollMonth, OfficeStaffType employeeType);

    @Query("SELECT COUNT(o) FROM OfficeStaffPayrollLog o INNER JOIN o.officeStaff s WHERE o.payrollMonth = ?1 AND s.employeeType = ?2")
    Integer countOfficeStaffByMonthAndEmployeeType(java.sql.Date payrollMonth, OfficeStaffType employeeType);

    @Query(nativeQuery = true,
            value = "select o.TEAM_ID as team," +
                    "     sum(case WHEN p.PAYROLL_MONTH = ?1 and o.SALARY_CURRENCY = 'USD' THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as currentMonthAmountInUSD, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 1 MONTH) and o.SALARY_CURRENCY = 'USD' THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmountInUSD, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 2 MONTH) and o.SALARY_CURRENCY = 'USD' THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmountInUSD, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = ?1  THEN 1 " +
                    "              else 0 " +
                    "         end) as currentMonthCount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 1 MONTH)  THEN 1 " +
                    "              else 0 " +
                    "         end) as prevOneMonthCount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 2 MONTH) THEN 1 " +
                    "              else 0 " +
                    "         end) as prevTowMonthCount, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = ?1 and o.SALARY_CURRENCY = 'AED' THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as currentMonthAmountInAED, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 1 MONTH) and o.SALARY_CURRENCY = 'AED' THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevOneMonthAmountInAED, " +
                    "     sum(case WHEN p.PAYROLL_MONTH = DATE_SUB(?1,INTERVAL 2 MONTH) and o.SALARY_CURRENCY = 'AED' THEN p.TOTAL_SALARY " +
                    "              else 0 " +
                    "         end) as prevTowMonthAmountInAED " +
                    "from  OFFICESTAFFPAYROLLLOGS p " +
                    "inner join OFFICESTAFFS o on o.ID = p.OFFICE_STAFF_ID " +
                    "where p.PAYROLL_MONTH >= DATE_SUB(?1,INTERVAL 2 MONTH) AND p.PAYROLL_MONTH <= ?1 " +
                    "   AND o.EMPLOYEE_TYPE = ?2 " +
                    "GROUP BY o.TEAM_ID")
    List<SalariesByTeamProjection> getSalariesByTeam(java.sql.Date payrollMonth, OfficeStaffType employeeType);

    @Query("SELECT o FROM OfficeStaffPayrollLog o WHERE o.payrollAccountantTodo = ?1 AND o.transferred = false and o.willBeIncluded = true")
    List<OfficeStaffPayrollLog> findBankTransferRecords(PayrollAccountantTodo accountantTodo);

    @Query("SELECT o FROM OfficeStaffPayrollLog o WHERE o.payrollAccountantTodo = ?1 AND o.transferred = false")
    List<OfficeStaffPayrollLog> findPensionAuthorityRecords(PayrollAccountantTodo accountantTodo);

    @Query("SELECT log FROM OfficeStaffPayrollLog log " +
            "INNER JOIN log.officeStaff o " +
            "WHERE log.transferred=true AND log.payrollMonth= :payrollMonth " +
            "AND log.payrollAccountantTodo IS NOT NULL " +
            "AND o.id IN :employeeIds")
    List<OfficeStaffPayrollLog> findReadyToGeneratePayslipsLogs(@Param("payrollMonth") java.sql.Date payrollMonth,
                                                                @Param("employeeIds") List<Long> employeesIds);

    @Query("SELECT log FROM OfficeStaffPayrollLog log " +
            "INNER JOIN log.officeStaff o " +
            "WHERE log.transferred=true AND log.payrollMonth= :payrollMonth " +
            "AND log.payrollAccountantTodo IS NOT NULL")
    List<OfficeStaffPayrollLog> findReadyToGeneratePayslipsLogs(@Param("payrollMonth") java.sql.Date payrollMonth);

    @Query("select s.id from OfficeStaffPayrollLog sl inner join sl.officeStaff s where sl.payrollMonth=?1 and sl.transferred=false")
    List<Long> findByPayrollMonthAndTransferredFalse(java.sql.Date payrollMonth);

    @Query("select sl from OfficeStaffPayrollLog sl where sl.officeStaff = ?1 and sl.payrollMonth in ?2 and sl.transferred=false")
    List<OfficeStaffPayrollLog> findByPayrollMonths(OfficeStaff officeStaff, List<Date> payrollMonths);

    OfficeStaffPayrollLog findTopByOfficeStaffAndPayrollMonthAndForEmployeeLoanFalse(OfficeStaff staff, Date payrollMonth);

    @Query("select l from OfficeStaffPayrollLog l where l.creator.id = ?1 and l.creationDate >= ?2 and l.creationDate <= ?3 and l.transferred = false")
    List<OfficeStaffPayrollLog> findByCreatorAndCreationDateAndTransferred(Long creator_id, java.util.Date sameDay, java.util.Date oneDayAfter);

    @Query("select distinct log.officeStaff.id from OfficeStaffPayrollLog log where log.payrollAccountantTodo = ?1 and log.transferred = false and log.willBeIncluded = true and log.payrollMonth = ?2 order by log.officeStaff.name ")
    List<Long> findDistinctOfficeStaffIdByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByOfficeStaffName(PayrollAccountantTodo accountantTodo, java.sql.Date payrollMonth);

//    @Query("select o from OfficeStaffPayrollLog o where o.totalSalary <> coalesce( (select o2.totalSalary from OfficeStaffPayrollLog o2 where o2.officeStaff = o.officeStaff and o2.payrollMonth = ?2 and o2.officeStaff in ?3), 0) " +
//            "and o.payrollMonth = ?1 and o.officeStaff in ?3")
//    List<OfficeStaffPayrollLog> findLogsBySalaryChanged(Date currentPayrollMonth, Date previousPayrollMonth, List<OfficeStaff> staffs);

    @Query("SELECT SUM(o.totalSalary) FROM OfficeStaffPayrollLog o WHERE o.officeStaff = ?1 and o.payrollMonth < ?2 and o.willBeIncluded = false and o.transferred = false")
    Double findRemainingOnHoldSalary(OfficeStaff staff, Date payrollMonth);

    @Query("select o from OfficeStaffPayrollLog o inner join o.officeStaff s where o.payrollAccountantTodo = ?1 and o.transferred = false and o.willBeIncluded = true and s.status <> ?2")
    List<OfficeStaffPayrollLog> findByPayrollAccountantTodoAndNotTerminated(PayrollAccountantTodo todo, OfficeStaffStatus status);

    List<OfficeStaffPayrollLog> findByMonthlyPaymentRuleAndLogStatus(MonthlyPaymentRule rule, OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus logStatus);

    List<OfficeStaffPayrollLog> findByIdIn(List<Long> ids);

    @Query("select o from OfficeStaffPayrollLog o where o.transferred = false and o.swiftCode is not null")
    List<OfficeStaffPayrollLog> findUnTransferredWithSwiftCode();
}
