package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

public enum HazardousCondition implements LabelValueEnum {
    CC_MAID_CLIENT_COUNT("Comparing CC maids without clients count with the total CC maids number in the current payroll."),
    CC_MAID_SALARY_COMPARISON("Comparing CC maids` total net salary in the current month with the previous month."),
    LOAN_REPAYMENT_BALANCE_COMPARISON("Comparing total loan repayment with total loan balance for all maids."),
    MV_MAID_SALARY_CLIENT_PAYMENT("Comparing MV maids' salaries with the total received payments from MV clients"),
    GRP_EARNINGS_COMPARISON("Comparing total earnings under GRP 2 with the total earnings of GRP 1. (same for GRP6 with GRP5).");

    private final String label;

    private HazardousCondition(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}

