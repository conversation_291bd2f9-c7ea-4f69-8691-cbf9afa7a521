package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.repository.HousemaidRepository;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class HousemaidFillReplacementSalaryStartDateJob implements MagnamediaJob {
    @Override
    public void run(Map<String, ?> parameters) {
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        Integer PolicyDuration = 21;

        Integer PolicyDurationLiveOut = 7;

        try {
            PolicyDuration = Integer.parseInt(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_START_DATE_N_WEEKS_POLICY_DURATION));
        } catch (Exception ignored) {
        }

        try {
            PolicyDurationLiveOut = Integer.parseInt(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION));
        } catch (Exception ignored) {
        }

        List<Housemaid> targetMaids = housemaidRepository.findByOldHousemaidTypeAndHousemaidTypeNotAndReplacementSalaryStartDateIsNull(HousemaidType.MAID_VISA, HousemaidType.MAID_VISA);

        for (Housemaid maid : targetMaids) {
            NewRequest visaNewRequest = maid.getVisaNewRequest();
            Date medicalTestDate = null;
            if (visaNewRequest != null && visaNewRequest.getAttachment("medicalCertificate") != null) {
                medicalTestDate = visaNewRequest.findTaskMoveOutDate("Pending medical certificate approval from DHA");
                if (medicalTestDate == null) continue;
                HistorySelectQuery<Housemaid> historySelectQuery = new HistorySelectQuery(Housemaid.class);
                historySelectQuery.filterBy("id", "=", maid.getId());
                historySelectQuery.filterByChanged("oldHousemaidType");
                historySelectQuery.sortBy("lastModificationDate", false, true);
                historySelectQuery.setLimit(1);

                List<Housemaid> history = historySelectQuery.execute();
                if (history == null || history.isEmpty()) {
                    continue;
                }
                Date housemaidUpdatedDate = history.get(0).getLastModificationDate();

                // Check if housemaid status was CC before Switching to MV
                HistorySelectQuery<Housemaid> historyQuery = new HistorySelectQuery(Housemaid.class);
                historyQuery.filterBy("id", "=", maid.getId());
                historyQuery.filterBy("lastModificationDate", "<", housemaidUpdatedDate);
                historyQuery.filterByChanged("housemaidType");
                historyQuery.filterBy("housemaidType", "<>", HousemaidType.MAID_VISA);
                List<Housemaid> historyHousemaid = historyQuery.execute();

                if (historyHousemaid == null || historyHousemaid.isEmpty()) { // maid status was not changed from CC to MV

                    if (housemaidUpdatedDate.after(medicalTestDate)) {
                        medicalTestDate = housemaidUpdatedDate;
                    }

                    if (maid.getLiveOut()) {
                        medicalTestDate = DateUtil.addDays(medicalTestDate, PolicyDurationLiveOut);
                    } else {
                        medicalTestDate = DateUtil.addDays(medicalTestDate, PolicyDuration);
                    }
                    maid.setReplacementSalaryStartDate(medicalTestDate);

                } else { // maid was CC before switching to MV
                    maid.setReplacementSalaryStartDate(housemaidUpdatedDate);
                }

                housemaidRepository.save(maid);
            }
        }


    }
}
