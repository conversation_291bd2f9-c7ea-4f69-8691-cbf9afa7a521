package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.service.payroll.generation.AccountantToDoService;

import java.util.List;
import java.util.Map;

public class CreatePensionAuthorityAccountantToDo implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> map) {
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.filterBy("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI)
                .and("status", "=", OfficeStaffStatus.ACTIVE);

        List<OfficeStaff> officeStaffs = query.execute();
        if(!officeStaffs.isEmpty()) {
            Setup.getApplicationContext().getBean(AccountantToDoService.class)
                    .createPensionAuthorityToDo(officeStaffs);
        }
    }
}
