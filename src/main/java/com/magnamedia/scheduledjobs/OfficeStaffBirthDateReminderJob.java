package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.core.type.SmsReceiverType;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.message.MessagingService;
import org.springframework.beans.factory.annotation.Autowired;

import static com.magnamedia.controller.PaySlipsController.normalizePhoneNumber;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 4/15/2021
 **/
public class OfficeStaffBirthDateReminderJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> parameters) {
        OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);

        List<OfficeStaff> officeStaffList = officeStaffRepository.getBirthDayTodayStaffs(OfficeStaffStatus.ACTIVE, new Date(System.currentTimeMillis()));

        Map<Long, List<OfficeStaff>> managerMap = new HashMap<>();

        OfficeStaff cooStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(Long.parseLong(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CFO_ID)));

        String JadBirthDayEmail = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_BIRTHDAY_REMINDER_JAD_EMAIL);

        for (OfficeStaff staff : officeStaffList) {
            OfficeStaff directManager = staff.getEmployeeManager();
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employees_names", staff.getName());
            paramValues.put("employee_email", staff.getEmail());
            paramValues.put("employee_phone_number", staff.getPhoneNumber());
            paramValues.put("direct_manager", directManager.getName());

            if (directManager != null) {
                if (cooStaff != null && staff.getFinalManager().getId().equals(cooStaff.getId())
                        && !directManager.getId().equals(cooStaff.getId())) {
//                    Setup.getApplicationContext().getBean(MessagingService.class)
//                            .send("Payroll_OfficeStaffs_BirthDay_Reminder", "BirthDay Reminder SMS",
//                                    null, cooStaff, paramValues, staff, null, cooStaff);
                    Setup.getApplicationContext().getBean(MessagingService.class).send(EmailHelper.getMailRecipients(JadBirthDayEmail), null
                            , "Payroll_OfficeStaffs_BirthDay_Reminder", "BirthDay Reminder SMS", paramValues, new ArrayList<>(), null);
//                    Setup.getApplicationContext().getBean(MessageTemplateService.class).sendMessageOrEmail(
//                            "BirthDay Reminder SMS",
//                            normalizePhoneNumber(cooStaff.getPhoneNumber()),
//                            JadBirthDayEmail,
//                            SmsReceiverType.Office_Staff,
//                            cooStaff.getId(),
//                            cooStaff.getName(),
//                            "OfficeStaff",
//                            "Payroll_OfficeStaffs_BirthDay_Reminder",
//                            "Payroll_OfficeStaffs_BirthDay_Reminder_sms",
//                            paramValues,
//                            null, cooStaff.getPreferredCommunicationMethod(),
//                            false);
                }

                String directManagerEmail = directManager.getEmail();

                if (cooStaff.getId().equals(directManager.getId()))
                    directManagerEmail = JadBirthDayEmail;

//                Setup.getApplicationContext().getBean(MessagingService.class)
//                        .send("Payroll_OfficeStaffs_BirthDay_Reminder", "BirthDay Reminder SMS",
//                                null, directManager, paramValues, staff, null, directManager);
//                Setup.getApplicationContext().getBean(MessagingService.class).send(EmailHelper.getMailRecipients(directManagerEmail), null
//                        , "Payroll_OfficeStaffs_BirthDay_Reminder", "BirthDay Reminder SMS", paramValues, new ArrayList<>(), null);
                Setup.getApplicationContext().getBean(MessagingService.class).send("Payroll_OfficeStaffs_BirthDay_Reminder", "BirthDay Reminder SMS", null,
                        directManager, paramValues, null, null, null, directManagerEmail);
//                Setup.getApplicationContext().getBean(MessageTemplateService.class).sendMessageOrEmail(
//                        "BirthDay Reminder SMS",
//                        normalizePhoneNumber(directManager.getPhoneNumber()),
//                        directManagerEmail,
//                        SmsReceiverType.Office_Staff,
//                        directManager.getId(),
//                        directManager.getName(),
//                        "OfficeStaff",
//                        "Payroll_OfficeStaffs_BirthDay_Reminder",
//                        "Payroll_OfficeStaffs_BirthDay_Reminder_sms",
//                        paramValues,
//                        null, directManager.getPreferredCommunicationMethod(),
//                        true);
            }
        }

    }
}
