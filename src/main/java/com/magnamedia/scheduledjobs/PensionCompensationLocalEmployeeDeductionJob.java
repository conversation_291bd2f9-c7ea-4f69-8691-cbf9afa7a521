package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.PayrollManagerNoteRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jul 3, 2019
 *         Jirra ACC-800 ACC-917
 */

public class PensionCompensationLocalEmployeeDeductionJob implements MagnamediaJob {

    private PicklistRepository picklistRepository;

    private PicklistItemRepository picklistItemRepository;

    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    public PensionCompensationLocalEmployeeDeductionJob() {
        picklistRepository = Setup.getRepository(PicklistRepository.class);
        picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.run();
    }

    public void run() {

        String localStaffTagName = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_lOCAL_STAFF_NATIONALITY_TAG);

        List<PicklistItem> localNationalitiesItems =
                picklistRepository.findByCode(Picklist.NATIONALITIES)
                        .getItemsWithTag(localStaffTagName);
        PicklistItem deductionReason
                = picklistItemRepository.findByListAndCodeIgnoreCase(
                picklistRepository.findByCode("DeductionReasons"),
                PicklistItem.getCode("pension_compensation"));
        if (localNationalitiesItems.isEmpty())
            throw new RuntimeException("There is no local nationalities.");
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.filterBy("nationality", "in", localNationalitiesItems);
        List<OfficeStaff> staffs = query.execute();
        List<PayrollManagerNote> notes = new ArrayList<>();
        for (OfficeStaff staff : staffs) {
            //skip if the doNotDeductContribution field is set true
            if(staff.getDoNotDeductContribution())
                continue;
            PayrollManagerNote note = new PayrollManagerNote();
            note.setOfficeStaff(staff);
            note.setAmount(staff.getBasicSalary() / 20);
            note.setNoteReasone("Auto Added Deduction due to Pension Compensation.");
            note.setNoteDate(new Date());
            note.setNoteType(PayrollManagerNote.ManagerNoteType.DEDUCTION);
            note.setDeductionReason(deductionReason);
            notes.add(note);
        }
        payrollManagerNoteRepository.save(notes);
    }
}
