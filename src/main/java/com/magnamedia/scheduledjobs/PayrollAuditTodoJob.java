package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.PayrollAuditTodo;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.service.payroll.generation.PayrollAuditTodoService;

import java.sql.Date;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/21/2020
 * Should run every day
 **/
public class PayrollAuditTodoJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> parameters) {

        //generate all Audit To-do for the monthly rules that lock date = today
        this.generatePayrollAuditTodos();

    }

    public void generatePayrollAuditTodos() {

        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("lockDate", "=", new Date(System.currentTimeMillis()));
        selectQuery.filterBy("finished", "=", false);
        selectQuery.filterBy("auditingFinished", "=", false);

        for (MonthlyPaymentRule rule : selectQuery.execute()) {
            try {
                for (PaymentRuleEmployeeType employeeType : rule.getEmployeeTypeList()) {
                    Setup.getApplicationContext().getBean(PayrollAuditTodoService.class)
                            .createPayrollAuditTodo(rule, employeeType);
                    if(employeeType.equals(PaymentRuleEmployeeType.HOUSEMAIDS) && PaymentRulePaymentMethod.WPS.equals(rule.getPaymentMethod())) {
                        // insert a new background task to createExcludedFromProfileManuallyTodo
                        Setup.getApplicationContext()
                                .getBean(BackgroundTaskService.class)
                                .addDirectCallBackgroundTaskForEntity(
                                        "createExcludedFromProfileManuallyTodo", "payrollAuditTodoService", "payroll",
                                        "createExcludedFromProfileManuallyTodo",
                                        rule.getEntityType(), rule.getId(), false,
                                        false, new Class[]{Long.class}, new Object[]{rule.getId()});

                        //create CC switching to MV audit to-do in case of Housemaid and Primary
                        if (PayrollType.PRIMARY.equals(rule.getPayrollType())) {
                            // insert a new background task to for create the to-do
                            Setup.getApplicationContext()
                                    .getBean(BackgroundTaskService.class)
                                    .addDirectCallBackgroundTaskForEntity(
                                            "createCcSwitchingToMvTodo", "payrollAuditTodoService", "payroll",
                                            "createCcSwitchingToMvTodo",
                                            rule.getEntityType(), rule.getId(), false,
                                            false, new Class[]{java.sql.Date.class}, new Object[]{rule.getPayrollMonth()});
                        }
                    }
                }
            } catch (Throwable e) {
                logger.log(Level.SEVERE, "Error while creating audit todo for monthly rule #" + rule.getId(), e);
            }
        }
    }
}
