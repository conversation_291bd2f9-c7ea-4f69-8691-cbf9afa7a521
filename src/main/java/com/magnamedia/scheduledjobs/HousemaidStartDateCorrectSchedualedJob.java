package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.HousemaidRepository;
import org.joda.time.LocalDate;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on July 19 2018
 */
public class HousemaidStartDateCorrectSchedualedJob implements MagnamediaJob {

    private HousemaidRepository housemaidRepository;

    private TemplateEngine templateEngine;

    private MailService mailService;

    private boolean PolicyEnabled = true;

    private Integer PolicyDuration = 3;

    private Integer PolicyDurationLiveOut = 7;


    private Date PolicyCutoffDate;

    private static final Logger LOGGER =
            Logger.getLogger(HousemaidStartDateCorrectSchedualedJob.class.getName());

    public HousemaidStartDateCorrectSchedualedJob() {
        housemaidRepository = Setup.getRepository(HousemaidRepository.class);
        templateEngine = Setup.getApplicationContext().getBean(TemplateEngine.class);
        mailService = Setup.getMailService();
    }

    @Override
    public void run(Map<String, ?> map) {
        this.run();
    }

    public void run() {

        LocalDate dt = new LocalDate();
        try {
            PolicyCutoffDate = DateUtil.parseDateDashed("2018-07-15");

            PolicyEnabled = Boolean.parseBoolean(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_START_DATE_N_WEEKS_POLICY_ENABLED))
                    || Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_START_DATE_N_WEEKS_POLICY_ENABLED).equals("1");

            PolicyDuration = Integer.parseInt(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_START_DATE_N_WEEKS_POLICY_DURATION));

            PolicyDurationLiveOut = Integer.parseInt(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_START_DATE_N_DAYS_LIVE_OUT_POLICY_DURATION));


            PolicyCutoffDate = DateUtil.parseDateDashed(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_START_DATE_N_WEEKS_POLICY_CUTOFF_DATE));

            if (PolicyEnabled) {

                List<Housemaid> maids = getTargetHousemaids();
                List<Housemaid> maidsToUpdate = new ArrayList<>();
                List<Housemaid> maidsNotUpdated = new ArrayList<>();
                for (Housemaid maid : maids) {
                    NewRequest visaNewRequest = maid.getVisaNewRequest();
                    Date medicalTestDate = null;
                    if (visaNewRequest != null && visaNewRequest.getAttachment("medicalCertificate") != null) {
                        medicalTestDate = visaNewRequest.findTaskMoveOutDate("Pending medical certificate approval from DHA");

                        if (medicalTestDate == null)
                            continue;

                        Date medicalTestDatePlusWeeks = null;
                        if (maid.getLiveOut()) {
                            medicalTestDatePlusWeeks = new LocalDate(medicalTestDate).plusDays(PolicyDurationLiveOut).toDate();
                        } else {
                            medicalTestDatePlusWeeks = new LocalDate(medicalTestDate).plusDays(PolicyDuration).toDate();
                        }
                        if (!medicalTestDatePlusWeeks.after(dt.toDate())) {
                            if (medicalTestDatePlusWeeks.before(PolicyCutoffDate))
                                maid.setStartDate(PolicyCutoffDate);
                            else
                                maid.setStartDate(medicalTestDatePlusWeeks);
                            maidsToUpdate.add(maid);
                            housemaidRepository.save(maid);
                        }
                    } else
                        maidsNotUpdated.add(maid);
                }

//                housemaidRepository.save(maidsToUpdate);
                senToMail(maidsToUpdate, maidsNotUpdated);
            }
        } catch (Exception e) {

            StringWriter errors = new StringWriter();
            e.printStackTrace(new PrintWriter(errors));
            String sb = errors.toString();

            Map<String, Object> params = new HashMap<>();
            params.put("title", "Updated Housemaids List Report Error");
            params.put("tableData", sb);
            //for local test
            Context context = new Context();
            context.setVariables(params);
            String s = templateEngine.process("DataCorrectionandIntegrity", context);

            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_ERROR_EMAIL);
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

            if (recipients.size() > 0) {
                mailService.sendEmail(recipients,
                        new TemplateEmail(
                                "Exception in Start Date N Weeks Policy for: " + DateUtil.formatFullDate(dt.toDate()),
                                "DataCorrectionandIntegrity", params), null);
            }
        }
    }

    public List<Housemaid> getTargetHousemaids() {

        List<Housemaid> maids;
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy("startDate", "IS NULL", null);
        //jirra ACC-1223
        //Payroll start and end date
        LocalDate dt = new LocalDate();

        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
        query.filterBy(PayrollGenerationLibrary.getPayrollMaidsFilter(
                payrollStart.toDate(), payrollEnd.toDate()));
//        query.filterBy(
//                new SelectFilter()
//                        .or("status", "=", HousemaidStatus.WITH_CLIENT)
//                        .or("status", "=", HousemaidStatus.VIP_RESERVATIONS)
//                        .or("status", "=", HousemaidStatus.AVAILABLE)
//                        .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
//                        .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
//                        .or("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
//                        .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
//                        .or("status", "=", HousemaidStatus.ON_VACATION)
//                        .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
//                        .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
//                        .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
//        );
//        query.filterBy(
//                new SelectFilter()
//                        .or("excludedFromPayroll", "=", false)
//                        .or("excludedFromPayroll", "IS NULL", null)
//        );
        maids = query.execute();
        return maids;
    }

    public void senToMail(List<Housemaid> maids, List<Housemaid> maidsWithNullLanded) {

        LocalDate dt = new LocalDate();

        StringBuilder sb = new StringBuilder("");
        sb.append("<div>");
        //Mail Header
        sb.append("<h2>").append("Updated Housemaids List").append("</h2>");

        sb.append("<h2>The result count is: ")
                .append(maids.size()).append("</h2>");
        //Tablecontent
        sb.append("<table border=\"1\" style=\"border-collapse:collapse;text-align:center\">");
        sb.append("<tr>");

        sb.append("<th>");
        sb.append("Housemaid Name");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Status");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Start Date");
        sb.append("</th>");

        for (Housemaid maid : maids) {
            sb.append("<tr>");

            sb.append("<td>");
            sb.append(maid.getName());
            sb.append("</td>");
            sb.append("<td>");
            sb.append(maid.getStatus());
            sb.append("</td>");
            sb.append("<td>");
            sb.append(DateUtil.formatFullDate(maid.getStartDate()));
            sb.append("</td>");

            sb.append("</tr>");
        }
        sb.append("</tr>");
        sb.append("</table>");
        sb.append("</div>");


        sb.append("<br>");
        sb.append("<br>");
        sb.append("<div>");
        //Mail Header
        sb.append("<h2>").append("Housemaids List could not be Updated becuase of null landedInDubai").append("</h2>");

        sb.append("<h2>The result count is: ")
                .append(maidsWithNullLanded.size()).append("</h2>");
        //Tablecontent
        sb.append("<table border=\"1\" style=\"border-collapse:collapse;text-align:center\">");
        sb.append("<tr>");

        sb.append("<th>");
        sb.append("Housemaid ID");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Housemaid Name");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Status");
        sb.append("</th>");

        for (Housemaid maid : maidsWithNullLanded) {
            sb.append("<tr>");

            sb.append("<td>");
            sb.append(maid.getId());
            sb.append("</td>");
            sb.append("<td>");
            sb.append(maid.getName());
            sb.append("</td>");
            sb.append("<td>");
            sb.append(maid.getStatus());
            sb.append("</td>");

            sb.append("</tr>");
        }
        sb.append("</tr>");
        sb.append("</table>");
        sb.append("</div>");

        Map<String, Object> params = new HashMap<>();
        params.put("title", "Updated Housemaids List Report");
        params.put("tableData", sb.toString());
        //for local test
        Context context = new Context();
        context.setVariables(params);
        String s = templateEngine.process("DataCorrectionandIntegrity", context);

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_START_DATE_N_WEEKS_POLICY_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

        if (recipients.size() > 0) {
            mailService.sendEmail(recipients,
                    new TemplateEmail(
                            "Start Date N Weeks Policy for: " + DateUtil.formatFullDate(dt.toDate()),
                            "DataCorrectionandIntegrity", params), null);
        }

    }
}
