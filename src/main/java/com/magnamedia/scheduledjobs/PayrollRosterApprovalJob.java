package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.service.payroll.generation.newversion.PayrollRosterApprovalService;

import java.util.Map;

public class PayrollRosterApprovalJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> map) {
        PayrollRosterApprovalService rosterApprovalService =
                Setup.getApplicationContext().getBean(PayrollRosterApprovalService.class);
        try {
//            rosterApprovalService.sendApprovalMails(false, true);
//            rosterApprovalService.sendApprovalMails(false, false);

            // insert a new background task to sendApprovalMails FOR EMARATI
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "sendApprovalMails", "payrollRosterApprovalService", "payroll",
                            "sendApprovalMails",
                            null, null, false,
                            false, new Class[]{Boolean.class, Boolean.class}, new Object[]{false,true});

            // insert a new background task to sendApprovalMails FOR OTHERS
            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "sendApprovalMails", "payrollRosterApprovalService", "payroll",
                            "sendApprovalMails",
                            null, null, false,
                            false, new Class[]{Boolean.class, Boolean.class}, new Object[]{false,false});
        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while sending roster emails", false);
        }
    }
}
