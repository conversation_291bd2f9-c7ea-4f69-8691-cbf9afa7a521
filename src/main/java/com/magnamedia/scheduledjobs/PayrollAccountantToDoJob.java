package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.service.payroll.generation.AccountantToDoService;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * Creation Date 19/06/2020
 * Should run every day at 6 A.M
 */
public class PayrollAccountantToDoJob implements MagnamediaJob {

    @Override
    public void run(Map<String, ?> map) {
        SelectQuery<MonthlyPaymentRule> query = new SelectQuery<>(MonthlyPaymentRule.class);
        query.filterBy("paymentDate", "<=", DateUtil.getDayEnd(new Date()))
                .and("paymentDate", ">=", DateUtil.getDayStart(new Date()))
                .and("finished", "=", false)
                .and("auditingFinished", "=", true);

        for(MonthlyPaymentRule rule: query.execute()) {
            try {

                Setup.getApplicationContext().getBean(AccountantToDoService.class)
                        .createAccountantToDoBasedOnPaymentRuleNew(rule);
            } catch (Exception ex) {
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Generate todo for rule #" + rule.getId(), false);
            }
        }
    }
}
