package com.magnamedia.extra;

public enum CardStatus {

    REQUEST_SENT_TO_ANSARI("Request Sent to Ansari"),
    REQUEST_RECEIVED("Request Received"),
    PROCESSING_REQUEST("Processing Request"),
    CARD_PREPARED_FOR_DISPATCH("Card Prepared for Dispatch"),
    CARD_IN_TRANSIT("Card in Transit"),
    CARD_READY_FOR_PICKUP("Card Ready for Pickup"),
    CARD_COLLECTED("Card Collected"),
    CARD_CANCELED("Card Canceled"),
    REPLACED_REQUEST_SENT_TO_ANSARI("Replaced Request Sent to Ansari"),
    REPLACED_REQUEST_RECEIVED("Replaced Request Received"),
    REPLACED_CARD_PROCESSING_REQUEST("Replaced Card - Processing Request"),
    REPLACED_CARD_PREPARED_FOR_DISPATCH("Replaced Card Prepared for Dispatch"),
    REPLACED_CARD_IN_TRANSIT("Replaced Card in Transit"),
    REPLACED_CARD_READY_FOR_PICKUP("Replaced Card Ready for Pickup"),
    REP<PERSON>CED_CARD_COLLECTED("Replaced Card Collected"),
    REPLACED_CARD_CANCELED("Replaced Card Canceled");

    private final String label;

    CardStatus(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }
}
