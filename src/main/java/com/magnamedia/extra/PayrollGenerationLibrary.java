package com.magnamedia.extra;

import com.aspose.words.Document;
import com.aspose.words.ImageSaveOptions;
import com.aspose.words.PageSet;
import com.aspose.words.SaveFormat;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.HousemaidPayrollLogRepository;
import com.magnamedia.repository.MonthlyPayrollDocumentRepository;
import com.magnamedia.repository.MonthlyPayrollRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.payslip.PayslipTranslateService;
import com.opencsv.CSVWriter;
import com.opencsv.bean.BeanToCsv;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.exceptions.CsvBadConverterException;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlException;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRow;
import org.springframework.data.domain.PageRequest;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Abbas <<EMAIL>>
 */
public class PayrollGenerationLibrary {

    public static final List<String> HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING = Arrays.asList("indexNum", "employeeUniqueId", "agentId", "employeeAccountWithAgent", "housemaidName", "startingDateV", "RenewalDate",
            "arrivalDateV", "finishedMedicalTest", "nationality", "clientName", "contractName", "status", "source", "freedomOperatorName", "orginalLoan", "remainingLoanBalance", "unpaidDeductionV", "thisMonthForgiveness", "additionToBalanceDeductionLimit",
            "loanRepayment", "startDateDeduction", "complaintDeduction", "managerDeduction", "failedInterviewDeduction", "replacementDeduction", "unpaidDeductionRepaymentV", "basicSalary", "workingVacationPay",
            /*"vacationAirFare", */"livingV", "companyAccommodated", "accommodationSalaryV", "mohreSalaryV", "fullSalaryV", "groupOneDays", "groupTwoDays", "groupThreeDays", "groupFourDays", "groupFiveDays", "groupSixDays", "earningInGroupOneV", "earningInGroupTwoV", "earningInGroupFourV", "earningInGroupFiveV", "earningInGroupSixV", "managerAddition", "previouslyUnpaidSalariesV", "ansari", "maidVisaAEContract", "housemaidType");


    //Jirra ACC-1227
    //...PAY-907...//
    public static final List<String> OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_ALL = Arrays.asList("indexV", "employeeUniqueId", "agentId", "employeeAccountWithAgent", "officeStaffName", "status", "startDate", "city", "jobTitle", "departments", "local", "pLcode", "moneyReceiverName", "remainingLoanBalance", "loanRepaymentV",
            "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "internetAllowanceV", "housing", "transportation", "managerAdditionV", "finalSettlementV", "extraShiftsV", "unpaidLoansV", "previouslyUnpaidSalariesV", "paymentMethod", "balanceV", "currencyUsed");
    //...PAY-907...//
    public static final List<String> OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_ALL_TERMINATED = Arrays.asList("indexV", "employeeUniqueId", "agentId", "employeeAccountWithAgent", "officeStaffName", "status", "startDate", "city", "jobTitle", "departments", "local", "pLcode", "moneyReceiverName", "remainingLoanBalance", "loanRepaymentV",
            "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "internetAllowanceV", "housing", "transportation", "managerAdditionV", "finalSettlementV", "compensationV", "extraShiftsV", "unpaidLoansV", "previouslyUnpaidSalariesV", "paymentMethod", "balanceV", "currencyUsed");

    //Jirra PAY-130
    public static final List<String> ROSTER_FILE_COLUMNS = Arrays.asList("officeStaffNameRoster", "manager", "rosterManager", "departments",
            "jobTitle", "salary", "currencyUsed", "balanceV", "salaryToTransfer",
            "loanRepaymentV", "deductionsReasons", "deductions", "reductions", "additionsReasons", "additions",
            "raises", "loansReasons", "unpaidLoansV", "country", "nationality", "salaryTransferMethod", "status");


    //Jirra ACC-593 ACC-1066
    //...PAY-907...//
    public static final List<String> OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_INTERNATIONAL = Arrays.asList("indexV", "officeStaffName", "status", "startDate", "city", "jobTitle", "departments", "local", "pLcode", "moneyReceiverName", "remainingLoanBalance", "loanRepaymentV",
            "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "internetAllowanceV", "housing", "transportation", "managerAdditionV", "finalSettlementV", "extraShiftsV", "unpaidLoansV", "previouslyUnpaidSalariesV", "paymentMethod", "balanceV", "currencyUsed");
    //...PAY-907...//
    public static final List<String> OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_INTERNATIONAL_TERMINATED = Arrays.asList("indexV", "officeStaffName", "status", "startDate", "city", "jobTitle", "departments", "local", "pLcode", "moneyReceiverName", "remainingLoanBalance", "loanRepaymentV",
            "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "internetAllowanceV", "housing", "transportation", "managerAdditionV", "finalSettlementV", "compensationV", "extraShiftsV", "unpaidLoansV", "previouslyUnpaidSalariesV", "paymentMethod", "balanceV", "currencyUsed");

    public static final List<String> HOUSEMAIDS_PWO_FILE_COLUMNS_MAPPING = Arrays.asList("sn", "recordType", "employeeUniqueId", "employeeName", "agentId", "employeeAccountWithAgent",
            "payStartDateV", "payEndDateV", "daysInPeriodV", "incomeFixedComponent", "incomeVariableComponent", "daysOnLeaveForPeriod", "conveyanceAllowance", "medicalAllowance", "annualPassageAllowance", "overtimeAllowance", "otherAllowance"
    );

    public static final List<String> OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_ANSARI_INTERNATIONAL = Arrays.asList("officeStaffName", "officeStaffArabicName", "destinationOfTransfere", "mobileNumber", "balanceV",
            "currencyUsed");

    public static final List<String> OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_QNP_WPS = Arrays.asList("indexV", "employeeUniqueId", "visaId", "officeStaffName", "bankShortName", "employeeAccountWithAgent", "salaryFrequency", "daysInPeriodV", "column6",
            "basicSalaryV", "column1", "column2", "column3", "column4", "column5");

    public static final List<String> QATAR_STAFF_CLEANERS_PWO_FILE_COLUMNS_MAPPING_QNP_WPS = Arrays.asList("indexV", "employeeUniqueId", "visaId", "officeStaffName", "bankShortName", "employeeAccountWithAgent", "salaryFrequency", "daysInPeriodV", "BalanceV",
            "basicSalaryV", "column1", "column2", "column3", "column4", "column5");

    public static final List<String> OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_ADEEB = Arrays.asList("officeStaffName", "officeStaffAccountName", "bankShortName", "currencyUsed", "destinationOfTransfere");

    public static final List<String> OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_ANSARI_WPS = Arrays.asList("sn", "recordType", "employeeUniqueId", "officeStaffName", "agentId", "employeeAccountWithAgent",
            "payStartDateV", "payEndDateV", "daysInPeriodV", "incomeFixedComponent", "incomeVariableComponent", "daysOnLeaveForPeriod",
            "column6", "conveyanceAllowance", "medicalAllowance", "annualPassageAllowance", "overtimeAllowance", "otherAllowance", "leaveEncashment");

    //Jirra ACC-1129
    public static List<Housemaid> getTestPayrollHousemaids(
            LocalDate payrollStart, LocalDate payrollEnd, String maidName) {

        LocalDate dt1 = new LocalDate().minusDays(1);
        LocalDate dt2 = new LocalDate().plusDays(1);
        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - dt1: " + dt1.toString());
        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - dt2: " + dt2.toString());
        SelectQuery<PayrollManagerNote> query1 = new SelectQuery<>(PayrollManagerNote.class);
        SelectQuery<Repayment> query2 = new SelectQuery<>(Repayment.class);
        SelectQuery<Housemaid> query3 = new SelectQuery<>(Housemaid.class);
        query1.filterBy(new SelectFilter("creationDate", ">=", dt1.toDate()).and("creationDate", "<=", dt2.toDate()));
        query2.filterBy(new SelectFilter("creationDate", ">=", dt1.toDate()).and("creationDate", "<=", dt2.toDate()));
        query3.filterBy(new SelectFilter("lastModificationDate", ">=", dt1.toDate()).and("lastModificationDate", "<=", dt2.toDate()));
        if (maidName != null && !maidName.isEmpty()) {
            query1.filterBy(new SelectFilter("housemaid.name", "Like", "%" + maidName + "%"));
            query2.filterBy(new SelectFilter("housemaid.name", "Like", "%" + maidName + "%"));
            query3.filterBy(new SelectFilter("name", "Like", "%" + maidName + "%"));
        }
        List<Housemaid> results = new ArrayList<>();
        List<Housemaid> results1 = query1.execute(PageRequest.of(0, 10)).map(x -> x.getHousemaid()).getContent();
        List<Housemaid> results2 = query2.execute(PageRequest.of(0, 10)).map(x -> x.getHousemaid()).getContent();
        List<Housemaid> results3 = query3.execute(PageRequest.of(0, 10)).getContent();
        if (results1 != null && !results1.isEmpty()) {
            results.addAll(results1);
        }
        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - results1: " + results.size());
        if (results2 != null && !results2.isEmpty()) {
            results.addAll(results2);
        }
        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - results2: " + results.size());
        if (results3 != null && !results3.isEmpty()) {
            results.addAll(results3);
        }
        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - results3: " + results.size());
        return results;
    }

    public static List<Housemaid> getPayrollHousemaids(
            LocalDate payrollStart, LocalDate payrollEnd) {

        return getPayrollHousemaidsQuery(payrollStart, payrollEnd)
                .execute().stream()
                .filter(x -> !checkVacationForHousemaid(payrollStart, payrollEnd, x))
                .collect(Collectors.toList());
    }

    public static List<Long> unpaidClients(LocalDate payrollEnd) {

        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        query.filterBy("contract", "IS NOT NULL", null);
        query.filterBy("contract.status", "=", ContractStatus.ACTIVE);
        query.filterBy("contract.housemaid", "IS NOT NULL", null);
        query.filterBy("contract.housemaid.housemaidType", "=", HousemaidType.MAID_VISA);
        query.filterBy("typeOfPayment", "=", PicklistHelper.getItem("TypeOfPayment", "Monthly Payment"));
        query.filterBy("status", "<>", PaymentStatus.RECEIVED);
        query.filterBy("dateOfPayment", "<=", payrollEnd.dayOfMonth().withMaximumValue().toDate());
        query.filterBy("dateOfPayment", ">=", payrollEnd.withDayOfMonth(1).toDate());

        Set<Long> ids = new HashSet<>(query.execute().stream().map(p -> p.getContract().getHousemaid().getId()).collect(Collectors.toList()));

        return new ArrayList<>(ids);
    }
    public static SelectQuery<Housemaid> getPayrollHousemaidsQuery(LocalDate payrollStart, LocalDate payrollEnd) {
        SelectQuery<Housemaid> query = new SelectQuery(Housemaid.class);
        LocalDate endDate = new LocalDate(payrollEnd);
        query.filterBy(getPayrollMaidsFilter(payrollStart.toDate(), payrollEnd.toDate()));
        query.filterBy("startDate", "<", endDate.plusDays(1).toDate());

        List<Long> unpaidClientHousemaids = unpaidClients(payrollEnd);
        if(!unpaidClientHousemaids.isEmpty()) {
            query.filterBy("id", "NOT IN", unpaidClientHousemaids);
        }
        return query;
    }

    //Jirra ACC-1085
    //Check Annual Vacations
    public static boolean checkVacationForHousemaid(
            LocalDate payrollStart,
            LocalDate payrollEnd,
            Housemaid housemaid) {

        //Filter maids not on vacation this month
        PicklistItem vacationType =
                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        "pre-paid_vacation");
        List<ScheduledAnnualVacation> annualVacations = housemaid.getScheduledAnnualVacations();
        boolean flag = false;
        if (annualVacations != null && annualVacations.size() > 0) {

            for (ScheduledAnnualVacation vacation : annualVacations) {
                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
                if (!payrollDueDate.isBefore(payrollStart.minusMonths(1))
                        && payrollDueDate.isBefore(payrollEnd.minusMonths(1).plusDays(1))
                        && vacation.getType() != null && vacation.getType().getCode().equals(vacationType.getCode())
                        && vacation.getAmount() != null && vacation.getAmount() > 0.0) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    public static SelectFilter getPayrollMaidsFilter(
            Date payrollStart, Date payrollEnd) {

        PicklistRepository picklistRepository =
                Setup.getRepository(PicklistRepository.class);
        Picklist reasonOfPendingPicklist =
                picklistRepository.findByCode("reasonOfPending");

        PicklistItemRepository picklistItemRepository =
                Setup.getRepository(PicklistItemRepository.class);
        PicklistItem delayedOnSickWithoutClientPicklistItem
                = picklistItemRepository
                .findByListAndCodeIgnoreCase(reasonOfPendingPicklist,
                        "absent-sick_without_client");
        PicklistItem delayedOnReturningBackFromVacationPicklistItem
                = picklistItemRepository
                .findByListAndCodeIgnoreCase(reasonOfPendingPicklist,
                        "did_not_return_from_her_vacation");

        List<PicklistItem> delayItems = new ArrayList();
        if (delayedOnSickWithoutClientPicklistItem != null)
            delayItems.add(delayedOnSickWithoutClientPicklistItem);
        if (delayedOnReturningBackFromVacationPicklistItem != null)
            delayItems.add(delayedOnReturningBackFromVacationPicklistItem);

        //pending filter
        SelectFilter sf = new SelectFilter()
                .or("status", "=", HousemaidStatus.WITH_CLIENT)
                .or("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                .or("status", "=", HousemaidStatus.AVAILABLE)
                .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                //Jirra ACC-1005
                //.or("status", "=", HousemaidStatus.ON_VACATION)
                .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                //Jirra ACC-1223
                .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                .or(
                        new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                //Jirra ACC-1005
                                .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                        .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                        .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                .and("pendingSince", ">=", new LocalDate().toDate())
                                                .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                        .and("pendingUntil", ">=", payrollStart))))
                );
        sf.and(new SelectFilter()
                .or("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null)
        );
        return sf;
    }

    //*********************************** Housemaid Payroll file ***************************************************
    public static void generateHousemaidPayrollFile(HttpServletResponse response, String fileName, List<HousemaidPayrollBean> data,
                                                    Double totalAnsari, LocalDate payrollDate, Boolean finalFile) throws FileNotFoundException, IOException, URISyntaxException, NoSuchAlgorithmException {
//        FileInputStream inputStream = new FileInputStream(new File("src\\main\\resources\\templates\\Payroll templates\\Housemaid Payroll Template.xlsx"));
        Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: sending mail");
        URL resource = PayrollGenerationLibrary.class.getResource("/Housemaid Payroll Template.xlsx");
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
            //add total to ansari column
            XSSFRow totalRow = spreadsheet.getRow(1);
            XSSFCell cell1 = totalRow.getCell(HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING.size() - 2);
            cell1.setCellValue(totalAnsari);

            //add average to ansari column
            XSSFRow averageRow = spreadsheet.getRow(2);
            XSSFCell cell2 = averageRow.getCell(HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING.size() - 2);
            cell2.setCellValue(totalAnsari / data.size());
            //shift total and average rows to fill data
            if (data.size() > 0)
                spreadsheet.shiftRows(1, 2, data.size());
            CellStyle style = createTextStyle(workbook);

            int j = 0;//for rows
            int i = 0;// for columns
            for (HousemaidPayrollBean bean : data) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(bean);
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row = spreadsheet.createRow(++j);
                //create columns
                for (String temp : HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsHousemaidPayrollFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }
                i = 0;//back to the first column
            }
            Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: sending mail 2");
//        //add total to ansari column
//        XSSFRow totalRow = spreadsheet.getRow(data.size() + 1);
//        XSSFCell cell1 = totalRow.getCell(23);
//        cell1.setCellValue(totalAnsari);
//
//        //add average to ansari column
//        XSSFRow averageRow = spreadsheet.getRow(data.size() + 2);
//        XSSFCell cell2 = averageRow.getCell(23);
//        cell2.setCellValue(totalAnsari / data.size());
            //create the file and download it
//        createAndDownloadFile(response, fileName, workbook);
            if (!finalFile) {
                sendMail(fileName, workbook, "Housemaids payroll file for " + DateUtil.formatMonth(payrollDate.toDate()), finalFile);
            } else {
                saveFinalPayrollFile(fileName, workbook, payrollDate, MonthlyPayrollDocumentType.MONTHLY_PAYROLL);
                saveFinalPayrollList(data, payrollDate);
//            sendMailv2(fileName, finalPayrollFile, "Final Housemaids payroll file of " + payrollMonth, finalFile);
            }
        }
    }


    // New Version PAY-58
    public static Attachment generateHousemaidPayrollFile(String fileName, List<HousemaidPayrollBean> data, Double totalAnsari) throws FileNotFoundException, IOException, URISyntaxException, NoSuchAlgorithmException {
//        FileInputStream inputStream = new FileInputStream(new File("src\\main\\resources\\templates\\Payroll templates\\Housemaid Payroll Template.xlsx"));
        Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: sending mail");
        URL resource = PayrollGenerationLibrary.class.getResource("/Housemaid Payroll Template.xlsx");
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
            //add total to ansari column
            XSSFRow totalRow = spreadsheet.getRow(1);
            XSSFCell cell1 = totalRow.getCell(HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING.size() - 2);
            cell1.setCellValue(totalAnsari);

            //add average to ansari column
            XSSFRow averageRow = spreadsheet.getRow(2);
            XSSFCell cell2 = averageRow.getCell(HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING.size() - 2);
            cell2.setCellValue(totalAnsari / data.size());
            //shift total and average rows to fill data
            if (data.size() > 0)
                spreadsheet.shiftRows(1, 2, data.size());
            CellStyle style = createTextStyle(workbook);

            int j = 0;//for rows
            int i = 0;// for columns
            for (HousemaidPayrollBean bean : data) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(bean);
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row = spreadsheet.createRow(++j);
                //create columns
                for (String temp : HOUSEMAIDS_PAYROLL_FILE_COLUMNS_MAPPING) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsHousemaidPayrollFile(temp)
                            && exp != null
                            && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }
                i = 0;//back to the first column
            }

            File directory = Paths.get(System.getProperty("java.io.tmpdir"),
                    "payroll").toFile();

            directory.mkdir();

            File payrollFile = Paths.get(System.getProperty("java.io.tmpdir"),
                    "payroll/" + fileName)
                    .toFile();

//        if (!finalFile) {
            try (FileOutputStream out = new FileOutputStream(payrollFile)) {
                //        if (!finalFile) {
                workbook.write(out);
            }

            return Storage.storeTemporary("payroll/" + fileName, new FileInputStream(payrollFile), "HousemaidDetailedPayrollFile", true);
        }
    }

    //*********************************** Office staff all Payroll file ***************************************************
    public static void generateOfficeStaffPayrollFile(HttpServletResponse response, String fileName, List<OfficeStaffPayrollBean> data, Double totalAnsari, boolean isSentByEmail, String type) throws FileNotFoundException, IOException, URISyntaxException {
        //Jirra ACC-593
        URL resource;
        List<String> columns;
        if (type.equals("OVERSEAS_STAFF")) {
            resource = PayrollGenerationLibrary.class.getResource("/International staff template.xlsx");
            columns = OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_INTERNATIONAL;
        } else {
            resource = PayrollGenerationLibrary.class.getResource("/All staff template.xlsx");
            columns = OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_ALL;
        }
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("Included office staffs");
            //shift total and average rows to fill data
            if (data.size() > 0)
                spreadsheet.shiftRows(1, 2, data.size());

            CellStyle style = createTextStyle(workbook);
            int j = 0;//for rows
            int i = 0;// for columns
            for (OfficeStaffPayrollBean bean : data) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(bean);
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row = spreadsheet.createRow(++j);
                //create columns
                for (String temp : columns) {
                    String command;
                    if (temp.equals("pLcode")) {
                        command = "getpLcode()";
                    } else {
                        command = generateGetter(temp);//Create the getter for each column
                    }
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsOfficeStaffPayrollFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }
                i = 0;//back to the first column
            }
            //add total to fifth column
            XSSFRow totalRow = spreadsheet.getRow(data.size() + 1);
            XSSFCell cell1 = totalRow.getCell(4);
            cell1.setCellValue("Total of " + data.size());

            //create the file and download it
            if (!isSentByEmail) {
                createAndDownloadFile(response, fileName, workbook);
            } else {
                sendMail(fileName + ".xlsx", workbook, fileName, false);
            }
        }
    }

    // New Version PAY-58
    public static Attachment generateOfficeStaffPayrollFile(String fileName, List<OfficeStaffPayrollBean> activeStaffsData, List<OfficeStaffPayrollBean> terminatedStaffsData, List<OfficeStaffPayrollBean> excludedStaffsData, String type) throws IOException, URISyntaxException {
        //Jirra ACC-593
        URL resource;
        List<String> columns;
        List<String> terminatedColumns;
        if (type.equals("OVERSEAS_STAFF")) {
            //...PAY-907...//
            //... Add column moneyReceiverName in file excel...//
            resource = PayrollGenerationLibrary.class.getResource("/International staff template.xlsx");
            columns = OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_INTERNATIONAL;
            terminatedColumns = OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_INTERNATIONAL_TERMINATED;
        } else {
            resource = PayrollGenerationLibrary.class.getResource("/All staff template.xlsx");
            columns = OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_ALL;
            terminatedColumns = OFFICE_STAFF_PAYROLL_FILE_COLUMNS_MAPPING_ALL_TERMINATED;
        }
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            //*************** handle Active Staff Sheet ***************//
            {
                XSSFSheet spreadsheet = workbook.getSheet("Included office staffs");
                //shift total and average rows to fill data
                if (activeStaffsData.size() > 0)
                    spreadsheet.shiftRows(1, 2, activeStaffsData.size());

                CellStyle style = createTextStyle(workbook);
                int j = 0;//for rows
                int i = 0;// for columns
                for (OfficeStaffPayrollBean bean : activeStaffsData) {
                    //Use spring spel to run getters
                    EvaluationContext context = new StandardEvaluationContext(bean);
                    ExpressionParser parser = new SpelExpressionParser();
                    //create new row
                    XSSFRow row = spreadsheet.createRow(++j);
                    //create columns
                    for (String temp : columns) {
                        String command;
                        if (temp.equals("pLcode")) {
                            command = "getpLcode()";
                        } else {
                            command = generateGetter(temp);//Create the getter for each column
                        }
                        Expression exp = parser.parseExpression(command);
                        //create new cell
                        XSSFCell cell = row.createCell(i++);
                        //convert numeric fields to double in order to make sum and average formulas work
                        if (checkDoubleColumnsOfficeStaffPayrollFile(temp) && exp.getValue(context, String.class) != null
                                && !exp.getValue(context, String.class).equals("N/A")
                                && !exp.getValue(context, String.class).equals("")) {
                            cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                        } else {
                            cell.setCellValue(exp.getValue(context, String.class));
                        }
                        //add style to cell
                        cell.setCellStyle(style);
                    }
                    i = 0;//back to the first column
                }
                //add total to fifth column
                XSSFRow totalRow = spreadsheet.getRow(activeStaffsData.size() + 1);
                XSSFCell cell1 = totalRow.getCell(4);
                cell1.setCellValue("Total of " + activeStaffsData.size());
            }

            //*************** handle Terminated Staff Sheet ***************//
            {
                XSSFSheet spreadsheet = workbook.getSheet("Terminated office staffs");
                //shift total and average rows to fill data
                if (terminatedStaffsData.size() > 0)
                    spreadsheet.shiftRows(1, 2, terminatedStaffsData.size());

                CellStyle style = createTextStyle(workbook);
                int j = 0;//for rows
                int i = 0;// for columns
                for (OfficeStaffPayrollBean bean : terminatedStaffsData) {
                    //Use spring spel to run getters
                    EvaluationContext context = new StandardEvaluationContext(bean);
                    ExpressionParser parser = new SpelExpressionParser();
                    //create new row
                    XSSFRow row = spreadsheet.createRow(++j);
                    //create columns
                    for (String temp : terminatedColumns) {
                        String command;
                        if (temp.equals("pLcode")) {
                            command = "getpLcode()";
                        } else {
                            command = generateGetter(temp);//Create the getter for each column
                        }
                        Expression exp = parser.parseExpression(command);
                        //create new cell
                        XSSFCell cell = row.createCell(i++);
                        //convert numeric fields to double in order to make sum and average formulas work
                        if (checkDoubleColumnsOfficeStaffPayrollFile(temp) && exp.getValue(context, String.class) != null
                                && !exp.getValue(context, String.class).equals("N/A")
                                && !exp.getValue(context, String.class).equals("")) {
                            cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                        } else {
                            cell.setCellValue(exp.getValue(context, String.class));
                        }
                        //add style to cell
                        cell.setCellStyle(style);
                    }
                    i = 0;//back to the first column
                }
                //add total to fifth column
                XSSFRow totalRow = spreadsheet.getRow(terminatedStaffsData.size() + 1);
                XSSFCell cell1 = totalRow.getCell(4);
                cell1.setCellValue("Total of " + terminatedStaffsData.size());
            }

            //*************** handle Excluded Staff Sheet ***************//
            {
                XSSFSheet spreadsheet = workbook.getSheet("Excluded office staffs");
                //shift total and average rows to fill data
                if (excludedStaffsData.size() > 0)
                    spreadsheet.shiftRows(1, 2, excludedStaffsData.size());

                CellStyle style = createTextStyle(workbook);
                int j = 0;//for rows
                int i = 0;// for columns
                for (OfficeStaffPayrollBean bean : excludedStaffsData) {
                    //Use spring spel to run getters
                    EvaluationContext context = new StandardEvaluationContext(bean);
                    ExpressionParser parser = new SpelExpressionParser();
                    //create new row
                    XSSFRow row = spreadsheet.createRow(++j);
                    //create columns
                    for (String temp : columns) {
                        String command;
                        if (temp.equals("pLcode")) {
                            command = "getpLcode()";
                        } else {
                            command = generateGetter(temp);//Create the getter for each column
                        }
                        Expression exp = parser.parseExpression(command);
                        //create new cell
                        XSSFCell cell = row.createCell(i++);
                        //convert numeric fields to double in order to make sum and average formulas work
                        if (checkDoubleColumnsOfficeStaffPayrollFile(temp) && exp.getValue(context, String.class) != null
                                && !exp.getValue(context, String.class).equals("N/A")
                                && !exp.getValue(context, String.class).equals("")) {
                            cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                        } else {
                            cell.setCellValue(exp.getValue(context, String.class));
                        }
                        //add style to cell
                        cell.setCellStyle(style);
                    }
                    i = 0;//back to the first column
                }
                //add total to fifth column
                XSSFRow totalRow = spreadsheet.getRow(excludedStaffsData.size() + 1);
                XSSFCell cell1 = totalRow.getCell(4);
                cell1.setCellValue("Total of " + excludedStaffsData.size());
            }

            File directory = Paths.get(System.getProperty("java.io.tmpdir"),
                    "payroll").toFile();

            directory.mkdir();

            File payrollFile = Paths.get(System.getProperty("java.io.tmpdir"),
                    "payroll/" + fileName)
                    .toFile();

//        if (!finalFile) {
            try (FileOutputStream out = new FileOutputStream(payrollFile)) {
                //        if (!finalFile) {
                workbook.write(out);
            }
            return Storage.storeTemporary("payroll/" + fileName, new FileInputStream(payrollFile), "OfficeStaffDetailedPayrollFile", true);
        }
    }

    // New Version PAY-13
    public static Attachment generateOfficeStaffRosterFile(String fileName, List<OfficeStaffPayrollBean> data) throws IOException, URISyntaxException {
        //Jirra ACC-593
        URL resource = PayrollGenerationLibrary.class.getResource("/Payroll Roster.xlsx");
        List<String> columns = ROSTER_FILE_COLUMNS;

        data.sort((o1, o2) -> {
            boolean o1HasValues = (o1.getTotalDeduction() != null && o1.getTotalDeduction() > 0) ||
                    (o1.getManagerReduction() != null && o1.getManagerReduction() > 0) ||
                    (o1.getAdditions() != null && !o1.getAdditions().isEmpty() && Double.parseDouble(o1.getAdditions()) > 0) ||
                    (o1.getManagerRaise() != null && o1.getManagerRaise() > 0) ||
                    (o1.getUnpaidLoansV() != null && !o1.getUnpaidLoansV().isEmpty() && Double.parseDouble(o1.getUnpaidLoansV()) > 0);


            boolean o2HasValues = (o2.getTotalDeduction() != null && o2.getTotalDeduction() > 0) ||
                    (o2.getManagerReduction() != null && o2.getManagerReduction() > 0) ||
                    (o2.getAdditions() != null && !o2.getAdditions().isEmpty() && Double.parseDouble(o2.getAdditions()) > 0) ||
                    (o2.getManagerRaise() != null && o2.getManagerRaise() > 0) ||
                    (o2.getUnpaidLoansV() != null && !o2.getUnpaidLoansV().isEmpty() && Double.parseDouble(o2.getUnpaidLoansV()) > 0);

            if (o1HasValues && !o2HasValues) return -1;
            if (!o1HasValues && o2HasValues) return 1;
            return 0;
        });

        List<String> changeableCols = Arrays.asList("loanRepaymentV",
                "deductions", "reductions", "additions", "raises", "unpaidLoansV");

        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
            List<Integer> columnWidths = Arrays.stream(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ROSTER_FILE_COLUMN_WIDTHS)
                    .split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            for (int i = 0; i < columnWidths.size(); i++) {
                spreadsheet.setColumnWidth(i, (int) (columnWidths.get(i) * 25.6));
            }
            //shift total and average rows to fill data
//        if (data.size() > 0)
//            spreadsheet.shiftRows(1, 2, data.size());

            int j = 0;//for rows
            int i = 0;// for columns
            for (OfficeStaffPayrollBean bean : data) {
                CellStyle style = createTextStyleRosterFile(workbook);
                style.setWrapText(true);

                if (bean.isNewHire()) {
                    style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.index);
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                }
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(bean);
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row = spreadsheet.createRow(++j);
                boolean salaryChanged = false;
                //create columns
                for (String temp : columns) {
                    String command;
                    style = createTextStyleRosterFile(workbook);
                    if (temp.equals("pLcode")) {
                        command = "getpLcode()";
                    } else {
                        command = generateGetter(temp);//Create the getter for each column
                    }
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsOfficeStaffPayrollFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("")
                            && !exp.getValue(context, String.class).contains("(")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));

                        if (changeableCols.contains(temp) && !bean.isNewHire()) {
                            double val = Double.parseDouble(exp.getValue(context, String.class));
                            if (val > 0) {
                                salaryChanged = true;
                                style = createTextStyleWithBlueBackground(workbook);
                            } else {
                                style = createTextStyleRosterFile(workbook);
                            }
                        }
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }

                    if (temp.equals("balanceV") && !bean.isNewHire()) {
                        if (salaryChanged) {
                            style = createTextStyleWithBlueBackground(workbook);
                        } else {
                            style = createTextStyleRosterFile(workbook);
                        }
                    }
                    if (bean.getShouldBeHighlighted()){
                        style.setFillForegroundColor(IndexedColors.YELLOW1.index);
                        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }
                i = 0;//back to the first column
            }
            //add total to fifth column
            XSSFRow totalRow = spreadsheet.createRow(data.size() + 1);
            XSSFCell cell1 = totalRow.createCell(4);
            cell1.setCellValue("Total of " + data.size());


            File payrollFile = Paths.get(System.getProperty("java.io.tmpdir"),
                    fileName)
                    .toFile();

//        if (!finalFile) {
            try (FileOutputStream out = new FileOutputStream(payrollFile)) {
                //        if (!finalFile) {
                workbook.write(out);
            }
            return Storage.storeTemporary(fileName, new FileInputStream(payrollFile), "RosterPayrollFile", false);
        }
    }


    //*********************************** Housemaid PPWO for ANSARI***************************************************
    public static void generateHousemaidPWOFile(HttpServletResponse response, String fileName, List<HousemaidPayrollBean> data, Double totalAnsari, LocalDate payrollDate, Boolean finalFile) throws FileNotFoundException, IOException, URISyntaxException, NoSuchAlgorithmException {
        URL resource = PayrollGenerationLibrary.class.getResource("/Housemaid PWO.xlsx");
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
            //shift total and average rows to fill data
            if (data.size() > 1)
                spreadsheet.shiftRows(1, 2, data.size() - 1);

            CellStyle style = createTextStyle(workbook);
            int j = 0;//for rows
            int i = 0;// for columns
            for (int k = 0; k < data.size(); k++) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(data.get(k));
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row;
                if (k == data.size() - 1) {
                    row = spreadsheet.createRow(data.size() + 1);
                } else {
                    row = spreadsheet.createRow(++j);
                }
                //create columns
                for (String temp : HOUSEMAIDS_PWO_FILE_COLUMNS_MAPPING) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsHousemaidPWOFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("") && !exp.getValue(context, String.class).equals("AED")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }
                XSSFCell cell1 = row.createCell(i++);
                cell1.setCellValue("");
                cell1.setCellStyle(style);
                XSSFCell cell2 = row.createCell(i++);
                cell2.setCellValue("");
                cell2.setCellStyle(style);
                i = 0;//back to the first column
            }

            //create the file and download it
//        createAndDownloadFile(response, fileName, workbook);
//        sendMail(fileName, workbook, "Housemaids PWO file of " + payrollMonth, finalFile);
            if (!finalFile) {
                sendMail(fileName, workbook, "Housemaids PWO file of " + DateUtil.formatMonth(payrollDate.toDate()), finalFile);
            } else {
                saveFinalPayrollFile(fileName, workbook, payrollDate, MonthlyPayrollDocumentType.PPWO);
//            saveFinalPayrollList(data, payrollMonth);
//            sendMailv2(fileName, finalPayrollFile, "Final Housemaids payroll file of " + payrollMonth, finalFile);
            }
        }
    }

    //*********************************** Office staff PPWO for ANSARI International***************************************************
    public static void generateOfficeStaffPWOFileAnsariInternational(HttpServletResponse response, String emailSubject, String fileName, List<OfficeStaffPayrollBean> data, Double totalAnsari, boolean isSentByEmail) throws FileNotFoundException, IOException, URISyntaxException {
        URL resource = PayrollGenerationLibrary.class.getResource("/ansari international.xlsx");
//        System.out.println(resource.getPath());
        System.out.println(resource.toURI());
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");

            CellStyle style = createTextStyle(workbook);
            int j = 2;//for rows
            int i = 0;// for columns
            for (int k = 0; k < data.size(); k++) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(data.get(k));
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row = spreadsheet.createRow(++j);

                //create columns
                for (String temp : OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_ANSARI_INTERNATIONAL) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsAnsariInternationalPWOFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("") && !exp.getValue(context, String.class).equals("AED")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }
                i = 0;//back to the first column
            }

            //create the file and download it
            if (!isSentByEmail) {
                createAndDownloadFile(response, fileName, workbook);
            } else {
                sendMail(fileName + ".xlsx", workbook, emailSubject, isSentByEmail);
            }
        }
    }

    //*********************************** Office staff PPWO for  Adeeb manual ***************************************************
    public static void generateOfficeStaffPWOFileInternationalOrUaeBankTransfer(HttpServletResponse response, String emailSubject, String fileName, List<OfficeStaffPayrollBean> data, String type, boolean isSentByEmail) throws FileNotFoundException, IOException, URISyntaxException {
        URL resource = PayrollGenerationLibrary.class.getResource("/International_uae bank transfer.xlsx");
//        System.out.println(resource.getPath());
        System.out.println(resource.toURI());
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
            XSSFRow headerRow = spreadsheet.getRow(1);
            headerRow.getCell(0).setCellValue(type.equals("international_bank_transfer") ? "International Bank Transfer Payroll Payment" : "UAE Bank Transfer Payroll Payment");
            CellStyle style = createTextStyle(workbook);
            CellStyle style2 = createTextStyle(workbook);
            int j = 2;//for rows
            int i = 0;// for columns
            for (int k = 0; k < data.size(); k++) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(data.get(k));
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row = spreadsheet.createRow(++j);

                //create columns
                for (String temp : OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_ADEEB) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //style = workbook.createCellStyle();
                    DataFormat format = workbook.createDataFormat();
                    style2.setDataFormat(format.getFormat("#,##"));
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsAnsariInternationalPWOFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("") && !exp.getValue(context, String.class).equals("AED")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                        cell.setCellStyle(style2);
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                        //add style to cell
                        cell.setCellStyle(style);
                    }
                }
                i = 0;//back to the first column
            }

            //create the file and download it
            if (!isSentByEmail) {
                createAndDownloadFile(response, fileName, workbook);
            } else {
                sendMail(fileName + ".xlsx", workbook, emailSubject, isSentByEmail);
            }
        }
    }

    //*********************************** Office staff PPWO for QNP wps***************************************************
    public static void generateOfficeStaffQNPWPS(HttpServletResponse response, String emailSubject, String fileName, List<OfficeStaffPayrollBean> data, Double totalAnsari, boolean isSentByEmail) throws FileNotFoundException, IOException, URISyntaxException {
        URL resource = PayrollGenerationLibrary.class.getResource("/qnpwps.xlsx");
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
            //shift total and average rows to fill data
            spreadsheet.shiftRows(1, 2, 1);
            CellStyle style = createTextStyle(workbook);
            int j = 2;//for rows
            int i = 0;// for columns
            for (int k = 0; k < data.size(); k++) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(data.get(k));
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row;
                if (k == 0) {
                    row = spreadsheet.createRow(1);
                } else {
                    row = spreadsheet.createRow(++j);
                }
                //create columns
                for (String temp : OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_QNP_WPS) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsQNPWPSFile(temp) && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("") && !exp.getValue(context, String.class).equals("AED")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }

                i = 0;//back to the first column
            }

            //create the file and download it
            if (!isSentByEmail) {
                createAndDownloadFile(response, fileName, workbook);
            } else {
                sendMail(fileName + ".xlsx", workbook, emailSubject, isSentByEmail);
            }
        }
    }

    //*********************************** Qatar staff Cleaners ***************************************************
    public static void generateQatarStaffCleanersPWO(HttpServletResponse response, String emailSubject,
                                                     String fileName, List<OfficeStaffPayrollBean> data, Double totalAnsari, boolean isSentByEmail)
            throws FileNotFoundException, IOException, URISyntaxException {
        URL resource = PayrollGenerationLibrary.class.getResource("/qtrstaffcln.xlsx");
        File file = new File(resource.toURI());
        try (FileInputStream inputStream = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            List<String> nonDoubleHeaders = Arrays.asList("Salary Year and Month",
                    "Total Salaries", "Total records", "Number of Working Days",
                    "Basic Salary", "Extra hours", "Extra Income");
            XSSFSheet spreadsheet = workbook.getSheet("Sheet1");
            //shift total and average rows to fill data
            //spreadsheet.shiftRows(1, 2, 1);
            CellStyle style = null;
            int j = 0;//for rows
            int i = 0;// for columns
            int numberOfColumns = QATAR_STAFF_CLEANERS_PWO_FILE_COLUMNS_MAPPING_QNP_WPS.size();
            int size = data.size();
            for (int k = 0; k < size; k++) {
                //Use spring spel to run getters
                EvaluationContext context = new StandardEvaluationContext(data.get(k));
                ExpressionParser parser = new SpelExpressionParser();
                //create new row
                XSSFRow row;
                String isNew = data.get(k).getIndexV();
                // If the data is headers
                if (isNew.equals("Employer EID") || isNew.equals("Record Sequence")) {

                    // Add a yellow background
                    style = createTextStyle(workbook);
                    style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                } else {
                    // Add a white background
                    style = createTextStyle(workbook);
                    style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                }
                row = spreadsheet.createRow(j++);
                //create columns
                for (String temp : QATAR_STAFF_CLEANERS_PWO_FILE_COLUMNS_MAPPING_QNP_WPS) {
                    String command = generateGetter(temp);//Create the getter for each column
                    Expression exp = parser.parseExpression(command);
                    //create new cell
                    XSSFCell cell = row.createCell(i++);
                    //convert numeric fields to double in order to make sum and average formulas work
                    if (checkDoubleColumnsQNPWPSFile(temp) && !nonDoubleHeaders.contains(exp.getValue(context, String.class))
                            && exp.getValue(context, String.class) != null
                            && !exp.getValue(context, String.class).equals("N/A")
                            && !exp.getValue(context, String.class).equals("") && !exp.getValue(context, String.class).equals("AED")) {
                        cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                    } else {
                        cell.setCellValue(exp.getValue(context, String.class));
                    }
                    //add style to cell
                    cell.setCellStyle(style);
                }

                i = 0;//back to the first column
            }
            for (i = 0; i < numberOfColumns; i++) {
                spreadsheet.autoSizeColumn(i);
            }
            //create the file and download it
            if (!isSentByEmail) {
                createAndDownloadFile(response, fileName, workbook);
            } else {
                sendMail(fileName + ".xlsx", workbook, emailSubject, isSentByEmail);
            }
        }
    }

    //*********************************** Office staff PPWO for Ansari wps***************************************************
    public static void generateOfficeStaffPWOFileAnsariWPS(HttpServletResponse response, String emailSubject, String fileName, List<OfficeStaffPayrollBean> data, Double totalAnsari, boolean isSentByEmail) throws FileNotFoundException, IOException, URISyntaxException {
        URL resource = PayrollGenerationLibrary.class.getResource("/ansariwps.xlsx");
        File file = new File(resource.toURI());
        XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(file));

        XSSFSheet spreadsheet = workbook.getSheet("sheet 1");
        //shift total and average rows to fill data
        if (data.size() > 1)
            spreadsheet.shiftRows(1, 2, data.size() - 1);
        CellStyle style = createTextStyle(workbook);
        int j = 0;//for rows
        int i = 0;// for columns
        for (int k = 0; k < data.size(); k++) {
            //Use spring spel to run getters
            EvaluationContext context = new StandardEvaluationContext(data.get(k));
            ExpressionParser parser = new SpelExpressionParser();
            //create new row
            XSSFRow row;
            if (k == data.size() - 1) {
                row = spreadsheet.createRow(data.size() + 1);
            } else {
                row = spreadsheet.createRow(++j);
            }
            //create columns
            for (String temp : OFFICE_STAFF_PWO_FILE_COLUMNS_MAPPING_ANSARI_WPS) {
                String command = generateGetter(temp);//Create the getter for each column
                Expression exp = parser.parseExpression(command);
                //create new cell
                XSSFCell cell = row.createCell(i++);
                //convert numeric fields to double in order to make sum and average formulas work
                if (checkDoubleColumnsAnsariWPSFile(temp) && exp.getValue(context, String.class) != null
                        && !exp.getValue(context, String.class).equals("N/A")
                        && !exp.getValue(context, String.class).equals("") && !exp.getValue(context, String.class).equals("AED")) {
                    cell.setCellValue(Double.valueOf(exp.getValue(context, String.class)));
                } else {
                    cell.setCellValue(exp.getValue(context, String.class));
                }
                //add style to cell
                cell.setCellStyle(style);
            }

            i = 0;//back to the first column
        }

        //create the file and download it
        if (!isSentByEmail) {
            createAndDownloadFile(response, fileName, workbook);
        } else {
            sendMail(fileName + ".xlsx", workbook, emailSubject, isSentByEmail);
        }
    }

    public static String generateGetter(String field) {
        return "get" + Character.toUpperCase(field.charAt(0)) + field.substring(1) + "()";
    }

    public static boolean checkDoubleColumnsHousemaidPayrollFile(String command) {

        List<String> doubleColumns = Arrays.asList("indexNum", "orginalLoan", "remainingLoanBalance", "thisMonthForgiveness", "loanRepayment", "startDateDeduction", "complaintDeduction", "managerDeduction",
                "basicSalary", "workingVacationPay", "vacationAirFare", "housingAllowance", "foodAllowance", "managerAddition", "failedInterviewDeduction", "noKidsDeduction", "replacementDeduction",
                "additionToBalanceDeductionLimit", "ansari", "accommodationSalaryV", "mohreSalaryV", "fullSalaryV", "earningInGroupOneV", "earningInGroupTwoV", "earningInGroupFourV", "previouslyUnpaidSalariesV", "unpaidDeductionRepaymentV", "unpaidDeductionV");
        if (doubleColumns.contains(command)) {
            return true;
        }
        return false;
    }

    public static boolean checkDoubleColumnsOfficeStaffPayrollFile(String command) {

        List<String> doubleColumns = Arrays.asList("indexV", "remainingLoanBalance", "loanRepaymentV",
                "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "managerRaiseV", "managerReductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "internetAllowanceV", "housing", "transportation", "managerAdditionV", "extraShiftsV", "balanceV", "finalSettlementV", "compensationV", "unpaidLoansV", "previouslyUnpaidSalariesV",
                "additions", "reductions", "raises", "deductions", "salary", "salaryToTransfer");
        return doubleColumns.contains(command);
    }

    public static boolean checkDoubleColumnsHousemaidPWOFile(String command) {

        List<String> doubleColumns = Arrays.asList("daysInPeriodV", "incomeFixedComponent", "incomeVariableComponent", "daysOnLeaveForPeriod", "conveyanceAllowance", "medicalAllowance", "annualPassageAllowance", "overtimeAllowance", "otherAllowance"
        );
        return doubleColumns.contains(command);
    }

    public static boolean checkDoubleColumnsAnsariInternationalPWOFile(String command) {

        List<String> doubleColumns = Arrays.asList("balanceV", "bankShortName");
        return doubleColumns.contains(command);
    }

    public static boolean checkDoubleColumnsQNPWPSFile(String command) {

        List<String> doubleColumns = Arrays.asList("daysInPeriodV", "balanceV",
                "basicSalaryV", "column1", "column2"
        );
        return doubleColumns.contains(command);
    }

    public static boolean checkDoubleColumnsAnsariWPSFile(String command) {

        List<String> doubleColumns = Arrays.asList("daysInPeriodV", "incomeFixedComponent", "incomeVariableComponent");
        return doubleColumns.contains(command);
    }

    public static CellStyle createTextStyle(XSSFWorkbook workbook) {
        //Create font style
        XSSFFont font = workbook.createFont();
        font.setFontName("Calibri");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        //create style
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setWrapText(true);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setFont(font);//link font to style
        return style;
    }

    public static CellStyle createTextStyleRosterFile(XSSFWorkbook workbook) {
        //Create font style
        XSSFFont font = workbook.createFont();
        font.setFontName("Calibri");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        font.setFontHeightInPoints((short) 10);
        //create style
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setWrapText(true);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setFont(font);//link font to style
        return style;
    }

    public static CellStyle createTextStyleWithBlueBackground(XSSFWorkbook workbook) {
        //Create font style
        XSSFFont font = workbook.createFont();
        font.setFontName("Calibri");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        font.setFontHeightInPoints((short) 10);
        //create style
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setWrapText(true);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setFont(font);//link font to style
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.index);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    public static CellStyle createTextStyleHousemaidPWO(XSSFWorkbook workbook) {
        //Create font style
        XSSFFont font = workbook.createFont();
        font.setFontName("Calibri");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        //create style
        CellStyle style = workbook.createCellStyle();
        style.setWrapText(true);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setFont(font);//link font to style
        return style;
    }

    public static void createAndDownloadFile(HttpServletResponse response, String fileName, XSSFWorkbook workbook) throws FileNotFoundException, IOException {
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();
        FileOutputStream out = null;
        try {
            new FileOutputStream(file);

            workbook.write(out);
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition",
                    "attachment; filename=\""
                            + fileName
                            + ".xlsx"
                            + "\"");

            org.apache.commons.io.IOUtils.copy(new FileInputStream(file),
                    response.getOutputStream());
            response.getOutputStream()
                    .flush();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void sendMail(String fileName, XSSFWorkbook workbook, String payrollMonth, Boolean finalFile) throws FileNotFoundException, IOException {

//        File file = Paths.get(System.getProperty("java.io.tmpdir"),
//                new Date().getTime() + ".xlsx")
//                .toFile();
        Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: sending mail 3");
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                fileName)
                .toFile();

//        if (!finalFile) {
        try (FileOutputStream out = new FileOutputStream(file)) {
            //        if (!finalFile) {
            workbook.write(out);
        }
//        }
//        List<EmailRecipient> recipients=new ArrayList<>();
//        recipients.add(new Recipient(email, email));
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_EMAILS);
//        List<EmailRecipient> recipients = EmailHelper.getMailRecipients("<EMAIL>");
        Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: sending mail " + emails);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
//        EmailRecipient recipient = new Recipient(email,email);
//        List<File> files= new ArrayList<File>();
//        files.add(file);
        TextEmail mail = new TextEmail(payrollMonth, "please find attached files");
        mail.addAttachement(file);
        Setup.getMailService().sendEmail(recipients, mail, null);
    }

    public static void sendMailv2(String fileName, MonthlyPayrollDocument finalPayrollFile, String payrollMonth, Boolean finalFile) throws FileNotFoundException, IOException {

        InputStream input = null;
        FileOutputStream out = null;
        try {
            Storage.getStream(finalPayrollFile.getAttachments().get(0));
            File file = Paths.get(System.getProperty("java.io.tmpdir"),
                    fileName)
                    .toFile();

            out = new FileOutputStream(file);
            int read;
            byte[] bytes = new byte[1024];

            while ((read = input.read(bytes)) != -1) {
                out.write(bytes, 0, read);
            }

            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_ADEEB_EMAIL);
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
            TextEmail mail = new TextEmail(payrollMonth, "Hash : " + finalPayrollFile.getFileHash());
            mail.addAttachement(file);
            Setup.getMailService().sendEmail(recipients, mail, null);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (input != null) {
                try{
                    input.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try{
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void sendFinalPayrollFilesViaEmail(String payrollFileName, String pwoFileName, LocalDate payrollDate) throws FileNotFoundException, IOException {

        MonthlyPayrollDocumentRepository monthlyPayrollDocumentRepository = Setup.getRepository(MonthlyPayrollDocumentRepository.class);
        List<MonthlyPayrollDocument> payrollFile = monthlyPayrollDocumentRepository.findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(payrollDate.toDate().getTime()), Boolean.TRUE, MonthlyPayrollDocumentType.MONTHLY_PAYROLL);
        List<MonthlyPayrollDocument> pwoFile = monthlyPayrollDocumentRepository.findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(payrollDate.toDate().getTime()), Boolean.TRUE, MonthlyPayrollDocumentType.PPWO);
        if (payrollFile.isEmpty() || pwoFile.isEmpty()) {
            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_ERROR_EMAIL);
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
            TextEmail mail = new TextEmail("Error in generating payroll", "Final Payroll file wasn't generated succesfully :( :( :( :( ");
            Setup.getMailService().sendEmail(recipients, mail, null);
        } else {
            InputStream input1 = null;
            FileOutputStream out1 = null;
            InputStream input2 = null;
            FileOutputStream out2 = null;
            try {
                input1 = Storage.getStream(payrollFile.get(0).getAttachments().get(0));
                File filePayroll = Paths.get(System.getProperty("java.io.tmpdir"),
                        payrollFileName)
                        .toFile();

                out1 = new FileOutputStream(filePayroll);
                int read;
                byte[] bytes1 = new byte[1024];

                while ((read = input1.read(bytes1)) != -1) {
                    out1.write(bytes1, 0, read);
                }
                //PWO file
                input2 = Storage.getStream(pwoFile.get(0).getAttachments().get(0));
                File filePWO = Paths.get(System.getProperty("java.io.tmpdir"),
                        pwoFileName)
                        .toFile();

                out2 = new FileOutputStream(filePWO);
                int read2 = 0;
                byte[] bytes2 = new byte[1024];

                while ((read2 = input2.read(bytes2)) != -1) {
                    out2.write(bytes2, 0, read2);
                }
                String emails = Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_ADEEB_EMAIL);
                Logger.getLogger(PayrollGenerationLibrary.class.getName())
                        .log(Level.SEVERE, "generateFinalPayroll - housemaids: 1" + emails);
                List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
//            TextEmail mail = new TextEmail("Final Payroll files of " + payrollMonth, "Housemaid Payroll file hash is : " + payrollFile.get(0).getFileHash() + "/n"
//                    + "PPWO file hash is : " + pwoFile.get(0).getFileHash() + " </br> ");
                String title = "Final Payroll files of " + DateUtil.formatMonth(payrollDate.toDate());
//            String body = "Housemaid Payroll file hash is : " + payrollFile.get(0).getFileHash() + "/n"
//                    + " PPWO file hash is : " + pwoFile.get(0).getFileHash() + "/n";

                //TextEmail mail = new TextEmail(title, body);
                Map<String, Object> params = new HashMap<>();
                params.put("payrollfile", payrollFile.get(0).getFileHash());
                params.put("pwofile", pwoFile.get(0).getFileHash());
                TemplateEmail mail = new TemplateEmail(title, "FinalPayrollFiles", params);
                mail.addAttachement(filePayroll);
                mail.addAttachement(filePWO);
                Setup.getMailService().sendEmail(recipients, mail, null);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (input1 != null) {
                    try {
                        input1.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (input2 != null) {
                    try {
                        input2.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (out1 != null) {
                    try {
                        out1.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (out2 != null) {
                    try {
                        out2.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

    }

    //*** Osamah ***//
    private final static List<HousemaidStatus> Statuses = Arrays.asList(HousemaidStatus.AVAILABLE,
            HousemaidStatus.PENDING_FOR_DISCIPLINE, HousemaidStatus.WITH_CLIENT,
            HousemaidStatus.RESERVED_FOR_PROSPECT, HousemaidStatus.SICK_WITHOUT_CLIENT,
            HousemaidStatus.ON_VACATION, HousemaidStatus.VIP_RESERVATIONS);

    private final static List<PaymentStatus> paymentStatuses = Arrays.asList(PaymentStatus.ADCB_PDC,
            PaymentStatus.PDC, PaymentStatus.RECEIVED);

    private static void putInMap(Map<String, Map<String, Date>> map, String key1, String key2, Date value) {
        if (map.get(key1) == null) {
            map.put(key1, new HashMap<>());
        }

        map.get(key1).put(key2, value);
    }

    public static List<PayrollException> getPayrollExceptions(List<Housemaid> housemaids, LocalDate dt, Map<String, Map<String, Date>> exceptionsTimes) {

        //Get Exit Maids
//        SelectQuery<Ticket> ticketQuery = new SelectQuery<>(Ticket.class);
//        ticketQuery.leftJoin("housemaid");
//        ticketQuery.filterBy(new SelectFilter()
//                .or(new SelectFilter().and("ticketType",
//                                "=",
//                                Ticket.TicketType.TO_EXIT)
//                ).or(new SelectFilter().and("ticketType",
//                                "=",
//                                Ticket.TicketType.TO_DUBAI)
//                ));
//        ticketQuery.filterBy("housemaid.isAgency", "=", false);
//        ticketQuery.filterBy("housemaid.freedomMaid", "=", false);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.REJECTED);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.EMPLOYEMENT_TERMINATED);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.UNREACHABLE);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.UNREACHABLE_AFTER_EXIT);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.TRACKED);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.IN_EXIT);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.PASSED_EXIT);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.FLYING_TO_EXIT);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.TERMINATED_BUT_STILL_ON_MOL);
//        ticketQuery.filterBy("housemaid.status", "!=", HousemaidStatus.VISA_UNSUCCESSFUL);
        debug("*******************************************************************************");
        debug("Starting Payroll Exceptions Generation Time: " + DateUtil.formatFullDateWithTime(new Date()));
        List<Housemaid> cleanExitMaids = housemaids.stream()
                .filter(x -> x.getCleanExitMaid() != null && x.getCleanExitMaid())
                .map(xx -> xx)
                .collect(Collectors.toList());//ticketQuery.execute();
        debug("Number of exit maids is " + cleanExitMaids.size());

        //Get Agency maids
//        SelectQuery<Ticket> ticketQuery2 = new SelectQuery<>(Ticket.class);
//        ticketQuery2.leftJoin("housemaid");
//        ticketQuery2.filterBy("ticketType",
//                "=",
//                Ticket.TicketType.TO_DUBAI);
//        ticketQuery2.filterBy("housemaid.isAgency", "=", true);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.REJECTED);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.EMPLOYEMENT_TERMINATED);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.UNREACHABLE);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.UNREACHABLE_AFTER_EXIT);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.TRACKED);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.IN_EXIT);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.PASSED_EXIT);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.FLYING_TO_EXIT);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.TERMINATED_BUT_STILL_ON_MOL);
//        ticketQuery2.filterBy("housemaid.status", "!=", HousemaidStatus.VISA_UNSUCCESSFUL);
        List<Housemaid> agencyMaids = housemaids.stream()
                .filter(x -> x.isIsAgency())
                .map(xx -> xx)
                .collect(Collectors.toList()); //ticketQuery2.stream.execute();
        debug("Number of agency maids is " + agencyMaids.size());

        List<Housemaid> freedomMaids = housemaids.stream()
                .filter(x -> x.getFreedomMaid() != null && x.getFreedomMaid())
                .map(xx -> xx)
                .collect(Collectors.toList()); //ticketQuery2.stream.execute();
        debug("Number of freedom maids is " + freedomMaids.size());

        List<PayrollException> resultList = new ArrayList<>();

//        HousemaidRepository housemaidRepository =
//                Setup.getRepository(HousemaidRepository.class);
        PaymentRepository paymentRepository
                = Setup.getRepository(PaymentRepository.class);

        PicklistRepository picklistRepository
                = Setup.getRepository(PicklistRepository.class);
        Picklist nationalityPicklist
                = picklistRepository.findByCode("nationalities");

        PicklistItemRepository picklistItemRepository
                = Setup.getRepository(PicklistItemRepository.class);
        PicklistItem ethiopianNationalityPicklistItem
                = picklistItemRepository.
                findByListAndCodeIgnoreCase(nationalityPicklist, "ethiopian");
        PicklistItem philippinesNationalityPicklistItem
                = picklistItemRepository.
                findByListAndCodeIgnoreCase(nationalityPicklist, "philippines");
        PicklistItem indonesianNationalityPicklistItem
                = picklistItemRepository.
                findByListAndCodeIgnoreCase(nationalityPicklist, "indonesian");

        Picklist typeOfPaymentPicklist
                = picklistRepository.findByCode("TypeOfPayment");

        PicklistItem monthlyPaymentTypePicklistItem
                = picklistItemRepository.
                findByListAndCodeIgnoreCase(typeOfPaymentPicklist, "monthly_payment");
        PicklistItem AccomindationFeesTypePicklistItem
                = picklistItemRepository.
                findByListAndCodeIgnoreCase(typeOfPaymentPicklist, "monthly_accommodation_fees");

        if (exceptionsTimes == null) {
            exceptionsTimes = new HashMap<>();
        }

//        for (int i = 1; i <= 7; i++) {
//            exceptionsTimes.put("e" + String.valueOf(i), new HashMap<>());
//        }
//        exceptionsTimes.put("e8&e9", new HashMap<>());
        //1
        //debug("Exceptions for housemaid " + bean.getHousemaidName());
        debug("first step - housemaid status");
        debug("Step 1 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e1").put("start", new Date());
        putInMap(exceptionsTimes, "e1", "start", new Date());
        resultList.addAll(housemaids.stream()
                .filter(x -> (x.getStatus() != null) && (!Statuses.contains(x.getStatus())))
                .map(xx -> new PayrollException(xx, PayrollExceptionType.WRONG_STATUS))
                .collect(Collectors.toList()));

//        exceptionsTimes.get("e1").put("end", new Date());
        putInMap(exceptionsTimes, "e1", "end", new Date());

        //2
        debug("Second step - housemaid Nationality - NON_EXIT_FILIPINO_WRONG_SALARY");
        debug("Step 2 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e2").put("start", new Date());
        putInMap(exceptionsTimes, "e2", "start", new Date());
        resultList.addAll(housemaids.stream()
                .filter(y -> (!isMaidVisa(y)) && y.getNationality() != null
                        && y.getNationality().getId() != null
                        && y.getNationality().getId().equals(philippinesNationalityPicklistItem.getId())
                        && y.getBasicSalary() != null
                        && y.getBasicSalary() > 1500
                        && (!(cleanExitMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                        && agencyMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                        && freedomMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty())))
                .map(xx -> new PayrollException(xx, PayrollExceptionType.NON_EXIT_FILIPINO_WRONG_SALARY))
                .collect(Collectors.toList()));

//        exceptionsTimes.get("e2").put("end", new Date());
        putInMap(exceptionsTimes, "e2", "end", new Date());

        //3
        debug("Third step - EXIT_FILIPINO_WRONG_SALARY");
        debug("Step 3 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e3").put("start", new Date());
        putInMap(exceptionsTimes, "e3", "start", new Date());

        try {
            Date date3 = DateUtil.parseDateDashed("2017-10-22");
            resultList.addAll(housemaids.stream()
                    .filter(y -> (!isMaidVisa(y)) && y.getNationality() != null
                            && y.getNationality().getId() != null
                            && y.getNationality().getId().equals(philippinesNationalityPicklistItem.getId())
                            && y.getBasicSalary() != null
                            && y.getBasicSalary() > 1800
                            && y.getLandedInDubaiDate().before(date3)
                            && (cleanExitMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && agencyMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && freedomMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()))
                    .map(xx -> new PayrollException(xx, PayrollExceptionType.EXIT_FILIPINO_WRONG_SALARY))
                    .collect(Collectors.toList()));
        } catch (ParseException ex) {
            Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, null, ex);
        }

//        exceptionsTimes.get("e3").put("end", new Date());
        putInMap(exceptionsTimes, "e3", "end", new Date());

        //4
        debug("Forth step - ETHIOPIAN_WRONG_SALARY");
        debug("Step 4 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e4").put("start", new Date());
        putInMap(exceptionsTimes, "e4", "start", new Date());

        resultList.addAll(housemaids.stream()
                .filter(y -> (!isMaidVisa(y)) && y.getNationality() != null
                        && y.getNationality().getId() != null
                        && y.getNationality().getId().equals(ethiopianNationalityPicklistItem.getId())
                        && y.getBasicSalary() != null
                        && y.getBasicSalary() > 1000)
                .map(xx -> new PayrollException(xx, PayrollExceptionType.ETHIOPIAN_WRONG_SALARY))
                .collect(Collectors.toList()));
//        exceptionsTimes.get("e4").put("end", new Date());
        putInMap(exceptionsTimes, "e4", "end", new Date());

        //5
        debug("5th step - INDONESIAN_WRONG_SALARY");
        debug("Step 5 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e5").put("start", new Date());
        putInMap(exceptionsTimes, "e5", "start", new Date());

        resultList.addAll(housemaids.stream()
                .filter(y -> (!isMaidVisa(y)) && y.getNationality() != null
                        && y.getNationality().getId() != null
                        && y.getNationality().getId().equals(indonesianNationalityPicklistItem.getId())
                        && y.getBasicSalary() != null
                        && y.getBasicSalary() > 1400)
                .map(xx -> new PayrollException(xx, PayrollExceptionType.INDONESIAN_WRONG_SALARY))
                .collect(Collectors.toList()));
//        exceptionsTimes.get("e5").put("end", new Date());
        putInMap(exceptionsTimes, "e5", "end", new Date());

        //6
        debug("6th step -AGENCY_WRONG_SALARY");
        debug("Step 6 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e6").put("start", new Date());
        putInMap(exceptionsTimes, "e6", "start", new Date());

        try {
            Date date6 = DateUtil.parseDateDashed("2017-11-01");
            resultList.addAll(housemaids.stream()
                    .filter(y -> (!isMaidVisa(y)) && !agencyMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && y.getBasicSalary() != null
                            && y.getBasicSalary() > 1500 && y.getStartDate().after(date6))
                    .map(xx -> new PayrollException(xx, PayrollExceptionType.AGENCY_WRONG_SALARY))
                    .collect(Collectors.toList()));
//            exceptionsTimes.get("e6").put("end", new Date());
            putInMap(exceptionsTimes, "e6", "end", new Date());
        } catch (ParseException ex) {
            Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, null, ex);
        }
        //7
        debug("7th step - LIVE_IN_MAID_WITH_FOOD_OR_HOUSING");
        debug("Step 7 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e7").put("start", new Date());
        putInMap(exceptionsTimes, "e7", "start", new Date());

        resultList.addAll(housemaids.stream()
                .filter(y -> (y.getLiving() != null
                        && y.getLiving() == HousemaidLiveplace.IN
                        && ((y.getHousingAllowance() != null && y.getHousingAllowance() != 0)
                        || (y.getFoodAllowance() != null && y.getFoodAllowance() != 0))))
                .map(xx -> new PayrollException(xx, PayrollExceptionType.LIVE_IN_MAID_WITH_FOOD_OR_HOUSING))
                .collect(Collectors.toList()));
//        exceptionsTimes.get("e7").put("end", new Date());
        putInMap(exceptionsTimes, "e7", "end", new Date());

        putInMap(exceptionsTimes, "e8&e9", "start", new Date());

//        exceptionsTimes.get("e8&e9").put("start", new Date());
        debug("8th step - MAID_WITH_FOOD_AND_HOUSING_AND_WRONG_CONTRACT_PAYMENT");
        debug("Step 8 Time: " + DateUtil.formatFullDateWithTime(new Date()));
        debug("9th step - LIVE_IN_MAID_WITH_FOOD_OR_HOUSING");
        debug("Step 9 Time: " + DateUtil.formatFullDateWithTime(new Date()));

        for (Housemaid housemaid : housemaids) {

            Contract currentContract = getCurrnetContract(housemaid);
            if (currentContract != null) {
                DateTime now = new DateTime(new Date().getTime());
                LocalDate nextMonth = dt.plusMonths(1);
                List<Payment> payments = paymentRepository.findByContract(currentContract);
                List<Payment> curMonthPayments = payments.stream()
                        .filter(x -> x.getStatus() != null && paymentStatuses.contains(x.getStatus())
                                && x.getTypeOfPayment() != null
                                && x.getTypeOfPayment().getId().equals(monthlyPaymentTypePicklistItem.getId())
                                && x.getDateOfPayment() != null
                                && dt.getYear() == new DateTime(x.getDateOfPayment().getTime()).getYear()
                                && dt.getMonthOfYear() == new DateTime(x.getDateOfPayment().getTime()).getMonthOfYear())
                        .collect(Collectors.toList());
                /////////////////
                List<Payment> curMonthPaymentsAccomindationFees = payments.stream()
                        .filter(x -> x.getStatus() != null && paymentStatuses.contains(x.getStatus())
                                && x.getTypeOfPayment() != null
                                && x.getTypeOfPayment().getId().equals(AccomindationFeesTypePicklistItem.getId())
                                && x.getDateOfPayment() != null
                                && dt.getYear() == new DateTime(x.getDateOfPayment().getTime()).getYear()
                                && dt.getMonthOfYear() == new DateTime(x.getDateOfPayment().getTime()).getMonthOfYear())
                        .collect(Collectors.toList());

                //////
                List<Payment> nextMonthPayments = payments.stream()
                        .filter(x -> x.getStatus() != null && paymentStatuses.contains(x.getStatus())
                                && x.getTypeOfPayment() != null
                                && x.getTypeOfPayment().getId().equals(monthlyPaymentTypePicklistItem.getId())
                                && x.getDateOfPayment() != null
                                && nextMonth.getYear() == new DateTime(x.getDateOfPayment().getTime()).getYear()
                                && nextMonth.getMonthOfYear() == new DateTime(x.getDateOfPayment().getTime()).getMonthOfYear())
                        .collect(Collectors.toList());

                List<Payment> nextMonthPaymentsAccomindationFees = payments.stream()
                        .filter(x -> x.getStatus() != null && paymentStatuses.contains(x.getStatus())
                                && x.getTypeOfPayment() != null
                                && x.getTypeOfPayment().getId().equals(AccomindationFeesTypePicklistItem.getId())
                                && x.getDateOfPayment() != null
                                && nextMonth.getYear() == new DateTime(x.getDateOfPayment().getTime()).getYear()
                                && nextMonth.getMonthOfYear() == new DateTime(x.getDateOfPayment().getTime()).getMonthOfYear())
                        .collect(Collectors.toList());

                //8
                double accomoundaitionFeesCur = (curMonthPaymentsAccomindationFees.size() > 0) ? curMonthPaymentsAccomindationFees.get(0).getAmountOfPayment() : 0;
                double accomoundaitionFeesNext = (nextMonthPaymentsAccomindationFees.size() > 0) ? nextMonthPaymentsAccomindationFees.get(0).getAmountOfPayment() : 0;

                if (curMonthPayments.size() > 0 && curMonthPayments.get(0) != null
                        && curMonthPayments.get(0).getAmountOfPayment() != null
                        && (curMonthPayments.get(0).getAmountOfPayment() + accomoundaitionFeesCur) < 4500 && ((housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() != 0)
                        || (housemaid.getFoodAllowance() != null && housemaid.getFoodAllowance() != 0))) {
                    PayrollException exception
                            = new PayrollException(housemaid,
                            PayrollExceptionType.MAID_WITH_FOOD_AND_HOUSING_AND_WRONG_CONTRACT_PAYMENT,
                            curMonthPayments.get(0).getAmountOfPayment());
                    resultList.add(exception);
                } else if (nextMonthPayments.size() > 0 && nextMonthPayments.get(0) != null
                        && nextMonthPayments.get(0).getAmountOfPayment() != null
                        && (nextMonthPayments.get(0).getAmountOfPayment() + accomoundaitionFeesNext) < 4500
                        && ((housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() != 0)
                        || (housemaid.getFoodAllowance() != null && housemaid.getFoodAllowance() != 0))) {
                    PayrollException exception
                            = new PayrollException(housemaid,
                            PayrollExceptionType.MAID_WITH_FOOD_AND_HOUSING_AND_WRONG_CONTRACT_PAYMENT,
                            nextMonthPayments.get(0).getAmountOfPayment());
                    resultList.add(exception);
                }

                //9
                if (curMonthPayments.size() > 0 && curMonthPayments.get(0) != null
                        && curMonthPayments.get(0).getAmountOfPayment() != null
                        && housemaid.getBasicSalary() != null
                        && Math.abs(curMonthPayments.get(0).getAmountOfPayment()
                        - housemaid.getBasicSalary()) < 1050
                        && isMaidVisa(housemaid)) {
                    PayrollException exception
                            = new PayrollException(housemaid,
                            PayrollExceptionType.WRONG_MAIDVISA_SALARY);
                    resultList.add(exception);
                }
            }
        }

//        exceptionsTimes.get("e8&e9").put("end", new Date());
        putInMap(exceptionsTimes, "e8&e9", "end", new Date());

        //10
        debug("10th step -AGENCY_WRONG_SALARY_BEFORE_NOV_2017");
        debug("Step 10 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e10").put("start", new Date());
        putInMap(exceptionsTimes, "e10", "start", new Date());

        try {
            Date date2 = DateUtil.parseDateDashed("2017-11-01");
            resultList.addAll(housemaids.stream()
                    .filter(y -> (!isMaidVisa(y)) && !agencyMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && y.getBasicSalary() != null
                            && y.getBasicSalary() > 1600 && y.getStartDate().before(date2))
                    .map(xx -> new PayrollException(xx, PayrollExceptionType.AGENCY_WRONG_SALARY_BEFORE_NOV_2017))
                    .collect(Collectors.toList()));
//            exceptionsTimes.get("e10").put("end", new Date());
            putInMap(exceptionsTimes, "e10", "end", new Date());

        } catch (ParseException ex) {
            Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, null, ex);
        }

        //11
        debug("Third step - EXIT_FILIPINO_WRONG_SALARY_BETWEEN");
        debug("Step 11 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e11").put("start", new Date());
        putInMap(exceptionsTimes, "e11", "start", new Date());

        try {
            Date date11 = DateUtil.parseDateDashed("2017-10-22");
            Date dateBefore11 = DateUtil.parseDateDashed("2018-05-01");
            resultList.addAll(housemaids.stream()
                    .filter(y -> (!isMaidVisa(y)) && y.getNationality() != null
                            && y.getNationality().getId() != null
                            && y.getNationality().getId().equals(philippinesNationalityPicklistItem.getId())
                            && y.getBasicSalary() != null
                            && y.getBasicSalary() > 1600
                            && y.getLandedInDubaiDate() != null
                            && y.getLandedInDubaiDate().after(date11)
                            && y.getLandedInDubaiDate().before(dateBefore11)
                            && (cleanExitMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && agencyMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && freedomMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()))
                    .map(xx -> new PayrollException(xx, PayrollExceptionType.EXIT_FILIPINO_WRONG_SALARY_BETWEEN_01_05_2018_AND_22_10_2017))
                    .collect(Collectors.toList()));
        } catch (ParseException ex) {
            Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, null, ex);
        }

//        exceptionsTimes.get("e11").put("end", new Date());
        putInMap(exceptionsTimes, "e11", "end", new Date());

        //12
        debug("Third step - EXIT_FILIPINO_WRONG_SALARY_AFTER");
        debug("Step 12 Time: " + DateUtil.formatFullDateWithTime(new Date()));
//        exceptionsTimes.get("e12").put("start", new Date());
        putInMap(exceptionsTimes, "e12", "start", new Date());

        try {
            Date date12 = DateUtil.parseDateDashed("2018-05-01");
            resultList.addAll(housemaids.stream()
                    .filter(y -> (!isMaidVisa(y)) && y.getNationality() != null
                            && y.getNationality().getId() != null
                            && y.getNationality().getId().equals(philippinesNationalityPicklistItem.getId())
                            && y.getBasicSalary() != null
                            && y.getBasicSalary() > 1500
                            && y.getLandedInDubaiDate() != null
                            && y.getLandedInDubaiDate().after(date12)
                            && (cleanExitMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && agencyMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()
                            && freedomMaids.stream().filter(x -> x.getId().equals(y.getId())).collect(Collectors.toList()).isEmpty()))
                    .map(xx -> new PayrollException(xx, PayrollExceptionType.EXIT_FILIPINO_WRONG_SALARY_AFTER_01_05_2018))
                    .collect(Collectors.toList()));
        } catch (ParseException ex) {
            Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, null, ex);
        }

//        exceptionsTimes.get("e12").put("end", new Date());
        putInMap(exceptionsTimes, "e12", "end", new Date());

        debug("Ending Payroll Exceptions Generation Time: : " + DateUtil.formatFullDateWithTime(new Date()));
        debug("*******************************************************************************");

        resultList.sort(Comparator.comparing(PayrollException::getType));

        return resultList;

    }

    /**
     * @param bean
     * @return
     */
    public static Contract getCurrnetContract(HousemaidPayrollBean bean) {
        List<Contract> contracts
                = bean.getHousemaid().getContracts().stream()
                .filter(x -> x.getStatus().equals(ContractStatus.ACTIVE))
                .collect(Collectors.toList());
        Contract currentContract = null;
        if (contracts.size() > 0) {
            currentContract = contracts.get(0);
        }
        return currentContract;
    }

    public static Contract getCurrnetContract(Housemaid housemaid) {
        List<Contract> contracts
                = housemaid.getContracts().stream()
                .filter(x -> x.getStatus().equals(ContractStatus.ACTIVE))
                .collect(Collectors.toList());
        Contract currentContract = null;
        if (contracts.size() > 0) {
            currentContract = contracts.get(0);
        }
        return currentContract;
    }

    //Jirra ACC-278
    public boolean isMaidVisa(HousemaidPayrollBean bean) {
        Contract currentContract = getCurrnetContract(bean);

        PicklistItemRepository picklistItemRepository
                = Setup.getRepository(PicklistItemRepository.class);
        PicklistRepository picklistRepository
                = Setup.getRepository(PicklistRepository.class);
        Picklist Picklist
                = picklistRepository.findByCode(PayrollManagementModule.PICKLIST_PROSPECTTYPE);
        PicklistItem maidVisaPicklistItem =
                picklistItemRepository.findByListAndCodeIgnoreCase(Picklist,
                        "maidvisa.ae_prospect");

        boolean isMaidVisa = false;
        if (currentContract != null &&
                currentContract.getContractProspectType() != null &&
                currentContract.getContractProspectType().getId() == maidVisaPicklistItem.getId()) {
            isMaidVisa = true;
        }
        return isMaidVisa;
    }

    //Jirra ACC-278
    public static boolean isMaidVisa(Housemaid h) {
        Contract currentContract = getCurrnetContract(h);

        PicklistItemRepository picklistItemRepository
                = Setup.getRepository(PicklistItemRepository.class);
        PicklistRepository picklistRepository
                = Setup.getRepository(PicklistRepository.class);
        Picklist Picklist
                = picklistRepository.findByCode(PayrollManagementModule.PICKLIST_PROSPECTTYPE);
        PicklistItem maidVisaPicklistItem =
                picklistItemRepository.findByListAndCodeIgnoreCase(Picklist,
                        "maidvisa.ae_prospect");

        boolean isMaidVisa = false;
        if (currentContract != null &&
                currentContract.getContractProspectType() != null &&
                currentContract.getContractProspectType().getId() == maidVisaPicklistItem.getId()) {
            isMaidVisa = true;
        }
        return isMaidVisa;
    }

    /**
     * This is the final payroll list that will be saved in the MonthlyPayroll
     * table
     *
     * @param housemaids
     * @return
     */
    @Transactional
    public static void saveFinalPayrollList(List<HousemaidPayrollBean> housemaids, LocalDate payrollDate) {

        List<MonthlyPayroll> finalHousemaidList = new ArrayList<MonthlyPayroll>();
        for (HousemaidPayrollBean bean : housemaids) {
            MonthlyPayroll record = new MonthlyPayroll();
            record.setPayslipGenerated(false);
            record.setEmployeeUniqueId(bean.getEmployeeUniqueId());
            record.setAgentId(bean.getAgentId());
            record.setEmployeeAccountWithAgent(bean.getEmployeeAccountWithAgent());
            record.setHousemaidName(bean.getHousemaidName());
            record.setContractName(bean.getContractName());
            record.setStatus(bean.getStatus());
            record.setSource(bean.getSource());
            record.setFreedomOperatorName(bean.getFreedomOperatorName());
            record.setOrginalLoan(bean.getOrginalLoan());
            record.setRemainingLoanBalance(bean.getRemainingLoanBalance());
//            record.setRemainingCashAdvance(bean.getRemainingCashAdvance());
            record.setThisMonthForgiveness(bean.getThisMonthForgiveness());
            record.setLoanRepayment(bean.getLoanRepayment());
            record.setStartDateDeduction(bean.getStartDateDeduction());
            record.setComplaintDeduction(bean.getComplaintDeduction());
            record.setManagerDedution(bean.getManagerDeduction());
            record.setPrimarySalary(bean.getHousemaid().getPrimarySalary() != null ? bean.getHousemaid().getPrimarySalary() : 0.0);
            record.setMonthlyLoan(bean.getHousemaid().getMonthlyLoan() != null ? bean.getHousemaid().getMonthlyLoan() : 0.0);
            record.setHoliday(bean.getHousemaid().getHoliday() != null ? bean.getHousemaid().getHoliday() : 0.0);
            record.setOverTime(bean.getHousemaid().getOverTime() != null ? bean.getHousemaid().getOverTime() : 0.0);
            record.setAirfareFee(bean.getHousemaid().getAirfareFee() != null ? bean.getHousemaid().getAirfareFee() : 0.0);
            record.setTotalSalary(bean.getBasicSalary());
            record.setWorkingVacationPay(bean.getWorkingVacationPay());
            record.setVacationAirFare(bean.getVacationAirFare());
            record.setLiving(bean.getLiving().toString());
            record.setCompanyAccommodated(bean.getCompanyAccommodated());
            record.setHousingAllowance(bean.getHousingAllowance());
            record.setFoodAllowance(bean.getFoodAllowance());
            record.setManagerAddition(bean.getManagerAddition());
            record.setAnsari(bean.getAnsari());
            record.setMaidVisaAEContract(bean.getMaidVisaAEContract());
            //record.setPayrollMonth(payrollDate.toString("MMMM"));
            record.setHousemaid(bean.getHousemaid());
            record.setPayrollDate(new java.sql.Date(payrollDate.toDate().getTime()));
            record.setStartingDate(bean.getStartingDate());
            finalHousemaidList.add(record);

        }
        MonthlyPayrollRepository monthlyPayrollRepository = Setup.getRepository(MonthlyPayrollRepository.class);
        monthlyPayrollRepository.save(finalHousemaidList);

    }

    /**
     * Save the final Payroll file in the DB
     */
    public static void saveFinalPayrollFile(String fileName, XSSFWorkbook workbook, LocalDate payrollDate, MonthlyPayrollDocumentType type) throws FileNotFoundException, IOException, NoSuchAlgorithmException {

        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                fileName)
                .toFile();

        try (FileOutputStream out = new FileOutputStream(file)) {
            workbook.write(out);
        }

        MonthlyPayrollDocument finalFile = new MonthlyPayrollDocument();
        finalFile.setFinalFile(true);
        finalFile.setPayrollDate(new java.sql.Date(payrollDate.toDate().getTime()));
        finalFile.setType(type);
        finalFile.setUploadDate(new LocalDate().toDate());

        //convert file to multipart file
        try (FileInputStream input = new FileInputStream(file)) {
            MultipartFile multiPartFile = new MockMultipartFile(fileName,
                    fileName, "text/plain", IOUtils.toByteArray(input));


            //Create Attachment
            Attachment attach = Storage.storeTemporary(multiPartFile, "PayrollFile", Boolean.TRUE);
            finalFile.setFileHash(hashFile(file, "MD5"));
            finalFile.setAttachments(new ArrayList<>(Arrays.asList(attach)));
        }

        MonthlyPayrollDocumentRepository monthlyPayrollDocumentRepository = Setup.getRepository(MonthlyPayrollDocumentRepository.class);
        monthlyPayrollDocumentRepository.save(finalFile);
    }

    public static String hashFile(File file, String algorithm) throws NoSuchAlgorithmException, IOException {
        MessageDigest digest = MessageDigest.getInstance(algorithm);
        byte[] bytesBuffer = new byte[1024];
        int bytesRead;

        try(FileInputStream inputStream = new FileInputStream(file)) {
            while ((bytesRead = inputStream.read(bytesBuffer)) != -1) {
                digest.update(bytesBuffer, 0, bytesRead);
            }

            byte[] hashedBytes = digest.digest();
            return convertByteArrayToHexString(hashedBytes);
        }
    }

    private static String convertByteArrayToHexString(byte[] arrayBytes) {
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < arrayBytes.length; i++) {
            stringBuffer.append(Integer.toString((arrayBytes[i] & 0xff) + 0x100, 16)
                    .substring(1));
        }
        return stringBuffer.toString();
    }

    public static void debug(String message) {
        //System.out.println(message);
    }

    //Generate Housemaid Payroll file as CSV
    public static InputStream generateHousemaidPayrollCSVFromData(List<HousemaidPayrollBean> data) throws FileNotFoundException {
        CSVWriter csvWriter = null;
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();

        try {
            //Create CSVWriter for writing to Employee.csv
            csvWriter = new CSVWriter(new FileWriter(file));

            BeanToCsv bc = new BeanToCsv();

            ColumnPositionMappingStrategy mappingStrategy
                    = new ColumnPositionMappingStrategy();
            mappingStrategy.setType(HousemaidPayrollBean.class);
            String[] columns = new String[]{"indexNum", "employeeUniqueId", "agentId", "employeeAccountWithAgent", "housemaidName",
                    "startingDateV", "contractName", "orginalLoan", "remainingLoanBalance", "remainingCashAdvance", "thisMonthForgiveness",
                    "loanRepaymentV", "startDateDeductionV", "complaintDeductionV", "managerDeductionV", "failedInterviewDeduction",
                    "replacementDeduction", "basicSalaryV", "workingVacationPayV", /*"vacationAirFareV",*/ "livingV",
                    "companyAccommodated", "housingAllowance", "foodAllowanceV", "managerAdditionV", "ansari", "maidVisaAEContract"};
            //Setting the colums for mappingStrategy
            mappingStrategy.setColumnMapping(columns);
            //Writing empList to csv file
            bc.write(mappingStrategy, csvWriter, data);

            System.out.println("CSV File written successfully!!!");

        } catch (IOException e) {
        } finally {
            try {
                //closing the writer
                if (csvWriter != null) {
                    csvWriter.close();
                }
            } catch (IOException ee) {
            }
        }
        return new FileInputStream(file);
    }

    //Generate Payslips to FB error reprot
    public static InputStream generatePayslipsFBErrorReport(List<FbPaySlipsLog> data) throws FileNotFoundException {
        CSVWriter csvWriter = null;
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();
        List<PaySlipError> errors = new ArrayList<PaySlipError>();
        for (FbPaySlipsLog log : data) {
            PaySlipError bean = new PaySlipError();
            bean.setHousemaidName(log.getHousemaid().getName());
            bean.setId(log.getHousemaid().getId().toString());
            bean.setHousemaidNumber(log.getHousemaid().getPhoneNumber());
            bean.setNationality(log.getHousemaid().getNationality().getName());
            bean.setStatus(log.getHousemaid().getStatus().toString());
            bean.setClientName(log.getHousemaid().getCurrentClient() != null ? log.getHousemaid().getCurrentClient().getName() : "");
            bean.setClientNumber(log.getHousemaid().getCurrentClient() != null ? log.getHousemaid().getCurrentClient().getMobileNumber() : "");
            bean.setError(log.getStatus().toString());
            errors.add(bean);
        }

        try {

            csvWriter = new CSVWriter(new FileWriter(file));

            BeanToCsv bc = new BeanToCsv();

            ColumnPositionMappingStrategy mappingStrategy
                    = new ColumnPositionMappingStrategy();
            mappingStrategy.setType(PaySlipError.class);
            String[] columns = new String[]{"id", "housemaidName", "housemaidNumber", "nationality", "status", "clientName", "clientNumber", "error"};
            //Setting the colums for mappingStrategy
            mappingStrategy.setColumnMapping(columns);
            //Writing empList to csv file
            bc.write(mappingStrategy, csvWriter, errors);

            System.out.println("CSV File written successfully!!!");

        } catch (CsvBadConverterException | IOException ee) {
        } finally {
            try {
                //closing the writer
                if (csvWriter != null) {
                    csvWriter.close();
                }
            } catch (IOException ee) {
            }
        }
        return new FileInputStream(file);
    }

    public static String formatValues(Double value){

        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(0);
        return value != null ? (value % 1 == 0 ? nf.format(value) : value.toString()) : "0";
    }

    public static File createHouseMaidPayslipAsWordDocument(HousemaidPayslip paySlip, String lang) throws URISyntaxException, IOException {

        PayslipTranslateService translateService = PayslipTranslateService.getTranslateService(lang);

        URL resource = PayrollGenerationLibrary.class.getResource("/Payslips-HouseMaid-" + lang + ".docx");
        File file = new File(resource.toURI());
        XWPFDocument document = new XWPFDocument(new FileInputStream(file));
        XWPFTable table = document.getTables().get(0);
        XWPFTableRow tableRowOne = table.getRow(1);
        //Employee name
        tableRowOne.getCell(0).getTables().get(0).getRow(0).getCell(1).setText(paySlip.getHousemaid().getName());

        //Payslip Month
        //Jirra ACC-386
        tableRowOne.getCell(0).getTables().get(0).getRow(1).getCell(1).setText(DateUtil.formatSimpleMonthYear(paySlip.getPaySlipDate()));

        //Start date
        tableRowOne.getCell(0).getTables().get(0).getRow(2).getCell(1).setText(DateUtil.formatFullDate(paySlip.getHousemaid().getStartDate()));

        HashMap<String, Integer> earnings = new HashMap<>();
        earnings.put("MOHRE", 1);
        earnings.put("OverTimeAndHolidays", 2);
        earnings.put("CashAdvacnceLoan", 3);
        earnings.put("BasicMonthlyPay", 4);

        XWPFTableRow tableRow5 = table.getRow(5);
        //Earnings
        XWPFTableCell row5Cell0 = tableRow5.getCell(0);

        XWPFTable earningsTable = row5Cell0.getTables().get(0);
        //MOHRE Salary
        earningsTable.getRow(earnings.get("MOHRE")).getCell(1)
                .setText(formatValues(paySlip.getMohreSalary()));


        //Over Time and Holidays
        earningsTable.getRow(earnings.get("OverTimeAndHolidays")).getCell(1).setText(
                formatValues(paySlip.getHolidays() != 0 ? paySlip.getHolidays() : paySlip.getOverTime()));
        //Cash Advanced Loan
        earningsTable.getRow(earnings.get("CashAdvacnceLoan")).getCell(1).setText(formatValues(paySlip.getCashAdvanceLoan()));

        //Basic Monthly Pay
        // to set the styling programmatically
        earningsTable.getRow(earnings.get("BasicMonthlyPay")).getCell(1).removeParagraph(0);
        XWPFParagraph basicMonthlyPayParagraph = earningsTable.getRow(earnings.get("BasicMonthlyPay")).getCell(1).addParagraph();
        basicMonthlyPayParagraph.setVerticalAlignment(TextAlignment.CENTER);
        basicMonthlyPayParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(basicMonthlyPayParagraph.createRun(), 10, "00b050", formatValues(paySlip.getBasicMonthlyPay()), true);


        XWPFTable basicPayThisMonth = row5Cell0.getTables().get(1);

        // this means that this is first month for maid and she starts before payroll ends.
        boolean isFirstPaySlipAndBeforePayrollEnds = false;
        if (!paySlip.getBasicPayThisMonth().equals(0D)) {
            isFirstPaySlipAndBeforePayrollEnds = true;
        }

        if (isFirstPaySlipAndBeforePayrollEnds) {

            basicPayThisMonth.getRow(0).getCell(0).removeParagraph(0);
            XWPFParagraph basicPayThisMonthTitleParagraph = basicPayThisMonth.getRow(0).getCell(0).addParagraph();
            basicPayThisMonthTitleParagraph.setVerticalAlignment(TextAlignment.CENTER);
            basicPayThisMonthTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
            setRun(basicPayThisMonthTitleParagraph.createRun(), 10, "000000",
                    translateService.basicPayThisMonth(paySlip.getOfWorkingDaysThisMonth()), true);

            basicPayThisMonth.getRow(0).getCell(1).removeParagraph(0);
            XWPFParagraph basicPayThisMonthValueParagraph = basicPayThisMonth.getRow(0).getCell(1).addParagraph();
            basicPayThisMonthValueParagraph.setVerticalAlignment(TextAlignment.CENTER);
            basicPayThisMonthValueParagraph.setAlignment(ParagraphAlignment.CENTER);
            setRun(basicPayThisMonthValueParagraph.createRun(), 10, "000000", formatValues(paySlip.getBasicPayThisMonth()), true);

        }

        XWPFTable managerAdditionsTable = row5Cell0.getTables().get(3);
        XWPFTable ticketAllowanceTable = row5Cell0.getTables().get(2);
        //Jirra ACC-1774
        if (paySlip.getVacationAirFare().equals(0D))
            ticketAllowanceTable.removeRow(0);
        else
            ticketAllowanceTable.getRow(0).getCell(1).setText(formatValues(paySlip.getVacationAirFare()));

        PicklistItem coverDeductionLimitAdditionReason =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_COVER_DEDUCTION_LIMIT_ADDITION_CODE);
        PicklistItem coverNegativeSalaryAdditionReason =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_COVER_NEGATIVE_SALARY_ADDITION_CODE);


        int lastUsedRowNumberAdditions = 1;
        Double additionsCoverDeductionLimitAndNegativeSalary = 0D;
        Double otherAdditions = 0D;
        List<PayrollManagerNote> additionsList = paySlip.getAdditionsList();
        for (PayrollManagerNote payrollManagerNote : additionsList) {
            String name = "";
            if (payrollManagerNote.getAdditionReason() != null) {
                if (!payrollManagerNote.getAdditionReason().getId().equals(coverDeductionLimitAdditionReason.getId())
                        && !payrollManagerNote.getAdditionReason().getId().equals(coverNegativeSalaryAdditionReason.getId())
                ) {
                    name = translateService.translateAdditionReason(payrollManagerNote);//.getAdditionReason().getName();PAY-47
                    otherAdditions += payrollManagerNote.getAmount();
                } else {
                    additionsCoverDeductionLimitAndNegativeSalary += payrollManagerNote.getAmount();
                    continue;
                }
            } else {
                name = translateService.managerAdditions(); // PAY-47
                otherAdditions += payrollManagerNote.getAmount();
            }
            try {
                XWPFTableRow expressionsRow = managerAdditionsTable.getRow(1);
                CTRow ctrow = CTRow.Factory.parse(expressionsRow.getCtRow().newInputStream());
                XWPFTableRow row = new XWPFTableRow(ctrow, managerAdditionsTable);

                row.getCell(0).setText(name);
                row.getCell(1)
                        .setText(formatValues(payrollManagerNote.getAmount()));

                managerAdditionsTable.addRow(row, lastUsedRowNumberAdditions + 1);

                lastUsedRowNumberAdditions++;

            } catch (XmlException e) {
                e.printStackTrace();
            }
        }
        if (otherAdditions == 0) {
            managerAdditionsTable.removeRow(2);
            managerAdditionsTable.removeRow(1);
            managerAdditionsTable.removeRow(0);
        } else {
            managerAdditionsTable.removeRow(1);
            // to set the styling programmatically
            managerAdditionsTable.getRow(lastUsedRowNumberAdditions).getCell(1).removeParagraph(0);
            XWPFParagraph totalAdditionsParagraph = managerAdditionsTable.getRow(lastUsedRowNumberAdditions).getCell(1).addParagraph();
            totalAdditionsParagraph.setVerticalAlignment(TextAlignment.CENTER);
            totalAdditionsParagraph.setAlignment(ParagraphAlignment.CENTER);
            setRun(totalAdditionsParagraph.createRun(), 10, "000000", formatValues(otherAdditions), true);

        }


        HashMap<String, Integer> deductions = new HashMap<>();
//        deductions.put("Repayment", 1);
        deductions.put("UnsettledDeductionPrevMonth", 1);
        deductions.put("TotalDeductionAmount", 2);

        //Deductions
        XWPFTableCell row5Cell2 = tableRow5.getCell(2);
        XWPFTable deductionTable = row5Cell2.getTables().get(0);

        //Repayment This Month
//        deductionTable.getRow(deductions.get("Repayment")).getCell(1).setText(paySlip.getRepaymentThisMonth().toString());


        Double unsettledDeductions
                = paySlip.getHousemaidPayrollLog().getRemainingLoan()
                + paySlip.getHousemaidPayrollLog().getLoanRepayment()
                - (paySlip.getAdditionToBalanceDeductionLimit() != null ? paySlip.getAdditionToBalanceDeductionLimit() : 0);

        // Unsettled Deduction from prev Month
        deductionTable.getRow(deductions.get("UnsettledDeductionPrevMonth")).getCell(1).setText(formatValues(unsettledDeductions));

        int lastUsedRowNumber = 2;
        int rowsAdded = 0;
        Double totalDeduction = 0D;
        for (PayrollManagerNote payrollManagerNote : paySlip.getDeduction()) {
            try {
                XWPFTableRow expressionsRow = deductionTable.getRow(2);
                CTRow ctrow = CTRow.Factory.parse(expressionsRow.getCtRow().newInputStream());
                XWPFTableRow row = new XWPFTableRow(ctrow, deductionTable);
                totalDeduction += payrollManagerNote.getAmount();
                // format this names as requested in balsamique
                String name = "";
                if (payrollManagerNote.getDeductionReason() != null) {
                    if (payrollManagerNote.getDeductionReason().hasTag("no_show")) {
                        name = translateService.noShowDeduction(DateUtil.getDateFormatDDMM(payrollManagerNote.getNoteDate()));
//                        "No show deduction on " PAY-47
//                                + DateUtil.getDateFormatDDMM(payrollManagerNote.getNoteDate());
                    } else if (payrollManagerNote.getDeductionReason().hasTag("replacement")) {
                        name = translateService.replacement(payrollManagerNote.getDeductionReason().getName());
//                        "Replacement ( " PAY-47
//                                + payrollManagerNote.getDeductionReason().getName()
//                                + " )";
                    } else if (payrollManagerNote.getDeductionReason().hasTag("failed_interview")) {
                        name = translateService.failedInterview(DateUtil.getDateFormatDDMM(payrollManagerNote.getNoteDate()),
                                DateUtil.getTimeFormatHHMMA(payrollManagerNote.getNoteDate()));
//                        "Failed interview on " PAY-47
//                                + DateUtil.getDateFormatDDMM(payrollManagerNote.getNoteDate())
//                                + " at " + DateUtil.getTimeFormatHHMMA(payrollManagerNote.getNoteDate());
                    } else {
                        name = payrollManagerNote.getDeductionReason().getName();
                    }
                } else {
                    name = "Manager Deduction";
                }

                row.getCell(0).setText(name);
                row.getCell(1).setText(formatValues(payrollManagerNote.getAmount()));

                deductionTable.addRow(row, lastUsedRowNumber + 1);

                lastUsedRowNumber++;
                rowsAdded++;
            } catch (XmlException e) {
                e.printStackTrace();
            }
        }

        Double totalDeductionPlusUnsettled = totalDeduction + (isFirstPaySlipAndBeforePayrollEnds ? 0 : unsettledDeductions);
        // to set the styling programmatically
        deductionTable.getRow(lastUsedRowNumber + 1).getCell(1).removeParagraph(0);
        XWPFParagraph totalDeductionParagraph = deductionTable.getRow(lastUsedRowNumber + 1).getCell(1).addParagraph();
        totalDeductionParagraph.setVerticalAlignment(TextAlignment.CENTER);
        totalDeductionParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(totalDeductionParagraph.createRun(), 10, "000000", formatValues(totalDeductionPlusUnsettled), true);

        deductionTable.removeRow(2);


        XWPFTableRow totalDeductionInSalaryRow = deductionTable.getRow(rowsAdded + 4);
        XWPFTableRow totalDeductionRemainingSalaryRow = deductionTable.getRow(rowsAdded + 6);

        // total deduction this salary
        Double totalDeductionThisSalary = paySlip.getHousemaidPayrollLog().getLoanRepayment()
                + totalDeduction
                - additionsCoverDeductionLimitAndNegativeSalary;

        // to set the styling programmatically
        totalDeductionInSalaryRow.getCell(1).removeParagraph(0);
        XWPFParagraph deductionThisSalaryParagraph = totalDeductionInSalaryRow.getCell(1).addParagraph();
        deductionThisSalaryParagraph.setVerticalAlignment(TextAlignment.CENTER);
        deductionThisSalaryParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(deductionThisSalaryParagraph.createRun(), 10, "ff0000", formatValues(totalDeductionThisSalary), true);


        // total remaining deduction from future salary
        Double totalRemainingDeductionFromFutureSalary = totalDeductionPlusUnsettled - totalDeductionThisSalary;
        // to set the styling programmatically
        totalDeductionRemainingSalaryRow.getCell(1).removeParagraph(0);
        XWPFParagraph deductionFutureSalaryParagraph = totalDeductionRemainingSalaryRow.getCell(1).addParagraph();
        deductionFutureSalaryParagraph.setVerticalAlignment(TextAlignment.CENTER);
        deductionFutureSalaryParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(deductionFutureSalaryParagraph.createRun(), 10, "000000", formatValues(totalRemainingDeductionFromFutureSalary), true);

//        end jira ACC-1537


        XWPFTableRow tableRow9 = table.getRow(8);
        //Total
        Double totalEarnings = 0D;
        if (isFirstPaySlipAndBeforePayrollEnds) {
            totalEarnings = otherAdditions + paySlip.getBasicPayThisMonth() + paySlip.getVacationAirFare();
        } else {
            totalEarnings = otherAdditions + paySlip.getBasicMonthlyPay() + paySlip.getVacationAirFare();

        }


        // to set the styling programmatically
        tableRow9.getCell(2).removeParagraph(0);
        XWPFParagraph totalEarningsFinalParagraph = tableRow9.getCell(2).addParagraph();
        totalEarningsFinalParagraph.setVerticalAlignment(TextAlignment.CENTER);
        totalEarningsFinalParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(totalEarningsFinalParagraph.createRun(), 10, "00b050", formatValues(totalEarnings), true);

        //Deduction

        tableRow9.getCell(4).removeParagraph(0);
        XWPFParagraph deductionFinalParagraph = tableRow9.getCell(4).addParagraph();
        deductionFinalParagraph.setVerticalAlignment(TextAlignment.CENTER);
        deductionFinalParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(deductionFinalParagraph.createRun(), 10, "ff0000", formatValues(totalDeductionThisSalary), true);

        //Final Answer
        Double finalAnswer = totalEarnings - totalDeductionThisSalary;

        // to set the styling programmatically
        tableRow9.getCell(6).removeParagraph(0);
        XWPFParagraph finalResultParagraph = tableRow9.getCell(6).addParagraph();
        finalResultParagraph.setVerticalAlignment(TextAlignment.CENTER);
        finalResultParagraph.setAlignment(ParagraphAlignment.CENTER);
        setRun(finalResultParagraph.createRun(), 10, "000000", formatValues(finalAnswer) + " AED", true);


        if (isFirstPaySlipAndBeforePayrollEnds) {
            // remove unsettled deductions from prev month
            deductionTable.removeRow(1);
        } else {
            // remove basic pay this month
            basicPayThisMonth.removeRow(0);
        }

        File paySlipFile =
                Paths.get(System.getProperty("java.io.tmpdir"),
                        paySlip.getHousemaid().getName() + (DateUtil.formatMonthWithSimpleTime(new Date()) + ".docx"))
//                Paths.get(System.getProperty("java.io.tmpdir") + paySlip.getHousemaid().getName() + ".docx")
                        .toFile();
        try (FileOutputStream out = new FileOutputStream(paySlipFile)) {
            document.write(out);
        }

//        return new FileInputStream(file);
        return paySlipFile;

    }

    //Jirra ACC-1270
    public static File createOfficeStaffPayslipAsWordDocument(OfficeStaffPayrollBean officeStaffPayrollBean) throws URISyntaxException, IOException {
        URL resource = PayrollGenerationLibrary.class.getResource("/Payslips-OfficeStaff.docx");
        File file = new File(resource.toURI());
        XWPFDocument document = new XWPFDocument(new FileInputStream(file));
        XWPFTable table = document.getTables().get(0);
        //Employee name
        table.getRow(0).getCell(1).setText(officeStaffPayrollBean.getOfficeStaffName());

        //Payslip Month
        table.getRow(1).getCell(1).setText(officeStaffPayrollBean.getPaySlipMonth());

        //Start date
        table.getRow(2).getCell(1).setText(officeStaffPayrollBean.getStartDate());

        HashMap<String, Integer> earnings = new HashMap<>();

        earnings.put("PaidOffDaysAmount", 0);
        earnings.put("TicketValue", 1);
        earnings.put("TransportationAllowance", 2);
        earnings.put("HousingAllowance", 3);
        earnings.put("managerAddition", 4);
        earnings.put("extraShifts", 5);
        earnings.put("MOHRE", 6);
        earnings.put("MonthlyCashAdvance", 7);
        earnings.put("BasicPayThisMonth", 8);

        table = document.getTables().get(1);
        XWPFTableRow tableRow1 = table.getRow(1);

        //Earnings
        XWPFTable earningsTable = tableRow1.getTableCells().get(0).getTables().get(0);
        //PaidOff Days Amount
        earningsTable.getRow(earnings.get("PaidOffDaysAmount")).getCell(1).setText(officeStaffPayrollBean.getOfficeStaffPaidOffDaysAmount().toString());
        //Ticket Value
        earningsTable.getRow(earnings.get("TicketValue")).getCell(1).setText(officeStaffPayrollBean.getOfficeStaffTicketValue().toString());
        //Transportation Allowance
        earningsTable.getRow(earnings.get("TransportationAllowance")).getCell(1).setText(officeStaffPayrollBean.getTransportation());
        //HousingAllowance
        earningsTable.getRow(earnings.get("HousingAllowance")).getCell(1).setText(officeStaffPayrollBean.getHousing());
        //managerAddition
        earningsTable.getRow(earnings.get("managerAddition")).getCell(1).setText(officeStaffPayrollBean.getManagerAdditionV());
        //extraShifts
        earningsTable.getRow(earnings.get("extraShifts")).getCell(1).setText(officeStaffPayrollBean.getExtraShiftsV());
        //MOHRE Salary
        earningsTable.getRow(earnings.get("MOHRE")).getCell(1).setText(officeStaffPayrollBean.getMohoreSalary().toString());
        //Monthly Cash Advance
        earningsTable.getRow(earnings.get("MonthlyCashAdvance")).getCell(1).setText(officeStaffPayrollBean.getMonthlyCashAdvance().toString());
        //Basic Pay This Month
        earningsTable.getRow(earnings.get("BasicPayThisMonth")).getCell(1).setText(officeStaffPayrollBean.getBasicSalary().toString());

        //Jirra ACC-1270
        if (officeStaffPayrollBean.getExtraShiftsV().isEmpty()
                || officeStaffPayrollBean.getExtraShiftsV().equals("0.0")) {
            earningsTable.removeRow(earnings.get("extraShifts"));
        }
        if (officeStaffPayrollBean.getManagerAdditionV().isEmpty()
                || officeStaffPayrollBean.getManagerAdditionV().equals("0.0")) {
            earningsTable.removeRow(earnings.get("managerAddition"));
        }
        if (officeStaffPayrollBean.getHousing().isEmpty()
                || officeStaffPayrollBean.getHousing().equals("0.0")) {
            earningsTable.removeRow(earnings.get("HousingAllowance"));
        }
        if (officeStaffPayrollBean.getTransportation().isEmpty()
                || officeStaffPayrollBean.getTransportation().equals("0.0")) {
            earningsTable.removeRow(earnings.get("TransportationAllowance"));
        }
        if (officeStaffPayrollBean.getOfficeStaffTicketValue().equals(0D)) {
            earningsTable.removeRow(earnings.get("TicketValue"));
        }
        if (officeStaffPayrollBean.getOfficeStaffPaidOffDaysAmount().equals(0D)) {
            earningsTable.removeRow(earnings.get("PaidOffDaysAmount"));
        }

        // remaininf tableRow1.getTableCells().get(1).getTables().get(0).getRow(1).getTableCells().get(1).getText()
        HashMap<String, Integer> deductions = new HashMap<>();
        deductions.put("RepaymentToday", 0);
        deductions.put("Remaining", 1);
        deductions.put("AbsenceDeductions", 2);
        deductions.put("PenaltyDeductions", 3);
        deductions.put("FinalSettlementDeduction", 4);
        deductions.put("ManagerDeduction", 5);
        //Deductions
        XWPFTable deductionTable = tableRow1.getTableCells().get(1).getTables().get(0);
//        getTableCells().get(1).getText()
        //Repayment Today
        deductionTable.getRow(deductions.get("RepaymentToday")).getCell(2).setText(officeStaffPayrollBean.getRepaymentToday().toString());
        //Remaining Loan
        deductionTable.getRow(deductions.get("Remaining")).getCell(2).setText(officeStaffPayrollBean.getRemainingLoanBalance());
        //Absence Deductions
        deductionTable.getRow(deductions.get("AbsenceDeductions")).getCell(1).setText("0.0");
        //Penalty Deductions
        deductionTable.getRow(deductions.get("PenaltyDeductions")).getCell(1).setText(officeStaffPayrollBean.getPenaltyDeduction().toString());
        //Final Settlement Deduction
        deductionTable.getRow(deductions.get("FinalSettlementDeduction")).getCell(1).setText(officeStaffPayrollBean.getFinalSettelmentDeduciont().toString());
        //Manager Deduction
        deductionTable.getRow(deductions.get("ManagerDeduction")).getCell(1).setText(officeStaffPayrollBean.getManagerDeduction().toString());

        //Total Earnings (USD)
        table.getRow(2).getTableCells().get(0).getTables().get(0).getRow(0).getCell(1).setText(officeStaffPayrollBean.getTotatIcome().toString());

        //Total Deductions (USD):
        table.getRow(2).getTableCells().get(1).getTables().get(0).getRow(0).getCell(1).setText(officeStaffPayrollBean.getTotalDeduction().toString());

        XWPFTableRow tableRow3 = table.getRow(3);
        //Total
        tableRow3.getTableCells().get(1).getTableRow().getCell(1).setText(officeStaffPayrollBean.getTotatIcome().toString());
        //Deduction
        tableRow3.getTableCells().get(1).getTableRow().getCell(3).setText(officeStaffPayrollBean.getTotalDeduction().toString());
        //Final Answer
        Double finalAnswer = officeStaffPayrollBean.getTotatIcome() - officeStaffPayrollBean.getTotalDeduction();
        tableRow3.getTableCells().get(1).getTableRow().getCell(5).setText(finalAnswer.toString());

        File paySlipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                officeStaffPayrollBean.getOfficeStaffName() + (DateUtil.formatMonth(new Date()) + ".docx"))
                .toFile();
        try (FileOutputStream out = new FileOutputStream(paySlipFile)) {
            document.write(out);
        }

        return paySlipFile;

    }

    public static Long roundValue(Double x) {
        return Math.round(x);
    }

    public static File generateHouseMaidPayslipPhoto(InputStream input) throws Exception {
        Document doc = new Document(input);
        ImageSaveOptions options = new ImageSaveOptions(SaveFormat.JPEG);
        options.setJpegQuality(100);
        options.setResolution(400);
        options.setUseHighQualityRendering(true);
        File finalPhoto = null;
        for (int i = 0; i < doc.getPageCount(); i++) {
//                String imageFilePath = "D://" + "img_" + i + ".jpeg";
            String imageFilePath = Paths.get(System.getProperty("java.io.tmpdir"),
                    i + new Date().getTime() + ".jpeg").toString();
            options.setPageSet(new PageSet(i));
            doc.save(imageFilePath, options);

            ///
            BufferedImage image = ImageIO.read(new File(imageFilePath));
            BufferedImage out = image.getSubimage(0, 280, 3400, 4100);

            if (i == 0) {
                finalPhoto = new File(imageFilePath);
                ImageIO.write(out, "jpg", finalPhoto);
            }
        }
        return finalPhoto;
    }

    public static File generateOfficeStaffPayslipPhoto(
            InputStream input, String officeStaffName) throws Exception {
        Document doc = new Document(input);
        ImageSaveOptions options = new ImageSaveOptions(SaveFormat.JPEG);
        options.setJpegQuality(100);
        options.setResolution(400);
        options.setUseHighQualityRendering(true);
        File finalPhoto = null;
        for (int i = 0; i < doc.getPageCount(); i++) {
//                String imageFilePath = "D://" + "img_" + i + ".jpeg";
            String imageFilePath = Paths.get(System.getProperty("java.io.tmpdir"),
                    officeStaffName + ".jpeg").toString();
            options.setPageSet(new PageSet(i));
            doc.save(imageFilePath, options);

            ///
            BufferedImage image = ImageIO.read(new File(imageFilePath));
            BufferedImage out = image.getSubimage(0, 280, 3400, 2800);

            if (i == 0) {
                finalPhoto = new File(imageFilePath);
                ImageIO.write(out, "jpg", finalPhoto);
            }
        }
        return finalPhoto;
    }

    public static void savePayslipPhoto(
            List<TranslatedPayslip> translatedPayslips, Housemaid housemaid,
            HousemaidPayrollLog log, String maidsaeEmployeeName) throws FileNotFoundException, IOException, NoSuchAlgorithmException {

        MonthlyPayrollDocument finalFile = new MonthlyPayrollDocument();
        finalFile.setFinalFile(true);
        //finalFile.setPayrollMonth(payrollDate.toString("MMMMM"));
        finalFile.setType(MonthlyPayrollDocumentType.PAYSLIPS);
        finalFile.setPayrollDate(log.getPayrollMonth());
        finalFile.setUploadDate(new LocalDate().toDate());
        finalFile.setHousemaid(housemaid);
        finalFile.setMaidsaeEmployeeName(maidsaeEmployeeName);
        List<Attachment> attachments = new ArrayList<>();
        for(TranslatedPayslip translatedPayslip: translatedPayslips) {
            MultipartFile multiPartFile;
            try ( //convert file to multipart file
                  FileInputStream input = new FileInputStream(translatedPayslip.getPayslip())) {
                multiPartFile = new MockMultipartFile("payslip",
                        "payslip", "image/jpeg", IOUtils.toByteArray(input));
            }

            //Create Attachment
            Attachment attach = Storage.storeTemporary(multiPartFile, "Payslip-" + translatedPayslip.getLang(), Boolean.TRUE);
            attachments.add(attach);
        }

        finalFile.setAttachments(attachments);
        MonthlyPayrollDocumentRepository monthlyPayrollDocumentRepository = Setup.getRepository(MonthlyPayrollDocumentRepository.class);
        monthlyPayrollDocumentRepository.save(finalFile);

        log.setPayslipGenerated(true);
        Setup.getRepository(HousemaidPayrollLogRepository.class).save(log);

    }

    public static LocalDate getPayrollEndDate(LocalDate dt) {
        return dt.getDayOfMonth() >= 27 ? dt.plusMonths(1)
                .withDayOfMonth(26) : dt.withDayOfMonth(26);
    }

    public static LocalDate getPayrollStartDate(LocalDate dt) {
        return dt.getDayOfMonth() < 27 ? dt.minusMonths(1)
                .withDayOfMonth(27) : dt.withDayOfMonth(27);
    }

    public static class PaySlipError {

        String id;
        String housemaidName;
        String housemaidNumber;
        String nationality;
        String status;
        String clientName;
        String clientNumber;
        String error;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getHousemaidName() {
            return housemaidName;
        }

        public void setHousemaidName(String housemaidName) {
            this.housemaidName = housemaidName;
        }

        public String getHousemaidNumber() {
            return housemaidNumber;
        }

        public void setHousemaidNumber(String housemaidNumber) {
            this.housemaidNumber = housemaidNumber;
        }

        public String getNationality() {
            return nationality;
        }

        public void setNationality(String nationality) {
            this.nationality = nationality;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getClientName() {
            if (clientName == null) {
                return "";
            }
            return clientName;
        }

        public void setClientName(String clientName) {
            this.clientName = clientName;
        }

        public String getClientNumber() {
            if (clientNumber == null) {
                return "";
            }
            return clientNumber;
        }

        public void setClientNumber(String clientNumber) {
            this.clientNumber = clientNumber;
        }

        public String getError() {
            if (error == null) {
                return "";
            }
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

    }

    private static void setRun(XWPFRun run, int fontSize, String colorRGB, String text, boolean bold) {
        run.setFontSize(fontSize);
        run.setColor(colorRGB);
        run.setText(text);
        run.setBold(bold);
    }

    public static Map<Long, List<HousemaidPayrollLog>> convertListToMap(List<HousemaidPayrollLog> payrollLogs){
        Map<Long, List<HousemaidPayrollLog>> logsMap = new HashMap<>();
        for (HousemaidPayrollLog log : payrollLogs){
            List<HousemaidPayrollLog> housemaidLogs = logsMap.getOrDefault(log.getHousemaid().getId(), new ArrayList<>());
            housemaidLogs.add(log);
            logsMap.put(log.getHousemaid().getId(), housemaidLogs);
        }
        return logsMap;
    }

    public static Map<String, Object> readFiles (List<Attachment> attachments){
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, Object> tempDataMap = new HashMap<>();
        List<String> tableHeaders = new ArrayList<>();
        for( Attachment attachment : attachments){
            switch (attachment.getTag()){
                case "WPSTransferFile":
                    tableHeaders.add("AED");
                    dataMap.put("tableHeaders", tableHeaders);
                    dataMap.putAll(readWPSFile(attachment));
                    break;
                case "BankTransferFile":
                    tableHeaders.add("AED");
                    tableHeaders.add("USD");
                    dataMap.put("tableHeaders", tableHeaders);
                    dataMap.putAll(readBankTransferFile(attachment));
                    break;
                case "LocalTransferFile":
                    tableHeaders.add("AED");
                    dataMap.put("tableHeaders", tableHeaders);
                    dataMap.putAll(readLocalFile(attachment));
                    break;
                case "InternationalTransferFile":
                    tableHeaders.add("AED");
                    tableHeaders.add("USD");
                    dataMap.put("tableHeaders", tableHeaders);
                    dataMap.putAll(readInternationalFile(attachment));
                    break;
                case "OfficeStaffDetailedPayrollFile":
                    tempDataMap = readOfficeStaffDetailedFile(attachment);
                    dataMap.put("auditNumbers", (Integer) dataMap.getOrDefault("auditNumbers", 0) + (Integer) tempDataMap.getOrDefault("auditNumbers", 0));
                    dataMap.put("auditTotal",  (Double) dataMap.getOrDefault("auditTotal", 0.0)  + (Double) tempDataMap.getOrDefault("auditTotal", 0.0));
                    dataMap.put("auditAED", (Double) dataMap.getOrDefault("auditAED", 0.0)  + (Double) tempDataMap.getOrDefault("auditAED", 0.0));
                    dataMap.put("auditUSD",  (Double) dataMap.getOrDefault("auditUSD", 0.0)  + (Double) tempDataMap.getOrDefault("auditUSD", 0.0));

                    break;
                case "HousemaidDetailedPayrollFile":
                    tempDataMap = readHousemaidDetailedFile(attachment);
                    dataMap.put("auditNumbers", (Integer) dataMap.getOrDefault("auditNumbers", 0) + (Integer) tempDataMap.getOrDefault("auditNumbers", 0));
                    dataMap.put("auditTotal",  (Double) dataMap.getOrDefault("auditTotal", 0.0)  + (Double) tempDataMap.getOrDefault("auditTotal", 0.0));
                    dataMap.put("auditAED", (Double) dataMap.getOrDefault("auditAED", 0.0)  + (Double) tempDataMap.getOrDefault("auditAED", 0.0));

                    break;
            }
        }

        return dataMap;
    }

    public static Map<String, Object> readWPSFile(Attachment attachment) {
        Map<String, Object> dataMap = new HashMap<>();
        Double sumSalaries = 0.0;
        Integer employeeNum = 0;
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook != null) {
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                Row row = null;

                //skip first row (it's only have the table headers)
                if (rowIterator.hasNext())
                    rowIterator.next();

                while (rowIterator.hasNext()) {
                    try {
                        row = rowIterator.next();
                        String salary = formatter.formatCellValue(row.getCell(9));
                        if (!NumberUtils.isParsable(salary))
                            continue;

                        sumSalaries += Double.parseDouble(salary);
                        ++employeeNum;
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail(
                                "<EMAIL>", e,
                                "Exception occurred while readWPSFile in the #row: " + row.getRowNum(), false);
                    }
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e,"Exception readWPSFile: ", false);
        }
        dataMap.put("paymentNumbers", employeeNum);
        dataMap.put("paymentTotal", NumberFormatter.formatNumber(sumSalaries));
        dataMap.put("paymentAED", NumberFormatter.formatNumber(sumSalaries));
        return dataMap;
    }

    public static Map<String, Object> readBankTransferFile(Attachment attachment) {
        Map<String, Object> dataMap = new HashMap<>();
        Double sumSalaries = 0.0;
        Double sumSalariesAED = 0.0;
        Double sumSalariesUSD = 0.0;
        Integer employeeNum = 0;
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook != null) {
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                Row row = null;

                //skip first row (it's only have the table headers)
                if (rowIterator.hasNext())
                    rowIterator.next();

                while (rowIterator.hasNext()) {
                    try {
                        row = rowIterator.next();
                        String salary = formatter.formatCellValue(row.getCell(9));
                        String currency = formatter.formatCellValue(row.getCell(10));
                        if (!NumberUtils.isParsable(salary))
                            continue;

                        if(currency != null && "USD".equals(currency.trim().toUpperCase())) {
                            sumSalaries += Double.parseDouble(salary);
                            sumSalariesUSD += Double.parseDouble(salary);
                        } else {
                            sumSalaries += Double.parseDouble(salary);
                            sumSalariesAED += Double.parseDouble(salary);
                        }

                        ++employeeNum;
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail(
                                "<EMAIL>", e,
                                "Exception occurred while readBankTransferFile in the #row: " + row.getRowNum(), false);
                    }
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e,"Exception readBankTransferFile: ", false);
        }
        dataMap.put("paymentNumbers", employeeNum);
        dataMap.put("paymentTotal", NumberFormatter.formatNumber(sumSalaries));
        dataMap.put("paymentAED", NumberFormatter.formatNumber(sumSalariesAED));
        dataMap.put("paymentUSD", NumberFormatter.formatNumber(sumSalariesUSD));
        return dataMap;
    }

    public static Map<String, Object> readLocalFile(Attachment attachment) {
        Map<String, Object> dataMap = new HashMap<>();
        Double sumSalaries = 0.0;
        Integer employeeNum = 0;
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook != null) {
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                Row row = null;

                //skip first row (it's only have the table headers)
                if (rowIterator.hasNext())
                    rowIterator.next();

                while (rowIterator.hasNext()) {
                    try {
                        row = rowIterator.next();
                        String salary = formatter.formatCellValue(row.getCell(3));
                        if (!NumberUtils.isParsable(salary))
                            continue;

                        sumSalaries += Double.parseDouble(salary);
                        ++employeeNum;
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail(
                                "<EMAIL>", e,
                                "Exception occurred while readLocalFile in the #row: " + row.getRowNum(), false);
                    }
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e,"Exception readLocalFile: ", false);
        }
        dataMap.put("paymentNumbers", employeeNum);
        dataMap.put("paymentTotal", NumberFormatter.formatNumber(sumSalaries));
        dataMap.put("paymentAED", NumberFormatter.formatNumber(sumSalaries));
        return dataMap;
    }

    public static Map<String, Object> readInternationalFile(Attachment attachment) {
        Map<String, Object> dataMap = new HashMap<>();
        Double sumSalaries = 0.0;
        Double sumSalariesAED = 0.0;
        Double sumSalariesUSD = 0.0;
        Integer employeeNum = 0;
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook != null) {
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                Row row = null;

                //skip first row (it's only have the table headers)
                if (rowIterator.hasNext())
                    rowIterator.next();

                while (rowIterator.hasNext()) {
                    try {
                        row = rowIterator.next();
                        String salary = formatter.formatCellValue(row.getCell(4));
                        String currency = formatter.formatCellValue(row.getCell(5));
                        if (!NumberUtils.isParsable(salary))
                            continue;

                        if(currency != null && "USD".equals(currency.trim().toUpperCase())) {
                            sumSalaries += Double.parseDouble(salary);
                            sumSalariesUSD += Double.parseDouble(salary);
                        } else {
                            sumSalaries += Double.parseDouble(salary);
                            sumSalariesAED += Double.parseDouble(salary);
                        }

                        ++employeeNum;
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail(
                                "<EMAIL>", e,
                                "Exception occurred while readInternationalFile in the #row: " + row.getRowNum(), false);
                    }
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e,"Exception readInternationalFile: ", false);
        }
        dataMap.put("paymentNumbers", employeeNum);
        dataMap.put("paymentTotal", NumberFormatter.formatNumber(sumSalaries));
        dataMap.put("paymentAED", NumberFormatter.formatNumber(sumSalariesAED));
        dataMap.put("paymentUSD", NumberFormatter.formatNumber(sumSalariesUSD));
        return dataMap;
    }

    public static Map<String, Object> readOfficeStaffDetailedFile(Attachment attachment) {
        Map<String, Object> dataMap = new HashMap<>();
        Double sumSalaries = 0.0;
        Double sumSalariesAED = 0.0;
        Double sumSalariesUSD = 0.0;
        Integer employeeNum = 0;
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook != null) {
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                Row row = null;
                int salaryColumnIndex = 27;
                int currencyColumnIndex = 28;
                //skip first row (it's only have the table headers)
                if (rowIterator.hasNext()) {
                    row = rowIterator.next();
                    Iterator<Cell> cellIterator = row.cellIterator();
                    while (cellIterator.hasNext()) {
                        Cell cell = cellIterator.next();
                        if (cell.getCellType().equals(CellType.STRING)) {
                            String cellText = cell.getStringCellValue();
                            if ("Ansari".equals(cellText)) {
                                salaryColumnIndex = cell.getColumnIndex();
                                currencyColumnIndex = salaryColumnIndex + 1;
                                break;
                            }
                        }
                    }
                }

                while (rowIterator.hasNext()) {
                    try {
                        row = rowIterator.next();
                        String index = formatter.formatCellValue(row.getCell(0));
                        String salary = formatter.formatCellValue(row.getCell(salaryColumnIndex));
                        String currency = formatter.formatCellValue(row.getCell(currencyColumnIndex));
                        if (!NumberUtils.isParsable(salary) || !NumberUtils.isParsable(index))
                            continue;

                        if(currency != null && "USD".equals(currency.trim().toUpperCase())) {
                            sumSalaries += Double.parseDouble(salary);
                            sumSalariesUSD += Double.parseDouble(salary);
                        } else {
                            sumSalaries += Double.parseDouble(salary);
                            sumSalariesAED += Double.parseDouble(salary);
                        }

                        ++employeeNum;
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail(
                                "<EMAIL>", e,
                                "Exception occurred while readOfficeStaffDetailedFile in the #row: " + row.getRowNum(), false);
                    }
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e,"Exception readOfficeStaffDetailedFile: ", false);
        }
        dataMap.put("auditNumbers", employeeNum);
        dataMap.put("auditTotal", sumSalaries);
        dataMap.put("auditAED", sumSalariesAED);
        dataMap.put("auditUSD", sumSalariesUSD);
        return dataMap;
    }

    public static Map<String, Object> readHousemaidDetailedFile(Attachment attachment) {
        Map<String, Object> dataMap = new HashMap<>();
        Double sumSalaries = 0.0;
        Double sumSalariesAED = 0.0;
        Integer employeeNum = 0;
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook != null) {
                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> rowIterator = sheet.iterator();
                Row row = null;
                int indexColumn = 0;
                int salaryColumnIndex = 43;
                //skip first row (it's only have the table headers)
                if (rowIterator.hasNext()) {
                    row = rowIterator.next();
                    Iterator<Cell> cellIterator = row.cellIterator();
                    while (cellIterator.hasNext()) {
                        Cell cell = cellIterator.next();
                        if (cell.getCellType().equals(CellType.STRING)) {
                            String cellText = cell.getStringCellValue();
                            if ("Ansari (AED)".equals(cellText)) {
                                salaryColumnIndex = cell.getColumnIndex();
                                break;
                            }
                        }
                    }
                }

                while (rowIterator.hasNext()) {
                    try {
                        row = rowIterator.next();
                        String index = formatter.formatCellValue(row.getCell(indexColumn));
                        String salary = formatter.formatCellValue(row.getCell(salaryColumnIndex));
                        if (!NumberUtils.isParsable(salary) || !NumberUtils.isParsable(index))
                            continue;

                        sumSalaries += Double.parseDouble(salary);
                        sumSalariesAED += Double.parseDouble(salary);
                        ++employeeNum;
                    } catch (Exception e) {
                        DebugHelper.sendExceptionMail(
                                "<EMAIL>", e,
                                "Exception occurred while readHousemaidDetailedFile in the #row: " + row.getRowNum(), false);
                    }
                }
            }
        }catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e,"Exception readHousemaidDetailedFile: ", false);
        }
        dataMap.put("auditNumbers", employeeNum);
        dataMap.put("auditTotal", sumSalaries);
        dataMap.put("auditAED", sumSalariesAED);
        return dataMap;
    }
}
