package com.magnamedia.extra;

public enum TemplateParameterType {
    MAID_NAME("maid_name"),
    THIS_MONTH("this_month"),
    NEXT_MONTH("next_month"),
    LAST_MONTH("last_month"),
    THIS_MONTH_2("this_month_2"),
    WORKER_SALARY("worker_salary"),
    UPCOMING_PRIMARY_PAYROLL_WITH_2DAYS("upcoming_primary_payroll+2days"),
    SPECIAL_UPCOMING_PRIMARY_PAYROLL_WITH_2DAYS("special_upcoming_primary_payroll+2days"),
    UPCOMING_PRIMARY_PAYROLL_WITH_1DAY("upcoming_primary_payroll+1day"),
    UPCOMING_SECONDARY_PAYROLL_WITH_1DAY("upcoming_secondary_payroll+1day"),
    PAYROLL_TRANSFER_DATE("payroll_transfer_date"),
    PREV_PAYROLL_TRANSFER_DATE("prev_payroll_transfer_date"),
    HER_YOU("her_you"),
    CONTRACT_MONTH_WITH_2MONTH("contract_month+2months"),
    CONTRACT_MONTH("contract_month"),
    NEXT_CONTRACT_MONTH("next_contract_month");
    private String label ;

    TemplateParameterType(String label) {
        this.label = label;
    }
    public String getLabel(){
        return label;
    }
}
