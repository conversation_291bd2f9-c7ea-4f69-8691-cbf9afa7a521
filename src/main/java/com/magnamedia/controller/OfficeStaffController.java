package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.ibanvalidator.SwiftCodeValidationResponse;
import com.magnamedia.core.helper.ibanvalidator.ValidateIBANService;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.projection.IdLabel;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.entity.accessmgmt.ExternalAccess;
import com.magnamedia.entity.accessmgmt.OfficeStaffAccess;
import com.magnamedia.entity.projection.OfficeStaffBirthdayProjection;
import com.magnamedia.entity.projection.OfficeStaffDetailedInfoProjection;
import com.magnamedia.entity.projection.OfficeStaffList;
import com.magnamedia.entity.projection.OfficeStaffProfile;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.OfficeStaffExport;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PhoneNumberUtil;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.OfficeStaffUpdateService;
import com.magnamedia.service.PendingManagerApprovalService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.PublicPageService;
import com.magnamedia.service.payroll.generation.OfficeStaffFinalSettlementService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("/officestaff")
@RestController
public class OfficeStaffController extends BaseRepositoryController<OfficeStaff> {

    @Autowired
    public PublicPageHelper publicPageHelper;
    @Autowired
    private OfficeStaffRepository RepOfficeStaff;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private MailService mailService;
    @Autowired
    private OfficeStaffFinalSettlementService finalSettlementService;
    @Autowired
    private OfficeStaffUpdateService officeStaffUpdateService;
    @Autowired
    private OfficeStaffAccessRepository accessRepository;
    @Autowired
    private ExternalAccessRepository externalAccessRepository;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    @Override
    public BaseRepository<OfficeStaff> getRepository() {
        return RepOfficeStaff;
    }

    @Autowired
    private PublicPageService publicPageService;
    @Autowired
    private FinalSettlementNoteRepository finalSettlementNoteRepository;

    @Autowired
    OfficeStaffFinalSettlementRepository officeStaffFinalSettlementRepository;
    @Autowired
    OfficeStaffRepository officeStaffRepository;

    @NoPermission
    @RequestMapping(
            value = {"/update"},
            method = {RequestMethod.POST}
    )
    @ResponseBody
    @JsonView({ViewScope.Normal.class})
    @Transactional
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (this.checkPermission("update")) {
            OfficeStaff updated = this.parse(objectNode);
            OfficeStaff origin = this.getRepository().findOne(updated.getId());

            if(updated != null) {
                // Normalize phone numbers
                if (updated.getPhoneNumber() != null && !updated.getPhoneNumber().isEmpty()) {
                    String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(updated.getPhoneNumber());
                    if (!normalizedPhoneNumber.equals(updated.getPhoneNumber())) {
                        updated.setPhoneNumber(normalizedPhoneNumber);
                    }
                    updated.setPhoneNumber(PhoneNumberUtil.normalizeInternationalPhoneNumber(updated.getPhoneNumber()));
                }

                if (updated.getEmergencyContactPhoneNumber() != null && !updated.getEmergencyContactPhoneNumber().isEmpty()) {
                    String normalizedEmergencyNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(updated.getEmergencyContactPhoneNumber());
                    if (!normalizedEmergencyNumber.equals(updated.getEmergencyContactPhoneNumber())) {
                        updated.setEmergencyContactPhoneNumber(normalizedEmergencyNumber);
                    }
                }
            }
            officeStaffUpdateService.checkStartingDateAndSendApproveMessages(origin, updated, objectNode);
            this.update(origin, updated, objectNode);
            return this.updateEntity(origin);
        } else {
            return this.unauthorizedReponse();
        }
    }


    @NoPermission
    @RequestMapping("/officestaffReport")
    public ResponseEntity WeeklyTerminationAddition() throws ParseException {

//        Date beforeWeek = LocalDate.now().minusYears(2).toDate();
        Date beforeWeek = LocalDate.now().minusDays(7).toDate();
        String staffsQuerySTR = "SELECT f " +
                "FROM OfficeStaff f " +
                "WHERE (f.status = 'ACTIVE' AND f.startingDate >= '" + DateUtil.formatDateDashedWithTime(beforeWeek) + "')" +
                "OR (f.status ='TERMINATED' AND f.terminationDate >= '" + DateUtil.formatDateDashedWithTime(beforeWeek) + "')";

        SelectQuery<OfficeStaff> query = new SelectQuery<>(staffsQuerySTR, "", OfficeStaff.class);
        List<OfficeStaff> Staffs = query.execute();

        System.out.println(Staffs);

        List<Map<String, Object>> departmentCount = officeStaffRepository.findActiveStaffCountPerDepartment();

        System.out.println(departmentCount);

        String table = "<table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\" white-space:nowrap;\">";
        table += "<tr style=\"font-size:14px;font-weight:bold!important;background-color:#000d50\">" +
                " <td colspan=\"7\" style=\"font-size:20px;font-weight:bold!important;text-align:center;color:white; border-bottom:solid 1px\">Payroll Termination/Addition Report</td>" +
                "</tr>";
        table += "<tr style=\"font-size:14px;font-weight:bold!important;background-color:#000d50\">" +
                " <td colspan=\"7\" style=\"font-size:20px;font-weight:bold!important;text-align:center;color: white;border-bottom:solid 1px\"> from " + DateUtil.formatFullDate(beforeWeek) + " to " + DateUtil.formatFullDate(LocalDate.now().toDate()) + " </td>" +
                "</tr>";
        table += " <tr style=\"background-color:#f1a2c9\">" +
                "<th style=\"text-align:center\">#</th>" +
                "<th style=\"text-align:center\">Name of the Employee</th>" +
                "<th style=\"text-align:center\">Job Title</th>" +
                "<th style=\"text-align:center\">Departments</th>" +
                "<th style=\"text-align:center\">Action</th>" +
                "<th style=\"text-align:center\">Date</th>" +
                "<th style=\"text-align:center\">Manager</th>" +
                "<th style=\"text-align:center\">Updated number of employees of that Department</th>" +
                "</tr>";
        int i = 0;
        String Action = "";
        String ActionDate = "";
        String ManagerName = "";
        String JobTitleName = "";
        String DepartmentsName = "";
        String count = "";
        for (OfficeStaff staff : Staffs) {
            JobTitleName = "";
            DepartmentsName = "";
            count = "";

            i++;
            if (staff.getStatus() == OfficeStaffStatus.ACTIVE) {
                Action = "Hired";
                ActionDate = DateUtil.formatFullDate(staff.getStartingDate());
            } else if (staff.getStatus() == OfficeStaffStatus.TERMINATED) {
                Action = "Terminated";
                ActionDate = DateUtil.formatFullDate(staff.getTerminationDate());
            }
            if (staff.getManager() != null)
                ManagerName = staff.getManager().getName();

            StringBuilder countBuilder = new StringBuilder();

            for (PicklistItem department : staff.getDepartments()) {
                for (Map<String, Object> record : departmentCount) {
                    Long departmentId = ((Number) record.get("departmentId")).longValue();
                    Long staffCount = ((Number) record.get("staffCount")).longValue();

                    if (department.getId().equals(departmentId)) {
                        countBuilder.append(department.getName())
                                .append(": ")
                                .append(staffCount)
                                .append("<br>");
                        break;
                    }
                }
            }

            count = countBuilder.toString().trim(); // final string result

            if (staff.getJobTitle() != null)
                JobTitleName = staff.getJobTitle().getLabel();
            if (staff.getDepartments() != null && !staff.getDepartments().isEmpty()) {
                DepartmentsName = staff.getDepartmentNames();
            }

            table += "<tr> <td style=\"text-align:center\">" + i
                    + "</td> <td style=\"text-align:center\">" + staff.getName()
                    + "</td> <td style=\"text-align:center\">" + JobTitleName
                    + "</td> <td style=\"text-align:center\">" + DepartmentsName
                    + "</td> <td style=\"text-align:center\"> " + Action
                    + "</td style=\"text-align:center; word-wrap:break-word;\"> <td>" + ActionDate
                    + "</td> <td style=\"text-align:center\">" + ManagerName
                    + "</td> <td style=\"text-align:center\">" + count + "</td> </tr>";
        }
        table += "</table>";

        String emails = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_OFFICESTAFF_WEEKLY_PAYROLL_REPORT_EMAIL);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        if (recipients.size() > 0) {
            String subject = "Weekly Office Staff Termination/Addition Report from  " + DateUtil.formatFullDate(beforeWeek) + " to " + DateUtil.formatFullDate(LocalDate.now().toDate());
            Map<String, Object> params = new HashMap<>();
            params.put("table", table);
            TemplateEmail templateEmail = new TemplateEmail(subject, "OfficeStaffTerminationAddition", params);
            mailService.sendEmail(recipients, templateEmail, MessageTemplateService.getMaidsCcSenderName(), null);
//            mailService.sendEmail(recipients, new TemplateEmail(subject, "OfficeStaffTerminationAddition", params), null);
        }

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/search/page",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity searchNameAndStatus(
            @RequestParam(name = "search",
                    required = false) String searchName,
            @RequestParam(name = "status",
                    required = false) OfficeStaffStatus searchStatus,
            Sort sort, Pageable pageable) {

        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        if (searchName != null && !searchName.isEmpty())
            query.filterBy("name", "LIKE", "%" + searchName + "%");
        if (searchStatus != null)
            query.filterBy("status", "=", searchStatus);

        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("name", true, false);
        }

        return new ResponseEntity<>(query.execute(pageable).map(obj
                -> projectionFactory.createProjection(OfficeStaffList.class, obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('officestaff','getProfile')")
    @RequestMapping(value = "/profile/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getProfile(@PathVariable("id") Long id) {

        return new ResponseEntity<>(project(RepOfficeStaff.findOne(id), OfficeStaffProfile.class), HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('officestaff','getoffdaysbalance')")
    @RequestMapping(value = "/getoffdaysbalance/{id}",
            method = RequestMethod.GET)
    public ResponseEntity getOffDaysBalance(@PathVariable("id") OfficeStaff officeStaff) {
        Map result = new HashMap();
        result.put("id", officeStaff.getId());

        // get working public holidays list
        SelectQuery<WorkingPublicHoliday> query = new SelectQuery(WorkingPublicHoliday.class);
        query.filterBy("officeStaff", "=", officeStaff);
        query.sortBy("lastModificationDate", false, true);
        result.put("workingPublicHolidays", query.execute());

        // get travel days list
        query = new SelectQuery(TravelDays.class);
        query.filterBy("officeStaff", "=", officeStaff);
        query.filterBy("confirmed", "=", true);
        query.sortBy("lastModificationDate", false, true);
        result.put("travelDays", query.execute());

        // get paid off days list
        query = new SelectQuery(PaidOffDays.class);
        query.filterBy("officeStaff", "=", officeStaff);
        query.filterBy("workingPublicHoliday", "IS NULL", null);
        query.sortBy("lastModificationDate", false, true);
        result.put("paidOffDays", query.execute());


        result.put("earnedOffDays", officeStaff.getEarnedOffDays());
        result.put("consumedOffDaysBalance", officeStaff.getConsumedOffDaysBalance());
        result.put("offDaysBalance", officeStaff.getOffDaysBalance());

        return new ResponseEntity(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('officestaff','asPicklist')")
    @RequestMapping("/asPicklist")
    public ResponseEntity<?> asPicklist(@RequestParam("search") String queryString, @RequestParam(value = "onlyExpatAndEmirati", required = false, defaultValue = "false") Boolean onlyExpatAndEmirati, Pageable pageable) {
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.filterBy("status", "=", OfficeStaffStatus.ACTIVE);
        if (queryString != null
                && !queryString.isEmpty()) {
            query.filterBy("name",
                    "like",
                    "%" + queryString + "%");
        }
        //PAY-873
        if (onlyExpatAndEmirati != null && onlyExpatAndEmirati)
            query.filterBy("employeeType", "IN", Arrays.asList(OfficeStaffType.DUBAI_STAFF_EXPAT, OfficeStaffType.DUBAI_STAFF_EMARATI));

        return new ResponseEntity<>(project(query.execute(pageable), IdLabel.class),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('officestaff','beforeTerminateReq')")
    @RequestMapping(value = "/beforeTerminateReq/{id}", method = RequestMethod.POST)
    public ResponseEntity<?> beforeTerminateReq(@PathVariable("id") OfficeStaff officeStaff, @RequestBody OfficeStaff updated){
        Date terminationDate = DateUtil.getDayStart(updated.getTerminationDate());
        Date currDate = DateUtil.getDayStart(new Date());
        officeStaff = RepOfficeStaff.findOne(officeStaff.getId());
        if (officeStaff.getStartingDate() == null)
            throw new BusinessException("Starting date is empty for this staff !!");

        if (terminationDate.before(currDate)) {
            throw new BusinessException("Termination date can't be less than current date!");
        }

        if (terminationDate.before(officeStaff.getStartingDate())) {
            throw new BusinessException("Termination date can't be less than the starting date!");
        }

        Date noticePeriodFrom = updated.getNoticePeriodStartDate();
        Date noticePeriodTo = updated.getNoticePeriodEndDate();

        if (noticePeriodFrom != null && noticePeriodTo != null && noticePeriodTo.before(noticePeriodFrom)) {
            throw new BusinessException("Notice period end date can't be less than the Notice period start date!");
        }

        if (noticePeriodTo != null && noticePeriodTo.after(terminationDate)) {
            throw new BusinessException("Notice period end date can't be after the termination date!");
        }

        if (externalAccessRepository.existsByAdmin(officeStaff))
            throw new BusinessException("Error: Access Handler Detected: The employee you are trying to terminate is currently assigned as an access handler." +
                    " Please ensure that the terminated employee is no longer responsible for any access before proceeding with the termination process.");

        officeStaff.setTerminationDate(terminationDate);
        officeStaff.setTerminationReason(updated.getTerminationReason());
        officeStaff.setTerminationNotes(updated.getTerminationNotes());
        officeStaff.setNoticePeriodStartDate(updated.getNoticePeriodStartDate());
        officeStaff.setNoticePeriodEndDate(updated.getNoticePeriodEndDate());
        officeStaff.setTerminationType(updated.getTerminationType());

        officeStaff.setWhoRequestedTermination(Setup.getRepository(OfficeStaffRepository.class)
                .findOne(updated.getWhoRequestedTermination().getId()));
        officeStaff.setExcludedFromPayroll(true);
        OfficeStaffFinalSettlement finalSettlement = finalSettlementService.createFinalSettlement(officeStaff);
        FinalSettlementNote finalSettlementNote = new FinalSettlementNote();
        finalSettlementNote.setText(updated.getTerminationNotes());
        finalSettlementNote.setFinalSettlement(finalSettlement);
        finalSettlementNote.setNoteType(FSNoteType.PAYROLL_MANAGER);
        Map<String, Object> response = publicPageService.getFinalSettlement(finalSettlement);
        return ResponseEntity.ok(response) ;
    }

    //for overseas only
    @PreAuthorize("hasPermission('officestaff','terminate')")
    @RequestMapping(value = "/terminate/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> terminate(@PathVariable("id") OfficeStaff officeStaff, @RequestBody OfficeStaff updated) {
        officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(officeStaff.getId());
        Date terminationDate = DateUtil.getDayStart(updated.getTerminationDate());
        Date currDate = DateUtil.getDayStart(new Date());
        if (officeStaff.getStartingDate() == null)
            throw new BusinessException("Starting date is empty for this staff !!");

        if (!OfficeStaffType.OVERSEAS_STAFF.equals(officeStaff.getEmployeeType()))
            throw new RuntimeException("Wrong type of employee");

        if (terminationDate.before(currDate)) {
            throw new BusinessException("Termination date can't be less than current date!");
        }

        if (terminationDate.before(officeStaff.getStartingDate())) {
            throw new BusinessException("Termination date can't be less than the starting date!");
        }

        Date noticePeriodFrom = updated.getNoticePeriodStartDate();
        Date noticePeriodTo = updated.getNoticePeriodEndDate();

        if (noticePeriodFrom != null && noticePeriodTo != null && noticePeriodTo.before(noticePeriodFrom)) {
            throw new BusinessException("Notice period end date can't be less than the Notice period start date!");
        }

        if (noticePeriodTo != null && noticePeriodTo.after(terminationDate)) {
            throw new BusinessException("Notice period end date can't be after the termination date!");
        }

        if (externalAccessRepository.existsByAdmin(officeStaff))
            throw new BusinessException("Error: Access Handler Detected: The employee you are trying to terminate is currently assigned as an access handler." +
                    " Please ensure that the terminated employee is no longer responsible for any access before proceeding with the termination process.");

        // Update office staff information
        officeStaff.setTerminationDate(terminationDate);
        officeStaff.setTerminationReason(updated.getTerminationReason());
        officeStaff.setTerminationNotes(updated.getTerminationNotes());
        officeStaff.setNoticePeriodStartDate(updated.getNoticePeriodStartDate());
        officeStaff.setNoticePeriodEndDate(updated.getNoticePeriodEndDate());
        officeStaff.setTerminationType(updated.getTerminationType());

        officeStaff.setWhoRequestedTermination(Setup.getRepository(OfficeStaffRepository.class)
                .findOne(updated.getWhoRequestedTermination().getId()));
        officeStaff.setExcludedFromPayroll(true);

        officeStaff.setCompensation(updated.getCompensation());

        if (terminationDate.getTime() <= currDate.getTime()) {
            officeStaff.setStatus(OfficeStaffStatus.TERMINATED);
            officeStaff.setCreateCancel(true);
            finalSettlementService.revokeAllAccesses(officeStaff, updated.getWhoRequestedTermination());
        }

        OfficeStaffFinalSettlement finalSettlement = finalSettlementService.createFinalSettlement(officeStaff);
        officeStaffFinalSettlementRepository.save(finalSettlement);

        OfficeStaff finalManager = officeStaff.getFinalManager();
        String publicPage = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_SETTLEMENT, finalSettlement.getId().toString(), String.valueOf(finalManager.getUser().getId()));
        pendingManagerApprovalService.insertNewPendingApprovalRequest(officeStaff.getFirstLastName(), DateUtil.formatFullDate(officeStaff.getStartingDate()),officeStaff.getSalaryWithCurrency(), officeStaff.getJobTitle() != null ? officeStaff.getJobTitle().getName() : "", officeStaff.getEmployeeManager(), finalManager, (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency() + " " : "") + NumberFormatter.formatNumber(updated.getCompensation()), "Termination Compensation", updated.getTerminationNotes(), publicPage, finalSettlement.getId().toString());

        if (officeStaff.getExternalAccesses() != null && !officeStaff.getExternalAccesses().isEmpty()) {
            OfficeStaff terminationManager = officeStaff.getWhoRequestedTermination();
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", officeStaff.getFirstLastName());

//            messageTemplateService.sendMessageOrEmail(
//                    "Waiting for Access handler to revoke accesses",
//                    terminationManager,
//                    "Payroll_Wait_Accesses_To_Be_Revoked",
//                    paramValues);
            messagingService.send("Payroll_Wait_Accesses_To_Be_Revoked", "Waiting for Access handler to revoke accesses",
                    null, terminationManager, paramValues, officeStaff, null, terminationManager);

        }
        this.getRepository().save(officeStaff);

        return okResponse();
    }

    @PreAuthorize("hasPermission('officestaff','terminateExpatAndEmarati')")
    @RequestMapping(value = "/terminateExpatAndEmarati", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> terminateExpatAndEmarati(@RequestBody OfficeStaffFinalSettlement officeStaffFinalSettlement) {
        OfficeStaff updated = officeStaffFinalSettlement.getOfficeStaff() ;
        if(updated == null){
            throw new RuntimeException("office staff is null");
        }
        if(updated.getId() == null){
            throw new RuntimeException("office staff id is null");
        }
        Date terminationDate = DateUtil.getDayStart(updated.getTerminationDate());
        Date currDate = DateUtil.getDayStart(new Date());
        OfficeStaff officeStaff = RepOfficeStaff.findOne(officeStaffFinalSettlement.getOfficeStaff().getId());
        if (officeStaff.getStartingDate() == null)
            throw new BusinessException("Starting date is empty for this staff !!");

        officeStaff.setTerminationDate(terminationDate);
        officeStaff.setTerminationReason(updated.getTerminationReason());
        officeStaff.setTerminationNotes(updated.getTerminationNotes());
        officeStaff.setNoticePeriodStartDate(updated.getNoticePeriodStartDate());
        officeStaff.setNoticePeriodEndDate(updated.getNoticePeriodEndDate());
        officeStaff.setTerminationType(updated.getTerminationType());
        officeStaff.setWhoRequestedTermination(Setup.getRepository(OfficeStaffRepository.class)
                .findOne(updated.getWhoRequestedTermination().getId()));
        officeStaff.setExcludedFromPayroll(true);

        if (officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            throw new RuntimeException("Wrong type of employee");
        }

        if (terminationDate.getTime() <= currDate.getTime()) {
            officeStaff.setStatus(OfficeStaffStatus.TERMINATED);
            if (!OfficeStaffType.DUBAI_STAFF_EXPAT.equals(officeStaff.getEmployeeType()))
                officeStaff.setCreateCancel(true);

            finalSettlementService.revokeAllAccesses(officeStaff, updated.getWhoRequestedTermination());
        }
        // change final settlement status
        if(ScenarioType.CFO_IS_THE_SAME_AS_THE_FINAL_MANAGER_AND_HE_IS_THE_DIRECT_MANAGER.equals(officeStaffFinalSettlement.getScenarioType()) ){
            officeStaffFinalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_CFO);
        }else {
            officeStaffFinalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_FINAL_MANAGER);
        }
        officeStaff = RepOfficeStaff.save(officeStaff);
        officeStaffFinalSettlement = officeStaffFinalSettlementRepository.save(officeStaffFinalSettlement);

        FinalSettlementNote finalSettlementNote = new FinalSettlementNote();
        finalSettlementNote.setText(updated.getTerminationNotes());
        finalSettlementNote.setFinalSettlement(officeStaffFinalSettlement);
        finalSettlementNote.setNoteType(FSNoteType.PAYROLL_MANAGER);
        finalSettlementNoteRepository.save(finalSettlementNote);

        boolean isExpat = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT);
        boolean isEmarati = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI);

        if (isEmarati || isExpat) {
            if (ScenarioType.CFO_IS_THE_SAME_AS_THE_FINAL_MANAGER_AND_HE_IS_THE_DIRECT_MANAGER.equals(officeStaffFinalSettlement.getScenarioType()))
                finalSettlementService.sendFSToCFODirectly(officeStaffFinalSettlement, officeStaff);
            else
                finalSettlementService.sendFSToFinalManagerDirectly(officeStaffFinalSettlement, officeStaff);
        } else
            throw new RuntimeException("Not Available");

        if (officeStaff.getExternalAccesses() != null && !officeStaff.getExternalAccesses().isEmpty()) {
            OfficeStaff terminationManager = officeStaff.getWhoRequestedTermination();
            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("employee_name", officeStaff.getFirstLastName());

            messagingService.send("Payroll_Wait_Accesses_To_Be_Revoked", "Waiting for Access handler to revoke accesses",
                    null, terminationManager, paramValues, officeStaff, null, terminationManager);
//            SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                    "Waiting for Access handler to revoke accesses",
//                    normalizePhoneNumber(terminationManager.getPhoneNumber()),
//                    terminationManager.getEmail(),
//                    SmsReceiverType.Office_Staff,
//                    terminationManager.getId(),
//                    terminationManager.getName(),
//                    "OfficeStaff",
//                    "Payroll_Wait_Accesses_To_Be_Revoked",
//                    paramValues,
//                    null,
//                    terminationManager.getPreferredCommunicationMethod(),
//                    false);

        }

        return ResponseEntity.ok("Success!");
    }


    @PreAuthorize("hasPermission('officestaff','terminationInfo')")
    @RequestMapping(value = "/termination/info/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> terminationInfo(@PathVariable("id") OfficeStaff officeStaff) {
        Map<String, Object> terminationInfo = new HashMap<>();
        terminationInfo.put("officeStaff", officeStaff);
        terminationInfo.put("finalSettlement", officeStaffFinalSettlementRepository
                .findFirstByOfficeStaffOrderByCreationDateDesc(officeStaff));
        return ResponseEntity.ok(terminationInfo);
    }

    @Override
    protected List<SearchField> getDefaultSearchFields() {
        return Arrays.asList(new SearchField("name",
                        "Employee",
                        "string",
                        Setup.STRING_OPERATIONS),
                new SearchField("team",
                        "Team",
                        "com.magnamedia.core.entity.PicklistItem",
                        Setup.DEFAULT_OPERATIONS,
                        "/public/picklist/items/OfficestaffTeam"),
                new SearchField("manager",
                        "Manager",
                        "com.magnamedia.core.entity.PicklistItem",
                        Setup.DEFAULT_OPERATIONS,
                        "/public/picklist/items/managers"),
                new SearchField("status",
                        "Status",
                        "com.magnamedia.extra.OfficeStaffStatus",
                        Setup.DEFAULT_OPERATIONS,
                        "/admin/officestaff/statuses"),
                new SearchField("employeeAccesses",
                        "Access",
                        "com.magnamedia.entity.accessmgmt.ExternalAccess",
                        Arrays.asList("Contains",
                                "Not Contains"),
                        "/admin/externalAccess/list"));
    }

    @GetMapping("/statuses")
    @ResponseBody
    @PreAuthorize("hasPermission('officestaff','getStatuses')")
    public ResponseEntity<?> getStatuses() {
        return ResponseEntity.ok(getList(OfficeStaffStatus.class));
    }

    @GetMapping("/accesses")
    @ResponseBody
    @PreAuthorize("hasPermission('officestaff','getAccesses')")
    public ResponseEntity<?> getAccesses() {
        return ResponseEntity.ok(accessRepository.findAccesses());
    }

    @NoPermission
    @RequestMapping(path = "/export",
            method = RequestMethod.GET)
    public void export(HttpServletResponse response) {
        try {
            SelectQuery<OfficeStaff> query = new SelectQuery<>(
                    OfficeStaff.class);
            query.filterBy(CurrentRequest.getSearchFilter());
            createDownloadResponse(response,
                    "officestaffs-accesses.csv",
                    generateCsv(query,
                            OfficeStaffExport.class,
                            4000));
        } catch (Exception ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
        }
    }

    @PreAuthorize("hasPermission('officestaff','addAccess')")
    @GetMapping(path = "/addAccess/{employeeId}/{accessId}")
    @Transactional
    public ResponseEntity<?> addAccess(@PathVariable(name = "employeeId") OfficeStaff employee,
                                       @PathVariable(name = "accessId") ExternalAccess externalAccess) {
        OfficeStaffAccess osa = accessRepository.findByEmployeeAndAccess(
                employee,
                externalAccess);
        if (osa == null) {
            osa = new OfficeStaffAccess(employee,
                    externalAccess,
                    true);
        } else {
            osa.setHasAccess(true);
        }
        accessRepository.save(osa);

        employee.getEmployeeAccesses()
                .add(externalAccess);
        RepOfficeStaff.save(employee);

        externalAccess = externalAccessRepository
                .findOne(externalAccess.getId());
        externalAccess.setTeams(externalAccess.getTeams());
        externalAccessRepository.save(externalAccess);

        return ResponseEntity.ok(osa);

    }

    @PreAuthorize("hasPermission('officestaff','removeAccess')")
    @GetMapping(path = "/removeAccess/{employeeId}/{accessId}")
    @Transactional
    public ResponseEntity<?> removeAccess(@PathVariable(name = "employeeId") OfficeStaff employee,
                                          @PathVariable(name = "accessId") ExternalAccess access) {
        OfficeStaffAccess osa = accessRepository.findByEmployeeAndAccess(
                employee,
                access);
        if (osa == null) {
        } else {
            osa.setHasAccess(false);
            accessRepository.delete(osa);

            employee.getEmployeeAccesses()
                    .remove(access);
            RepOfficeStaff.save(employee);

            access = externalAccessRepository
                    .findOne(access.getId());
            access.setTeams(access.getTeams());
            externalAccessRepository.save(access);
        }

        return okResponse();

    }

    @PreAuthorize("hasPermission('officestaff','managers')")
    @GetMapping(path = "/managers/{id}")
    public ResponseEntity<?> managers(@PathVariable(name = "id") OfficeStaff officeStaff) {
        List<OfficeStaff> managers = new ArrayList<>();
        // get all managers
        OfficeStaff curr = officeStaff;
        while (curr.getEmployeeManager() != null && !curr.getEmployeeManager().getId().equals(curr.getId())) {
            managers.add(curr.getEmployeeManager());
            curr = curr.getEmployeeManager();
        }

        if (managers.isEmpty()) {
            OfficeStaff finalManager = officeStaff.getFinalManager();
            // always should be true
            if (finalManager != null) managers.add(finalManager);
        }

        return new ResponseEntity<>(project(managers, IdLabel.class), HttpStatus.OK);
    }

    //    @PreAuthorize("hasPermission('officestaff','getBreakDownPercentages')")
    @NoPermission
    @RequestMapping(path = "/getBreakDownPercentages")
    public ResponseEntity<?> getBreakDownPercentages() {
        HashMap resultMap = new HashMap();
        Double basicSalaryPercentage = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXPAT_BASIC_SALARY_PERCENTAGE));
        Double housingPercentage = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXPAT_HOUSING_PERCENTAGE));
        Double transportationPercentage = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXPAT_TRANSPORTATION_PERCENTAGE));

        resultMap.put("basicSalaryPercentage", basicSalaryPercentage);
        resultMap.put("housingPercentage", housingPercentage);
        resultMap.put("transportationPercentage", transportationPercentage);

        return ResponseEntity.ok(resultMap);
    }

    @PreAuthorize("hasPermission('officestaff','rehire')")
    @RequestMapping(value = "/rehire/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> rehire(@PathVariable("id") OfficeStaff officeStaff) {

        if (Setup.getRepository(OfficeStaffCandidateRepository.class).existsByRehiredAndRehiredIdAnd(officeStaff.getId(), true, false)) {
            throw new BusinessException("Rehiring request is already sent");
        }
        OfficeStaffCandidate candidate = new OfficeStaffCandidate();
        candidate.setRehired(true);
        candidate.setRehiredId(officeStaff.getId());
        candidate.setFirstName(officeStaff.getFirstName());
        candidate.setLastName(officeStaff.getLastName());
        candidate.setPhoneNumber(officeStaff.getPhoneNumber());
        candidate.setEmail(officeStaff.getEmail());
        candidate.setSource("ERP System");
        candidate.setEmployeeName(officeStaff.getName());
        if (officeStaff.getJobTitle() != null) {
            candidate.setJazzHRJobTitle(officeStaff.getJobTitle().getName());
        }
        candidate.setJobTitle(officeStaff.getJobTitle());
        candidate.setEmployeeManager(officeStaff.getEmployeeManager());

        // Set salaries
        candidate.setSalary(officeStaff.getSalary());
        candidate.setBasicSalary(officeStaff.getSalary());
        candidate.setTransportation(0d);
        candidate.setHousing(0d);
        /**********************/

        candidate.setCityName(officeStaff.getCityName());
        candidate.setCountry(officeStaff.getCountry());
        candidate.setTeam(officeStaff.getTeam());
        candidate.setDepartments(officeStaff.getDepartments());
        candidate.setEmployeeType(officeStaff.getEmployeeType());
        candidate.setVisaRequired(officeStaff.getVisaRequired());
        candidate.setStartingDate(new Date());
        candidate.setNationality(officeStaff.getNationality());
        if (officeStaff.getWeeklyOffDayList() != null && !officeStaff.getWeeklyOffDayList().isEmpty()) {
            candidate.setWeeklyOffDayList(new ArrayList<>(officeStaff.getWeeklyOffDayList()));
        }
        Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);

        OfficeStaffTodo officeStaffTodo = new OfficeStaffTodo(OfficeStaffTodoType.HIRE_EMPLOYEE.toString());
        officeStaffTodo.setCandidate(candidate);
        Setup.getRepository(OfficeStaffTodoRepository.class).save(officeStaffTodo);

        return ResponseEntity.ok(candidate);
    }

    @NoPermission
    @GetMapping("/get-daily-birthdays")
    public ResponseEntity<?> getDailyBirthdays() {
        java.time.LocalDate today = java.time.LocalDate.now();
        List<OfficeStaffBirthdayProjection> officeStaffs = Setup.getRepository(OfficeStaffRepository.class)
                .findActiveOfficeStaffWithBirthday(today.getDayOfMonth(), today.getMonthValue());

        return ResponseEntity.ok(officeStaffs);
    }

    @PreAuthorize("hasPermission('officestaff','getByType')")
    @RequestMapping(value = "/getByType", method = RequestMethod.GET)
    protected ResponseEntity<?> getByType(
            @RequestParam(name = "type") String type) {
        if(type != null && !type.isEmpty()){
            if (type.equals("All")) {
                // return list of all office staff
                List<OfficeStaff> allEmployees = RepOfficeStaff.findAll();
                List<OfficeStaffList> employeesList = allEmployees.stream()
                        .map(employee -> projectionFactory.createProjection(OfficeStaffList.class, employee))
                        .collect(Collectors.toList());

                return new ResponseEntity<>(employeesList, HttpStatus.OK);
            } else{
                // return office staff of specific office staff type
                return new ResponseEntity<>(RepOfficeStaff.findByEmployeeType(OfficeStaffType.valueOf(type)), HttpStatus.OK);
            }
        }



        return new ResponseEntity<>("", HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/validateSwiftCode/{swift}", method = RequestMethod.GET)
    public ResponseEntity<?> validateSwiftCode(@PathVariable("swift") String swift) {
        if(swift == null || swift.isEmpty()) {
            throw new BusinessException("Swift code required");
        }

        SwiftCodeValidationResponse response = Setup.getApplicationContext().getBean(ValidateIBANService.class).validateSwiftCode(swift);
        if (response == null || response.getData() == null || !response.isSuccess()) {
            throw new BusinessException("Swift code is not valid");
        }
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/detailedInfo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getDetailedInfo(Pageable pageable) {
        Page<OfficeStaff> staffPage = RepOfficeStaff.findByStatus(OfficeStaffStatus.ACTIVE, pageable);
        return new ResponseEntity<>(project(staffPage, OfficeStaffDetailedInfoProjection.class), HttpStatus.OK);
    }

}