package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PhoneNumberUtil;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.OfficeStaffCandidateRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.OfficeStaffTodoRepository;
import com.magnamedia.repository.PayrollRosterApproveRequestRepository;
import com.magnamedia.service.ChangeBankDetailsService;
import com.magnamedia.service.PublicPageService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.OfficeStaffFinalSettlementService;
import com.magnamedia.service.payroll.generation.newversion.PayrollRosterApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 5/9/2020
 **/
@RestController
@RequestMapping("/officeStaffTodo")
public class OfficeStaffTodoController extends BaseRepositoryController<OfficeStaffTodo> {


    @Autowired
    private MailService mailService;

    @Autowired
    public PublicPageHelper publicPageHelper;

    @Autowired
    private PublicPageService publicPageService;

    @Autowired
    ProjectionFactory projectionFactory;
    @Autowired
    private OfficeStaffTodoRepository officeStaffTodoRepository;
    @Autowired
    private OfficeStaffCandidateRepository officeStaffCandidateRepository;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private OfficeStaffCandidateController candidateController;

    @Autowired
    private ChangeBankDetailsService changeBankDetailsService;

    @Autowired
    OfficeStaffFinalSettlementRepository officeStaffFinalSettlementRepository;
    @Autowired
    FinalSettlementNoteRepository finalSettlementNoteRepository;

    @Autowired
    OfficeStaffFinalSettlementService officeStaffFinalSettlementService;

    @Autowired
    AccountantToDoService accountantToDoService;

    @Autowired
    MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Override
    public BaseRepository<OfficeStaffTodo> getRepository() {
        return officeStaffTodoRepository;
    }

//    @PreAuthorize("hasPermission('officeStaffTodo', 'findOpenedTodos')")
    @NoPermission
    @RequestMapping(value = "/findOpenedTodos", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> findOpenedTodos(Pageable pageable) {

        return ResponseEntity.ok(officeStaffTodoRepository.findOpenedTodos(pageable));
    }

    @PreAuthorize("hasPermission('officeStaffTodo', 'confirmPayrollQuestionnaireTodo')")
    @RequestMapping(value = "/confirmPayrollQuestionnaireTodo/{id}", method = RequestMethod.POST)
    @ResponseBody
    @Transactional
    public ResponseEntity<?> confirmPayrollQuestionnaireTodo(@PathVariable Long id, @RequestBody ObjectNode objectNode) throws IOException {

        //update OfficeStaffCandidate
        if (objectNode != null)
            candidateController.updateEntity(objectNode);

        OfficeStaffTodo todo = officeStaffTodoRepository.findOne(id);
        if (todo == null)
            throw new BusinessException("The requested To-Do isn't found!");

        //save the data into the officeStaff record
        OfficeStaffCandidate candidate = officeStaffCandidateRepository.findOne(todo.getCandidate().getId());

        // Normalize phone numbers
        if (candidate.getPhoneNumber() != null && !candidate.getPhoneNumber().isEmpty()) {
            String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(candidate.getPhoneNumber());
            if (!normalizedPhoneNumber.equals(candidate.getPhoneNumber())) {
                candidate.setPhoneNumber(normalizedPhoneNumber);
            }
        }

        if (candidate.getEmergencyContactPhoneNumber() != null && !candidate.getEmergencyContactPhoneNumber().isEmpty()) {
            String normalizedEmergencyNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(candidate.getEmergencyContactPhoneNumber());
            if (!normalizedEmergencyNumber.equals(candidate.getEmergencyContactPhoneNumber())) {
                candidate.setEmergencyContactPhoneNumber(normalizedEmergencyNumber);
            }
        }

        if (candidate.getSelectedTransferDestination() != null
                && candidate.getSelectedTransferDestination().getSelfReceiver() != null && !candidate.getSelectedTransferDestination().getSelfReceiver()
                && candidate.getSelectedTransferDestination().getPhoneNumber() != null && !candidate.getSelectedTransferDestination().getPhoneNumber().isEmpty()) {
            TransferDestination destination = candidate.getSelectedTransferDestination();
            String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(destination.getPhoneNumber());

            if (!normalizedPhoneNumber.equals(destination.getPhoneNumber())) {
                destination.setPhoneNumber(normalizedPhoneNumber);
            }
        }

        OfficeStaff officeStaff = officeStaffRepository.findFirstByOfficeStaffCandidate(candidate);
        officeStaff.copyFromCandidate(candidate.getNationality(),candidate.getCountry(),
                candidate.getFullNameInArabic(),candidate.getCityName(),candidate.getFullAddress(),
                //...PAY-899...//
                candidate.getEmergencyContactName(),candidate.getEmergencyContactPhoneNumber(),
                candidate.getTransferDestinations(),candidate.getDocuments(),candidate.getEidNumber(),
                candidate.getJazzHRProfile(), candidate.getFirstName(),
                candidate.getMiddleName(), candidate.getLastName(), candidate.getPreferredCommunicationMethod(), candidate.getBirthDate(), candidate.getNewEmail(), candidate.getDepartments());

        officeStaffRepository.save(officeStaff);

//        try {
//            if(officeStaff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EXPAT) {
//                TransferDestination transferDestination = officeStaff.getSelectedTransferDestination();
//                if(transferDestination.getReceiveMoneyMethod() == ReceiveMoneyMethod.BANK_TRANSFER) {
//                    changeBankDetailsService.sendChangeBankDetailsEmail(officeStaff, transferDestination.getIban(), transferDestination.getAccountHolderName(),
//                            transferDestination.getBankName(), transferDestination.getAccountNumber());
//                }
//            }
//        } catch (Throwable e) {
//            logger.log(Level.SEVERE, "Unable to send change bank details email to Ansari.", e);
//            throw new BusinessException("Unable to send change bank details email to Ansari. Please check bank name and IBAN");
//        }
        //save  the To-Do as completed
        todo.setCompleted(true);
        officeStaffTodoRepository.save(todo);

        return ResponseEntity.ok("Success");
    }

//    @PreAuthorize("hasPermission('officeStaffTodo', 'getDeclinedRosterTodoDetails')")
//    @GetMapping("/getDeclinedRosterTodoDetails/{id}")
//    public ResponseEntity<?> getDeclinedRosterTodoDetails(@PathVariable("id") OfficeStaffTodo officeStaffTodo) {
//
//        if (officeStaffTodo == null)
//            throw new RuntimeException("Undefined Todo!");
//
//        PayrollRosterApproveRequest rosterApproveRequest = officeStaffTodo.getRosterApproveRequest();
//        if(rosterApproveRequest == null)
//            throw new RuntimeException("Undefined Todo!");
//
//        return ResponseEntity.ok(rosterApproveRequest.getRosterNotes());
//    }

    @PreAuthorize("hasPermission('officeStaffTodo', 'sendEditedRosterFileAgainToManager')")
    @RequestMapping("/sendEditedRosterFileAgainToManager/{id}")
    public ResponseEntity<?> sendEditedRosterFileAgainToManager(@PathVariable("id") OfficeStaffTodo officeStaffTodo) {

        if (officeStaffTodo == null)
            throw new BusinessException("Undefined Todo!");

        PayrollRosterApproveRequest rosterApproveRequest = officeStaffTodo.getRosterApproveRequest();
        if(rosterApproveRequest == null)
            throw new BusinessException("Undefined Todo!");

        //set todo as completed
        officeStaffTodo.setCompleted(true);
        officeStaffTodoRepository.save(officeStaffTodo);


        //reset the request to activate the reminder job and the approval link
        rosterApproveRequest.setActionTaken(false);
        rosterApproveRequest.setLastEditingDate(new Date());
        rosterApproveRequest.setRemindedBefore(false);
        rosterApproveRequest = Setup.getRepository(PayrollRosterApproveRequestRepository.class).save(rosterApproveRequest);

        //send the roster file back to manager
        Setup.getApplicationContext().getBean(PayrollRosterApprovalService.class).sendApprovalMailToSelectedManager(rosterApproveRequest);

        return ResponseEntity.ok(rosterApproveRequest.getRosterNotes());
    }

    @PreAuthorize("hasPermission('officeStaffTodo', 'fetchFinalSettlement')")
    @RequestMapping(value = "/fetchFinalSettlement/{id}",method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> fetchFinalSettlement(@PathVariable("id") OfficeStaffTodo officeStaffTodo) {
        if (officeStaffTodo == null || officeStaffTodo.getCompleted())
            throw new RuntimeException("Todo is already closed!");

        if (officeStaffTodo.getRelatedEntityId() == null
                || !officeStaffTodo.getRelatedEntityType().equals("OfficeStaffFinalSettlement")) {
            throw new RuntimeException("Wrong Todo Type, go back to the main page");
        }
        OfficeStaffFinalSettlement finalSettlement = officeStaffFinalSettlementRepository.findOne(officeStaffTodo.getRelatedEntityId());
        if (finalSettlement == null)
            throw new RuntimeException("finalSettlement not found");
        if (OfficeStaffFinalSettlementStatus.FINISHED_AND_PAID_NOW.equals(finalSettlement.getCurrentStatus()) || OfficeStaffFinalSettlementStatus.FINISHED_AND_PAID_AT_THE_END_OF_THE_MONTH.equals(finalSettlement.getCurrentStatus())) {
            throw new RuntimeException("finalSettlement is finished");
        }
        Map<String, Object> infoMap = new HashMap<String, Object>();
        infoMap.put("officeStaff", finalSettlement.getOfficeStaff());
        infoMap.put("finalSettlement", finalSettlement);
        infoMap.put("actions", finalSettlement.getActions());
        infoMap.put("notes", finalSettlement.getNotes());
        return ResponseEntity.ok(infoMap);

    }

    @PreAuthorize("hasPermission('officeStaffTodo', 'finalSettlementTodoTakeAction')")
    @RequestMapping(value = "/finalSettlementTodoTakeAction/{id}", method = RequestMethod.POST)
    @ResponseBody
    @Transactional
    public ResponseEntity<?> finalSettlementTodoTakeAction(@PathVariable("id") OfficeStaffTodo officeStaffTodo,
                                                           @RequestParam(value = "action") String action,
                                                           @RequestParam(value = "note", required = false) String note,
                                                           @RequestParam(value = "visaCancellationDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date visaCancellationDate,
                                                           @RequestBody Map<String, Double> updatedFinalSettlement) {
        if (officeStaffTodo.getRelatedEntityType() == null || officeStaffTodo.getRelatedEntityId() == null) {
            throw new RuntimeException("finalSettlement is not found");
        }

        OfficeStaffFinalSettlement finalSettlement = officeStaffFinalSettlementRepository.findOne(officeStaffTodo.getRelatedEntityId());
        OfficeStaff officeStaff = finalSettlement.getOfficeStaff();

        FinalSettlementNote finalSettlementNote = null;
        if (finalSettlement == null) {
            throw new RuntimeException("finalSettlement is not found");
        }
        switch (action) {
            case "sendBackToCFO":
                if (note == null || note.isEmpty()) {
                    throw new RuntimeException("the note is required in case send back to manager");
                }
                finalSettlementNote = new FinalSettlementNote();
                finalSettlementNote.setText(note);
                finalSettlementNote.setFinalSettlement(finalSettlement);
                finalSettlementNote.setNoteType(FSNoteType.PAYROLL_MANAGER);
                finalSettlementNoteRepository.save(finalSettlementNote);
                updateFinalSettlement(finalSettlement, updatedFinalSettlement);
                finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_CFO);
                officeStaffFinalSettlementService.sendUpdatedFSToCFO(finalSettlement, officeStaff);
                officeStaffTodo.setCompleted(true);
                break;

            case "sendBackToTheManager":
                if (note == null || note.isEmpty()) {
                    throw new RuntimeException("the note is required in case send back to manager");
                }
                finalSettlementNote = new FinalSettlementNote();
                finalSettlementNote.setText(note);
                finalSettlementNote.setFinalSettlement(finalSettlement);
                finalSettlementNote.setNoteType(FSNoteType.PAYROLL_MANAGER);
                finalSettlementNoteRepository.save(finalSettlementNote);
                updateFinalSettlement(finalSettlement, updatedFinalSettlement);
                finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_FINAL_MANAGER);
                officeStaffFinalSettlementService.sendUpdatedFSToFinalManager(finalSettlement, officeStaff, officeStaff.getLastManagerOtherThanCFO());
                officeStaffTodo.setCompleted(true);
                break;
            case "payLater":
                finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.FINISHED_WITHOUT_PAID);
                break;
            case "payNow":
                MonthlyPaymentRule rule = accountantToDoService.createMonthlyRuleForSingleOfficeStaff(officeStaff);
                rule = monthlyPaymentRuleRepository.save(rule);
                OfficeStaffPayrollLog log = accountantToDoService.createPayrollLogForSingleOfficeStaffWithCalculatingTransactions(officeStaff, rule);
                List<OfficeStaffPayrollLog> logs = new ArrayList<>();
                logs.add(log);
                accountantToDoService.createPaymentToDoForSingleOfficeStaff(rule, officeStaff, logs, true);

                finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.FINISHED_AND_PAID_NOW);
                officeStaffTodo.setCompleted(true);
                break;
            case "payAtTheEndOfTheMonth":
                finalSettlement.setIncludeInPayroll(true);
                finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.FINISHED_AND_PAID_AT_THE_END_OF_THE_MONTH);
                officeStaffTodo.setCompleted(true);
                break;
        }
        if (visaCancellationDate != null){
            if(!OfficeStaffType.DUBAI_STAFF_EXPAT.equals(finalSettlement.getOfficeStaff().getEmployeeType())){
                throw new RuntimeException("Wrong in Employee Type");
            }
            if (finalSettlement.getOfficeStaff().getTerminationDate() == null ||
                    visaCancellationDate.before(DateUtil.getDayStart(finalSettlement.getOfficeStaff().getTerminationDate())) ||
                    visaCancellationDate.after(DateUtil.addMonths(finalSettlement.getOfficeStaff().getTerminationDate(),2))){
                throw new RuntimeException("Visa Cancellation Date cannot be before the Termination Date or after the termination date with two months ");
            }
            finalSettlement.setVisaCancellationDate(new java.sql.Date(visaCancellationDate.getTime()));
            java.sql.Date dateNow = new java.sql.Date(new Date().getTime());
            if(dateNow.compareTo(visaCancellationDate) >= 0) {
                officeStaff.setCreateCancel(true);
                officeStaffRepository.save(officeStaff);
            }
        }
        officeStaffFinalSettlementRepository.save(finalSettlement);
        officeStaffTodoRepository.save(officeStaffTodo);

        return ResponseEntity.ok("Done");
    }

    public void updateFinalSettlement (OfficeStaffFinalSettlement finalSettlement, Map<String, Double> updatedFinalSettlement) {
        Double salaryOfLastMonth = updatedFinalSettlement.getOrDefault("salaryOfLastMonth", finalSettlement.getSalaryOfLastMonth());
        Double offDays = updatedFinalSettlement.getOrDefault("offDays", finalSettlement.getOffDays());
        Double gratuity = updatedFinalSettlement.getOrDefault("gratuity", finalSettlement.getGratuity());
        Double compensation = updatedFinalSettlement.getOrDefault("compensation", finalSettlement.getCompensation());
        Double airfareTicket = updatedFinalSettlement.getOrDefault("airfareTicket", finalSettlement.getAirfareTicket());
        Double loanRepayments = updatedFinalSettlement.getOrDefault("loanRepayments", finalSettlement.getLoanRepayments());
        finalSettlement.setSalaryOfLastMonth(salaryOfLastMonth != null ? salaryOfLastMonth : 0.0);
        finalSettlement.setOffDays(offDays != null ? offDays : 0.0);
        finalSettlement.setGratuity(gratuity != null ? gratuity : 0.0);
        finalSettlement.setCompensation(compensation != null ? compensation : 0.0);
        finalSettlement.setAirfareTicket(airfareTicket != null ? airfareTicket : 0.0);
        finalSettlement.setLoanRepayments(loanRepayments != null ? loanRepayments : 0.0);
    }

    @Transactional
    @PreAuthorize("hasPermission('officeStaffTodo', 'deleteHireEmployeeTodo')")
    @RequestMapping(value = "/deleteHireEmployeeTodo/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    public ResponseEntity<?> deleteHireEmployeeTodo(@PathVariable("id") OfficeStaffTodo officeStaffTodo,
                                                    @RequestParam String note) {

        if (!OfficeStaffTodoType.HIRE_EMPLOYEE.toString().equals(officeStaffTodo.getTaskName())){
            throw new BusinessException("action is invalid");
        }

        String staffName = officeStaffTodo.getCandidate() != null ? officeStaffTodo.getCandidate().getEmployeeName() : "";
        String subject = "Payroll manager reversed the onboarding of " + staffName;

        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_RECRUITMENT_MANAGER_EMAIL));
        Map<String, String> params = new HashMap<>();
        params.put("employee_name", staffName);
        params.put("note", note);
        params.put("link", (officeStaffTodo.getCandidate() != null ? officeStaffTodo.getCandidate().getJazzHRProfile() : ""));

        Setup.getApplicationContext().getBean(MessagingService.class).send(recipients, null, "Payroll_Hire_New_Employee_Todo_Deleted_Template",
                subject, params, new ArrayList<>(), null);

//        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Hire_New_Employee_Todo_Deleted_Template", params);
//        Setup.getMailService().sendEmail(recipients, templateEmail, null);

        return super.deleteEntity(officeStaffTodo);
    }

}
