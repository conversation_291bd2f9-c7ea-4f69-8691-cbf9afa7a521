package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.PaymentRuleConfiguration;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.repository.PaymentRuleConfigurationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 6/11/2020
 **/
@RequestMapping("/paymentRuleConfiguration")
@RestController
public class PaymentRuleConfigurationController extends BaseRepositoryController<PaymentRuleConfiguration> {

    @Autowired
    private PaymentRuleConfigurationRepository paymentRuleConfigurationRepository;

    @Override
    public BaseRepository<PaymentRuleConfiguration> getRepository() {
        return paymentRuleConfigurationRepository;
    }

    @Override
    protected ResponseEntity<?> createEntity(PaymentRuleConfiguration entity) {

        SelectQuery<PaymentRuleConfiguration> selectQuery = new SelectQuery<>(PaymentRuleConfiguration.class);

        if (!entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.OVERSEAS))
            entity.setCountries(new ArrayList<>());

        if (!entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS))
            entity.setHousemaidTypeList(null);

        if (!entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS) && !entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.EXPATS))
            entity.setMolType(null);

        if (!entity.getEmployeeTypeList().isEmpty()) {
            selectQuery.join("employeeTypeList");
            selectQuery.filterBy("employeeTypeList", "IN", entity.getEmployeeTypeList());
        }
        if (!entity.getHousemaidTypeList().isEmpty()) {
            selectQuery.join("housemaidTypeList");
            selectQuery.filterBy("housemaidTypeList", "IN", entity.getHousemaidTypeList());
        }
        if (entity.getMolType() != null)
            selectQuery.filterBy("molType", "=", entity.getMolType());
        if (entity.getCountries() != null && entity.getCountries().size() > 0) {
            selectQuery.join("countries");
            selectQuery.filterBy("countries", "IN", entity.getCountries());
        }
        if (entity.getPayrollType() != null)
            selectQuery.filterBy("payrollType", "=", entity.getPayrollType());

        List<PaymentRuleConfiguration> oldRules = selectQuery.execute();
        if (oldRules != null && !oldRules.isEmpty())
            throw new BusinessException("This Rule is already existed before!");
        return super.createEntity(entity);
    }

    @Override
    protected ResponseEntity<?> updateEntity(PaymentRuleConfiguration entity) {
        SelectQuery<PaymentRuleConfiguration> selectQuery = new SelectQuery<>(PaymentRuleConfiguration.class);

        if (!entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.OVERSEAS))
            entity.setCountries(new ArrayList<>());

        if (!entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS))
            entity.setHousemaidTypeList(null);

        if (!entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS) && !entity.getEmployeeTypeList().contains(PaymentRuleEmployeeType.EXPATS))
            entity.setMolType(null);

            selectQuery.filterBy("id", "<>", entity.getId());
        if (!entity.getEmployeeTypeList().isEmpty()) {
            selectQuery.join("employeeTypeList");
            selectQuery.filterBy("employeeTypeList", "IN", entity.getEmployeeTypeList());
        }
        if (!entity.getHousemaidTypeList().isEmpty()) {
            selectQuery.join("housemaidTypeList");
            selectQuery.filterBy("housemaidTypeList", "IN", entity.getHousemaidTypeList());
        }
        if (entity.getMolType() != null)
            selectQuery.filterBy("molType", "=", entity.getMolType());
        if (entity.getCountries().size() > 0)
            selectQuery.filterBy("country", "IN", entity.getCountries());
        if (entity.getPayrollType() != null)
            selectQuery.filterBy("payrollType", "=", entity.getPayrollType());

        List<PaymentRuleConfiguration> oldRules = selectQuery.execute();
        if (oldRules != null && !oldRules.isEmpty())
            throw new BusinessException("This Rule is already existed before!");
        return super.updateEntity(entity);
    }

    @PreAuthorize("hasPermission('paymentRuleConfiguration','getRulesConfiguration')")
    @RequestMapping(value = "/getRulesConfiguration", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getRulesConfiguration() {

        return new ResponseEntity<>(paymentRuleConfigurationRepository.findAll(), HttpStatus.OK);
    }
}
