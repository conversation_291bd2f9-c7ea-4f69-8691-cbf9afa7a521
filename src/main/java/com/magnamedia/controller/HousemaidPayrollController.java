package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.projection.AccountantToDoProjection;
import com.magnamedia.entity.projection.HousemaidList;
import com.magnamedia.entity.projection.HousemaidNoEidProjection;
import com.magnamedia.entity.projection.HousemaidPayrollLogProjection;
import com.magnamedia.entity.projection.HousemaidPayrollLog2class;
import com.magnamedia.extra.*;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.HousemaidLiveplace;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.MonthlyPayrollDocumentType;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.HousemaidSalaryTransaction;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.NegativeSalariesService;
import com.magnamedia.service.ScheduledMonthlyService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.HousemaidPayrollGenerationService;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import org.apache.commons.io.FilenameUtils;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Abbas <<EMAIL>>
 */
@RequestMapping("/HousemaidPayroll")
@RestController
public class HousemaidPayrollController extends BaseRepositoryController<Housemaid> {

    @Autowired
    private HousemaidRepository housemaidRep;
    //PAY-801
    @Autowired
    private ClientRepository clientRep;

    //Jirra 1384
    @Autowired
    private RenewVisaRequestRepository renewVisaRequestRepository;

    @Autowired
    private MailService mailService;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private AccommodationRepository accRep;

    @Autowired
    private MonthlyPayrollRepository monthlyPayrollRepository;

    @Autowired
    private MonthlyPayrollDocumentRepository monthlyPayrollDocumentRepository;

    @Autowired
    private PaymentWorkOrderController pwoController;

    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    private RepaymentRepository repaymentRepository;

    @Autowired
    private EmployeeLoanRepository employeeLoanRepository;

    @Autowired
    private ScheduledMonthlyService scheduledMonthlyService;

    @Autowired
    private NegativeSalariesService negativeSalariesService;

    @Autowired
    private HousemaidPayrollController selfReference;

    @Autowired
    private HousemaidPayrollGenerationService housemaidPayrollGenerationService;

    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    private HousemaidExcludeHistoryRepository housemaidExcludeHistoryRepository;

    private Boolean payrollThreadTriggered = false;

    private Boolean payrollCheckListTriggered = false;

    //Jirra ACC-1349
    private ThreadLocal<Map<String, BaseEntity>> thread = ThreadLocal.withInitial(() -> new HashMap<>());

    @Override
    public BaseRepository<Housemaid> getRepository() {
        return housemaidRep;
    }

    @NoPermission
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    @Override
    public ResponseEntity<?> update(
            @RequestBody ObjectNode objectNode) throws IOException {

        if (checkPermission("update")) {

            Housemaid updated = parse(objectNode);
            Housemaid origin = getRepository().findOne(updated.getId());

            boolean needValidateSalaryComponent = false;
            if (objectNode.has("primarySalary")) {
                if (!Objects.equals(updated.getPrimarySalary(), origin.getPrimarySalary())) {
                    needValidateSalaryComponent = true;
                }
            }
            if (objectNode.has("overTime")) {
                if (!Objects.equals(updated.getOverTime(), origin.getOverTime())) {
                    needValidateSalaryComponent = true;
                }
            }
            if (objectNode.has("monthlyLoan")) {
                if (!Objects.equals(updated.getMonthlyLoan(), origin.getMonthlyLoan())) {
                    needValidateSalaryComponent = true;
                }
            }
            if (objectNode.has("holiday")) {
                if (!Objects.equals(updated.getHoliday(), origin.getHoliday())) {
                    needValidateSalaryComponent = true;
                }
            }
            if (objectNode.has("accommodationSalary")) {
                if (!Objects.equals(updated.getAccommodationSalary(), origin.getAccommodationSalary())) {
                    needValidateSalaryComponent = true;
                }
            }


            LocalDate dt = new LocalDate();
            String lockPayrollEnabled = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_ENABLED);
            Integer lockPayrollStart = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_START));
            Integer lockPayrollEnd = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_END));

            if ((origin.getPrimarySalary() == null && updated.getPrimarySalary() == null)
                    || (origin.getPrimarySalary() != null
                    && updated.getPrimarySalary() != null
                    && origin.getPrimarySalary().equals(updated.getPrimarySalary()))
                    || (!lockPayrollEnabled.equals("1"))
                    || (dt.getDayOfMonth() < lockPayrollStart && dt.getDayOfMonth() > lockPayrollEnd)) {

                //PAY-526 insert housemaid exclude history
                if(updated.getExcludedFromPayroll() != null
                        && !origin.getExcludedFromPayroll().equals(updated.getExcludedFromPayroll())) {
                    HousemaidExcludeHistory excludeHistory = new HousemaidExcludeHistory(origin, updated.getExcludedFromPayroll(), updated.getExcludedManuallyNotes());
                    Setup.getRepository(HousemaidExcludeHistoryRepository.class).save(excludeHistory);

                    if(updated.getExcludedFromPayroll()) {

                        HousemaidLastExcludeDetails lastExcludeDetails = origin.getHousemaidLastExcludeDetails();
                        if (lastExcludeDetails == null) {
                            lastExcludeDetails = new HousemaidLastExcludeDetails(origin, true, (String) updated.getExcludedManuallyNotes(), new Date());
                        } else {
                            lastExcludeDetails.setExcludedManuallyFromProfile(true);
                            lastExcludeDetails.setNotes(updated.getExcludedManuallyNotes());
                            lastExcludeDetails.setLastExclusionManuallyDate(new Date());
                        }
                        lastExcludeDetails = Setup.getRepository(HousemaidLastExcludeDetailsRepository.class).save(lastExcludeDetails);
                        User manager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(Setup.getParameter(Setup.getCurrentModule(),HousemaidType.MAID_VISA.equals(origin.getHousemaidType()) ? PayrollManagementModule.PARAMETER_MV_MANAGER_USER_ID : PayrollManagementModule.PARAMETER_CC_MANAGER_USER_ID)));
                        if (manager != null && manager.getEmail() != null) {
                            List<EmailRecipient> recipients = Recipient.parseEmailsString(manager.getEmail());
                            String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDE_MAID_EMAIL_TITLE);
                            subject = subject.replace("@maid_name@", origin.getName());
                            Map<String, String> params = new HashMap<>();
                            params.put("maid_name", origin.getName());
                            params.put("user_name", CurrentRequest.getUser().getFullName());
                            params.put("user_notes", updated.getExcludedManuallyNotes());
                            messagingService.send(recipients, null, "Payroll_Exclude_Maid_Manually_From_Profile",
                                    subject, null, null, params, origin, null, origin);
//                            TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Exclude_Maid_Manually_From_Profile", params);
//                            mailService.sendEmail(recipients, templateEmail, MessageTemplateService.getMaidsCcSenderName(), null);
                        }
                    } else {
                        HousemaidLastExcludeDetails lastExcludeDetails = origin.getHousemaidLastExcludeDetails();
                        if (lastExcludeDetails == null) {
                            lastExcludeDetails = new HousemaidLastExcludeDetails(origin, false, (String) updated.getExcludedManuallyNotes(), new Date());
                        } else {
                            lastExcludeDetails.setExcludedManuallyFromProfile(false);
                            lastExcludeDetails.setNotes(updated.getExcludedManuallyNotes());
                            lastExcludeDetails.setLastExclusionManuallyDate(new Date());
                        }
                        lastExcludeDetails = Setup.getRepository(HousemaidLastExcludeDetailsRepository.class).save(lastExcludeDetails);
                    }
                }


                update(origin, updated, objectNode);
                if (needValidateSalaryComponent) {
                    if (!Setup.getApplicationContext().getBean(PayrollGroupService.class).validateComponent(origin.caculateBasicSalary(), origin)) {
                        origin = getRepository().save(origin);
                        Setup.getApplicationContext().getBean(HousemaidController.class).zeroingHousemaidSalaryComponents(origin.getId());
                        origin = getRepository().findOne(origin.getId());
                    }
                }
                return super.updateEntity(origin);
            } else {
                return new ResponseEntity<>("Housemaid Salary Could not be updated.", HttpStatus.BAD_REQUEST);
            }
        } else {
            return unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('HousemaidPayroll', 'getHousemaidExcludeHistories')")
    @RequestMapping(value = "/getHousemaidExcludeHistories/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> findOpenedTodos(@PathVariable(name = "id") Housemaid housemaid) {

        return ResponseEntity.ok(housemaidExcludeHistoryRepository.getHousemaidExcludeHistories(housemaid));
    }
    
    @PreAuthorize("hasPermission('HousemaidPayroll','filterHousemaids')")
    @RequestMapping(value = "/filterHousemaids", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> filterHousemaids(@RequestBody Housemaid housemaid,
                                              Pageable pageable) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if (housemaid.getName() != null) {
            query.filterBy("name", "Like", "%" + housemaid.getName() + "%");
        }
        if (housemaid.getStatus() != null) {
            query.filterBy("status", "=", housemaid.getStatus());
        }
//        List<Housemaid> houseamidList = query.execute(pageable).getContent();
//        List<HousemaidList> projected = houseamidList.stream().map(maid -> projectionFactory.createProjection(HousemaidList.class,
//                maid)).collect(Collectors.toList());
//        Page<HousemaidList> page = new PageImpl<HousemaidList>(projected, pageable, projected.size());
        return new ResponseEntity<>(query.execute(pageable).map(maid -> projectionFactory.createProjection(HousemaidList.class,
                maid)), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','lockPayroll')")
    @RequestMapping("lockPayroll")
    public ResponseEntity<?> lockPayroll() {
        throw new RuntimeException("Not implemented yet");
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','getStoredPayrollFile')")
    @RequestMapping("/getStoredPayrollFile")
    public ResponseEntity<?> getStoredPayrollFile(HttpServletResponse response,
                                                  @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                  @RequestParam(required = true, value = "sentByEmail") boolean isSentByEmail,
                                                  @RequestParam(required = true, value = "type") String type) throws IOException {

        MonthlyPayrollDocumentType fileType = MonthlyPayrollDocumentType.valueOf(type);
        LocalDate dt;
        //If payroll date isn't included in the request then the payroll file will be generated for the current month
        if (date != null) {
            dt = new LocalDate(date).withDayOfMonth(26);
        } else {
            dt = new LocalDate().withDayOfMonth(26);
        }
        LocalDate todayDate = new LocalDate();

        List<MonthlyPayrollDocument> files = monthlyPayrollDocumentRepository.
                findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, fileType);
        if (isSentByEmail && files.size() > 0) {
            sendMail(fileType, files, DateUtil.formatMonth(dt.toDate()));
        }

        if (files.size() > 0) {
            return new ResponseEntity<>(files, HttpStatus.OK);
        } else if (dt.getMonthOfYear() == todayDate.getMonthOfYear()
                || dt.getMonthOfYear() == todayDate.minusMonths(1).getMonthOfYear()) {
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            throw new BusinessException("File is missing from the System");
        }
    }

    public static void sendMail(MonthlyPayrollDocumentType type, List<MonthlyPayrollDocument> Files, String payrollMonth) throws FileNotFoundException, IOException {

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        TextEmail mail = new TextEmail((type.equals(MonthlyPayrollDocumentType.MONTHLY_PAYROLL) ? "Housemaids payroll" : "PWO") + " file for: " + payrollMonth, "please find attached files");

        for (MonthlyPayrollDocument MonthlyPayrollDocumentfile : Files) {
            try (InputStream input = Storage.getStream(MonthlyPayrollDocumentfile.getAttachments().get(0))) {
                File file = Paths.get(System.getProperty("java.io.tmpdir"), MonthlyPayrollDocumentfile.getAttachments().get(0).getName()).toFile();

                try (FileOutputStream out = new FileOutputStream(file)) {
                    int read = 0;
                    byte[] bytes = new byte[1024];

                    while ((read = input.read(bytes)) != -1) {
                        out.write(bytes, 0, read);
                    }
                }

                mail.addAttachement(file);
            }
        }
        Setup.getMailService().sendEmail(recipients, mail, null);
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','generatePayrollv2')")
    @RequestMapping("/generatePayrollv2")
    public ResponseEntity<?> generatePayrollv2(HttpServletResponse response,
                                               @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                               @RequestParam(required = false, value = "old") boolean old,
                                               @RequestParam(required = true, value = "sentByEmail") boolean isSentByEmail,
                                               @RequestParam(required = false, value = "finalFile") boolean finalFile) throws FileNotFoundException, IOException, URISyntaxException, InterruptedException {

        if (!payrollThreadTriggered) {
            payrollThreadTriggered = true;
            Thread one;
            one = new Thread() {
                @Override
                public void run() {
                    try {
                        //Jirra ACC-1085
//                        boolean withJop = true;
//                        LocalDate dt;
//                        if (date != null) {
//                            dt = new LocalDate(date).withDayOfMonth(26);
//                        } else {
//                            dt = new LocalDate().withDayOfMonth(26);
//                        }
//                        List<MonthlyPayrollDocument> payrollfiles = monthlyPayrollDocumentRepository.
//                                findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.MONTHLY_PAYROLL);
//                        List<MonthlyPayrollDocument> pwofiles = monthlyPayrollDocumentRepository.
//                                findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.PPWO);
//                        if (payrollfiles.size() > 0 || pwofiles.size() > 0) {
//                            withJop = false;
//                        }
//                        createAndDownloadFiles(response, date, isSentByEmail, finalFile, old, withJop);

                        if (finalFile) {
                            housemaidPayrollGenerationService.executeFinalVersionAsync(date);
                        } else {
                            housemaidPayrollGenerationService.executeAsync(date);
                        }

                        payrollThreadTriggered = false;
                        thread.remove();
                    } catch (Exception e) {
                        payrollThreadTriggered = false;
                        logger.log(Level.SEVERE,
                                e.getMessage(),
                                e);
                        StringWriter errors = new StringWriter();
                        e.printStackTrace(new PrintWriter(errors));
                        String errorsString = errors.toString();
                        String emails = Setup.getParameter(Setup.getCurrentModule(),
                                PayrollManagementModule.PARAMETER_PAYROLL_EMAILS);
                        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
                        TextEmail mail = new TextEmail("generate Payroll Exception", errorsString);
                        Setup.getMailService().sendEmail(recipients, mail, null);
                    }
                }
            };
            one.start();
            if (isSentByEmail) {
                return new ResponseEntity<>("Your request was registered, the file will be sent by email shortly", HttpStatus.OK);
            }
            //The thread is used to give the API the needed time to generate the payroll file as CSV
            TimeUnit.SECONDS.sleep(10);
            return new ResponseEntity<>(HttpStatus.OK);
        } else
            throw new BusinessException("This feature is requested before and still processing.");
    }

    //Jirra ACC-1129
    @PreAuthorize("hasPermission('HousemaidPayroll','generatePayrollTest')")
    @RequestMapping("/generatePayrollTest")
    public ResponseEntity<?> generatePayrollTest(
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = false, value = "maidName") String maidName) {

        if (!payrollThreadTriggered) {
            payrollThreadTriggered = true;
            //Jirra ACC-1085
            boolean withJop = true;
            LocalDate dt;
            if (date != null) {
                dt = new LocalDate(date).withDayOfMonth(26);
            } else {
                dt = new LocalDate().withDayOfMonth(26);
            }
            List<MonthlyPayrollDocument> payrollfiles = monthlyPayrollDocumentRepository.
                    findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.MONTHLY_PAYROLL);
            List<MonthlyPayrollDocument> pwofiles = monthlyPayrollDocumentRepository.
                    findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.PPWO);
            if (payrollfiles.size() > 0 || pwofiles.size() > 0) {
                withJop = false;
            }
            if (date != null) {
                dt = new LocalDate(date);
            } else {
                dt = new LocalDate();
            }

            //Calculate start date and end date of payroll
            LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
            LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
            Double totalAnsari = 0.0;
            List<HousemaidPayrollBean> result = new ArrayList<>();
            Logger.getLogger(PayrollGenerationLibrary.class.getName())
                    .log(Level.SEVERE, "getTestPayrollHousemaids - result: " + result.size());
            totalAnsari = generateHousemaidsPayrollList(
                    totalAnsari, payrollStart, payrollEnd, maidName, result, false, false, withJop, true);
            Logger.getLogger(PayrollGenerationLibrary.class.getName())
                    .log(Level.SEVERE, "getTestPayrollHousemaids - result: " + result.size());
            payrollThreadTriggered = false;
            return new ResponseEntity<>(result, HttpStatus.OK);
        } else
            throw new BusinessException("This feature is requested before and still processing.");
    }

    //Jirra ACC-1129
    public void createAndDownloadFiles(
            HttpServletResponse response, Date date, boolean isSentByEmail,
            boolean finalFile, Boolean old, boolean withJop)
            throws IOException, FileNotFoundException, URISyntaxException, NoSuchAlgorithmException {

        if (old == null)
            old = false;

        //TODO add long running task
        LocalDate dt;
        //If payroll date isn't included in the request then the payroll file will be generated for the current month
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        //Calculate start date and end date of payroll
        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);

        //This variable is used to hold total salaries of the housemaids
        Double totalAnsari = 0.0;
        //Final List that holds payroll data
        List<HousemaidPayrollBean> result = new ArrayList<>();
        //Calculate everything needed for every housemaid
        Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: 1");
        totalAnsari = generateHousemaidsPayrollList(totalAnsari, payrollStart, payrollEnd, null, result, old, finalFile, withJop, false);
        Logger.getLogger(PayrollGenerationLibrary.class.getName()).log(Level.SEVERE, "generateFinalPayroll - housemaids: 2");

        if (isSentByEmail) {
            //If is sent by email is checked from the interface then the payroll file will be sent by email
            PayrollGenerationLibrary.generateHousemaidPayrollFile(response, "Maids.cc Housemaids Payroll of " + DateUtil.formatSimpleMonth(payrollEnd.toDate()) + (finalFile ? " FINAL" : " NOT_FINAL") + ".xlsx", result, totalAnsari, payrollEnd, finalFile);
        } else {
            //This will download payroll file as CSV
            createDownloadResponse(response, "Maids.cc Housemaids Payroll of " + DateUtil.formatSimpleMonth(payrollEnd.toDate()) + ".csv", PayrollGenerationLibrary.generateHousemaidPayrollCSVFromData(result));
        }
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','generatePayrollException')")
    @RequestMapping("/generatePayrollExceptions")
    public ResponseEntity<?> generatePayrollException(HttpServletResponse response,
                                                      @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        if (true) {
            throw new BusinessException("Feature only available by email");
        }
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
        Double totalAnsari = 0.0;
        List<HousemaidPayrollBean> result = new ArrayList<>();
        generateHousemaidsPayrollList(totalAnsari, payrollStart, payrollEnd, null, result, false, false, true, false);
        List<Housemaid> housemaids = PayrollGenerationLibrary.getPayrollHousemaids(payrollStart, payrollEnd);
        return new ResponseEntity<>(PayrollGenerationLibrary.getPayrollExceptions(housemaids, dt, null), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','sendPayrollExceptionToEmail')")
    @RequestMapping("/sendPayrollExceptionToEmail")
    public ResponseEntity<?> sendPayrollExceptionToEmail(HttpServletResponse response,
                                                         @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                         @RequestParam(required = false, value = "summary") Boolean summary) throws InterruptedException {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        if (summary == null) {
            summary = false;
        }
        final Boolean includeSummary = summary;

        Lock lock = new Lock();
        Thread one;
        one = new Thread() {
            @Override
            public void run() {

                LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
                LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
                Map<String, Map<String, Date>> exceptionsTimes = new HashMap<>();
                try {
                    lock.lock();
                    sendPayrollExceptionToEmail(payrollStart, payrollEnd, exceptionsTimes, dt, includeSummary);
                    lock.unlock();

                } catch (InterruptedException ex) {
                    Logger.getLogger(HousemaidPayrollController.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        };
        one.start();
        TimeUnit.SECONDS.sleep(2);
        return new ResponseEntity<>("Your request was registered, the final Exceptions List will be sent by email shortly", HttpStatus.OK);

    }

    public void sendPayrollExceptionToEmail(LocalDate payrollStart, LocalDate payrollEnd, Map<String, Map<String, Date>> exceptionsTimes, LocalDate dt, Boolean includeSummary) {
        Date processStart = new Date();
        Date getHousemaidsStart = new Date();
        List<Housemaid> housemaids = PayrollGenerationLibrary.getPayrollHousemaids(payrollStart, payrollEnd);
        Date getHousemaidsEnd = new Date();

        Date getExceptionsStart = new Date();
        List<PayrollException> Exceptions = PayrollGenerationLibrary.getPayrollExceptions(housemaids, dt, exceptionsTimes);
        Date getExceptionsEnd = new Date();

        Date buildingEmailStart = new Date();
        Exceptions.stream().sorted(Comparator.comparing(x -> x.getType().toString()));

        StringBuilder sb = new StringBuilder("");
        sb.append("<div>");
        //Mail Header
        sb.append("<h2>").append("Exceptions List").append("</h2>");

        sb.append("<h2>The result count is: ")
                .append(Exceptions.size()).append("</h2>");
        //Tablecontent
        sb.append("<table border=\"1\" style=\"border-collapse:collapse;text-align:center\">");
        sb.append("<tr>");

        sb.append("<th>");
        sb.append("Housemaid");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Montly Payment");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Basic Salary");
        sb.append("</th>");

        sb.append("<th>");
        sb.append("Type");
        sb.append("</th>");

        for (PayrollException exception : Exceptions) {
            sb.append("<tr>");

            sb.append("<td>");
            sb.append(exception.getHousemaid().getName());
            sb.append("</td>");

            sb.append("<td>");
            sb.append(exception.getMontlyPayment());
            sb.append("</td>");

            sb.append("<td>");
            sb.append(exception.getHousemaid().getBasicSalary());
            sb.append("</td>");

            sb.append("<td>");
            sb.append(exception.getType().toString());
            sb.append("</td>");

            sb.append("</tr>");
        }
        sb.append("</tr>");
        sb.append("</table>");
        sb.append("</div>");

        Date buildingEmailEnd = new Date();
        Date processEnd = new Date();

        if (includeSummary) {

            String summary = "";
            summary += getTimingDetails("Process", processStart, processEnd);
            summary += getTimingDetails("getHousemaids", getHousemaidsStart, getHousemaidsEnd);
            summary += getTimingDetails("getExceptions", getExceptionsStart, getExceptionsEnd);
            summary = exceptionsTimes.keySet().stream().map((title) -> getTimingDetails(title, exceptionsTimes.get(title).get("start"), exceptionsTimes.get(title).get("end"))).reduce(summary, String::concat);
            summary += getTimingDetails("buildingEmail", buildingEmailStart, buildingEmailEnd);

            sb.append("<div style='margin-top:20px;'>").append(summary).append("</div>");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("title", "Exceptions List Report");
        params.put("tableData", sb.toString());
        //for local test
        Context context = new Context();
        context.setVariables(params);
        String s = templateEngine.process("DataCorrectionandIntegrity", context);

        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("title", "Exceptions List Report");
        emailParams.put("tableData", sb.toString());
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_EXCEPTIONS_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

        if (recipients.size() > 0) {
            messagingService.send(recipients, null, "DataCorrectionandIntegrity", "Housemaids Exceptions List",
                    null, null, emailParams, null, null, null);
//            mailService.sendEmail(recipients,
//                    new TemplateEmail("Housemaids Exceptions List", "DataCorrectionandIntegrity", params), null);
        }
    }

    public static String getTimingDetails(String name, Date processStart, Date processEnd) {
        String summary = "";

        summary += name + " Start: " + (processStart == null ? "NULL" : DateUtil.formatFullDateWithTime(processStart));
        summary += "<br />";
        summary += name + " End: " + (processEnd == null ? "NULL" : DateUtil.formatFullDateWithTime(processEnd));
        summary += "<br /><b>";
        summary += name + " Time: " + (processEnd == null || processStart == null ? "NULL" : String.valueOf((processEnd.getTime() - processStart.getTime()) / 1000.0));
        summary += "</b><br />";
        summary += "<br />";

        return summary;
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','generateFinalPayroll')")
    @RequestMapping("/generateFinalPayroll")
    public ResponseEntity<?> generateFinalPayroll(HttpServletResponse response,
                                                  @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date
    ) throws IOException, FileNotFoundException, URISyntaxException, InterruptedException {

        if (!payrollThreadTriggered) {
            payrollThreadTriggered = true;
            LocalDate dt;
            if (date != null) {
                dt = new LocalDate(date).withDayOfMonth(26);
            } else {
                dt = new LocalDate().withDayOfMonth(26);
            }
            List<MonthlyPayrollDocument> payrollfiles = monthlyPayrollDocumentRepository.
                    findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.MONTHLY_PAYROLL);
            List<MonthlyPayrollDocument> pwofiles = monthlyPayrollDocumentRepository.
                    findByPayrollDateAndFinalFileAndTypeOrderByCreationDateDesc(new java.sql.Date(dt.toDate().getTime()), true, MonthlyPayrollDocumentType.PPWO);
            if (payrollfiles.size() > 0 || pwofiles.size() > 0) {
                throw new BusinessException("Final payroll files for this month were generated before, you can't generate them again");
            }
            //Lock lock = new Lock();
            Thread one = new Thread() {
                public void run() {

                    LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
                    try {
                        //lock.lock();
                        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                                .log(Level.SEVERE, "generateFinalPayroll - housemaids: 1");
                        createAndDownloadFiles(response, date, true, true, false, true);
                        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                                .log(Level.SEVERE, "generateFinalPayroll - housemaids: 2");
                        pwoController.createAndDownloadFiles(response, date, true, true, false);
                        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                                .log(Level.SEVERE, "generateFinalPayroll - housemaids: 3");
                        //lock.unlock();
                        DateTime todayDate = new DateTime();
                        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                                .log(Level.SEVERE, "generateFinalPayroll - housemaids: 4");
                        PayrollGenerationLibrary.sendFinalPayrollFilesViaEmail("Maids.cc Housemaids Payroll of " + DateUtil.formatSimpleMonth(payrollEnd.toDate()) + " FINAL.xlsx",
                                "Housemaid Ansari WPS Payroll Payment Work Order_M_" + DateUtil.formatFullDate(todayDate.toDate()) + " FINAL.xlsx", dt);
                        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                                .log(Level.SEVERE, "generateFinalPayroll - housemaids: 5");

                        //Jirra ACC-1135
                        // set maid.visa salary on hold for cancelled or scheduled for cancellation contracts
                        selfReference.setCancelledContractsMaidVisaSalaryOnHold();
                    } catch (Exception ex) {
                        Logger.getLogger(HousemaidPayrollController.class.getName()).log(Level.SEVERE, null, ex);
                        payrollThreadTriggered = false;
                    }


                    payrollThreadTriggered = false;
                }
            };
            one.start();
            TimeUnit.SECONDS.sleep(10);

            return new ResponseEntity<>("Your request was registered, the final files will be sent by email shortly", HttpStatus.OK);
        } else
            throw new BusinessException("This feature is requested before and still processing.");
    }

    public Double generateHousemaidsPayrollList(
            Double totalAnsari, LocalDate payrollStart, LocalDate payrollEnd, String maidName,
            List<HousemaidPayrollBean> result, boolean old, boolean finalFile,
            boolean withJop, boolean forTest) {

        if (withJop)
            selfReference.recoverSimulationData();
        List<Housemaid> housemaids;
        if (forTest)
            housemaids = PayrollGenerationLibrary.getTestPayrollHousemaids(payrollStart, payrollEnd, maidName);
        else
            housemaids = PayrollGenerationLibrary.getPayrollHousemaids(payrollStart, payrollEnd);

        Integer jobStartDate =
                Integer.parseInt(
                        Setup.getParameter(Setup.getCurrentModule(),
                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_START));
        Integer jobEndDate =
                Integer.parseInt(
                        Setup.getParameter(Setup.getCurrentModule(),
                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_END));
        Reflections reflections =
                new Reflections("com.magnamedia.salarycalculation.housemaid");
        Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
                reflections.getSubTypesOf(HousemaidSalaryTransaction.class);
        //Jirra ACC-645
        int counter = 0;
        //Jirra ACC-1085
        LocalDate todayDT = new LocalDate();
        if (withJop
                && ((todayDT.getDayOfMonth() >= jobStartDate && todayDT.getMonthOfYear() == payrollEnd.getMonthOfYear())
                || (todayDT.getDayOfMonth() <= jobEndDate && todayDT.getMonthOfYear() == payrollEnd.plusMonths(1).getMonthOfYear()))) {
            scheduledMonthlyService.housemaidRepayments(housemaids, payrollEnd.withDayOfMonth(1), finalFile);
            negativeSalariesService.setConstant();
        }

        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - housemaids: " + housemaids.size());
        int i = 0;
        for (Housemaid housemaid : housemaids) {

            HousemaidPayrollBean bean = null;
            if (old)
                bean = generateHousemaidPayroll(
                        payrollStart, payrollEnd, housemaid);
            else
                bean = generateHousemaidPayroll2(
                        payrollStart, payrollEnd, housemaid, allClasses);

            if (bean != null) {
                Logger.getLogger(PayrollGenerationLibrary.class.getName())
                        .log(Level.SEVERE, "getTestPayrollHousemaids - housemaids beans: " + i++);
                bean.setAdditionToBalanceDeductionLimit(0D);
                if (withJop
                        && ((todayDT.getDayOfMonth() >= jobStartDate && todayDT.getMonthOfYear() == payrollEnd.getMonthOfYear())
                        || (todayDT.getDayOfMonth() <= jobEndDate && todayDT.getMonthOfYear() == payrollEnd.plusMonths(1).getMonthOfYear()))
                        && !negativeSalariesService.negativeSalariesBean(bean, payrollStart, payrollEnd, finalFile))
                    bean = generateHousemaidPayroll2(
                            payrollStart, payrollEnd, housemaid, allClasses);
                bean.setIndexNum(String.valueOf(++counter));
                bean.setSn("H-" + (counter));
                result.add(bean);
                totalAnsari += bean.getTotalBalance();
            }
        }
        Logger.getLogger(PayrollGenerationLibrary.class.getName())
                .log(Level.SEVERE, "getTestPayrollHousemaids - result: " + result.size());
        return totalAnsari;
    }

    public HousemaidPayrollBean generateHousemaidPayroll(LocalDate payrollStart, LocalDate payrollEnd, Housemaid housemaid) {

        //PicklistItem vacationType = getItem(AccountingModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE, "pre-paid_vacation");
        PicklistItem noKidsDeductionReason =
                getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE, "prefer_to_have_a_family_with_no_kids");
        PicklistItem exceededNumberOfFailedInterviewsDeductionReason =
                getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE, "exceeded_number_of_failed_interviews");
        PicklistItem maidReplacedClientDeductionReason =
                getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE, "maid_replaced_client");
        PicklistItem failed7thInterviewOrMore =
                getItem(PayrollManagementModule.PICKLIST_WARNING_LETTER_TYPE, "failed_7th_interview_or_more");
        PicklistItem clientReplacedTheMaidDuetoComplaint =
                getItem(PayrollManagementModule.PICKLIST_WARNING_LETTER_TYPE, "client_replaced_the_maid_due_to_complaint");

        //Jirra ACC-278
        PicklistItem maidVisa = getItem(PayrollManagementModule.PICKLIST_PROSPECTTYPE, "maidvisa.ae_prospect");

        //Jirra ACC-608
//            List<HousemaidSalarySuspend> housemaidSalarySuspends =
//                    housemaidSalarySuspendRepository.findByHousemaidAndPayrollDate(housemaid, new java.sql.Date(payrollEnd.toDate().getTime()));
//            if (!housemaidSalarySuspends.isEmpty()){
//                if (housemaidSalarySuspends.get(0).isRefreshSalary()){
//                    housemaid = housemaidRep.findOne(housemaid.getId());
//                    housemaid.setBasicSalary(0D);
//                    housemaidRep.save(housemaid);
//                }
//                continue;
//            }
        //Filter maids not on vacation this month
        List<ScheduledAnnualVacation> annualVacations = housemaid.getScheduledAnnualVacations();
//        boolean flag = false;
//        if (annualVacations != null && annualVacations.size() > 0) {
//
//            for (ScheduledAnnualVacation vacation : annualVacations) {
//                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                if (!payrollDueDate.isBefore(payrollStart.minusMonths(1))
//                        && payrollDueDate.isBefore(payrollEnd.minusMonths(1).plusDays(1))
//                        && vacation.getType() != null && vacation.getType().getCode().equals(vacationType.getCode())
//                        && vacation.getAmount() != null && vacation.getAmount() > 0.0) {
//                    flag = true;
//                    break;
//                }
//            }
//        }
//        if (!flag) {
        Date checkout = null;
        HousemaidAccommodation accommodation = accRep.findOneByHousemaid(housemaid);
        if (accommodation != null && accommodation.getCheckOutDate() != null) {
            checkout = accommodation.getCheckOutDate();
        }
        HousemaidPayrollBean bean = new HousemaidPayrollBean();
        bean.setHousemaidName(housemaid.getName());

        if (housemaid.getNationality() != null)
            bean.setNationality(housemaid.getNationality().getName());
        else
            bean.setNationality("");
        if (housemaid.getStatus().equals(HousemaidStatus.WITH_CLIENT)) {
            Client c = housemaid.getCurrentClient();
            bean.setClientName(c != null ? c.getName() : "");
        }

        bean.setStatus(housemaid.getStatus().toString());

        bean.setSource(housemaid.isIsAgency() ? "Agency" : ((housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid()) ? "Freedom Operator" : "Exit"));
        bean.setFreedomOperatorName(
                (housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid() && housemaid.getFreedomOperator() != null) ? housemaid.getFreedomOperator().getName() : "");

        NewRequest visaNewRequest = housemaid.getVisaNewRequest();
        if (visaNewRequest != null) {
            if (visaNewRequest.getEmployeeUniqueId() != null) {
                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
            }
            if (visaNewRequest.getAgentId() != null) {
                bean.setAgentId(visaNewRequest.getAgentId());
            }
            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
            }
        }

        //Contract
        List<Contract> contractsList = housemaid.getContracts();
        if (contractsList != null && contractsList.size() > 0) {
            List<Contract> contracts = contractsList.stream().filter(x -> ContractStatus.ACTIVE.equals(x.getStatus())).collect(Collectors.toList());
            if (contracts.size() > 0) {
                bean.setContractName("Contr-" + contracts.get(0).getId().toString());
                if (contracts.get(0).getContractProspectType() != null &&
                        contracts.get(0).getContractProspectType().getId().equals(maidVisa.getId())) {
                    bean.setMaidVisaAEContract("Yes");
//                            logger.log(Level.INFO, null, "Housemaid ID: (" + housemaid.getId() + ")");
//                            logger.log(Level.INFO, null, "Contract Prospect Type: (ID" + contracts.get(0).getContractProspectType().getId() + ", name" + contracts.get(0).getContractProspectType().getName() +")");
                } else {
                    bean.setMaidVisaAEContract("No");
                    //logger.log(Level.INFO, null, "Housemaid ID: (" + housemaid.getId() + ")");
//                            if (contracts.get(0).getContractProspectType() != null)
//                                logger.log(Level.INFO, null, "Contract Prospect Type: (ID" + contracts.get(0).getContractProspectType().getId() + ", name" + contracts.get(0).getContractProspectType().getName() +")");
//
                }
            }
        }
        //Jirra ACC-278
        if (bean.getMaidVisaAEContract() == null || bean.getMaidVisaAEContract().isEmpty()) {
            bean.setMaidVisaAEContract("No");
        }

        //Start Date
        if (housemaid.getStartDate() != null) {
            bean.setStartingDate(new java.sql.Date(housemaid.getNewStartDate().getTime()));
        }

        //Arrival Date
        if (housemaid.getLandedInDubaiDate() != null) {
            bean.setArrivalDate(new java.sql.Date(housemaid.getLandedInDubaiDate().getTime()));
        }

        //Orginal Loan - Remaining Loan balance - Remaining Cash Advance
        Double loanBalance = housemaid.getLoanBalance();
        Double forgivenessBalance = housemaid.getForgiveness();
        Double repaymentBalance = housemaid.getRepayments();
//        if (checkout != null) {

        Double orginalLoan = (loanBalance != null ? loanBalance : 0)
                + (forgivenessBalance != null ? forgivenessBalance : 0)
                + (repaymentBalance != null ? repaymentBalance : 0);
        bean.setOrginalLoan(orginalLoan.toString());
        bean.setRemainingLoanBalance(loanBalance != null ? loanBalance.toString() : "N/A");
//            bean.setRemainingCashAdvance("N/A");
//        } else {
//            bean.setOrginalLoan("N/A");
////            bean.//                            bean.setRemainingLoanBalance("N/A");("N/A");
////            bean.setRemainingCashAdvance(loanBalance != null ? loanBalance.toString() : "N/A");
//            bean.setRemainingLoanBalance(loanBalance != null ? loanBalance.toString() : "N/A");
//        }

        //This month forgiveness
        List<HousemaidForgiveness> forgivenessList = housemaid.getForgivenesses();
        if (forgivenessList != null && forgivenessList.size() > 0) {

            Double sum = 0.0;
            for (HousemaidForgiveness forgiveness : forgivenessList) {
                LocalDate forgivenessDate = new LocalDate(forgiveness.getForgivenessDate());
                if (!forgivenessDate.isBefore(payrollStart)
                        && forgivenessDate.isBefore(payrollEnd.plusDays(1))
                        && forgiveness.getAmount() > 0) {
                    sum += forgiveness.getAmount();
                }
            }

            if (sum != 0) {
                bean.setThisMonthForgiveness(sum.toString());
            } else {
                bean.setThisMonthForgiveness("0");
            }
        } else {
            bean.setThisMonthForgiveness("0");
        }

        //Loan Repayment
        bean.setLoanRepayment(0.0);
        List<Repayment> repaymentList = housemaid.getRepaymentsList();
        if (housemaid.getStartDate() != null) {
            LocalDate startDate = new LocalDate(housemaid.getStartDate());
            //jirra ACC-382
            if (startDate.isBefore(payrollEnd.withDayOfMonth(1))) {
                if (repaymentList.size() > 0) {
                    Date d1 = payrollStart.toDate();
                    Date d2 = payrollEnd.plusDays(1).toDate();
                    List<Repayment> tempRepaymentList = repaymentList.stream()
                            .filter(x -> x.getAmount() > 0.0
                                    && x.getRepaymentDate().before(d2)
                                    && !x.getRepaymentDate().before(d1)
                                    && !x.getExculdedFromPayroll() && x.getPaidRepayment()
                            ).collect(Collectors.toList());
                    if (tempRepaymentList.size() > 0) {
                        bean.setLoanRepayment(tempRepaymentList.stream().mapToDouble(z -> z.getAmount()).sum());
                    }
                }
            }
        }

        //Start date deduction
        bean.setStartDateDeduction(0.0);
        if (housemaid.getStartDate() != null) {
            LocalDate startDate = new LocalDate(housemaid.getStartDate());
            if (startDate.isAfter(payrollEnd.withDayOfMonth(1)) && housemaid.getBasicSalary() != null) {
                Double deduction = (housemaid.getBasicSalary() / 30.4)
                        * (Math.abs((Days.daysBetween(startDate, payrollEnd.withDayOfMonth(1))).getDays()));
                bean.setStartDateDeduction(deduction);
            }
        }

        //Warning Letters
        List<WarningLetter> warningLettersList = housemaid.getWarningLetters();
        Double failedInterviewDeductionSum = 0.0;
        Double replacementDeductionSum = 0.0;
        if (warningLettersList != null && warningLettersList.size() > 0) {
            Double sum = 0.0;
            for (WarningLetter temp : warningLettersList) {
                LocalDate letterDate = new LocalDate(temp.getDate());

                if (!letterDate.isBefore(payrollStart)
                        && letterDate.isBefore(payrollEnd.plusDays(1))) {
                    if (temp.getViolationType() != null
                            && temp.getViolationType().getId().equals(failed7thInterviewOrMore.getId()))
                        failedInterviewDeductionSum += temp.getDeduction();
                    else if (temp.getViolationType() != null
                            && temp.getViolationType().getId().equals(clientReplacedTheMaidDuetoComplaint.getId()))
                        replacementDeductionSum += temp.getDeduction();
                    else
                        sum += temp.getDeduction();
                }
            }
            bean.setComplaintDeduction(sum);
        } else {
            bean.setComplaintDeduction(0.0);
        }

        //Manager Deduction && Addittions
        List<PayrollManagerNote> managerNotesList = housemaid.getManagerNotes();
        if (managerNotesList.size() > 0) {
            Double addtionSum = 0.0;
            Double deductionSum = 0.0;
            Double noKidsDeductionSum = 0.0;
            for (PayrollManagerNote temp : managerNotesList) {
                LocalDate noteDate = new LocalDate(temp.getNoteDate());
                if (!noteDate.isBefore(payrollStart)
                        && noteDate.isBefore(payrollEnd.plusDays(1))
                        && temp.getNoteType() == PayrollManagerNote.ManagerNoteType.ADDITION) {
                    addtionSum += temp.getAmount();
                }
                if (!noteDate.isBefore(payrollStart)
                        && noteDate.isBefore(payrollEnd.plusDays(1))
                        && temp.getNoteType() == PayrollManagerNote.ManagerNoteType.DEDUCTION) {
                    Double amount = 0D;
                    amount = temp.getAmount();
                    if (temp.getDeductionReason() != null && temp.getDeductionReason().getId().equals(noKidsDeductionReason.getId())) {
                        noKidsDeductionSum += amount;
                    } else if (temp.getDeductionReason() != null && temp.getDeductionReason().getId().equals(exceededNumberOfFailedInterviewsDeductionReason.getId())) {
                        failedInterviewDeductionSum += amount;
                        //Jirra ACC-289
                    } else if (temp.getDeductionReason() != null && temp.getDeductionReason().getId().equals(maidReplacedClientDeductionReason.getId())) {
                        replacementDeductionSum += amount;
                    } else {
                        deductionSum += amount;
                    }
                }

            }
            bean.setManagerAddition(addtionSum);
            bean.setManagerDeduction(deductionSum);
            bean.setNoKidsDeduction(noKidsDeductionSum.toString());
        } else {
            bean.setManagerAddition(0.0);
            bean.setManagerDeduction(0.0);
            bean.setNoKidsDeduction("0.0");
        }

        //Jirra ACC-289
        bean.setFailedInterviewDeduction(failedInterviewDeductionSum.toString());
        bean.setReplacementDeduction(replacementDeductionSum.toString());

        //Basic Salary
        bean.setBasicSalary(housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0);

        //Working Vacation Pay && Vacation airfare
        Double airfareSum = 0.0;
        for (ScheduledAnnualVacation vacation : annualVacations) {
            LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
            if ((payrollDueDate.equals(payrollStart) || payrollDueDate.isAfter(payrollStart))
                    && (payrollDueDate.equals(payrollEnd) || payrollDueDate.isBefore(payrollEnd))) {
                if (vacation.getType().getCode().toLowerCase().contains("airfare")) {
                    airfareSum += vacation.getAmount();
                }
            }
        }
        bean.setVacationAirFare(airfareSum);

        //Living place && Food allowance
        if (housemaid.getLiving() == HousemaidLiveplace.OUT) {
            bean.setLiving(HousemaidLiveplace.OUT);
            bean.setFoodAllowance(250.0);
        } else {
            bean.setLiving(HousemaidLiveplace.IN);
            bean.setFoodAllowance(0.0);
        }

        //Company Accommodate
        if (housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() > 0) {
            bean.setCompanyAccommodated("No");
        } else {
            bean.setCompanyAccommodated("Yes");
        }

        //Housing
        if (housemaid.getHousingAllowance() != null) {
            bean.setHousingAllowance(housemaid.getHousingAllowance().toString());
        } else {
            bean.setHousingAllowance("0.0");
        }

        //Jirra ACC-322
        //for PWO
        bean.setIncomeVariableComponent("0.0");
        bean.setPayStartDate(new java.sql.Date(payrollEnd.withDayOfMonth(1).toDate().getTime()));
        bean.setPayEndDate(new java.sql.Date(payrollEnd.dayOfMonth().withMaximumValue().toDate().getTime()));
        bean.setDaysInPeriod(Math.abs(DateUtil.getDaysBetween(bean.getPayEndDate(), bean.getPayStartDate())) + 1);
        bean.setRecordType("EDR");
        //

        bean.setHousemaid(housemaid);
        bean.calculateTotatIcome();
        bean.calculateTotalDeduction();
        return bean;
//        }
//        return null;
    }

    //Jirra ACC-1042
    public HousemaidPayrollBean generateHousemaidPayroll2(
            LocalDate payrollStart,
            LocalDate payrollEnd,
            Housemaid housemaid) {

        Reflections reflections =
                new Reflections("com.magnamedia.salarycalculation.housemaid");
        Set<Class<? extends HousemaidSalaryTransaction>> allClasses =
                reflections.getSubTypesOf(HousemaidSalaryTransaction.class);
        return generateHousemaidPayroll2(payrollStart, payrollEnd, housemaid, allClasses);
    }

    //Jirra ACC-322 ACC-677
    @Transactional
    public HousemaidPayrollBean generateHousemaidPayroll2(
            LocalDate payrollStart,
            LocalDate payrollEnd,
            Housemaid housemaid,
            Set<Class<? extends HousemaidSalaryTransaction>> allClasses) {

//        PicklistItem vacationType = getItem(AccountingModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE, "pre-paid_vacation");
//
//        //Filter maids not on vacation this month
//        List<ScheduledAnnualVacation> annualVacations = housemaid.getScheduledAnnualVacations();
//        boolean flag = false;
//        if (annualVacations != null && annualVacations.size() > 0) {
//
//            for (ScheduledAnnualVacation vacation : annualVacations) {
//                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                if (!payrollDueDate.isBefore(payrollStart.minusMonths(1))
//                        && payrollDueDate.isBefore(payrollEnd.minusMonths(1).plusDays(1))
//                        && vacation.getType() != null && vacation.getType().getCode().equals(vacationType.getCode())
//                        && vacation.getAmount() != null && vacation.getAmount() > 0.0) {
//                    flag = true;
//                    break;
//                }
//            }
//        }
//        if (!flag) {
        HousemaidPayrollBean bean = new HousemaidPayrollBean();
        for (Class<?> clazz : allClasses) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                Housemaid.class,
                                LocalDate.class,
                                LocalDate.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                HousemaidPayrollBean.class,
                                Double.class);
                HousemaidSalaryTransaction t = (HousemaidSalaryTransaction) clazz.newInstance();
                bean = (HousemaidPayrollBean) setInSalaryObjectMethod.invoke(t, bean, (Double) calculateMethod.invoke(t, housemaid, payrollStart, payrollEnd));
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(HousemaidPayrollController.class.getName()).log(Level.SEVERE, null, ex);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(HousemaidPayrollBean.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

        bean = setHousemaidInfo(bean, housemaid, payrollEnd);
        return bean;
//        }
//        return null;
    }


    /**
     * Set Housemaid information:
     * name, nationality, status, client name, source, Freedom Operator name,
     * employee unique id, agent id, employee account with agent, MaidVisa AE Contract,
     * start date, arrival date, living, company accommodated, Income variable component,
     * pay start date, pay end date, days in period, record type
     * Return HousemaidPayrollBean
     */
    private HousemaidPayrollBean setHousemaidInfo(
            HousemaidPayrollBean bean,
            Housemaid housemaid,
            LocalDate payrollEnd) {

        bean.setHousemaid(housemaid);
        bean.setHousemaidName(housemaid.getName());

        if (housemaid.getNationality() != null)
            bean.setNationality(housemaid.getNationality().getName());
        else
            bean.setNationality("");
        if (housemaid.getStatus().equals(HousemaidStatus.WITH_CLIENT)) {
            Client c = housemaid.getCurrentClient();
            bean.setClientName(c != null ? c.getName() : "");
        }

        bean.setStatus(housemaid.getStatus().toString());

        bean.setSource(housemaid.isIsAgency() ? "Agency" : ((housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid()) ? "Freedom Operator" : "Exit"));
        bean.setFreedomOperatorName(
                (housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid() && housemaid.getFreedomOperator() != null) ? housemaid.getFreedomOperator().getName() : "");

        NewRequest visaNewRequest = housemaid.getVisaNewRequest();
        if (visaNewRequest != null) {
            if (visaNewRequest.getEmployeeUniqueId() != null) {
                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
            }
            if (visaNewRequest.getAgentId() != null) {
                bean.setAgentId(visaNewRequest.getAgentId());
            }
            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
            }
        }
        //Jirra 1384
        RenewRequest renewRequest = renewVisaRequestRepository.findFirstOneByHousemaidAndCompletedOrderByCreationDateDesc(housemaid, true);
        if (renewRequest != null && renewRequest.getLastMoveDate() != null) {
            bean.setRenewalDate(new java.sql.Date(renewRequest.getLastMoveDate().getTime()));
        }

        //Contract
        PicklistItem maidVisa = getItem(PayrollManagementModule.PICKLIST_PROSPECTTYPE, "maidvisa.ae_prospect");
        List<Contract> contractsList = housemaid.getContracts();
        if (contractsList != null && contractsList.size() > 0) {
            List<Contract> contracts = contractsList.stream().filter(x -> ContractStatus.ACTIVE.equals(x.getStatus())).collect(Collectors.toList());
            if (contracts.size() > 0) {
                bean.setContractName("Contr-" + contracts.get(0).getId().toString());
                if (contracts.get(0).getContractProspectType() != null &&
                        contracts.get(0).getContractProspectType().getId().equals(maidVisa.getId())) {
                    bean.setMaidVisaAEContract("Yes");
                } else {
                    bean.setMaidVisaAEContract("No");
                }
            }
        }
        //Jirra ACC-278
        if (bean.getMaidVisaAEContract() == null || bean.getMaidVisaAEContract().isEmpty()) {
            bean.setMaidVisaAEContract("No");
        }

        //Start Date
        if (housemaid.getStartDate() != null) {
            bean.setStartingDate(new java.sql.Date(housemaid.getNewStartDate().getTime()));
        }

        //Arrival Date
        if (housemaid.getLandedInDubaiDate() != null) {
            bean.setArrivalDate(new java.sql.Date(housemaid.getLandedInDubaiDate().getTime()));
        }

        //Living place && Food allowance
        if (housemaid.getLiving() == HousemaidLiveplace.OUT) {
            bean.setLiving(HousemaidLiveplace.OUT);
        } else {
            bean.setLiving(HousemaidLiveplace.IN);
        }

        //Company Accommodate
        if (housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() > 0) {
            bean.setCompanyAccommodated("No");
        } else {
            bean.setCompanyAccommodated("Yes");
        }

        //Jirra ACC-322
        //for PWO
        bean.setIncomeVariableComponent("0.0");
        bean.setPayStartDate(new java.sql.Date(payrollEnd.withDayOfMonth(1).toDate().getTime()));
        bean.setPayEndDate(new java.sql.Date(payrollEnd.dayOfMonth().withMaximumValue().toDate().getTime()));
        bean.setDaysInPeriod(Math.abs(DateUtil.getDaysBetween(bean.getPayEndDate(), bean.getPayStartDate())) + 1);
        bean.setRecordType("EDR");
        return bean;
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','generatePayroll')")
    @RequestMapping("/generatePayroll")
    public ResponseEntity<?> generatePayroll(HttpServletResponse response,
                                             @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                             @RequestParam(required = true, value = "sentByEmail") boolean isSentByEmail,
                                             @RequestParam(required = false, value = "finalFile") boolean finalFile) throws FileNotFoundException, IOException, URISyntaxException, InterruptedException {

        Thread one;
        one = new Thread() {
            public void run() {
                //TODO add long running task
                LocalDate dt;
                if (date != null) {
                    dt = new LocalDate(date);
                } else {
                    dt = new LocalDate();
                }
                LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
                LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);

                Double totalAnsari = 0.0;

                List<Housemaid> housemaids = PayrollGenerationLibrary.getPayrollHousemaids(payrollStart, payrollEnd);
                List<HousemaidPayrollBean> result = new ArrayList<>();
                int counter = 0;
//                PicklistItem vacationType =
//                        getItem(AccountingModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE, "pre-paid_vacation");
                PicklistItem noKidsDeductionReason =
                        getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE, "prefer_to_have_a_family_with_no_kids");
                PicklistItem exceededNumberOfFailedInterviewsDeductionReason =
                        getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE, "exceeded_number_of_failed_interviews");
                PicklistItem failed7thInterviewOrMore =
                        getItem(PayrollManagementModule.PICKLIST_WARNING_LETTER_TYPE, "failed_7th_interview_or_more");
                PicklistItem clientReplacedTheMaidDuetoComplaint =
                        getItem(PayrollManagementModule.PICKLIST_WARNING_LETTER_TYPE, "client_replaced_the_maid_due_to_complaint");
                //Jirra ACC-278
                PicklistItem maidVisa = getItem(PayrollManagementModule.PICKLIST_PROSPECTTYPE, "maidvisa.ae_prospect");

                try {
                    for (Housemaid housemaid : housemaids) {
                        //Filter maids not on vacation this month
                        List<ScheduledAnnualVacation> annualVacations = housemaid.getScheduledAnnualVacations();
//                        boolean flag = false;
//
//                        if (annualVacations != null && annualVacations.size() > 0) {
//
//                            for (ScheduledAnnualVacation vacation : annualVacations) {
//                                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                                if (!payrollDueDate.isBefore(payrollStart.minusMonths(1))
//                                        && payrollDueDate.isBefore(payrollEnd.minusMonths(1).plusDays(1))
//                                        && vacation.getType() != null && vacation.getType().getCode().equals(vacationType.getCode())
//                                        && vacation.getAmount() != null && vacation.getAmount() > 0.0) {
//                                    flag = true;
//                                    break;
//                                }
//                            }
//                        }
//                        if (!flag){
                        Date checkout = null;
                        HousemaidAccommodation accommodation = accRep.findOneByHousemaid(housemaid);
                        if (accommodation != null && accommodation.getCheckOutDate() != null) {
                            checkout = accommodation.getCheckOutDate();
                        }
                        HousemaidPayrollBean bean = new HousemaidPayrollBean();
                        bean.setIndexNum(String.valueOf(++counter));
                        bean.setHousemaidName(housemaid.getName());

                        NewRequest visaNewRequest = housemaid.getVisaNewRequest();
                        if (visaNewRequest != null) {
                            if (visaNewRequest.getEmployeeUniqueId() != null) {
                                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
                            }
                            if (visaNewRequest.getAgentId() != null) {
                                bean.setAgentId(visaNewRequest.getAgentId());
                            }
                            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
                            }
                        }

                        //Contract
                        List<Contract> contractsList = housemaid.getContracts();
                        if (contractsList != null && contractsList.size() > 0) {
                            List<Contract> contracts = contractsList.stream().filter(x -> ContractStatus.ACTIVE.equals(x.getStatus())).collect(Collectors.toList());
                            if (contracts.size() > 0) {
                                bean.setContractName("Contr-" + contracts.get(0).getId().toString());
                                bean.setMaidVisaAEContract(
                                        contracts.get(0).getContractProspectType() != null &&
                                                contracts.get(0).getContractProspectType().getId().equals(maidVisa.getId()) ?
                                                "Yes" : "No");
                            }
                        }
                        //Jirra ACC-278
                        if (bean.getMaidVisaAEContract() == null || bean.getMaidVisaAEContract().isEmpty()) {
                            bean.setMaidVisaAEContract("No");
                        }

                        //Start Date
                        if (housemaid.getStartDate() != null) {
                            bean.setStartingDate(new java.sql.Date(housemaid.getNewStartDate().getTime()));
                        }

                        //Orginal Loan - Remaining Loan balance - Remaining Cash Advance
                        Double loanBalance = housemaid.getLoanBalance();
                        Double forgivenessBalance = housemaid.getForgiveness();
                        Double repaymentBalance = housemaid.getRepayments();
                        //if (checkout != null) {

                        Double orginalLoan = (loanBalance != null ? loanBalance : 0)
                                + (forgivenessBalance != null ? forgivenessBalance : 0)
                                + (repaymentBalance != null ? repaymentBalance : 0);
                        bean.setOrginalLoan(orginalLoan.toString());
                        bean.setRemainingLoanBalance(loanBalance != null ? loanBalance.toString() : "0");

//                            bean.setRemainingCashAdvance("N/A");
//                        } else {
//                            bean.setOrginalLoan("N/A");
//                            bean.setRemainingLoanBalance("N/A");
//                            bean.setRemainingCashAdvance(loanBalance.toString());
//                        }

                        //This month forgiveness
                        bean.setThisMonthForgiveness("0");
                        List<HousemaidForgiveness> forgivenessList = housemaid.getForgivenesses();
                        if (forgivenessList != null && forgivenessList.size() > 0) {

                            Double sum = 0.0;
                            for (HousemaidForgiveness forgiveness : forgivenessList) {
                                LocalDate forgivenessDate = new LocalDate(forgiveness.getForgivenessDate());
                                if (!forgivenessDate.isBefore(payrollStart)
                                        && forgivenessDate.isBefore(payrollEnd.plusDays(1))
                                        && forgiveness.getAmount() > 0) {
                                    sum += forgiveness.getAmount();
                                }
                            }
                            bean.setThisMonthForgiveness(sum.toString());
                        }

                        //Loan Repayment
                        bean.setLoanRepayment(0.0);
                        List<Repayment> repaymentList = housemaid.getRepaymentsList();
                        if (housemaid.getStartDate() != null) {
                            LocalDate startDate = new LocalDate(housemaid.getStartDate());
                            if (startDate.isBefore(payrollStart)) {
                                if (repaymentList.size() > 0) {
                                    Date d1 = payrollStart.toDate();
                                    Date d2 = payrollEnd.plusDays(1).toDate();
                                    List<Repayment> tempRepaymentList = repaymentList.stream()
                                            .filter(x -> x.getAmount() > 0.0
                                                    && x.getRepaymentDate().before(d2)
                                                    && !x.getRepaymentDate().before(d1)
                                                    && !x.getExculdedFromPayroll() && x.getPaidRepayment()
                                            ).collect(Collectors.toList());
                                    if (tempRepaymentList.size() > 0) {
                                        bean.setLoanRepayment(tempRepaymentList.stream().mapToDouble(z -> z.getAmount()).sum());
                                    }
                                }
                            }
                        }

                        //Start date deduction
                        bean.setStartDateDeduction(0.0);
                        if (housemaid.getStartDate() != null) {
                            LocalDate startDate = new LocalDate(housemaid.getStartDate());
                            if (startDate.isAfter(payrollEnd.withDayOfMonth(1)) && housemaid.getBasicSalary() != null) {
                                Double deduction = (housemaid.getBasicSalary() / 30.4)
                                        * (Math.abs((Days.daysBetween(startDate, payrollEnd.withDayOfMonth(1))).getDays()));
                                bean.setStartDateDeduction(deduction);
                            }
                        }

                        //Warning Letters
                        List<WarningLetter> warningLettersList = housemaid.getWarningLetters();
                        Double failedInterviewDeductionSum = 0.0;
                        if (warningLettersList != null && warningLettersList.size() > 0) {
                            Double sum = 0.0;
                            Double replacementDeductionSum = 0.0;
                            for (WarningLetter temp : warningLettersList) {
                                LocalDate letterDate = new LocalDate(temp.getDate());
                                if (!letterDate.isBefore(payrollStart)
                                        && letterDate.isBefore(payrollEnd.plusDays(1))) {
                                    if (temp.getViolationType() != null
                                            && temp.getViolationType().getId().equals(failed7thInterviewOrMore.getId()))
                                        failedInterviewDeductionSum += temp.getDeduction();
                                    else if (temp.getViolationType() != null
                                            && temp.getViolationType().getId().equals(clientReplacedTheMaidDuetoComplaint.getId()))
                                        replacementDeductionSum += temp.getDeduction();
                                    else
                                        sum += temp.getDeduction();
                                }
                            }
                            bean.setReplacementDeduction(replacementDeductionSum.toString());
                            bean.setComplaintDeduction(sum);
                        } else {
                            bean.setReplacementDeduction("0");
                            bean.setComplaintDeduction(0.0);
                        }

                        //Manager Deduction && Addittions
                        List<PayrollManagerNote> managerNotesList = housemaid.getManagerNotes();
                        if (managerNotesList.size() > 0) {
                            Double addtionSum = 0.0;
                            Double deductionSum = 0.0;
                            Double noKidsDeductionSum = 0.0;
                            for (PayrollManagerNote temp : managerNotesList) {
                                LocalDate noteDate = new LocalDate(temp.getNoteDate());
                                if (!noteDate.isBefore(payrollStart)
                                        && noteDate.isBefore(payrollEnd.plusDays(1))
                                        && temp.getNoteType() == PayrollManagerNote.ManagerNoteType.ADDITION) {
                                    addtionSum += temp.getAmount();
                                }
                                if (!noteDate.isBefore(payrollStart)
                                        && noteDate.isBefore(payrollEnd.plusDays(1))
                                        && temp.getNoteType() == PayrollManagerNote.ManagerNoteType.DEDUCTION) {
                                    Double amount = 0D;
//                                        if(temp.getPostponedAmount()!=null && temp.getPostponedAmount() != 0)
//                                            amount = temp.getAmount() - temp.getPostponedAmount();
//                                        else
                                    amount = temp.getAmount();
                                    if (temp.getDeductionReason() != null && temp.getDeductionReason().getId().equals(noKidsDeductionReason.getId())) {
                                        noKidsDeductionSum += amount;
                                    } else if (temp.getDeductionReason() != null && temp.getDeductionReason().getId().equals(exceededNumberOfFailedInterviewsDeductionReason.getId())) {
                                        failedInterviewDeductionSum += amount;
                                    } else {
                                        deductionSum += amount;
                                    }
                                }
                            }
                            bean.setManagerAddition(addtionSum);
                            bean.setManagerDeduction(deductionSum);
                            bean.setNoKidsDeduction(noKidsDeductionSum.toString());
                            bean.setFailedInterviewDeduction(failedInterviewDeductionSum.toString());
                        } else {
                            bean.setManagerAddition(0.0);
                            bean.setManagerDeduction(0.0);
                            bean.setNoKidsDeduction("0");
                            bean.setFailedInterviewDeduction(failedInterviewDeductionSum.toString());
                        }

                        //Basic Salary
                        bean.setBasicSalary(housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0);

                        //Working Vacation Pay && Vacation airfare
                        Double airfareSum = 0.0;
                        if (annualVacations != null)
                            for (ScheduledAnnualVacation vacation : annualVacations) {
                                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
                                if (!payrollDueDate.isBefore(payrollStart)
                                        && (payrollDueDate.isBefore(payrollEnd.plusDays(1)))) {
                                    if (vacation.getType().getCode().toLowerCase().contains("airfare")) {
                                        airfareSum += vacation.getAmount();
                                    }
                                }
                            }

                        bean.setVacationAirFare(airfareSum);
                        //Living place && Food allowance
                        if (housemaid.getLiving() == HousemaidLiveplace.OUT) {
                            bean.setLiving(HousemaidLiveplace.OUT);
                            bean.setFoodAllowance(250.0);
                        } else {
                            bean.setLiving(HousemaidLiveplace.IN);
                            bean.setFoodAllowance(0.0);
                        }

                        //Company Accommodate
                        if (housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() > 0) {
                            bean.setCompanyAccommodated("No");
                        } else {
                            bean.setCompanyAccommodated("Yes");
                        }

                        //Housing
                        if (housemaid.getHousingAllowance() != null) {
                            bean.setHousingAllowance(housemaid.getHousingAllowance().toString());
                        } else {
                            bean.setHousingAllowance("0.0");
                        }

                        bean.setHousemaid(housemaid);
                        result.add(bean);

                        totalAnsari += bean.getTotalBalance();

//                        }
                    }

                    if (isSentByEmail) {
                        PayrollGenerationLibrary.generateHousemaidPayrollFile(response, "Maids.cc Housemaids Payroll of " + DateUtil.formatSimpleMonth(payrollEnd.toDate()) + ".xlsx", result, totalAnsari, payrollEnd, finalFile);
                    } else {
                        createDownloadResponse(response, "Maids.cc Housemaids Payroll of " + DateUtil.formatSimpleMonth(payrollEnd.toDate()) + ".csv", PayrollGenerationLibrary.generateHousemaidPayrollCSVFromData(result));
                    }
                } catch (IOException | URISyntaxException | NoSuchAlgorithmException e) {
                    logger.log(Level.SEVERE,
                            e.getMessage(),
                            e);
                }
            }
        };
        one.start();

        if (isSentByEmail) {
            return new ResponseEntity<>("Your request was registered, the file will be sent by email shortly", HttpStatus.OK);
        }
        TimeUnit.SECONDS.sleep(10);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    public class Lock {

        private boolean isLocked = false;

        public synchronized void lock()
                throws InterruptedException {
            while (isLocked) {
                wait();
            }
            isLocked = true;
        }

        public synchronized void unlock() {
            isLocked = false;
            notify();
        }
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','getmaidswithnoeid')")
    @RequestMapping("/getmaidswithnoeid")
    public ResponseEntity<?> getMaidsWithNoEid(
            //HttpServletResponse response,
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = true, value = "sentByEmail") boolean isSentByEmail) throws IOException {

        //Jirra ACC-341
        if (!isSentByEmail) {
            return new ResponseEntity<>("This feature is not supported right now, you can only send file by mail.", HttpStatus.BAD_REQUEST);
        } else {
            Thread one;
            one = new Thread() {
                @Override
                public void run() {
                    try {
                        getMaidsWithNoEid(date);
                    } catch (IOException ex) {
                        Logger.getLogger(HousemaidPayrollController.class.getName()).log(Level.SEVERE, null, ex);
                        StringWriter errors = new StringWriter();
                        ex.printStackTrace(new PrintWriter(errors));
                        String errorsString = errors.toString();
                        String emails = "<EMAIL>";
                        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
                        TextEmail mail = new TextEmail("Maids With No Eid Exception", errorsString);
                        Setup.getMailService().sendEmail(recipients, mail, null);
                    }
                }
            };
            one.start();
            return new ResponseEntity<>("Your request was registered, the file will be sent by email shortly.", HttpStatus.OK);
        }

    }

    //The following API is for testing only
    @PreAuthorize("hasPermission('HousemaidPayroll','gethousemaidmonthlypayroll')")
    @RequestMapping("/gethousemaidmonthlypayroll/{id}")
    public ResponseEntity<?> getHousemaidMonthlyPayroll(
            @PathVariable Long id,
            @RequestParam(required = false, value = "date")
            @DateTimeFormat(pattern = "yyyy-MM-dd") Date date)
            throws IOException, FileNotFoundException, NoSuchAlgorithmException, Exception {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
        Housemaid housemaid = housemaidRep.findOne(id);
        if (housemaid == null) {
            throw new BusinessException("The requested Housemaid isn't found!");
        }
        List<MonthlyPayroll> payroll = monthlyPayrollRepository.findByHousemaidAndPayrollDate(housemaid, new java.sql.Date(payrollEnd.toDate().getTime()));
        if (payroll.isEmpty()) {
            throw new BusinessException("The requested Housemaid isn't inculded in the payroll or it hasn't been generated yet!");
        }
        return new ResponseEntity(payroll.get(0), HttpStatus.OK);

    }

    private void getMaidsWithNoEid(Date date) throws IOException {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollStart = dt.getDayOfMonth() < 27 ? dt.minusMonths(1)
                .withDayOfMonth(27) : dt.withDayOfMonth(27);
        LocalDate payrollEnd = dt.getDayOfMonth() >= 27 ? dt.plusMonths(1)
                .withDayOfMonth(26) : dt.withDayOfMonth(26);

        List<Housemaid> housemaids = PayrollGenerationLibrary.getPayrollHousemaids(payrollStart, payrollEnd);
        List<Housemaid> neededHousemaids = new ArrayList<>();
        List<String> docTags = Arrays.asList(
                "EMIRATES_ID_FRONT_SIDE", "EMIRATES_ID_BACK_SIDE",
                "EMIRATE_ID", "eid_front_side", "eid_front", "eid_front",
                "eid_back_side", "eid_back");
        List<String> newRequestTags = Arrays.asList(
                "EMIRATES_ID_FRONT_SIDE", "EMIRATES_ID_BACK_SIDE",
                "EMIRATE_ID", "eid_front_side", "eid_front", "eid_back_side",
                "eid_back");
        //Jirra ACC-641
        List<String> newRequestTaskNames = Arrays.asList(
                "Prepare EID application, Prepare medical application, Prepare Tasheel contract",
                "Prepare folder containing E-visa medical application and EID",
                "Apply for Ansari",
                "Waiting for reply of Ansari",
                "Waiting for the maid to go to medical test and EID fingerprinting",
                //"Upload passport and Evisa",
                "Upload tasheel contract to ERP, Receival of EID Card",
                "Upload tasheel contract to ERP",
                "Pending medical certificate approval from DHA",
                "Repeat Medical",
                "Upload Contract to Tasheel",
                "Prepare insurance application",
                "Pending Insurance Policy Issuance",
                "Pending Insurance Policy Issuance",
                "Apply for R-visa",
                "Get Form from GDRFA",
                "Insert Labour Card Expiry Date",
                "Prepare Folder for Zajel",
                "Prepare Folder for Zajel",
                "Prepare Folder for Zajel",
                "With Zajel for Visa Stamping",
                "With Zajel for Visa Stamping",
                "Receival of EID Card",
                "Receival of EID Card",
                "Send EID copy for Insurance Activation",
                "Put EID Passport and Insurance in 1 Package",
                "Pending Aramex to pick up EIDs and Insurance cards",
                "Put EID Passport and Insurance in 1 Package");
        //"Confirm the employee has the EID"

        for (Housemaid h : housemaids) {
            List<HousemaidDocument> docs = h.getDocuments().stream()
                    .filter(x -> x.getAttachments().stream().anyMatch(a -> !a.getTag().isEmpty() && docTags.contains(a.getTag())))
                    .collect(Collectors.toList());
            if (docs.isEmpty()) {
                if (h.getVisaNewRequest() != null && (h.getVisaNewRequest().getAttachments().isEmpty()
                        || h.getVisaNewRequest().getAttachments().stream().allMatch(a -> !newRequestTags.contains(a.getTag())))
                        && h.getVisaNewRequest().getTaskName() != null) {
                    for (String s : h.getVisaNewRequest().getTaskName().split(","))
                        if (newRequestTaskNames.contains(s)) {
                            neededHousemaids.add(h);
                            break;
                        }
                }
            }
        }

        HousemaidNoEidBean.counter = 0;
        List<HousemaidNoEidBean> beans = neededHousemaids.stream()
                .map(x -> new HousemaidNoEidBean(x, payrollStart, payrollEnd))
                .collect(Collectors.toList());

        String title = "Employees with no EID (Housemaids) Report - " + DateUtil.formatFullDate(new Date());
        String[] headers =
                new String[]{"Personal Number", "Employee Name", "Total Salary",
                        "Documents In Possession", "Branch Location"};

        String[] columns =
                new String[]{"employeeUniqueId", "housemaidName", "totalBalance",
                        "documentsInPossession", "branchLocation"};

        File file = CsvHelper.generateCsv(beans, HousemaidNoEidProjection.class, headers, columns, title);
        InputStream inputStreamFile = Files.newInputStream(file.toPath());
        String fileName = FilenameUtils.removeExtension(file.getName());
        Attachment attachment = Storage.storeTemporary(fileName, inputStreamFile, fileName, Boolean.FALSE);
        List<Attachment> attachments = Collections.singletonList(attachment);

//        if (isSentByEmail){
        //Jirra ACC-323
//          response = null;
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS);
        List<EmailRecipient> recipients = Recipient.parseEmailsString(emails);
        Map<String, String> params = new HashMap<>();
        params.put("title", title);
//        TemplateEmail templateEmail = new TemplateEmail(title, "Payroll_Housemaids_With_No_EID_Report", params);
//        templateEmail.addAttachement(file);
        messagingService.send(recipients, null, "Payroll_Housemaids_With_No_EID_Report",
                title, params, attachments, null);
//        mailService.sendEmail(recipients, templateEmail, null);
//          return new ResponseEntity<>("File has been sent by Email.", HttpStatus.OK);
//        }else{
//            InputStream is = new FileInputStream(file);
//            createDownloadResponse(response, "report.csv", is);
//            return new ResponseEntity<>("", HttpStatus.OK);
//        }
    }

//    private <T, Y> File generateCsv(List<T> data,
//            Class<Y> projectionClass, String[] namesOrdered,
//            String[] columns, String fileName) throws IOException {
//
//        File file = Paths.get(System.getProperty("java.io.tmpdir"),
//                fileName+".csv")
//                .toFile();
//        String[] headers = namesOrdered;
//        FileOutputStream os = new FileOutputStream(file);
//        os.write(0xef);
//        os.write(0xbb);
//        os.write(0xbf);
//
//        try (ICsvBeanWriter beanWriter = new CsvBeanWriter(
//                new OutputStreamWriter(os, "UTF-8"),
//                CsvPreference.EXCEL_PREFERENCE)) {
//            beanWriter.writeHeader(headers);
//            for (Y c : project(data, projectionClass)) {
//                beanWriter.write(c, columns);
//            }
//        }
//        return file;
//    }

    //Jirra ACC-874
    @PreAuthorize("hasPermission('HousemaidPayroll','generateHousemaidChecklist')")
    @RequestMapping(value = "/generateHousemaidChecklist",
            method = RequestMethod.GET)
    public ResponseEntity<?> generateHousemaidChecklist(
            @RequestParam(required = false, value = "date")
            @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {

        Thread one;
        one = new Thread() {
            @Override
            public void run() {
                if (!payrollCheckListTriggered) {
                    payrollCheckListTriggered = true;
                    createAndSendHousemaidChecklistFiles(date);
                    payrollCheckListTriggered = false;
                }
            }
        };
        one.start();

        return new ResponseEntity<>("Your request was registered, the file will be sent by email shortly", HttpStatus.OK);
    }

    public void createAndSendHousemaidChecklistFiles(Date date) {
        try {
            LocalDate dt;
            if (date != null) {
                dt = new LocalDate(date);
            } else {
                dt = new LocalDate();
            }

            //Calculate start date and end date of payroll
            LocalDate startDate = PayrollGenerationLibrary.getPayrollStartDate(dt);
            LocalDate endDate = PayrollGenerationLibrary.getPayrollEndDate(dt).plusDays(1);

            LocalDate startPaymentDate = dt.withDayOfMonth(1);
            LocalDate endPaymentDate = dt.dayOfMonth().withMaximumValue();
            LocalDate nextStartPaymentDate = dt.plusMonths(1).withDayOfMonth(1);
            LocalDate nextEndPaymentDate = dt.plusMonths(1).dayOfMonth().withMaximumValue();

            URL resource = PayrollGenerationLibrary.class.getResource("/Housemaids Payroll Checklist.xlsx");
            File file = new File(resource.toURI());
            XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(file));
            //Picklist Items
            PicklistItem failedInterviewsPicklistItem =
                    PicklistHelper.getItem("MAID_MANAGER_REASON", "failed_interviews");
            PicklistItem monthlyPaymentPicklistItem =
                    PicklistHelper.getItem("TypeOfPayment", "Monthly Payment");
            PicklistItem maidVisaProspectType =
                    PicklistHelper.getItem("ProspectType", "maidvisa.ae_prospect");

            //SHEETs
            workbook = HousemaidChecklist.getReport1(workbook, endDate);
            workbook = HousemaidChecklist.getReport2_a(workbook, endDate);
            workbook = HousemaidChecklist.getReport2_b(workbook, endDate);
            workbook = HousemaidChecklist.getReport2_c(workbook, endDate);
            workbook = HousemaidChecklist.getReport2_d(workbook, endDate);
            workbook = HousemaidChecklist.getReport2_e(workbook, endDate);
            workbook = HousemaidChecklist.getReport2_f(workbook, endDate);
            workbook = HousemaidChecklist.getReport3(workbook, startDate, endDate);
            workbook = HousemaidChecklist.getReport4(workbook, startPaymentDate, endPaymentDate);
            workbook = HousemaidChecklist.getReport5(workbook, startDate, endDate);
            if (maidVisaProspectType != null)
                workbook =
                        HousemaidChecklist.getReport6(
                                workbook, startPaymentDate, endPaymentDate,
                                nextStartPaymentDate, nextEndPaymentDate,
                                maidVisaProspectType.getId());
            workbook = HousemaidChecklist.getReport7(workbook);
            workbook = HousemaidChecklist.getReport8(workbook, endDate);
            workbook = HousemaidChecklist.getReport9(workbook, startDate, endDate);
            workbook = HousemaidChecklist.getReport10(workbook, startDate, endDate);
            if (failedInterviewsPicklistItem != null)
                workbook =
                        HousemaidChecklist.getReport11(
                                workbook, failedInterviewsPicklistItem.getId(), startDate, endDate);
            workbook = HousemaidChecklist.getReport12(workbook);
            if (monthlyPaymentPicklistItem != null)
                workbook =
                        HousemaidChecklist.getReport13(
                                workbook, monthlyPaymentPicklistItem.getId(), startPaymentDate, endPaymentDate, endDate);
            workbook = HousemaidChecklist.getReport14(workbook, endDate);
            workbook = HousemaidChecklist.getReport15(workbook, startDate, endDate);

            HousemaidChecklist.sendMail(
                    "Housemaids payroll Checklist file for "
                            + DateUtil.formatMonth(endDate.toDate()),
                    workbook);
            payrollCheckListTriggered = false;

        } catch (Exception ex) {
            payrollCheckListTriggered = false;
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
            StringWriter errors = new StringWriter();
            ex.printStackTrace(new PrintWriter(errors));
            String errorsString = errors.toString();
            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS);
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
            TextEmail mail = new TextEmail("Generate Checklist Exception", errorsString);
            Setup.getMailService().sendEmail(recipients, mail, null);
            payrollCheckListTriggered = false;
        }
    }

    //Jirra ACC-1085
    @Transactional
    public void recoverSimulationData() {

        List<PayrollManagerNote> notes = payrollManagerNoteRepository.findByNotFinal(true);
        for (PayrollManagerNote note : notes) {
//            if (note.getPostponedAmount() == null)
            payrollManagerNoteRepository.delete(note);
//            else{
//                note.setAmount(note.getAmount() + note.getPostponedAmount());
//                note.setPostponedAmount(null);
//                note.setPostponedDate(null);
//                note.setNotFinal(false);
//                payrollManagerNoteRepository.save(note);
//            }
        }

        List<Repayment> repayments = repaymentRepository.findByNotFinal(true);
        for (Repayment repayment : repayments) {
//            if (repayment.getPostponedAmount() == null)
            repaymentRepository.delete(repayment);
//            else{
//                repayment.setAmount(repayment.getAmount() + repayment.getPostponedAmount());
//                repayment.setPostponedAmount(null);
//                repayment.setPostponedDate(null);
//                repayment.setNotFinal(false);
//                repaymentRepository.save(repayment);
//            }
        }
//
//        List<WarningLetter> letters = warningLetterRepository.findByNotFinal(true);
//        for (WarningLetter warningLetter : letters){
//            if (warningLetter.getPostponedAmount() == null)
//                warningLetterRepository.delete(warningLetter);
//            else{
//                warningLetter.setDeduction(warningLetter.getDeduction() + warningLetter.getPostponedAmount());
//                warningLetter.setPostponedAmount(null);
//                warningLetter.setPostponedDate(null);
//                warningLetter.setNotFinal(false);
//                warningLetterRepository.save(warningLetter);
//            }
//        }

        //Jirra ACC-1093
        List<EmployeeLoan> loans = employeeLoanRepository.findByNotFinal(true);
        for (EmployeeLoan loan : loans) {
            employeeLoanRepository.delete(loan);
        }
    }

    @Transactional
    public void setCancelledContractsMaidVisaSalaryOnHold() {
        Calendar dueDate = Calendar.getInstance();
        dueDate.set(Calendar.SECOND, 0);
        dueDate.set(Calendar.MINUTE, 0);
        dueDate.set(Calendar.HOUR_OF_DAY, 0);
        dueDate.add(Calendar.MONTH, -1);
        dueDate.set(Calendar.DATE, dueDate.getActualMaximum(Calendar.DAY_OF_MONTH) - Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.DDS_CANCELLATION_SCHEDULED_JOB_START_DAY)));

        HistorySelectQuery<Contract> historyQuery = new HistorySelectQuery(Contract.class);
        SelectFilter selectFilter = new SelectFilter(
                new SelectFilter(
                        // scheduled for termination
                        new SelectFilter("scheduledDateOfTermination", "has_changed", "")
                                .and("isScheduledForTermination", "has_changed", "")
                                .and("scheduledDateOfTermination", "IS NOT NULL", null)
                                .and("isScheduledForTermination", "=", true))

                        // cancelled or expired
                        .or(new SelectFilter("status", "has_changed", "").
                                and("status", "IN", Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED))))

                // limit the modification date
                .and("lastModificationDate", ">=", new DateTime().minusMonths(1).withDayOfMonth(1).toDate())
                .and("lastModificationDate", "<", dueDate.getTime());

        historyQuery.filterBy(selectFilter);

        List<Contract> contracts = historyQuery.execute();

        String prefix = "MMM ";

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        if (contracts != null) {
            logger.log(Level.SEVERE, prefix + " contract list size" + contracts.size());
            for (Contract contract : contracts) {
                contract = contractRepository.findOne(contract.getId());

                // maid visa
                if (!contract.getContractProspectType().getCode().equals(PicklistItem.getCode(PayrollManagementModule.MAID_VISA_PEOSPECT_TYPE))) {
                    continue;
                }

                logger.log(Level.SEVERE, prefix + " contract id" + contract.getId());
                if (contract.getHousemaid() != null) {
                    Housemaid housemaid = contract.getHousemaid();
                    logger.log(Level.SEVERE, prefix + " housemaid id" + housemaid.getId());
                    logger.log(Level.SEVERE, prefix + " housemaid excluded" + housemaid.getExcludedFromPayroll());
                    housemaid.setExcludedFromPayroll(Boolean.TRUE);
                    housemaidRep.save(housemaid);
                }
            }
        }
    }

    //Jirra ACC-1349
    public ThreadLocal<Map<String, BaseEntity>> getThread() {
        return thread;
    }

    public void setThread(ThreadLocal<Map<String, BaseEntity>> thread) {
        this.thread = thread;
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','getHistoryLog')")
    @RequestMapping(value = "/{housemaidId}/getHistoryLog", method = RequestMethod.GET)
    public ResponseEntity<?> getPayrollLog(@PathVariable Long housemaidId,
                                           @RequestParam(required = false, defaultValue = "false") boolean lastSixMonths,
                                           @RequestParam(required = false, defaultValue = "0") Integer monthsCount){

        Housemaid housemaid=this.housemaidRep.findOne(housemaidId);

        if(housemaid == null) throw new BusinessException("Housemaid does not exist");

        java.sql.Date beforeMonthsCountDate = monthsCount > 0 ? new java.sql.Date(new LocalDate().minusMonths(monthsCount).toDate().getTime()) :
                (lastSixMonths ? new java.sql.Date(new LocalDate().minusMonths(6).toDate().getTime()) : null);
        beforeMonthsCountDate = beforeMonthsCountDate == null ? null : new java.sql.Date(new LocalDate(beforeMonthsCountDate).withDayOfMonth(1).toDate().getTime());

        return new ResponseEntity<>(this.project(this.housemaidPayrollLogRepository.getHousemaidPayrollLogBy_Id(housemaidId, beforeMonthsCountDate), HousemaidPayrollLogProjection.class),
                HttpStatus.OK);
    }

    @Override
    public ResponseEntity<?> get(@PathVariable("id") Long id){
        ResponseEntity<?> response = super.get(id);
        if (response.getBody() != null && response.getBody() instanceof Housemaid){
            Housemaid housemaid = (Housemaid) response.getBody();
            housemaid.setSwitchedMaid(housemaid.calculateSwitchedMaid());
            return new ResponseEntity<>(housemaid, HttpStatus.OK);
        }
        return this.unauthorizedReponse();
    }

    //PAY-801
    //API(1)
    @PreAuthorize("hasPermission('HousemaidPayroll','getPendingPayrollMonths')")
    @RequestMapping(value = "/{housemaid}/getPendingPayrollMonths/{contract}", method = RequestMethod.GET)
    public ResponseEntity<?> getPendingPayrollMonths(@PathVariable Housemaid housemaid,
                                                     @PathVariable Contract contract,
                                                     @RequestParam(required = false, defaultValue = "6") int monthsBefore){
        if(housemaid == null) throw new RuntimeException("Housemaid does not exist");
        if(contract == null) throw new RuntimeException("contract does not exist");

        java.sql.Date beforeSixMonthsDate = new java.sql.Date(new LocalDate().withDayOfMonth(1).minusMonths(monthsBefore).toDate().getTime());
        List<HousemaidPayrollLog> logs = housemaidPayrollLogRepository.getHousemaidPendingPayrollMonths(housemaid.getId(), beforeSixMonthsDate);

        List<HousemaidPayrollLog2class> result = new ArrayList<>();
        for (HousemaidPayrollLog log : logs){

            Map<String,Object> paymentResult = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getReceivedPaymentForHousemaidForMonth(log.getMonthlyPaymentRule(), housemaid, contract);

            Map<String, Object> payrollLogMap = new HashMap<>();
            payrollLogMap.put("id", log.getId());
            payrollLogMap.put("payrollMonth", DateUtil.formatYearMonthDashed(log.getPayrollMonth()));
            payrollLogMap.put("totalSalary", log.getTotalSalary());

            Map<String, Object> paymentMap = new HashMap<>();
            String note = "";
            if (paymentResult != null) {
                Payment payment = (Payment) paymentResult.get("payment");
                note = (String) paymentResult.get("note");
                paymentMap.put("id", payment.getId());
                paymentMap.put("amountOfPayment", payment.getAmountOfPayment());
            }
            result.add(new HousemaidPayrollLog2class(payrollLogMap, log.getHousemaid().getId(), log.getTotalSalary(), note, paymentMap));

        }
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    //PAY-801
    //API(2)
    @PreAuthorize("hasPermission('HousemaidPayroll','markPayrollAsPaidAndRefunded')")
    @RequestMapping(value = "/markPayrollAsPaidAndRefunded/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getPaidSalaryPayroll(@PathVariable(value = "id") HousemaidPayrollLog housemaidPayrollLog){
        if(housemaidPayrollLog == null)
            throw new RuntimeException("housemaidPayrollLog does not exist");

        housemaidPayrollLog.setTransferred(true);
        housemaidPayrollLog.setPaidOnDate(DateUtil.formatFullDate(new Date()));
        housemaidPayrollLog.setWillBeIncluded(true);
        housemaidPayrollLog.setPaidOnStatus("Refunded to the client due to outside country absconding case");

        Setup.getRepository(HousemaidPayrollLogRepository.class).save(housemaidPayrollLog);
        return ResponseEntity.ok("Done");
    }
}
