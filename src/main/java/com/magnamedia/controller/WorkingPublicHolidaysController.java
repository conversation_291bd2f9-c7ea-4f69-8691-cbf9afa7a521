package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SmsResponse;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PaidOffDays;
import com.magnamedia.entity.WorkingPublicHoliday;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.WorkingHolidayType;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.PaidOffDaysRepository;
import com.magnamedia.repository.PublicHolidayRepository;
import com.magnamedia.repository.WorkingPublicHolidayRepository;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.message.MessagingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Jan 28, 2020
 *         Jirra ACC-1227
 */

@RequestMapping("/workingpublicholidays")
@RestController
public class WorkingPublicHolidaysController extends BaseRepositoryController<WorkingPublicHoliday> {

    @Autowired
    private WorkingPublicHolidayRepository workingPublicHolidayRepository;
    @Autowired
    private OfficeStaffRepository officeStaffRepository;
    @Autowired
    private PaidOffDaysRepository paidOffDaysRepository;

    @Autowired
    private MessagingService messagingService;
    @Autowired
    private PaidOffDaysController paidOffDaysController;

    @Override
    protected ResponseEntity createEntity(WorkingPublicHoliday workingPublicHoliday) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH,1);
        calendar.set(Calendar.MONTH,0);
        calendar.set(Calendar.HOUR,-12);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);

        if(workingPublicHoliday.getPublicHoliday() != null) {
            workingPublicHoliday.setPublicHoliday(Setup.getRepository(PublicHolidayRepository.class)
            .findOne(workingPublicHoliday.getPublicHoliday().getId()));
        }
        //check if WEEKLY_DAY_OFF and in past year
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF) && workingPublicHoliday.getStartDate().compareTo(calendar.getTime()) < 0)
            throw new BusinessException("You can't add working weekly day off not in this year!");

        OfficeStaff officeStaff = officeStaffRepository.findOne(workingPublicHoliday.getOfficeStaff().getId());

        //check if WEEKLY_DAY_OFF and before employee's starting date
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF) && workingPublicHoliday.getStartDate().compareTo(officeStaff.getStartingDate()) < 0)
            throw new BusinessException("You can't add working weekly day off before employee's starting day!");

        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WORKING_PUBLIC_HOLIDAY) && workingPublicHoliday.getStartDate().compareTo  (officeStaff.getStartingDate()) < 0)
            throw new BusinessException("You can't add a working public holiday before employee's starting day!");

        //check if WEEKLY_DAY_OFF and don't match employee weekly off day
        LocalDate start = workingPublicHoliday.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF) &&
                !officeStaff.getWeeklyOffDayList().contains(start.getDayOfWeek()))
            throw new BusinessException("the selected day is not weekly off day for this employee");

        //check if there is an intersection
        List<WorkingPublicHoliday> intersectHolidays = workingPublicHolidayRepository.findByOfficeStaffAndStartDateBetweenOrEndDateBetween(officeStaff,workingPublicHoliday.getStartDate(), workingPublicHoliday.getEndDate());
        if(!intersectHolidays.isEmpty())
            throw new BusinessException("there is already working public holiday that intersects this one!");

        Long daysLimit = Long.parseLong(Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT));

        calendar.setTime(workingPublicHoliday.getStartDate());
        Integer currentYear = calendar.get(Calendar.YEAR);

        Long currentYearAvailableDaysCount = daysLimit - officeStaff.getWorkingPublicHolidaysBalance(currentYear,false);

        if (workingPublicHoliday.getNumOfDays(currentYear) > currentYearAvailableDaysCount)
            throw new BusinessException("Exceeding the limit of working public holidays for Year " + currentYear + " which is: " + daysLimit);


        Integer nextYear = currentYear + 1;
        Long nextYearAvailableDaysCount = daysLimit - officeStaff.getWorkingPublicHolidaysBalance(nextYear, false);

        if (workingPublicHoliday.getNumOfDays(nextYear) > nextYearAvailableDaysCount)
            throw new BusinessException("Exceeding the limit of working public holidays for Year " + nextYear + " which is: " + daysLimit);

        ResponseEntity responseEntity = super.createEntity(workingPublicHoliday);
        workingPublicHoliday = (WorkingPublicHoliday) responseEntity.getBody();

        //if it is selected to be paid then insert a paid off day also
        PaidOffDays newPaidOffDays = null;
        if(workingPublicHoliday.getIsPaid()){
            Double numDays = 1.5 * (DateUtil.getDaysBetween(workingPublicHoliday.getStartDate(), workingPublicHoliday.getEndDate()) + 1);
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() + numDays);

            newPaidOffDays = new PaidOffDays();
            newPaidOffDays.setNumOfDays(numDays);
            newPaidOffDays.setOfficeStaff(officeStaff);
            newPaidOffDays.setWorkingPublicHoliday(workingPublicHoliday);
            newPaidOffDays.setPayrollMonth(new org.joda.time.LocalDate(workingPublicHoliday.getEndDate()).withDayOfMonth(1).toDate());
            newPaidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Done);
            newPaidOffDays.setActionTaken(true);
            officeStaffRepository.save(officeStaff);
            paidOffDaysRepository.save(newPaidOffDays);
        }
        officeStaff = officeStaffRepository.findOne(officeStaff.getId());

        //Sending messages
        String messageTemplateCode;
        Map<String, String> paramValues = new HashMap<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String type = "";
        if (workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF)) {
            messageTemplateCode = "Payroll_Adding_Working_On_Weekly_Off_Day";
            type = "Working on weekly off days is added";
            paramValues.put("date_of_working_weekly_off_day", formatter.format(workingPublicHoliday.getStartDate()));

            // just in case we are paying WEEKLY DAY OFF then we need these parameters
            paramValues.put("holiday_name", "WEEKLY DAY OFF");
            paramValues.put("starting_date", formatter.format(workingPublicHoliday.getStartDate()));
            paramValues.put("end_date", formatter.format(workingPublicHoliday.getEndDate()));
        } else{
            messageTemplateCode = "Payroll_Adding_Working_On_Public_Holiday";
            type = "Working on public holiday is added";
            paramValues.put("holiday_name", workingPublicHoliday.getPublicHoliday().getHolidayTitle());
            paramValues.put("starting_date", formatter.format(workingPublicHoliday.getStartDate()));
            paramValues.put("end_date", formatter.format(workingPublicHoliday.getEndDate()));
        }

        paramValues.put("employee_name", officeStaff.getFirstLastName());
        if(!workingPublicHoliday.getIsPaid()) {
            paramValues.put("new_off_days_balance", NumberFormatter.formatNumber(officeStaff.getOffDaysBalance()));
            paramValues.put("number_of_earned_off_days", NumberFormatter.formatNumber(1.5 * workingPublicHoliday.getNumOfDays(currentYear)));
        }else{
            messageTemplateCode = "Payroll_Adding_Paid_Working_On_Public_Holiday";
            paramValues.put("paid_holiday_amount",officeStaff.getSalaryCurrency() + " " + NumberFormatter.formatNumber(newPaidOffDays.getAmount()));
        }

        messagingService.sendSmsToOfficeStaffManagers(officeStaff, messageTemplateCode, type, paramValues);


        return responseEntity;
    }

    @Override
    protected ResponseEntity updateEntity(WorkingPublicHoliday workingPublicHoliday) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH,1);
        calendar.set(Calendar.MONTH,0);
        calendar.set(Calendar.HOUR,-12);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);

        if(workingPublicHoliday.getPublicHoliday() != null) {
            workingPublicHoliday.setPublicHoliday(Setup.getRepository(PublicHolidayRepository.class)
                    .findOne(workingPublicHoliday.getPublicHoliday().getId()));
        }

        //check if WEEKLY_DAY_OFF and in past year
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF) && workingPublicHoliday.getStartDate().compareTo(calendar.getTime()) < 0)
            throw new BusinessException("You can't add working weekly day off not in this year!");

        OfficeStaff officeStaff = officeStaffRepository.findOne(workingPublicHoliday.getOfficeStaff().getId());

        //check if WEEKLY_DAY_OFF and before employee's starting Ddate
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF) && workingPublicHoliday.getStartDate().compareTo(officeStaff.getStartingDate()) < 0)
            throw new BusinessException("You can't add working weekly day off before employee's starting day!");

        //check if WEEKLY_DAY_OFF and don't match employee weekly off day
        LocalDate start = workingPublicHoliday.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WEEKLY_DAY_OFF) &&
                !officeStaff.getWeeklyOffDayList().contains(start.getDayOfWeek()))
            throw new BusinessException("the selected day is not weekly off day for this employee");

        //check WORKING_PUBLIC_HOLIDAY
        if(workingPublicHoliday.getWorkingHolidayType().equals(WorkingHolidayType.WORKING_PUBLIC_HOLIDAY)) {
            if(!(workingPublicHoliday.getStartDate().getTime() >= workingPublicHoliday.getPublicHoliday().getStartDate().getTime()
            && workingPublicHoliday.getEndDate().getTime() <= workingPublicHoliday.getPublicHoliday().getEndDate().getTime()))
            throw new BusinessException("You are only allowed to enter dates between public holiday period!");
        }
        //check if there is an intersection
        List<WorkingPublicHoliday> intersectHolidays = workingPublicHolidayRepository.findByOfficeStaffAndStartDateBetweenOrEndDateBetweenNotSame(officeStaff,workingPublicHoliday.getStartDate(), workingPublicHoliday.getEndDate(), workingPublicHoliday.getId());
        if(!intersectHolidays.isEmpty())
            throw new BusinessException("there is already working public holiday that intersects this one!");


        Long daysLimit = Long.parseLong(Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT));

        calendar.setTime(workingPublicHoliday.getStartDate());
        Integer currentYear = calendar.get(Calendar.YEAR);

        Long currentYearAvailableDaysCount = daysLimit - officeStaff.getWorkingPublicHolidaysBalance(currentYear, false)
                + workingPublicHoliday.getNumOfDays(currentYear);

        if (workingPublicHoliday.getNumOfDays(currentYear) > currentYearAvailableDaysCount)
            throw new BusinessException("Exceeding the limit of working public holidays for Year " + currentYear + " which is: " + daysLimit);

        Integer nextYear = currentYear + 1;
        Long nextYearAvailableDaysCount = daysLimit - officeStaff.getWorkingPublicHolidaysBalance(nextYear, false)
                + workingPublicHoliday.getNumOfDays(nextYear);

        if (workingPublicHoliday.getNumOfDays(nextYear) > nextYearAvailableDaysCount)
            throw new BusinessException("Exceeding the limit of working public holidays for Year " + nextYear + " which is: " + daysLimit);


        //check if it is selected to be paid
        PaidOffDays paidOffDays = paidOffDaysRepository.findTopByWorkingPublicHoliday(workingPublicHoliday);
        if(paidOffDays != null) {
            paidOffDaysController.deleteEntity(paidOffDays);
        }

        // insert a new PaidOffDays record
        if(workingPublicHoliday.getIsPaid()){
            Double numDays = 1.5 * (DateUtil.getDaysBetween(workingPublicHoliday.getStartDate(), workingPublicHoliday.getEndDate()) + 1);
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() + numDays);

            PaidOffDays newPaidOffDays = new PaidOffDays();
            newPaidOffDays.setNumOfDays(numDays);
            newPaidOffDays.setOfficeStaff(officeStaff);
            newPaidOffDays.setWorkingPublicHoliday(workingPublicHoliday);
            newPaidOffDays.setPayrollMonth(new org.joda.time.LocalDate(workingPublicHoliday.getEndDate()).withDayOfMonth(1).toDate());
            newPaidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Done);
            newPaidOffDays.setActionTaken(true);
            officeStaffRepository.save(officeStaff);
            paidOffDaysRepository.save(newPaidOffDays);
        }
        return super.updateEntity(workingPublicHoliday);
    }

    @Override
    public ResponseEntity deleteEntity(WorkingPublicHoliday workingPublicHoliday) {
        //check if it is selected to be paid
        PaidOffDays paidOffDays = paidOffDaysRepository.findTopByWorkingPublicHoliday(workingPublicHoliday);
        if(paidOffDays != null) {
            paidOffDaysController.deleteEntity(paidOffDays);
        }
        return super.deleteEntity(workingPublicHoliday);
    }

    @PreAuthorize("hasPermission('workingpublicholidays','availableDaysCount')")
    @RequestMapping(value = "/availableDaysCount/{officeStaffId}")
    public ResponseEntity getAvailableDaysCount(@PathVariable("officeStaffId") Long officeStaffId, @RequestParam(value = "year", required = false) Integer year) {
        OfficeStaff officeStaff = officeStaffRepository.findOne(officeStaffId);

        Integer currentYear = LocalDate.now().getYear();

        Long numberOfDays = Long.parseLong(Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT)) - officeStaff.getWorkingPublicHolidaysBalance(year == null ? currentYear : year, false);

        return new ResponseEntity(numberOfDays > 0 ? numberOfDays : 0, HttpStatus.OK);
    }

    @Override
    public BaseRepository<WorkingPublicHoliday> getRepository() {
        return workingPublicHolidayRepository;
    }
}
