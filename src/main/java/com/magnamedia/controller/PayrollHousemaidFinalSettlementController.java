package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.HousemaidSalaryGroup;
import com.magnamedia.extra.TerminationMode;
import com.magnamedia.extra.WorkDays;
import com.magnamedia.extra.payroll.init.HousemaidIntegerAmountProjection;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidUnpaidStatus;
import com.magnamedia.module.type.VisaContractType;
import com.magnamedia.repository.*;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import com.magnamedia.service.payroll.generation.newversion.ProRatedSalariesService;
import org.apache.commons.collections.map.HashedMap;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.Months;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> Al-masto
 * Created on 1/12/21
 */
@RestController
@Component
@RequestMapping("/housemaidfinalsettlement")
public class PayrollHousemaidFinalSettlementController extends BaseRepositoryController<PayrollHousemaidFinalSettlement> {

    @Autowired
    private PayrollHousemaidFinalSettlementRepository payrollHousemaidFinalSettlementRepository;

    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    private NewVisaRequestRepository newVisaRequestRepository;

    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    private HousemaidController housemaidController;

    @Autowired
    private ProRatedSalariesService proRatedSalariesService;

    @Autowired
    private RenewVisaRequestRepository renewVisaRequestRepository;

    @Autowired
    private EmailTemplateService emailTemplateService;

    @Autowired
    private PayrollEmailTemplateRepository emailTemplateRepository;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private MonthlyLoanHistoryRepository monthlyLoanHistoryRepository;

    @Autowired
    private EmployeeLoanRepository employeeLoanRepository;

    @Autowired
    private HousemaidForgivenessRepository housemaidForgivenessRepository;

    @Autowired
    private RepaymentRepository repaymentRepository;

    @Autowired
    private HousemaidPayrollPaymentServiceV2 payrollPaymentServiceV2;

    @Override
    public BaseRepository<PayrollHousemaidFinalSettlement> getRepository() {
        return payrollHousemaidFinalSettlementRepository;
    }

    @RequestMapping("/getfinalsettlemet")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','getFinalSettlement')")
    public PayrollHousemaidFinalSettlement getFinalSettlement(@RequestParam Housemaid housemaid){
        PayrollHousemaidFinalSettlement payrollHousemaidFinalSettlement = payrollHousemaidFinalSettlementRepository
                .findByHousemaidAndRevised(housemaid, false);
        if(payrollHousemaidFinalSettlement == null)
            throw new BusinessException("Sorry, there is no final settlement for this maid.");

        if(payrollHousemaidFinalSettlement.isRevised())
            throw new BusinessException("Sorry, the final settlement of this maid is already revised.");

        return payrollHousemaidFinalSettlement;
    }


    @RequestMapping("/getfinalsettlemetforhousemaidprofile")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','getFinalSettlementForHousemaidProfile')")
    public PayrollHousemaidFinalSettlement getFinalSettlementForHousemaidProfile(@RequestParam Housemaid housemaid){
        return payrollHousemaidFinalSettlementRepository
                .findTop1ByHousemaidOrderByCreationDateDesc(housemaid);
    }

    @RequestMapping("/revisedbyuser")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','revisedByUser')")
    @Transactional
    public ResponseEntity<?> revisedByUser(@RequestBody ObjectNode node) throws IOException {
        PayrollHousemaidFinalSettlement payrollHousemaidFinalSettlement = parse(node);
        if(payrollHousemaidFinalSettlement == null)
            throw new BusinessException("Sorry, there is no final settlement for this maid");

        PayrollHousemaidFinalSettlement originObject = payrollHousemaidFinalSettlementRepository.findOne(payrollHousemaidFinalSettlement.getId());

        if(originObject.isRevised())
            throw new BusinessException("Sorry, the final settlement of this maid is already revised.");

        if(payrollHousemaidFinalSettlement.getRevisedFinalSettlement() == null){
            payrollHousemaidFinalSettlement.setRevisedFinalSettlement(originObject.getFinalValue());
        }

        if(payrollHousemaidFinalSettlement.getTerminationMode().equals(TerminationMode.FIRED)
                && payrollHousemaidFinalSettlement.getRevisedFinalSettlement() < 0)
            throw new BusinessException("This is a fired maid, final settlement amount should be zero or greater. " +
                    "The current value is: " + payrollHousemaidFinalSettlement.getRevisedFinalSettlement());
//        }

        super.update(originObject, payrollHousemaidFinalSettlement, node);
        originObject.setRevised(true);
        originObject.setReviseSMSNeeded(payrollHousemaidFinalSettlement.getReviseSMSNeeded());
        originObject.setFinalValue(payrollHousemaidFinalSettlement.getRevisedFinalSettlement());
        super.updateEntity(originObject);
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/createfinalsettlementcomponent", method = RequestMethod.POST) // called using IMC from staff
    @ResponseBody
    @Transactional
    public ResponseEntity<?> createFinalSettlementComponent(@RequestParam Housemaid housemaid,
                                                           @RequestParam TerminationMode terminationMode,
                                                            @RequestParam @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  Date calculationDate){
        payrollHousemaidFinalSettlementRepository.save(calculateWithRules(housemaid, terminationMode, calculationDate, true));
        return new ResponseEntity<>("Done", HttpStatus.OK);
    }

    @RequestMapping("/calculatewithrules")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','calculateWithRules')")
    @Transactional
    public PayrollHousemaidFinalSettlement calculateWithRules(@RequestParam Housemaid housemaid,
                                                              @RequestParam TerminationMode terminationMode,
                                                              @RequestParam @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  Date calculationDate,
                                                              @RequestParam(required = false, defaultValue = "false") Boolean createCase){
        housemaid = housemaidRepository.findOne(housemaid.getId());
        NewRequest newRequest = housemaid.getVisaNewRequest();

        Date startDate = getStartDate(housemaid, newRequest);

        Date proratedSalaryStartDate = housemaid.getReplacementSalaryStartDate() != null ? housemaid.getReplacementSalaryStartDate() : housemaid.getStartDate();

        if (proratedSalaryStartDate == null) {
            Date firstAvailableDate = getFirstAvailableDate(housemaid);
            proratedSalaryStartDate = firstAvailableDate;
            housemaid.setFirstAvailableDate(firstAvailableDate);
        }

        if(startDate == null){
            PayrollHousemaidFinalSettlement payrollHousemaidFinalSettlement =
                    payrollHousemaidFinalSettlementRepository.findByHousemaidAndRevised(housemaid, false);
            if(payrollHousemaidFinalSettlement == null)
                payrollHousemaidFinalSettlement = new PayrollHousemaidFinalSettlement();
            payrollHousemaidFinalSettlement.setHousemaid(housemaid);
            payrollHousemaidFinalSettlement.setFinalSettlementCalculationDate(calculationDate);
            payrollHousemaidFinalSettlement.setTerminationMode(terminationMode);
            payrollHousemaidFinalSettlement.setFinalValue(0D);

            return payrollHousemaidFinalSettlement;
        }

        Double basicSalary = housemaid.getBasicSalary();
        //Double totalSalary = (housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0) + (housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0);
        Double primarySalary = housemaid.getPrimarySalary();
        Double monthlyLoan = housemaid.getMonthlyLoan();
        validateCriticalFields(housemaid, newRequest, basicSalary, primarySalary, terminationMode, monthlyLoan);


        PayrollHousemaidFinalSettlement payrollHousemaidFinalSettlement =
                payrollHousemaidFinalSettlementRepository.findByHousemaidAndRevised(housemaid, false);
        if(payrollHousemaidFinalSettlement == null)
            payrollHousemaidFinalSettlement = new PayrollHousemaidFinalSettlement();

        // set salary break downs components
        Map<String, Object> proratedBreakDowns = calculateProrated(housemaid, proratedSalaryStartDate, calculationDate);
        payrollHousemaidFinalSettlement.setProrated((double)proratedBreakDowns.getOrDefault("total", 0.0));
        payrollHousemaidFinalSettlement.setGroup1Salary((double)proratedBreakDowns.getOrDefault("group1Salary", 0.0));
        payrollHousemaidFinalSettlement.setGroup2Salary((double)proratedBreakDowns.getOrDefault("group2Salary", 0.0));
        payrollHousemaidFinalSettlement.setGroup4Salary((double)proratedBreakDowns.getOrDefault("group4Salary", 0.0));
        payrollHousemaidFinalSettlement.setGroup5Salary((double)proratedBreakDowns.getOrDefault("group5Salary", 0.0));
        payrollHousemaidFinalSettlement.setGroup6Salary((double)proratedBreakDowns.getOrDefault("group6Salary", 0.0));
        payrollHousemaidFinalSettlement.setGroup1Days((int)proratedBreakDowns.getOrDefault("group1Days", 0));
        payrollHousemaidFinalSettlement.setGroup2Days((int)proratedBreakDowns.getOrDefault("group2Days", 0));
        payrollHousemaidFinalSettlement.setGroup4Days((int)proratedBreakDowns.getOrDefault("group4Days", 0));
        payrollHousemaidFinalSettlement.setGroup5Days((int)proratedBreakDowns.getOrDefault("group5Days", 0));
        payrollHousemaidFinalSettlement.setGroup6Days((int)proratedBreakDowns.getOrDefault("group6Days", 0));

        payrollHousemaidFinalSettlement.setHousemaid(housemaid);
        payrollHousemaidFinalSettlement.setFinalSettlementCalculationDate(calculationDate);
        payrollHousemaidFinalSettlement.setTerminationMode(terminationMode);

//        payrollHousemaidFinalSettlement.setRenewalExpenses(calculateRenewalExpenses(housemaid));
        payrollHousemaidFinalSettlement.setVacations(calculateVacationsV2(terminationMode, primarySalary, startDate, calculationDate));
        payrollHousemaidFinalSettlement.setGratuity(calculateGratuityV2(primarySalary, startDate, calculationDate, payrollHousemaidFinalSettlement.getGratuityBasedDays()));
        payrollHousemaidFinalSettlement.setCashAdvance(calculateCashAdvanceV2(housemaid, calculationDate, createCase));
        payrollHousemaidFinalSettlement.setResignationFees(calculateResignationFeesV2(terminationMode, basicSalary, payrollHousemaidFinalSettlement.getNoticePeriodServed(), payrollHousemaidFinalSettlement.getDaysServed()));
        payrollHousemaidFinalSettlement.setAdditions(calculateAdditions(housemaid, calculationDate));
        payrollHousemaidFinalSettlement.setAdditionsWithoutRaffleAndReferral(calculateAdditionsWithoutRaffleAndReferral(housemaid, calculationDate));
        payrollHousemaidFinalSettlement.setDeductions(calculateDeductions(housemaid));

        payrollHousemaidFinalSettlement.setDesiredFinalSettlementAmountDecidedByManager(calculateDesiredAmount(terminationMode, payrollHousemaidFinalSettlement.getProrated(), payrollHousemaidFinalSettlement.getAdditionsWithoutRaffleAndReferral(),payrollHousemaidFinalSettlement.getAdditions(), payrollHousemaidFinalSettlement.getResignationFees(), calculateDeductions(payrollHousemaidFinalSettlement.getHousemaid())));


        calculateFinalSettlement(payrollHousemaidFinalSettlement, housemaid, false, terminationMode);

        return payrollHousemaidFinalSettlement;
    }


    @RequestMapping("/calculatefinalsettlement")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','calculateFinalSettlement')")
    public PayrollHousemaidFinalSettlement calculateFinalSettlement(@RequestBody PayrollHousemaidFinalSettlement payrollHousemaidFinalSettlement,
                                                                     @RequestParam(required = false) Housemaid housemaid,
                                                                     @RequestParam boolean isReviseCase,
                                                                     @RequestParam(required = false) TerminationMode terminationMode){

        housemaid = housemaid == null ? payrollHousemaidFinalSettlement.getHousemaid() : housemaid;
        if(housemaid == null)
            throw new BusinessException("Maid not found");
        housemaid = housemaidRepository.findOne(housemaid.getId());
        Double finalValue = payrollHousemaidFinalSettlement.getFinalValue();
        Double revisedFinalSettlementValue = null;
        Double calculatedValue = null;

        double equationValue = payrollHousemaidFinalSettlement.getProrated()
                + payrollHousemaidFinalSettlement.getGratuity()
                + payrollHousemaidFinalSettlement.getVacations()
                + payrollHousemaidFinalSettlement.getAdditions()
                - (payrollHousemaidFinalSettlement.getResignationFees()
                    +payrollHousemaidFinalSettlement.getDeductions()
//                    + payrollHousemaidFinalSettlement.getRenewalExpenses()
                    + payrollHousemaidFinalSettlement.getCashAdvance());

        double employeeOwesTheCompany = getSumOfEmployeeOwesTheCompany(payrollHousemaidFinalSettlement);

        double automaticForgiveness = calculateAutomaticForgivenessAmount(payrollHousemaidFinalSettlement.getDesiredFinalSettlementAmountDecidedByManager(), equationValue, employeeOwesTheCompany);

        payrollHousemaidFinalSettlement.setAutomaticForgivenessAmount(automaticForgiveness);
        payrollHousemaidFinalSettlement.setDesiredFinalSettlementAmountDecidedByErp(calculateDesiredAmount(terminationMode, payrollHousemaidFinalSettlement.getProrated(), payrollHousemaidFinalSettlement.getAdditionsWithoutRaffleAndReferral(),payrollHousemaidFinalSettlement.getAdditions() ,payrollHousemaidFinalSettlement.getResignationFees(), payrollHousemaidFinalSettlement.getDeductions()));

        if(!isReviseCase)
            payrollHousemaidFinalSettlement.setCalculatedFinalSettlementValue(equationValue);

        calculatedValue = equationValue + automaticForgiveness;

        if(isReviseCase) revisedFinalSettlementValue = calculatedValue; else finalValue = calculatedValue;

        payrollHousemaidFinalSettlement.setFinalValue(finalValue);
        payrollHousemaidFinalSettlement.setRevisedFinalSettlement(revisedFinalSettlementValue);

        payrollHousemaidFinalSettlement.setHousemaid(housemaid);
        return payrollHousemaidFinalSettlement;

    }

    @RequestMapping("/recalculateGratuity")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','recalculateGratuity')")
    @Transactional
    public Double recalculateGratuity(@RequestParam Housemaid housemaid,
                                      @RequestParam @org.springframework.format.annotation.DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  Date calculationDate,
                                      @RequestParam Integer gratuityBasedDays) {
        housemaid = housemaidRepository.findOne(housemaid.getId());
        Double primarySalary = housemaid.getPrimarySalary();
        Date startDate = getStartDate(housemaid, housemaid.getVisaNewRequest());

        return calculateGratuityV2(primarySalary, startDate, calculationDate, gratuityBasedDays);
    }

    @RequestMapping("/recalculateResignationFees")
    @PreAuthorize("hasPermission('PayrollHousemaidFinalSettlementController','recalculateResignationFees')")
    @Transactional
    public Double recalculateResignationFees(@RequestParam Housemaid housemaid,
                                             @RequestParam TerminationMode terminationMode,
                                             @RequestParam PayrollHousemaidFinalSettlement.NoticePeriodServed noticePeriodServed,
                                             @RequestParam Integer daysServed) {
        housemaid = housemaidRepository.findOne(housemaid.getId());
        //Double totalSalary = (housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0) + (housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0);

        return calculateResignationFeesV2(terminationMode, housemaid.getBasicSalary(), noticePeriodServed, daysServed);
    }

    private Map<String, Object> calculateProrated(Housemaid housemaid, Date startDate, Date finalSettlementCalculationDate){
        HousemaidPayrollLog housemaidPayrollLog = housemaidPayrollLogRepository
                .findTop1ByHousemaidAndTransferredTrueOrderByCreationDateDesc(housemaid);

        Date firstUnpaidPayrollMonth = housemaidPayrollLog != null ? new DateTime(housemaidPayrollLog.getPayrollMonth()).plusMonths(1).toDate() : null;
        Date startOfProratedDays = firstUnpaidPayrollMonth == null ? startDate : (firstUnpaidPayrollMonth.after(startDate) ? firstUnpaidPayrollMonth : startDate);

        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> tempMap;
        if (startOfProratedDays == null) return resultMap;

        if(new LocalDate(startOfProratedDays).getMonthOfYear() != new LocalDate(finalSettlementCalculationDate).getMonthOfYear()){
            for(Map<String, Date> map : getMonthsSlices(startOfProratedDays, finalSettlementCalculationDate)){
                tempMap = calculateProratedForMonthSlice(map.get("start"), map.get("end"), housemaid);
                resultMap = plusTwoSalaryBreakDownMaps(resultMap, tempMap);
            }
        }else{
            resultMap = calculateProratedForMonthSlice(startOfProratedDays, finalSettlementCalculationDate, housemaid);
        }

        return resultMap;
    }

    private Map<String, Object> calculateProratedForMonthSlice(Date startOfProratedDays, Date finalSettlementCalculationDate, Housemaid housemaid){
        java.sql.Date fromDate = new java.sql.Date(startOfProratedDays.getTime());
        java.sql.Date toDate = new java.sql.Date(finalSettlementCalculationDate.getTime());
        Map<Long, Long> vacationDaysMap = new HashMap<>();

//        List<WorkDays> workDays = housemaidController.computeStatusHistoryNew(housemaid.getId(), new java.sql.Date(finalSettlementCalculationDate.getTime()),
//                fromDate, toDate);
//        Map<HousemaidSalaryGroup, Long> numberOfDaysForEachGroup = Setup.getApplicationContext().getBean(PayrollGroupService.class)
//                .getNumberOfDaysForEachGroup(housemaid, workDays, LocalDate.fromDateFields(startOfProratedDays));

        HousemaidIntegerAmountProjection housemaidIntegerAmountProjection = housemaidPayrollLogRepository
                .getHousemaidVacationDays(housemaid.getId(), HousemaidUnpaidStatus.ON_VACATION, fromDate);

        long previousVacationDays = housemaidIntegerAmountProjection != null ? housemaidIntegerAmountProjection.getTotal() : 0;
//        int groupOne = housemaidDetails.get(housemaid.getId()) == null ? 0 : housemaidDetails.get(housemaid.getId());
//        long vacationDays = vacationDaysMap.get(housemaid.getId()) == null ? 0 : vacationDaysMap.get(housemaid.getId());

        Map<String, Object> proratedSalaryDetails = proRatedSalariesService
                .getSalaryBreakDownNew(housemaid, null, fromDate, toDate, previousVacationDays);

        return proratedSalaryDetails;
    }

    private double calculateVacations(TerminationMode terminationMode, Double primarySalary){
        return (terminationMode.equals(TerminationMode.FIRED) || terminationMode.equals(TerminationMode.RESIGNATION))
                ? 0
                : ((terminationMode.equals(TerminationMode.NON_RENEWAL) && primarySalary != null)
                        ? primarySalary : 0);
    }

    private double calculateVacationsV2(TerminationMode terminationMode, Double primarySalary, Date startDate, Date finalSettlementCalculationDate) {
        int numDays = 0;
        int months = Math.abs(Months.monthsBetween(new LocalDate(startDate), new LocalDate(finalSettlementCalculationDate)).getMonths());
        if(months < 6 )
            return 0.0;
        if (terminationMode.equals(TerminationMode.FIRED) || terminationMode.equals(TerminationMode.RESIGNATION)) {
            numDays = months >= 6 ? Math.min(2* months, 30) : 0; //the number of months must be more than 6, else consider it  Zero (two days for each month)
            return primarySalary * numDays / 30;
        } else if (terminationMode.equals(TerminationMode.NON_RENEWAL)) {
            numDays = ((int) Math.ceil(((months / 24.0) - (months / 24)) * 24.0)) * 2; //this is a fixed formula which lead to pay the maid only for the months of the last incomplete two years (two days for each month)
            numDays = Math.min(numDays, 30);
            return primarySalary * numDays / 30;
        }
        return 0.0;
    }

    private double calculateGratuity(VisaContractType visaContractType, TerminationMode terminationMode,
                                     Double primarySalary, Date startDate, Date finalSettlementCalculationDate) {
        int years = (Days.daysBetween(new LocalDate(startDate)
                , new LocalDate(finalSettlementCalculationDate)).getDays()) / 365;
        if (primarySalary == null)
            return 0;
        double basedDays = 0;
        if(terminationMode.equals(TerminationMode.FIRED) || terminationMode.equals(TerminationMode.RESIGNATION)){
            if(visaContractType.equals(VisaContractType.Limited))
                basedDays = 0;
            else if(visaContractType.equals(VisaContractType.Unlimited)){
                if(years < 1)
                    basedDays = 0;
                else if(years < 3)
                    basedDays = ((double) 21 * 1) / 3;
                else if(years < 5)
                    basedDays = ((double) 21 * 2) / 3;
                else
                    basedDays = 21;
            }
        }else if(terminationMode.equals(TerminationMode.NON_RENEWAL)){
            if(visaContractType.equals(VisaContractType.Limited)){
                if(years < 1)
                    basedDays = 0;
                else if(years < 5)
                    basedDays = 21;
                else
                    basedDays = 30;
            }else if(visaContractType.equals(VisaContractType.Unlimited)){
                if(years < 1)
                    basedDays = 0;
                else if(years < 3)
                    basedDays = ((double) 21 * 1) / 3;
                else if(years < 5)
                    basedDays = ((double) 21 * 2) / 3;
                else
                    basedDays = 21;
            }
        }

        int servedYears = (Days.daysBetween(new LocalDate(startDate)
                , new LocalDate(DateUtil.addDays(finalSettlementCalculationDate, 45))).getDays()) / 365;

        return (primarySalary / 30.4) * basedDays * servedYears;
    }

    private double calculateGratuityV2(Double primarySalary, Date startDate, Date finalSettlementCalculationDate, int basedDays) {
        int months = Math.abs(Months.monthsBetween(new LocalDate(startDate), new LocalDate(finalSettlementCalculationDate)).getMonths());
        if(months < 12)
            return 0;
        double years = months / 12.0;
        if (primarySalary == null)
            return 0;

        return (primarySalary / 30.0) * basedDays * years;
    }

    private double calculateRenewalExpenses(Housemaid housemaid){
        return 0;
    }

    private double calculateCashAdvance(Housemaid housemaid, Date finalSettlementCalculationDate, boolean createCase){

        // V3 PAY-516
        double monthlyLoan = 0;
        List<MonthlyLoanHistory> monthlyLoanHistories = monthlyLoanHistoryRepository.findByHousemaidOrderByCreationDateAsc(housemaid);
        for (MonthlyLoanHistory monthlyLoanHistory : monthlyLoanHistories){
            Date toDate = monthlyLoanHistory.getToDate();
            if(toDate == null){
                toDate = finalSettlementCalculationDate;
                if(createCase){
                    monthlyLoanHistory.setToDate(finalSettlementCalculationDate);
                    monthlyLoanHistory = monthlyLoanHistoryRepository.save(monthlyLoanHistory);
                }
            }
            monthlyLoan += (DateUtil.getDaysBetween(monthlyLoanHistory.getFromDate(), toDate) / 30.4) * monthlyLoanHistory.getAmount();
        }
        return monthlyLoan;

    }

    /**
     * The cash advance for cc maids should be calculated monthly{For each month} from the cash advance start date until today`s date based on the Actual Salary and Total Salary as follows
     *     Actual Salary <= Total Salary: then the cash advance 0
     *     Actual Salary > Total Salary: Then the cash advance equals MIN(Cash Advance in the HM profile, Difference between Actual Salary and Total Salary )
     */
    private double calculateCashAdvanceV2(Housemaid housemaid, Date finalSettlementCalculationDate, boolean createCase){
        double monthlyLoan = 0;
        double totalMaidSalary = 0;
        List<MonthlyLoanHistory> monthlyLoanHistories = monthlyLoanHistoryRepository.findByHousemaidOrderByCreationDateAsc(housemaid);
        Set<MonthlyLoanHistory> monthlyLoanHistoriesToSave = new HashSet<>();
        List<HousemaidPayrollLog> housemaidPayrollLogs = housemaidPayrollLogRepository.findByHousemaid(housemaid, true);
        for (HousemaidPayrollLog log : housemaidPayrollLogs) {
            Date logDate = log.getCreationDate() != null ? log.getCreationDate() : log.getPayrollMonth();
            if(logDate == null)
                continue;
            for (MonthlyLoanHistory monthlyLoanHistory : monthlyLoanHistories) {
                Date fromDate = monthlyLoanHistory.getFromDate();
                Date toDate = monthlyLoanHistory.getToDate();
                if (toDate == null) {
                    toDate = finalSettlementCalculationDate;
                    if (createCase) {
                        monthlyLoanHistory.setToDate(finalSettlementCalculationDate);
                        monthlyLoanHistoriesToSave.add(monthlyLoanHistory);
                    }
                }

                if(logDate.compareTo(fromDate) >= 0 && logDate.compareTo(toDate) <= 0) {
                    totalMaidSalary = (log.getPrimarySalary() != null ? log.getPrimarySalary() : 0.0) + (log.getHoliday() != null ? log.getHoliday() : 0.0);

                    monthlyLoan += log.getTotalSalary().compareTo(totalMaidSalary) > 0 ? Math.min(monthlyLoanHistory.getAmount(), log.getTotalSalary() - totalMaidSalary) : 0;
                    break;
                }
            }
        }

        if(createCase)
            monthlyLoanHistoryRepository.save(monthlyLoanHistoriesToSave);

        return monthlyLoan;

    }

    private double calculateResignationFees(NewRequest newRequest, TerminationMode terminationMode
            , VisaContractType visaContractType, Double basicSalary, Date finalSettlementCalculationDate){

        if(basicSalary == null)
            return 0;
        if(terminationMode.equals(TerminationMode.FIRED) || terminationMode.equals(TerminationMode.RESIGNATION)){
            if(visaContractType.equals(VisaContractType.Limited))
                return basicSalary * ((double) (3/2));
            else if(visaContractType.equals(VisaContractType.Unlimited)){
                int days = DateUtil.getDaysBetween(finalSettlementCalculationDate, newRequest.getLaborCardExpiryDate());
                if((days / 30.4) < 3){
                    return days * (basicSalary / 30.4);
                }else{
                    return basicSalary * 3;
                }
            }
        }
        return 0;
    }

    private double calculateResignationFeesV2(TerminationMode terminationMode, Double totalSalary, PayrollHousemaidFinalSettlement.NoticePeriodServed noticePeriodServed, int daysServed){

        if(totalSalary == null)
            return 0;

        if(terminationMode.equals(TerminationMode.RESIGNATION)){
            if (PayrollHousemaidFinalSettlement.NoticePeriodServed.Yes.equals(noticePeriodServed))
                return 0.0;
            else if(PayrollHousemaidFinalSettlement.NoticePeriodServed.No.equals(noticePeriodServed))
                return totalSalary;
            else if (PayrollHousemaidFinalSettlement.NoticePeriodServed.Partially.equals(noticePeriodServed))
                return totalSalary / 30.0 * (30 - daysServed);
        } else if (terminationMode.equals(TerminationMode.FIRED) || terminationMode.equals(TerminationMode.NON_RENEWAL))
            return 0;
        return 0;
    }

    /**
     * Here we are referring to the manager's note with the type addition added in the month of termination and not paid yet.
     */
    private double calculateAdditions(Housemaid housemaid, Date finalSettlementCalculationDate) {

        double additions = 0.0;
        java.sql.Date payrollMonth = new java.sql.Date(new LocalDate(finalSettlementCalculationDate).withDayOfMonth(1).toDate().getTime());
        java.sql.Date previousMonthPaymentDate = payrollPaymentServiceV2.getPayrollStartPaymentDateOfPayrollMonth(payrollMonth);
        java.sql.Date today = new java.sql.Date(System.currentTimeMillis());
        Double result = null;
        if (today.before(previousMonthPaymentDate)){
            result = payrollManagerNoteRepository.sumByHousemaidAndNoteType(housemaid, payrollMonth, AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
        }
        else{
             result =  payrollManagerNoteRepository.sumByHousemaidAndNoteType(housemaid, previousMonthPaymentDate, AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
        }
        additions = (result != null) ? result : 0.0;
        return additions;
    }

    /**
     * Here we are referring to the manager's note with the type addition added in the month of termination and not paid yet and excluding some addition
     * used for calculating the Desired FS amount
     */
    private double calculateAdditionsWithoutRaffleAndReferral(Housemaid housemaid, Date finalSettlementCalculationDate) {

        double additions = 0.0;
        PicklistItem raffleReason = PicklistHelper.getItem("AdditionReasons", "raffle_prize");
        PicklistItem bonusReason = PicklistHelper.getItem("AdditionReasons", "bonus");
        PicklistItem referralBonus = PicklistHelper.getItem("HousemaidPurposesForBonusAdditionalDescription", "referral_bonus");

        java.sql.Date payrollMonth = new java.sql.Date(new LocalDate(finalSettlementCalculationDate).withDayOfMonth(1).toDate().getTime());
        java.sql.Date previousMonthPaymentDate = payrollPaymentServiceV2.getPayrollStartPaymentDateOfPayrollMonth(payrollMonth);
        java.sql.Date today = new java.sql.Date(System.currentTimeMillis());
        Double result = null;
        if (today.before(previousMonthPaymentDate))
            result = payrollManagerNoteRepository.sumByHousemaidAndNoteTypeWithoutRaffleAndReferral(housemaid, payrollMonth, AbstractPayrollManagerNote.ManagerNoteType.ADDITION, raffleReason, bonusReason, referralBonus);
        else
            result = payrollManagerNoteRepository.sumByHousemaidAndNoteTypeWithoutRaffleAndReferral(housemaid, previousMonthPaymentDate, AbstractPayrollManagerNote.ManagerNoteType.ADDITION, raffleReason, bonusReason, referralBonus);

        additions = (result != null) ? result : 0.0;
        return additions;
    }

    /**
     *  The amount here should be the remaining balance amount after EXCLUDING the exit loan amount.
     */
    private double calculateDeductions(Housemaid housemaid) {

        Double loansAmount = 0.0;
        Double exitLoanAmount = 0.0;
        Double forgivenessAmount = 0.0;
        Double repaymentsAmount = 0.0;

        List<EmployeeLoan> loans = employeeLoanRepository.findByHousemaid(housemaid);
        for (EmployeeLoan loan : loans) {
            if (AbstractEmployeeLoan.LoanType.EXIT_LOAN.equals(loan.getLoanType()))
                exitLoanAmount += loan.getAmount();
            else
                loansAmount += loan.getAmount();
        }

        repaymentsAmount = repaymentRepository.sumRepaymentsByMaid(housemaid);
        forgivenessAmount = housemaidForgivenessRepository.sumForgivenessByMaid(housemaid);

        if (repaymentsAmount == null) {
            repaymentsAmount = 0.0;
        }

        if (forgivenessAmount == null) {
            forgivenessAmount = 0.0;
        }

        if (exitLoanAmount > 0.0)
            repaymentsAmount -= exitLoanAmount;
        if (repaymentsAmount < 0.0)
            repaymentsAmount = 0.0;

        return loansAmount - repaymentsAmount - forgivenessAmount;
    }

    private double calculateDesiredAmount(TerminationMode terminationMode, Double prorated, Double AdditionsWithoutRaffleAndReferral,Double AdditionsWithRaffleAndReferral, Double resignationFees, Double deductions) {

        double desiredAmount = 0.0;

        if (TerminationMode.FIRED.equals(terminationMode) || TerminationMode.RESIGNATION.equals(terminationMode)) {
            // Calculate for Fired or Resignation cases
            desiredAmount = (prorated != null ? prorated : 0.0)
                    + (AdditionsWithoutRaffleAndReferral != null ? AdditionsWithoutRaffleAndReferral : 0.0);
            desiredAmount -= (resignationFees != null ? resignationFees : 0.0);
            desiredAmount -= (deductions != null ? deductions : 0.0);
        } else if (TerminationMode.NON_RENEWAL.equals(terminationMode)) {
            // Calculate for Non-Renewal case
            desiredAmount = (prorated != null ? prorated : 0.0)
                    + (AdditionsWithRaffleAndReferral != null ? AdditionsWithRaffleAndReferral : 0.0);
            // Notice fee (resignation fee) should not be subtracted in Non-Renewal
            desiredAmount -= (deductions != null ? deductions : 0.0);
        }
        return desiredAmount;
    }

    private double calculateAutomaticForgivenessAmount(Double desiredFSAmount, Double calculatedFSAmount, double employeeOwesTheCompany) {

        double automaticForgiveness = desiredFSAmount - calculatedFSAmount;

        automaticForgiveness =  Math.min(automaticForgiveness, employeeOwesTheCompany);
        automaticForgiveness = Math.max(automaticForgiveness, 0.0);

        return automaticForgiveness;
    }

    private double getSumOfEmployeeOwesTheCompany(PayrollHousemaidFinalSettlement payrollHousemaidFinalSettlement){
        return payrollHousemaidFinalSettlement.getResignationFees() + payrollHousemaidFinalSettlement.getCashAdvance() + payrollHousemaidFinalSettlement.getDeductions();
    }

    private void validateCriticalFields(Housemaid housemaid, NewRequest newRequest, Double basicSalary, Double primarySalary,
                                        TerminationMode terminationMode, Double monthlyLoan){
        String failedFieldName = null;
        if(newRequest == null)
            failedFieldName = "New request";
        else if(newRequest.getContractType() == null)
            failedFieldName = "Contract type";
        else if(basicSalary == null)
            failedFieldName = "Primary salary";
        else if(primarySalary == null)
            failedFieldName = "Basic salary";
        else if(terminationMode == null)
            failedFieldName = "Termination mode";
        else if(monthlyLoan == null)
            failedFieldName = "Monthly loan";

        if(failedFieldName != null){
            sendFailEmail(housemaid, failedFieldName);
            throw new BusinessException(
                    String.format("Unable to terminate the maid because %s is null, technical responsible has been informed.", failedFieldName));
        }
    }

    public Date getFirstAvailableDate(Housemaid housemaid) {
        HistorySelectQuery<Housemaid> historyQuery = new HistorySelectQuery<>(Housemaid.class);
        historyQuery.filterBy("id", "=", housemaid.getId());
        historyQuery.filterByChanged("status");
        historyQuery.filterBy("status", "=", HousemaidStatus.AVAILABLE);
        historyQuery.sortBy("lastModificationDate", true);
        historyQuery.setLimit(1);

        List<Housemaid> result = historyQuery.execute();

        if (result.size() > 0) {
            return result.get(0).getLastModificationDate();
        }
        return null;
    }

    private Date getStartDate(Housemaid housemaid, NewRequest newRequest){
        Date changeOfStatusDate = newRequest != null ? newRequest.findTaskMoveOutDate("ChangeofStatus") : null;
        return changeOfStatusDate != null ? changeOfStatusDate :housemaid.getLandedInDubaiDate();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void sendFailEmail(Housemaid housemaid, String fieldName){
        housemaid = housemaidRepository.findOne(housemaid.getId());
        Map<String, String> subjectParams = new HashMap();
        subjectParams.put("housemaid_name", housemaid.getName());

        Map<String, String> bodyParams = new HashMap();

        bodyParams.put("housemaid_name", housemaid.getName());
        bodyParams.put("housemaid_id", housemaid.getId().toString());
        bodyParams.put("null_field", fieldName);

        List<EmailRecipient> receivers = Recipient.parseEmailsString(getParameter(
                PayrollManagementModule.PARAMETER_FAILED_CREATE_MAID_FINAL_SETTLEMENT_EMAIL_RECIPIENTS));

        emailTemplateService.sendEmail("FAILED_CREATE_FINAL_SETTLEMENT_FOR_MAID_EMAIL", receivers,
                EmailReceiverType.Office_Staff, subjectParams, bodyParams, null);
    }

    public List<Map<String, Date>> getMonthsSlices(Date startOfProratedDays, Date finalSettlementCalculationDate){

        List<Map<String, Date>> monthsSlices = new ArrayList<>();
        while (!new DateTime(startOfProratedDays).dayOfMonth().withMaximumValue().toDate().after(finalSettlementCalculationDate)){
            Date finalStartOfProratedDays = startOfProratedDays;
            monthsSlices.add(new HashedMap(){{
                put("start", new DateTime(finalStartOfProratedDays).toDate());
                put("end", new DateTime(finalStartOfProratedDays).dayOfMonth().withMaximumValue().toDate());
            }});

            startOfProratedDays = new DateTime(startOfProratedDays).withDayOfMonth(1).plusMonths(1).toDate();
        }

        monthsSlices.add(new HashedMap(){{
            put("start", new DateTime(finalSettlementCalculationDate).withDayOfMonth(1).toDate());
            put("end", new DateTime(finalSettlementCalculationDate).toDate());
        }});

        return monthsSlices;
    }

    public Map<String, Object> plusTwoSalaryBreakDownMaps(Map<String, Object> resultMap, Map<String, Object> addedMap) {
        resultMap.put("group1Salary", ((double)resultMap.getOrDefault("group1Salary", 0.0)) + ((double)addedMap.getOrDefault("group1Salary", 0.0)));
        resultMap.put("group2Salary", ((double)resultMap.getOrDefault("group2Salary", 0.0)) + ((double)addedMap.getOrDefault("group2Salary", 0.0)));
        resultMap.put("group4Salary", ((double)resultMap.getOrDefault("group4Salary", 0.0)) + ((double)addedMap.getOrDefault( "group4Salary", 0.0)));
        resultMap.put("group5Salary", ((double)resultMap.getOrDefault("group5Salary", 0.0)) + ((double)addedMap.getOrDefault( "group5Salary", 0.0)));
        resultMap.put("group6Salary", ((double)resultMap.getOrDefault("group6Salary", 0.0)) + ((double)addedMap.getOrDefault( "group6Salary", 0.0)));

        resultMap.put("group1Days", ((int)resultMap.getOrDefault("group1Days", 0)) + ((int)addedMap.getOrDefault("group1Days", 0)));
        resultMap.put("group2Days", ((int)resultMap.getOrDefault("group2Days", 0)) + ((int)addedMap.getOrDefault("group2Days", 0)));
        resultMap.put("group3Days", ((int)resultMap.getOrDefault("group3Days", 0)) + ((int)addedMap.getOrDefault("group3Days", 0)));
        resultMap.put("group4Days", ((int)resultMap.getOrDefault("group4Days", 0)) + ((int)addedMap.getOrDefault("group4Days", 0)));
        resultMap.put("group5Days", ((int)resultMap.getOrDefault("group5Days", 0)) + ((int)addedMap.getOrDefault("group5Days", 0)));
        resultMap.put("group6Days", ((int)resultMap.getOrDefault("group6Days", 0)) + ((int)addedMap.getOrDefault("group6Days", 0)));

        resultMap.put("total", ((double)resultMap.getOrDefault("total", 0.0)) + ((double)addedMap.getOrDefault("total", 0.0)));

        return resultMap;
    }
}
