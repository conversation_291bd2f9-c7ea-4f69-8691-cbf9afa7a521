package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PaidOffDays;
import com.magnamedia.entity.PendingApprovalRequest;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.PaidOffDaysRepository;
import com.magnamedia.repository.PendingApprovalRequestRepository;
import com.magnamedia.service.PendingManagerApprovalService;
import com.magnamedia.service.PublicPageService;
import com.magnamedia.service.message.MessagingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> Masod <<EMAIL>>
 * Created on Jan 28, 2020
 * Jirra ACC-1227
 */

@RequestMapping("/payoffdays")
@RestController
public class PaidOffDaysController extends BaseRepositoryController<PaidOffDays> {

    @Autowired
    private OfficeStaffRepository officeStaffRepository;
    @Autowired
    private PaidOffDaysRepository paidOffDaysRepository;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private PublicPageHelper publicPageHelper;
    @Autowired
    private PublicPageService publicPageService;
    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;
    @Autowired
    private PendingApprovalRequestRepository pendingApprovalRequestRepository;

    @Override
    protected ResponseEntity createEntity(PaidOffDays paidOffDays) {

        OfficeStaff officeStaff = officeStaffRepository.findOne(paidOffDays.getOfficeStaff().getId());
        paidOffDays.setOfficeStaff(officeStaff);

        //check if start working day is more than 24 months
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(officeStaff.getStartingDate());

        LocalDate now = LocalDate.now();
        LocalDate startWorking = LocalDate.of(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));

        Period period = Period.between(startWorking, now);

        //make sure that the staff passed 2 years and off days balance - requested days > 30
        Integer diffYear = period.getYears();
        if (diffYear < 2 || (officeStaff.getOffDaysBalance() - paidOffDays.getNumOfDays() < 30)) {
            paidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Pending);

            paidOffDays = paidOffDaysRepository.save(paidOffDays);

            OfficeStaff coo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
            String url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, paidOffDays.getId().toString() + "#Payroll_Add_PaidOffDays_Approval", String.valueOf(coo.getUser().getId()));
            String notes = NumberFormatter.formatNumber(paidOffDays.getNumOfDays()) + " days requested to be added to current salary. Current total number of days off is " + NumberFormatter.formatNumber(officeStaff.getOffDaysBalance());
            pendingManagerApprovalService.insertNewPendingApprovalRequest(officeStaff.getName(), DateUtil.formatFullDate(officeStaff.getStartingDate()), officeStaff.getSalaryWithCurrency(), officeStaff.getJobTitle() != null ? officeStaff.getJobTitle().getName() : "", officeStaff.getEmployeeManager(), coo, (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(paidOffDays.getAmount()), "New Paid Off Days", notes, url, paidOffDays.getId().toString() + "#Payroll_Add_PaidOffDays_Approval");
            return ResponseEntity.ok("Your request will be sent to Coo to be approved");
        } else {
            paidOffDays = paidOffDaysRepository.save(paidOffDays);
            publicPageService.approveAddPaidOffDays(paidOffDays.getId().toString(), true, "", false);
        }


        return ResponseEntity.ok(paidOffDays);
    }

    @Override
    protected ResponseEntity updateEntity(PaidOffDays paidOffDays) {
        OfficeStaff officeStaff = officeStaffRepository.findOne(paidOffDays.getOfficeStaff().getId());

        HistorySelectQuery<PaidOffDays> historySelectQuery = new HistorySelectQuery(PaidOffDays.class);
        historySelectQuery.filterBy("id", "=", paidOffDays.getId());
        historySelectQuery.sortBy("lastModificationDate", false, true);
        historySelectQuery.setLimit(1);
        List<PaidOffDays> oldList = historySelectQuery.execute();

        if (oldList == null || oldList.isEmpty())
            throw new BusinessException("Can't update");

        PaidOffDays old = oldList.get(0);

        if (!old.getCanEditDelete())
            throw new BusinessException("can't update it's already included in the last payroll");
        if (PaidOffDays.PaidOffDayStatus.Rejected.equals(old.getStatus()))
            throw new BusinessException("can't update rejected request");

        //get coo request if exists
        PendingApprovalRequest pendingApprovalRequest = pendingApprovalRequestRepository.findTopByTokenLoadOrderByIdDesc(paidOffDays.getId() + "#Payroll_Add_PaidOffDays_Approval");

        //simple case means that the request doesn't need approval from COO which mean that the staff has the right balance and more than 2 years
        boolean oldIsSimpleCase = pendingApprovalRequest == null;
        boolean newIsSimpleCase = true;
        boolean oldIsApproved = PaidOffDays.PaidOffDayStatus.Done.equals(old.getStatus());

        //check if start working day is more than 24 months
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(officeStaff.getStartingDate());
        LocalDate now = LocalDate.now();
        LocalDate startWorking = LocalDate.of(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, calendar.get(Calendar.DAY_OF_MONTH));
        Period period = Period.between(startWorking, now);
        int diffYear = period.getYears();

        //make sure that the staff passed 2 years and off days balance - requested days > 30
        if (diffYear < 2 || (officeStaff.getOffDaysBalance() - paidOffDays.getNumOfDays() + (PaidOffDays.PaidOffDayStatus.Done.equals(old.getStatus()) ? old.getNumOfDays() : 0) < 30))
            newIsSimpleCase = false;

        //both are simple cases
        if (oldIsSimpleCase && newIsSimpleCase) { //simple --> simple
            // return number of days to PaidOffDaysBalance days
            officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() - old.getNumOfDays());
            // Jirra ACC-1569 return paid off days to consumed off days balance
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - old.getNumOfDays());

            // deduct number of days from PaidOffDaysBalance days
            officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() + paidOffDays.getNumOfDays());
            // Jirra ACC-1569 add paid off days to consumed off days balance
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() + paidOffDays.getNumOfDays());

            // save changes
            officeStaffRepository.save(officeStaff);
            officeStaff = officeStaffRepository.findOne(officeStaff.getId());

            //Sending messages
            Map<String, String> paramValues = new HashMap<>();

            paramValues.put("employee_name", officeStaff.getFirstLastName());
            paramValues.put("old_requested_days", NumberFormatter.formatNumber(old.getNumOfDays()));
            paramValues.put("new_requested_paid_off", NumberFormatter.formatNumber(paidOffDays.getNumOfDays()));
            paramValues.put("previous_amount_of_paid_off_days", (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() +" " : "") + Math.round((officeStaff.getSalary() / 30.4) * old.getNumOfDays() * 100) / 100);
            paramValues.put("new_amount_of_paid_off_days", (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() +" " : "") + Math.round((officeStaff.getSalary() / 30.4) * paidOffDays.getNumOfDays() * 100) / 100);

//            messageTemplateService.sendMessageOrEmail("Edit Paid Off Days for employee.",
//                    officeStaff.getFinalManager(),
//                    "Payroll_Paid_Off_Days_Edited_For_Employee",
//                    paramValues);
            messagingService.send("Payroll_Paid_Off_Days_Edited_For_Employee", "Edit Paid Off Days for employee.",
                    null, officeStaff.getFinalManager(), paramValues, officeStaff, null, officeStaff.getFinalManager());

        } else if (oldIsSimpleCase) { //simple --> complicated
            // return number of days to PaidOffDaysBalance days
            officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() - old.getNumOfDays());
            // Jirra ACC-1569 return paid off days to consumed off days balance
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - old.getNumOfDays());

            // save changes
            officeStaffRepository.save(officeStaff);
            officeStaff = officeStaffRepository.findOne(officeStaff.getId());


            //set PaidOffDays status to pending
            paidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Pending);
            paidOffDays.setActionTaken(false);

            OfficeStaff coo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
            String url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, paidOffDays.getId().toString() + "#Payroll_Add_PaidOffDays_Approval", String.valueOf(coo.getUser().getId()));

            String notes = NumberFormatter.formatNumber(paidOffDays.getNumOfDays()) + " days requested to be added to current salary. Current total number of days off is " + NumberFormatter.formatNumber(officeStaff.getOffDaysBalance());
            pendingManagerApprovalService.insertNewPendingApprovalRequest(officeStaff.getName(), DateUtil.formatFullDate(officeStaff.getStartingDate()), officeStaff.getSalaryWithCurrency(), officeStaff.getJobTitle() != null ? officeStaff.getJobTitle().getName() : "", officeStaff.getEmployeeManager(), coo, (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(paidOffDays.getAmount()), "New Paid Off Days", notes, url, paidOffDays.getId().toString() + "#Payroll_Add_PaidOffDays_Approval");
        } else if (newIsSimpleCase) {//complicated --> simple
            //if already approved then return balance from old
            if (oldIsApproved) {
                // return number of days to PaidOffDaysBalance days
                officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() - old.getNumOfDays());
                // Jirra ACC-1569 return paid off days to consumed off days balance
                officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - old.getNumOfDays());
            }

            // deduct number of days from PaidOffDaysBalance days
            officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() + paidOffDays.getNumOfDays());
            // Jirra ACC-1569 add paid off days to consumed off days balance
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() + paidOffDays.getNumOfDays());

            // save changes
            officeStaffRepository.save(officeStaff);
            officeStaff = officeStaffRepository.findOne(officeStaff.getId());

            //modify PaidOffDays status
            paidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Done);
            paidOffDays.setActionTaken(true);

            //delete coo request
            pendingApprovalRequestRepository.delete(pendingApprovalRequest);

            //Sending messages
            Map<String, String> paramValues = new HashMap<>();

            paramValues.put("employee_name", officeStaff.getFirstLastName());
            paramValues.put("old_requested_days", NumberFormatter.formatNumber(old.getNumOfDays()));
            paramValues.put("new_requested_paid_off", NumberFormatter.formatNumber(paidOffDays.getNumOfDays()));
            paramValues.put("previous_amount_of_paid_off_days", (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() +" " : "") + Math.round((officeStaff.getSalary() / 30.4) * old.getNumOfDays() * 100) / 100);
            paramValues.put("new_amount_of_paid_off_days", (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().toString() +" " : "") + Math.round((officeStaff.getSalary() / 30.4) * paidOffDays.getNumOfDays() * 100) / 100);

//            messageTemplateService.sendMessageOrEmail("Edit Paid Off Days for employee.",
//                    officeStaff.getFinalManager(),
//                    "Payroll_Paid_Off_Days_Edited_For_Employee",
//                    paramValues);
            messagingService.send("Payroll_Paid_Off_Days_Edited_For_Employee", "Edit Paid Off Days for employee.",
                    null, officeStaff.getFinalManager(), paramValues, officeStaff, null, officeStaff.getFinalManager());

        } else {//!oldIsSimpleCase && !newIsSimpleCase //complicated --> complicated

            //if old is approved then modify balance and coo request
            if (oldIsApproved) {
                // return number of days to PaidOffDaysBalance days
                officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() - old.getNumOfDays());
                // Jirra ACC-1569 return paid off days to consumed off days balance
                officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - old.getNumOfDays());

                // save changes
                officeStaffRepository.save(officeStaff);
                officeStaff = officeStaffRepository.findOne(officeStaff.getId());

                //delete and create a new coo request
                pendingApprovalRequestRepository.delete(pendingApprovalRequest);

                OfficeStaff coo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
                String url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, paidOffDays.getId().toString() + "#Payroll_Add_PaidOffDays_Approval", String.valueOf(coo.getUser().getId()));

                String notes = NumberFormatter.formatNumber(paidOffDays.getNumOfDays()) + " days requested to be added to current salary. Current total number of days off is " + NumberFormatter.formatNumber(officeStaff.getOffDaysBalance());
                pendingManagerApprovalService.insertNewPendingApprovalRequest(officeStaff.getName(), DateUtil.formatFullDate(officeStaff.getStartingDate()), officeStaff.getSalaryWithCurrency(), officeStaff.getJobTitle() != null ? officeStaff.getJobTitle().getName() : "", officeStaff.getEmployeeManager(), coo, (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(paidOffDays.getAmount()), "New Paid Off Days", notes, url, paidOffDays.getId().toString() + "#Payroll_Add_PaidOffDays_Approval");
            }else {
                //update coo request amount & notes
                pendingApprovalRequest.setAmountWithCurrency((officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(paidOffDays.getAmount()));
                String notes = NumberFormatter.formatNumber(paidOffDays.getNumOfDays()) + " days requested to be added to current salary. Current total number of days off is " + NumberFormatter.formatNumber(officeStaff.getOffDaysBalance());
                pendingApprovalRequest.setNotes(notes);
                pendingApprovalRequestRepository.save(pendingApprovalRequest);
            }

            //update paidOffDays status
            paidOffDays.setStatus(PaidOffDays.PaidOffDayStatus.Pending);
            paidOffDays.setActionTaken(false);
        }

        return super.updateEntity(paidOffDays);
    }

    @Override
    protected ResponseEntity deleteEntity(PaidOffDays paidOffDays) {
        if (!paidOffDays.getCanEditDelete())
            throw new BusinessException("can't delete it's already included in the last payroll");

        if (PaidOffDays.PaidOffDayStatus.Done.equals(paidOffDays.getStatus())) {
            OfficeStaff officeStaff = officeStaffRepository.findOne(paidOffDays.getOfficeStaff().getId());

            // return number of days to PaidOffDaysBalance days
            officeStaff.setPaidOffDaysBalance(officeStaff.getPaidOffDaysBalance() - paidOffDays.getNumOfDays());
            // Jirra ACC-1569 return paid off days to consumed off days balance
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - paidOffDays.getNumOfDays());

            // save changes
            officeStaffRepository.save(officeStaff);

            //Sending messages
            Map<String, String> paramValues = new HashMap<>();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            paramValues.put("employee_name", officeStaff.getFirstLastName());
            paramValues.put("date_of_paid_off_days_addition", formatter.format(paidOffDays.getCreationDate()));

//            messageTemplateService.sendMessageOrEmail("Delete paid off days",
//                    officeStaff.getFinalManager(),
//                    "Payroll_Paid_Off_Days_Deleted_For_Employee",
//                    paramValues);
            messagingService.send("Payroll_Paid_Off_Days_Deleted_For_Employee", "Delete paid off days",
                    null, officeStaff.getFinalManager(), paramValues, officeStaff, null, officeStaff.getFinalManager());

        }

        PendingApprovalRequest pendingApprovalRequest = pendingApprovalRequestRepository.findTopByTokenLoadOrderByIdDesc(paidOffDays.getId() + "#Payroll_Add_PaidOffDays_Approval");
        if (pendingApprovalRequest != null) {
            pendingApprovalRequestRepository.delete(pendingApprovalRequest);
        }

        return super.deleteEntity(paidOffDays);
    }

    @Override
    public BaseRepository<PaidOffDays> getRepository() {
        return paidOffDaysRepository;
    }
}
