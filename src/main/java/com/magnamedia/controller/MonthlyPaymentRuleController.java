package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.PaymentRuleConfiguration;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.MaidType;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.MonthlyPaymentRuleRepository;
import com.magnamedia.repository.PaymentRuleConfigurationRepository;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.TransferFilesService;
import com.magnamedia.service.yayaapp.YayaAppNotificationService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static java.util.Calendar.MONTH;
import static java.util.Calendar.YEAR;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 6/11/2020
 **/
@RequestMapping("/monthlyRules")
@RestController
public class MonthlyPaymentRuleController extends BaseRepositoryController <MonthlyPaymentRule> {

    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    @Autowired
    private YayaAppNotificationService yayaAppNotificationService;

    @Override
    public BaseRepository<MonthlyPaymentRule> getRepository() {
        return monthlyPaymentRuleRepository;
    }

    @NoPermission
    @RequestMapping(value = "/getFirstPayrollDate", method = RequestMethod.GET)
    public ResponseEntity<?> getFirstPayrollDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.get(YEAR), calendar.get(MONTH), 1, 0, 0, 0);
        java.sql.Date payrollMonth = new java.sql.Date(calendar.getTimeInMillis());

        List<MonthlyPaymentRule> rules = monthlyPaymentRuleRepository.findByPayrollMonthAndPayrollType(payrollMonth, PayrollType.PRIMARY);
        if (rules == null || rules.size() == 0){
            throw new BusinessException("No Payroll Date Found");
        }

        return new ResponseEntity<>(rules.get(0).getPaymentDate(), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('monthlyRules','getMonthlyRules')")
    @RequestMapping(value = "/getMonthlyRules", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getMonthlyRules() {

        return new ResponseEntity<>(monthlyPaymentRuleRepository.findByFinished(false), HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    @Override
    public ResponseEntity<?> update(
            @RequestBody ObjectNode objectNode) throws IOException {
        if (checkPermission("update")) {
            boolean paymentDateUpdatedManually = false;
            MonthlyPaymentRule updated = parse(objectNode);

            MonthlyPaymentRule origin = getRepository().findOne(updated.getId());

            if(updated.getLockDate()!= null && updated.getLockDate().compareTo(updated.getPaymentDate()) > 0)
                throw new BusinessException("Lock Date must be less than or equal to Payment Date!");

            //PAY-818 add validation and send a notification for the maid if valid
            if (origin.getPayrollType().equals(PayrollType.PRIMARY) && origin.isTargetingExpats() && origin.getPaymentDate().compareTo(updated.getPaymentDate()) != 0) {
                int day = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_HOUSEMAID_PRIMARY_PAYMENT_DATE_VALIDATION_DAY));
                if(DateUtil.getDaysBetween(new java.sql.Date(new LocalDate(origin.getPayrollMonth()).withDayOfMonth(day).toDate().getTime()), new java.sql.Date(System.currentTimeMillis())) < 0) {
                    throw new BusinessException("You can't edit payment date before " + day + "th of payroll month");
                } else if (origin.getPaymentDate().compareTo(updated.getPaymentDate()) < 0){
                    paymentDateUpdatedManually = true;
                    // insert a new background task to notifyAllEligibleMaids
                    Setup.getApplicationContext()
                            .getBean(BackgroundTaskService.class)
                            .addDirectCallBackgroundTaskForEntity(
                                    "notifyAllEligibleMaidsBT", "messagingService", "payroll",
                                    "notifyAllEligibleMaidsBT",
                                    origin.getEntityType(), origin.getId(), false,
                                    false, new Class[]{Long.class, java.sql.Date.class}, new Object[]{origin.getId(), updated.getPaymentDate()});
                }
            }

            if (origin.isTargetingHousemaid()) {
                List<MonthlyPaymentRule> monthlyPaymentRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate
                        (origin.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, origin.getPayrollType(), origin.getLockDate());

                for (MonthlyPaymentRule rule : monthlyPaymentRules) {
                    if (!rule.getId().equals(origin.getId())) {
                        rule.setPaymentDate(updated.getPaymentDate());
                        rule.setLockDate(updated.getLockDate());
                        monthlyPaymentRuleRepository.save(rule);
                    }
                }
            }

            update(origin, updated, objectNode);

            if (paymentDateUpdatedManually)
                origin.setPaymentDateUpdatedManually(true);
            return super.updateEntity(origin);
        } else {
            return unauthorizedReponse();
        }
    }

    @PreAuthorize("hasPermission('monthlyRules','currentMonthHousemaidsLockDate')")
    @RequestMapping(value = "/currentMonthHousemaidsLockDate", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> currentMonthHousemaidsLockDate(@RequestParam(value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(date.getTime()));

        for(MonthlyPaymentRule monthlyPaymentRule: rules) {
            if(monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return ResponseEntity.ok(monthlyPaymentRule.getLockDate());
            }
        }
        java.sql.Date defaultDate = new java.sql.Date(PayrollGenerationLibrary.getPayrollEndDate(new LocalDate(date)).toDate().getTime());
        return ResponseEntity.ok(defaultDate);
    }

    //PAY-494 get the next payroll payment date regardless if primary or secondary (used by staff management and accounting)
    @NoPermission
    @RequestMapping("/getNextPaymentDate")
    public java.sql.Date getNextPaymentDate(@RequestParam(required = false, value = "date") java.sql.Date date,
                                            @RequestParam(required = false, value = "isPrimary") Boolean isPrimary,
                                            @RequestParam(required = false, value = "isMaidVisa", defaultValue = "false") Boolean isMaidVisa){
        List<MonthlyPaymentRule> monthlyPaymentRules;
        if (date == null)
            date = new java.sql.Date(System.currentTimeMillis());

        if(isMaidVisa)
            date = new java.sql.Date(DateUtil.addDays(date,1).getTime());

        if(isPrimary == null)
            monthlyPaymentRules = monthlyPaymentRuleRepository
                    .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(
                            date, null);
        else
            monthlyPaymentRules = monthlyPaymentRuleRepository
                    .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(
                            date, isPrimary.equals(true) ? PayrollType.PRIMARY : PayrollType.SECONDARY);

        Optional<MonthlyPaymentRule> filteredRules = monthlyPaymentRules.stream().filter(
                mpr -> mpr.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)
                        && mpr.getHousemaidTypeList()
                        .contains(MaidType.MAIDS_CC)).findFirst();

        MonthlyPaymentRule monthlyPaymentRule = filteredRules.orElse(null);

        return monthlyPaymentRule != null ? monthlyPaymentRule.getPaymentDate() : null;
    }


    @PreAuthorize("hasPermission('monthlyRules','generatePayrollAccountantTodoNew')")
    @RequestMapping(value = "/generatePayrollAccountantTodoNew/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> generatePayrollAccountantTodoNew(@PathVariable(name = "id") MonthlyPaymentRule monthlyPaymentRule,
                                                              @RequestParam(name = "type", defaultValue = "new", required = false) String type) throws Exception {
        if(monthlyPaymentRule.getFinished() || !monthlyPaymentRule.getAuditingFinished())
            throw  new BusinessException("Rule is not valid to generate Payroll Accountant Todo");

        try {
//            if("old".equals(type))
//                Setup.getApplicationContext().getBean(AccountantToDoService.class)
//                        .createAccountantToDoBasedOnPaymentRule(monthlyPaymentRule);
//            else
                Setup.getApplicationContext().getBean(AccountantToDoService.class)
                        .createAccountantToDoBasedOnPaymentRuleNew(monthlyPaymentRule);
        }catch (Exception ex){
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception in generatePayrollAccountantTodoNew: " + type, false);
            throw ex;
        }
        return ResponseEntity.ok("Done");
    }

    //todo: Debug API only
    @NoPermission
    @RequestMapping(value = "/getTargetListIds/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getTargetListIds(@PathVariable(name = "id") MonthlyPaymentRule monthlyPaymentRule) {
        List<Housemaid> housemaids = housemaidPayrollPaymentServiceV2.getTargetList(monthlyPaymentRule);
        List<Long> ids = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
        return ResponseEntity.ok(ids);
    }

    //todo: Debug API only
    @NoPermission
    @RequestMapping(value = "/getIncludedTargetListIds/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getIncludedTargetListIds(@PathVariable(name = "id") MonthlyPaymentRule monthlyPaymentRule) {
        List<Housemaid> housemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule);
        List<Long> ids = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
        return ResponseEntity.ok(ids);
    }

    //todo: Debug API only
    @NoPermission
    @RequestMapping(value = "/getTargetMVListIds/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getTargetMVListIds(@PathVariable(name = "id") MonthlyPaymentRule monthlyPaymentRule) {

        return ResponseEntity.ok(housemaidPayrollPaymentServiceV2.getPaidMVMaidsIdsNewV2(monthlyPaymentRule, new ArrayList<>(), false));
    }

}
