package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.SmsResponse;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.AirFareTicket;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.TravelDays;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.repository.AirFareTicketRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.TravelDaysRepository;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.message.MessagingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.*;

/**
 * <AUTHOR> Masod <<EMAIL>>
 * Created on Jan 28, 2020
 * Jirra ACC-1227
 */

@RequestMapping("/traveldays")
@RestController
public class TravelDaysController extends BaseRepositoryController<TravelDays> {

    @Autowired
    private TravelDaysRepository travelDaysRepository;
    @Autowired
    private OfficeStaffRepository officeStaffRepository;
    @Autowired
    private AirFareTicketRepository airFareTicketRepository;

    @Autowired
    private MessagingService messagingService;

    @Override
    protected ResponseEntity createEntity(TravelDays travelDays) {
        if (travelDays.getIncludeAirFareTicket() != null && travelDays.getIncludeAirFareTicket()) {
            Map map = availableAirFareTicketCount(travelDays.getOfficeStaff().getId());
            if ((Long) map.get("count") <= 0)
                throw new BusinessException("This employee isn't entitled to an airfare ticket yet, his last paid airfare ticket was on "
                        + map.get("lastTicketDate"));
        }

        if(travelDays.getEndDate().before(travelDays.getStartDate())) {
            throw new BusinessException("Start date can't be after end date!");
        }
        OfficeStaff officeStaff = officeStaffRepository.findOne(travelDays.getOfficeStaff().getId());

        // Jirra ACC-1472
        if (travelDays.getIncludeAirFareTicket() != null && travelDays.getIncludeAirFareTicket() && officeStaff.getAirfareTicketType() == null) {
            throw new BusinessException("attention: kindly update the employees profile to add the Airfare Ticket Type");
        }

        //check if count of consumed off days are equal to 0
        if (travelDays.getNumOfDays().equals(0L))
            throw new BusinessException("can't select weekly off day!");

        //Jirra Pay-2
        //set it as confirmed always
        travelDays.setConfirmed(true);

        // add number of days from consumed days
        if (travelDays.getConfirmed()) {
            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() + travelDays.getNumOfDays());
            officeStaffRepository.save(officeStaff);
        }
        //PAY-853
        if (travelDays.getIncludeAirFareTicket() != null && travelDays.getIncludeAirFareTicket()&& travelDays.getConfirmed())
            travelDays.setCheckDate(new Date());

        this.travelDaysRepository.save(travelDays);

        //save the airfare ticket if found and prepare form message send
        AirFareTicket airFareTicket = addAirfareTicketIfValid(officeStaff, travelDays);
        if (airFareTicket != null)
            airFareTicket = airFareTicketRepository.save(airFareTicket);

        // PAY-1198 //PAY-1305 stop sending for Emirates too
        return okResponse();

        //sending messages in case Employee Type is not  DUBAI_STAFF_EXPAT
//        String messageTemplateCode;
//        Map<String, String> paramValues = new HashMap<>();
//
//        String type = "";
//        if (airFareTicket != null) {
//            messageTemplateCode = "Payroll_Vacation_Added_With_Airfare_Ticket";
//            paramValues.put("airfare_ticket_amount", airFareTicket.getTicketAmount() != null ? airFareTicket.getTicketAmount().toString() : "");
//            type = "Vacation added with an airfare ticket";
//        } else {
//            messageTemplateCode = "Payroll_Vacation_Added_Off_Days";
//            type = "Vacation added (Off days)";
//        }
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//        paramValues.put("employee_name", officeStaff.getFirstLastName());
//        paramValues.put("vacation_start_date", formatter.format(travelDays.getStartDate()));
//        paramValues.put("vacation_end_date", formatter.format(travelDays.getEndDate()));
//
//        messagingService.sendSmsToOfficeStaffManagers(officeStaff, messageTemplateCode, type, paramValues);
//
//        return okResponse();
    }


//    @Override
//    protected ResponseEntity updateEntity(TravelDays travelDays) {
//
//        HistorySelectQuery<TravelDays> historySelectQuery = new HistorySelectQuery(TravelDays.class);
//        historySelectQuery.join("officeStaff");
//        historySelectQuery.filterBy("id", "=", travelDays.getId());
//        historySelectQuery.sortBy("lastModificationDate", false, true);
//        historySelectQuery.setLimit(1);
//        List<TravelDays> oldList = historySelectQuery.execute();
//
//        if (oldList == null || oldList.isEmpty())
//            throw new RuntimeException("Can't update");
//
//
//        TravelDays old = oldList.get(0);
//        Long oldOffDaysCount= old.getNumOfDays();
//
//        if (travelDays.getIncludeAirFareTicket() && travelDays.getConfirmed()) {
//            Map map = availableAirFareTicketCount(travelDays.getOfficeStaff().getId());
//            if ((Long) map.get("count") <= 0)
//                throw new RuntimeException("This employee isn't entitled to an airfare ticket yet, his last paid airfare ticket was on "
//                        + map.get("lastTicketDate"));
//        }
//
//        OfficeStaff officeStaff = officeStaffRepository.findOne(travelDays.getOfficeStaff().getId());
//
//        // Jirra ACC-1472
//        if (travelDays.getIncludeAirFareTicket() && officeStaff.getAirfareTicketType() == null) {
//            throw new RuntimeException("attention: kindly update the employees profile to add the Airfare Ticket Type");
//        }
//
//        //check if count of consumed off days are equal to 0
//        if(travelDays.getNumOfDays().equals(0L))
//            throw new RuntimeException("can't select weekly off day!");
//
//        // add number of days from consumed days
//        if (travelDays.getConfirmed())
//            officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - oldOffDaysCount + travelDays.getNumOfDays());
//
//        AirFareTicket airFareTicket = addAirfareTicketIfValid(officeStaff, travelDays);
//
//        // save changes
//        officeStaffRepository.save(officeStaff);
//
//        if (airFareTicket != null) {
//            airFareTicketRepository.save(airFareTicket);
//        }
//
//        return super.updateEntity(travelDays);
//    }

    @NoPermission
    @RequestMapping(
            value = {"/update"},
            method = {RequestMethod.POST}
    )
    @ResponseBody
    @JsonView({ViewScope.Normal.class})
    @Transactional
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (this.checkPermission("update")) {
            TravelDays updated = this.parse(objectNode);
            TravelDays origin = this.getRepository().findOne(updated.getId());

            if(updated.getEndDate().before(updated.getStartDate())) {
                throw new BusinessException("Start date can't be after end date!");
            }

            Long oldOffDaysCount = origin.getNumOfDays();

            if (isIncludeAirfareTicket(updated) && !isIncludeAirfareTicket(origin) && updated.getConfirmed()) {
                Map map = availableAirFareTicketCount(updated.getOfficeStaff().getId());
                if ((Long) map.get("count") <= 0)
                    throw new BusinessException("This employee isn't entitled to an airfare ticket yet, his last paid airfare ticket was on "
                            + map.get("lastTicketDate"));
            }

            OfficeStaff officeStaff = officeStaffRepository.findOne(updated.getOfficeStaff().getId());

            // Jirra ACC-1472
            if (updated.getIncludeAirFareTicket() != null && updated.getIncludeAirFareTicket() && officeStaff.getAirfareTicketType() == null) {
                throw new BusinessException("attention: kindly update the employees profile to add the Airfare Ticket Type");
            }

            //check if count of consumed off days are equal to 0
            if (updated.getNumOfDays().equals(0L))
                throw new BusinessException("can't select weekly off day!");

            if(isIncludeAirfareTicket(origin) && !isIncludeAirfareTicket(updated)) {
                removeAirfareTicket(origin);
            }

            // add number of days from consumed days
            if (updated.getConfirmed())
                officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - oldOffDaysCount + updated.getNumOfDays());

            boolean shouldUpdateCheckDate = false;
            if(!isIncludeAirfareTicket(origin) && isIncludeAirfareTicket(updated)) {
                updated.setCheckDate(origin.getCreationDate());
                shouldUpdateCheckDate = true;
                AirFareTicket airFareTicket = addAirfareTicketIfValid(officeStaff, updated);

                if (airFareTicket != null) {
                    airFareTicketRepository.save(airFareTicket);
                }
            }

            // save changes
            officeStaffRepository.save(officeStaff);


            // PAY-1198
            if (!OfficeStaffType.DUBAI_STAFF_EXPAT.equals(officeStaff.getEmployeeType())) {
                //sending messages
                String messageTemplateCode;
                Map<String, String> paramValues = new HashMap<>();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                if (origin.getStartDate().compareTo(updated.getStartDate()) != 0 || origin.getEndDate().compareTo(updated.getEndDate()) != 0) {
                    messageTemplateCode = "Payroll_Vacation_Edit_Added_Off_Days";
                    paramValues.put("employee_name", officeStaff.getFirstLastName());
                    paramValues.put("vacation_start_date", formatter.format(updated.getStartDate()));
                    paramValues.put("vacation_end_date", formatter.format(updated.getEndDate()));

                    messagingService.sendSmsToOfficeStaffManagers(officeStaff, messageTemplateCode, "Edit added Vacation (Off days)", paramValues);

                }

                if (origin.getIncludeAirFareTicket() != null && origin.getIncludeAirFareTicket() &&
                        (updated.getIncludeAirFareTicket() == null || !updated.getIncludeAirFareTicket())) {
                    messageTemplateCode = "Payroll_Vacation_Edit_And_Removed_Airfare_Ticket";
                    paramValues.put("employee_name", officeStaff.getFirstLastName());
                    paramValues.put("vacation_start_date", formatter.format(updated.getStartDate()));
                    paramValues.put("vacation_end_date", formatter.format(updated.getEndDate()));

                    messagingService.sendSmsToOfficeStaffManagers(officeStaff, messageTemplateCode, "Edit added vacation and removed the airfare ticket", paramValues);

                }
            }

            this.update(origin, updated, objectNode);

            if (shouldUpdateCheckDate)
                origin.setCheckDate(origin.getCreationDate());

            return this.updateEntity(origin);
        } else {
            return this.unauthorizedReponse();
        }
    }

    @Override
    protected ResponseEntity deleteEntity(TravelDays travelDays) {
        if (!travelDays.getIsDeletable())
            throw new BusinessException("it can't be deleted");

        TravelDays old = travelDaysRepository.findOne(travelDays.getId());
        OfficeStaff officeStaff = officeStaffRepository.findOne(travelDays.getOfficeStaff().getId());
        officeStaff.setConsumedOffDaysBalance(officeStaff.getConsumedOffDaysBalance() - old.getNumOfDays());

        // save changes
        officeStaffRepository.save(officeStaff);

        if(isIncludeAirfareTicket(travelDays)) {
            removeAirfareTicket(travelDays);
        }

        //sending messages
        Map<String, String> paramValues = new HashMap<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        paramValues.put("employee_name", officeStaff.getFirstLastName());
        paramValues.put("vacation_start_date", formatter.format(travelDays.getStartDate()));
        paramValues.put("vacation_end_date", formatter.format(travelDays.getEndDate()));

        messagingService.sendSmsToOfficeStaffManagers(officeStaff, "Payroll_Vacation_Deleted", "Delete added vacation", paramValues);

        return super.deleteEntity(travelDays);
    }

    @PreAuthorize("hasPermission('traveldays','availableAirFareTicket')")
    @RequestMapping(value = "/availableAirFareTicket/{officeStaffId}")
    public ResponseEntity checkIfAirFareTicketIsAvailable(@PathVariable("officeStaffId") Long officeStaffId) {
        Map result = availableAirFareTicketCount(officeStaffId);

        return new ResponseEntity(result, HttpStatus.OK);
    }

    private Map availableAirFareTicketCount(Long officeStaffId) {
        OfficeStaff officeStaff = officeStaffRepository.findOne(officeStaffId);

        SelectQuery selectQuery = new SelectQuery(AirFareTicket.class);
        selectQuery.filterBy("travelDays.officeStaff", "=", officeStaff);
        selectQuery.filterBy("travelDays.confirmed", "=", true);
        selectQuery.sortBy("ticketDate", false, true);
        List<AirFareTicket> airFareTicketList = selectQuery.execute();

        Calendar startWorkingCalendar = Calendar.getInstance();
        startWorkingCalendar.setTime(officeStaff.getStartingDate());
        startWorkingCalendar.add(Calendar.DAY_OF_MONTH, 1);

        LocalDate now = LocalDate.now();
        LocalDate startWorking = LocalDate.of(startWorkingCalendar.get(Calendar.YEAR), startWorkingCalendar.get(Calendar.MONTH) + 1,
                startWorkingCalendar.get(Calendar.DAY_OF_MONTH));

        Period period = Period.between(startWorking, now);
        Integer diff = period.getYears();

        Map result = new HashMap();
        result.put("count", (diff.longValue() / 2) - airFareTicketList.size());
        result.put("lastTicketDate", airFareTicketList.size() > 0 ? DateUtil.formatClientFullDate(airFareTicketList.get(0).getTicketDate()) : "");

        return result;
    }

    private AirFareTicket addAirfareTicketIfValid(OfficeStaff officeStaff, TravelDays travelDays) {
        if (travelDays.getIncludeAirFareTicket() == null || !travelDays.getIncludeAirFareTicket() || !travelDays.getConfirmed())
            return null;

        AirFareTicket airFareTicket = new AirFareTicket();
        airFareTicket.setTicketDate(travelDays.getStartDate());
        airFareTicket.setTravelDays(travelDays);

        //Jirra ACC-1472
        airFareTicket.setAirfareTicketType(officeStaff.getAirfareTicketType());
        airFareTicket.setTicketAmount(officeStaff.getAirfareTicketType().getAmount());

        return airFareTicket;
    }

    private void removeAirfareTicket(TravelDays travelDays) {
        AirFareTicket ticket = Setup.getRepository(AirFareTicketRepository.class).findFirstByTravelDays(travelDays);
        if(ticket != null)  Setup.getRepository(AirFareTicketRepository.class).delete(ticket);
    }

    public BaseRepository<TravelDays> getRepository() {
        return travelDaysRepository;
    }

    private boolean isIncludeAirfareTicket(TravelDays travelDays) {
        return travelDays.getIncludeAirFareTicket() != null && travelDays.getIncludeAirFareTicket();
    }
}
