package com.magnamedia.controller;

import com.aspose.words.Document;
import com.aspose.words.ImageSaveOptions;
import com.aspose.words.PageSet;
import com.aspose.words.SaveFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.itextpdf.html2pdf.HtmlConverter;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.whatsapp.LivePersonCampaignResponse;
import com.magnamedia.core.helper.whatsapp.WhatsappCampaignService;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.core.type.SmsReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.HousemaidPaymentSlipProjection;
import com.magnamedia.entity.projection.HousemaidPayslipProjection;
import com.magnamedia.entity.projection.OfficeStaffPaymentSlipProjection;
import com.magnamedia.extra.*;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.PayslipsScheduledJob;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import com.magnamedia.service.payroll.generation.newversion.OfficeStaffPayrollAuditService;
import com.magnamedia.service.payslip.PayslipService;
import com.magnamedia.service.payslip.PayslipTranslateService;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
/**
 * <AUTHOR> Abbas <<EMAIL>>
 * <AUTHOR> Kanaan <<EMAIL>>
 */
@RequestMapping("/payslip")
@RestController
public class PaySlipsController extends BaseRepositoryController<Housemaid> {

    @Autowired
    private HousemaidRepository housemaidRep;

    @Autowired
    ProjectionFactory projectionFactory;

    @Autowired
    FbPaySlipsLogRepository fbPaySlipsLogRepository;

    @Autowired
    private MailService mailService;

    @Autowired
    public PicklistItemRepository itemRep;

    @Autowired
    public HousemaidForgivenessRepository forgivenessRep;

    @Autowired
    public RepaymentRepository repaymentRep;

    @Autowired
    public EmployeeLoanRepository employeeLoanRep;

    @Autowired
    public MonthlyPayrollRepository payrollRep;

    @Autowired
    public MonthlyPayrollDocumentRepository payrollDocumentRep;

    @Autowired
    public MonthlyPayrollRepository monthlyPayrollRep;

    public PayslipsScheduledJob payslipsScheduledJob;

    //ACC-286
    @Autowired
    public SmsService smsService;

    @Autowired
    public Shortener shortener;

    @Autowired
    private YayapotMsgHelper yayapotMsgHelper;

    @Autowired
    private HousemaidPsidRepository housemaidPsidRepository;

    @Autowired
    private PayslipService payslipService;

    @Override
    public BaseRepository<Housemaid> getRepository() {
        return housemaidRep;
    }

    private static final String  SENDING_PAYSLIPS_TASK_NAME = "SendingHousemaidsPayslips";


    //***********************Housemaids PaySlips ***********************************

    @PreAuthorize("hasPermission('paySlip','housemaidPaymentSlip')")
    @RequestMapping("/housemaidPaymentSlip")
    public ResponseEntity housemaidPaymentSlip(HttpServletResponse response,
                                               @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                               @RequestParam(value = "Name", required = false) String maidName,
                                               Pageable pageable) throws FileNotFoundException {

        //Payroll start and end date
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);

        //Get Housemaids
        //Jirra ACC-1223
        SelectQuery<Housemaid> query = PayrollGenerationLibrary.getPayrollHousemaidsQuery(payrollStart, payrollEnd);
//        query.filterBy("startDate", "<", payrollEnd.plusDays(1).toDate());
//        query.filterBy(
//                new SelectFilter()
//                        .or("status", "=", HousemaidStatus.WITH_CLIENT)
//                        .or("status", "=", HousemaidStatus.VIP_RESERVATIONS)
//                        .or("status", "=", HousemaidStatus.AVAILABLE)
//                        .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
//                        .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
//                        .or("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
//                        .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
//                        .or("status", "=", HousemaidStatus.ON_VACATION)
//                        .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
//                        .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
//                        .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
//        );

        if (maidName != null && !maidName.isEmpty()) {
            query.filterBy("name", "like", "%" + maidName + "%");
        }
//        query.filterBy(
//                new SelectFilter()
//                        .or("excludedFromPayroll", "=", false)
//                        .or("excludedFromPayroll", "IS NULL", null)
//        );

        List<Housemaid> housemaids = query.execute(pageable).getContent();
//        housemaids = housemaids.stream().filter(x -> !(x.getStatus() != null && !x.getStatus().equals(HousemaidStatus.WITH_CLIENT)
//                && x.getStartDate() != null
//                && (x.getStartDate().equals(firstDayThisMonth) || x.getStartDate().after(firstDayThisMonth.toDate()))
//                && x.getStartDate().before(lastDayThisMonth.plusDays(1).toDate())))
//                .collect(Collectors.toList());
        List<Housemaid> housemaidsTemp = query.execute();
//        housemaidsTemp=housemaidsTemp.stream().filter(x->!(x.getStatus()!=null&&!x.getStatus().equals(HousemaidStatus.WITH_CLIENT)
//                        &&x.getStartDate()!=null
//                        && (x.getStartDate().equals(firstDayThisMonth) || x.getStartDate().after(firstDayThisMonth.toDate()))
//                        &&x.getStartDate().before(lastDayThisMonth.plusDays(1).toDate())))
//                        .collect(Collectors.toList());
        System.out.println(housemaids.size());
        List<HousemaidPayrollBean> result = new ArrayList<HousemaidPayrollBean>();

        PicklistItem vacationType = getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE, "pre-paid_vacation");
        PicklistItem noKidsDeductionReason = getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE, "prefer_to_have_a_family_with_no_kids");

        List<Housemaid> finalHousemaids = new ArrayList<Housemaid>();
        for (Housemaid housemaid : housemaids) {
            //Filter maids not on vacation this month
            List<ScheduledAnnualVacation> annualVacations = housemaid.getScheduledAnnualVacations();
            boolean flag = false;
            if (annualVacations != null && annualVacations.size() > 0) {

                for (ScheduledAnnualVacation vacation : annualVacations) {
                    LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
                    if ((payrollDueDate.equals(payrollStart.minusMonths(1)) || payrollDueDate.isAfter(payrollStart.minusMonths(1)))
                            && (payrollDueDate.equals(payrollEnd.minusMonths(1)) || payrollDueDate.isBefore(payrollEnd.minusMonths(1)))
                            && vacation.getType() != null && vacation.getType().getCode().equals(vacationType.getCode()) && vacation.getAmount() != null && vacation.getAmount() > 0.0) {
                        flag = true;
                        break;
                    }
                }
            }
            if (flag) {
                continue;
            } else {
                finalHousemaids.add(housemaid);
            }
        }
        //Get Exit Maids
//        LocalDate arraD1=new LocalDate("2017-10-22");
//        LocalDate arraD2=new LocalDate("2017-10-28");
//        LocalDate arraD3=new LocalDate("2017-10-31");
//        SelectQuery<Ticket> ticketQuery = new SelectQuery<>(Ticket.class);
//        ticketQuery.leftJoin("housemaid");
//        ticketQuery.filterBy(new SelectFilter()
//                            .or( new SelectFilter().and("ticketType",
//                                    "=",
//                                    Ticket.TicketType.TO_EXIT)
//                            .and(new SelectFilter().and("arrivalDate",
//                                    ">=",
//                                    arraD1.toDate()))
//                            ).or( new SelectFilter().and("ticketType",
//                                    "=",
//                                    Ticket.TicketType.TO_DUBAI)
//                            .and(new SelectFilter().and("arrivalDate",
//                                    ">=",
//                                    arraD2.toDate()))));
//        ticketQuery.filterBy("housemaid.isAgency","=",false);
//        ticketQuery.filterBy("housemaid.freedomMaid","=",false);
//        List<Ticket> exitMaidsTickets=ticketQuery.execute();
//
//        //Get Agency maids
//        SelectQuery<Ticket> ticketQuery2 = new SelectQuery<>(Ticket.class);
//        ticketQuery2.leftJoin("housemaid");
//        ticketQuery2.filterBy("ticketType",
//                                    "=",
//                                    Ticket.TicketType.TO_DUBAI);
//        ticketQuery2.filterBy("arrivalDate",
//                                    ">=",
//                                    arraD2.toDate());
//        ticketQuery2.filterBy("housemaid.isAgency","=",true);
//        List<Ticket> agencyMaidsTickets=ticketQuery2.execute();

        //Clean exit maids
//        List<Housemaid> cleanExitMaids=finalHousemaids.stream().filter(x->x.getCleanExitMaid()!=null&&x.getCleanExitMaid()).collect(Collectors.toList());
        //Freedome housemaids
//        List<Housemaid> freedomHousemaids=finalHousemaids.stream().filter(x->x.getFreedomMaid()
//                &&x.getFreedomSource()!=null&&
//                x.getCreationDate().after(new LocalDate("2017-10-26").toDate())).collect(Collectors.toList());
        for (Housemaid housemaid : finalHousemaids) {
            List<ScheduledAnnualVacation> annualVacations = housemaid.getScheduledAnnualVacations();
            HousemaidPayrollBean bean = new HousemaidPayrollBean();

            bean.setHousemaidName(housemaid.getName());
            if (housemaid.getStartDate() != null) {
                bean.setStartingDate(new java.sql.Date(housemaid.getNewStartDate().getTime()));
            }
            bean.setPaySlipDate(new java.sql.Date(dt.toDate().getTime()));

            //Loan Repayment
            List<Repayment> repaymentList = housemaid.getRepaymentsList();
            bean.setLoanRepayment(0.0);
            if (housemaid.getStartDate() != null) {
                LocalDate startDate = new LocalDate(housemaid.getStartDate());
                if (startDate.isBefore(payrollStart)) {
                    if (repaymentList.size() > 0) {
                        Double amount = 0.0;
                        Date d1 = payrollStart.toDate();
                        Date d2 = payrollEnd.plusDays(1).toDate();
                        List<Repayment> tempRepaymentList = repaymentList.stream().filter(x -> x.getAmount() > 0.0
                                && (x.getRepaymentDate().before(d2))
                                && (!x.getRepaymentDate().after(d1))
                                && !x.getExculdedFromPayroll() && x.getPaidRepayment()
                        ).collect(Collectors.toList());

                        if (tempRepaymentList.size() > 0) {
                            for (Repayment rep : tempRepaymentList) {
                                amount += rep.getAmount();
                            }
                            bean.setLoanRepayment(amount);
                        }
                    }
                }
            }
            bean.setRepaymentToday(bean.getLoanRepayment());
            Double forgivenessSum = forgivenessRep.sumForgivenessByMaid(housemaid, payrollEnd.plusDays(1).toDate());
            Double repaymentSum = repaymentRep.sumRepaymentsByMaid(housemaid, payrollEnd.plusDays(1).toDate());
            Double loansSum = employeeLoanRep.sumLoansByMaid(housemaid, payrollEnd.plusDays(1).toDate());

            if (loansSum == null) {
                loansSum = 0.0;
            }
            if (forgivenessSum == null) {
                forgivenessSum = 0.0;
            }
            if (repaymentSum == null) {
                repaymentSum = 0.0;
            }

            bean.setRemainingLoanBalance(String.valueOf(loansSum - forgivenessSum - repaymentSum));

            //Start date deduction
            if (housemaid.getStartDate() != null) {
                LocalDate startDate = new LocalDate(housemaid.getStartDate());
                if (startDate.isAfter(payrollEnd.withDayOfMonth(1)) && housemaid.getBasicSalary() != null) {
                    Double deduction = (housemaid.getBasicSalary() / 30.4)
                            * (Math.abs((Days.daysBetween(startDate, payrollEnd.withDayOfMonth(1))).getDays()));
                    bean.setStartDateDeduction(deduction);
                } else {
                    bean.setStartDateDeduction(0.0);
                }
            } else {
                bean.setStartDateDeduction(0.0);
            }
            //Warning Letters
            List<WarningLetter> warningLettersList = housemaid.getWarningLetters();
            if (warningLettersList != null && warningLettersList.size() > 0) {
                Double sum = 0.0;
                for (WarningLetter temp : warningLettersList) {
                    LocalDate letterDate = new LocalDate(temp.getDate());
                    if ((letterDate.equals(payrollStart) || letterDate.isAfter(payrollStart)
                            && (letterDate.equals(payrollEnd) || letterDate.isBefore(payrollEnd)))) {
                        sum += temp.getDeduction();
                    }
                }
                bean.setComplaintDeduction(sum);
            } else {
                bean.setComplaintDeduction(0.0);
            }

            //Manager Deduction && Addittions
            List<PayrollManagerNote> managerNotesList = housemaid.getManagerNotes();
            if (managerNotesList.size() > 0) {
                Double addtionSum = 0.0;
                Double deductionSum = 0.0;
                Double noKidsDeductionSum = 0.0;
                for (PayrollManagerNote temp : managerNotesList) {
                    LocalDate noteDate = new LocalDate(temp.getNoteDate());
                    if ((noteDate.equals(payrollStart) || noteDate.isAfter(payrollStart)
                            && (noteDate.equals(payrollEnd) || noteDate.isBefore(payrollEnd)))
                            && temp.getNoteType() == PayrollManagerNote.ManagerNoteType.ADDITION) {
                        addtionSum += temp.getAmount();
                    }
                    if ((noteDate.equals(payrollStart) || noteDate.isAfter(payrollStart)
                            && (noteDate.equals(payrollEnd) || noteDate.isBefore(payrollEnd)))
                            && temp.getNoteType() == PayrollManagerNote.ManagerNoteType.DEDUCTION) {
                        if (temp.getDeductionReason() != null && temp.getDeductionReason().getId().equals(noKidsDeductionReason.getId())) {
                            noKidsDeductionSum += temp.getAmount();
                        } else {
                            deductionSum += temp.getAmount();
                        }
                    }

                }
                bean.setManagerAddition(addtionSum);
                bean.setManagerDeduction(deductionSum);
                bean.setNoKidsDeduction(noKidsDeductionSum.toString());
            } else {
                bean.setManagerAddition(0.0);
                bean.setManagerDeduction(0.0);
                bean.setNoKidsDeduction("0");
            }

            //Basic Salary
            bean.setBasicSalary(housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0);

            //Working Vacation Pay && Vacation airfare
//           Double workingSum=0.0;
            Double airfareSum = 0.0;
            for (ScheduledAnnualVacation vacation : annualVacations) {
                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
                if ((payrollDueDate.equals(payrollStart) || payrollDueDate.isAfter(payrollStart))
                        && (payrollDueDate.equals(payrollEnd) || payrollDueDate.isBefore(payrollEnd))) {
//                                                        if(vacation.getType().getCode().toLowerCase().contains("working"))
//                                                            workingSum+=vacation.getAmount();
//                                                        else if(vacation.getType().getCode().toLowerCase().contains("airfare"))
//                                                            airfareSum+=vacation.getAmount();
                    if (vacation.getType().getCode().toLowerCase().contains("airfare")) {
                        airfareSum += vacation.getAmount();
                    }
                }
            }
//                                                bean.setWorkingVacationPay(workingSum);
            bean.setVacationAirFare(airfareSum);

            //Living place && Food allowance
            if (housemaid.getLiving() == HousemaidLiveplace.OUT) {
                bean.setLiving(HousemaidLiveplace.OUT);
                bean.setFoodAllowance(250.0);
            } else {
                bean.setLiving(HousemaidLiveplace.IN);
                bean.setFoodAllowance(0.0);
            }

            //Housing
            if (housemaid.getHousingAllowance() != null) {
                bean.setHousingAllowance(housemaid.getHousingAllowance().toString());
            } else {
                bean.setHousingAllowance("0.0");
            }
            bean.setIncomeVariableComponent("0.0");
            org.joda.time.LocalDate payrollEndDate = LocalDate.parse(payrollEnd.withDayOfMonth(1).toString());
            if(payrollEndDate != null) {
                bean.setPayStartDate(new java.sql.Date(payrollEndDate.toDate().getTime()));
                bean.setPayEndDate(new java.sql.Date(payrollEnd.dayOfMonth().withMaximumValue().toDate().getTime()));
                bean.setDaysInPeriod(Math.abs(DateUtil.getDaysBetween(bean.getPayEndDate(), bean.getPayStartDate())) + 1);
            }

            // Defaul Monthly repayment
//                bean.setRepaymentToday(housemaid.getDefaulMonthlyRepayment() != null ? housemaid.getDefaulMonthlyRepayment() : 0.0);
//                if(!exitMaidsTickets.stream().filter(x->x.getHousemaid().getId().equals(housemaid.getId())).collect(Collectors.toList()).isEmpty()
//                        ||!agencyMaidsTickets.stream().filter(x->x.getHousemaid().getId().equals(housemaid.getId())).collect(Collectors.toList()).isEmpty()
//                        ||!freedomHousemaids.stream().filter(x->x.getId().equals(housemaid.getId())).collect(Collectors.toList()).isEmpty()
//                        ||!cleanExitMaids.stream().filter(x->x.getId().equals(housemaid.getId())).collect(Collectors.toList()).isEmpty())
//                    bean.setBasicSalaryBreakDown(getSalaryBreakDown(housemaid));
//                else{
            BasicSalaryBreakDown brd = new BasicSalaryBreakDown();
            if (housemaid.getPrimarySalary() != null) {
                brd.setBasicSalary(housemaid.getPrimarySalary());
            }
            brd.setOverTime(housemaid.getOverTime() != null ? housemaid.getOverTime() : 0.0);
            brd.setHolidayPay(housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0);
            brd.setMonthlyLoan(housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0.0);
            brd.setAirFare(housemaid.getAirfareFee() != null ? housemaid.getAirfareFee() : 0.0);
            bean.setBasicSalaryBreakDown(brd);
//                }
            result.add(bean);

        }

        PageImpl page = new PageImpl(result, pageable, housemaidsTemp.size());
        return new ResponseEntity<>(page.map(maid
                -> projectionFactory.createProjection(HousemaidPaymentSlipProjection.class,
                maid)), HttpStatus.OK);

    }
    //    public BasicSalaryBreakDown getSalaryBreakDown(Housemaid housemaid) {
//        BasicSalaryBreakDown brd = new BasicSalaryBreakDown();
//        PicklistItem ethiopian = getItem("nationalities", "ethiopian");
//        PicklistItem filipino = getItem("nationalities", "philippines");
//        PicklistItem sri_lanka = getItem("nationalities", "sri_lanka");
//        PicklistItem sri_lankan = getItem("nationalities", "sri_lankan");
//        if (housemaid.getBasicSalary() != null) {
//            brd.setBasicMonthlhPay(housemaid.getBasicSalary());
//        }
//        if (housemaid.getCleanExitMaid() != null && housemaid.getCleanExitMaid() == Boolean.TRUE) {
//            brd.setOverTime(409.0);
//            brd.setHolidayPay(58.0);
//            brd.setMonthlyLoan(50.0);
//            brd.setAirFare(83.0);
//        } else if (housemaid.getFreedomMaid() && housemaid.getFreedomSource() != null) {
//            if (housemaid.getNationality() != null) {
//                if (filipino.getId().equals(housemaid.getNationality().getId())) {
//                    brd.setOverTime(309.0);
//                    brd.setHolidayPay(58.0);
//                    brd.setMonthlyLoan(50.0);
//                    brd.setAirFare(83.0);
//                } else if (ethiopian.getId().equals(housemaid.getNationality().getId())
//                        || sri_lanka.getId().equals((housemaid.getNationality().getId()))
//                        || sri_lankan.getId().equals(housemaid.getNationality().getId())) {
//                    brd.setOverTime(208.0);
//                    brd.setHolidayPay(37.0);
//                    brd.setMonthlyLoan(32.0);
//                    brd.setAirFare(83.0);
//                } else {
//                    brd.setOverTime(247.0);
//                    brd.setHolidayPay(50.0);
//                    brd.setMonthlyLoan(43.0);
//                    brd.setAirFare(83.0);
//                }
//
//            } else {
//                brd.setOverTime(247.0);
//                brd.setHolidayPay(50.0);
//                brd.setMonthlyLoan(43.0);
//                brd.setAirFare(83.0);
//            }
//        } else if (housemaid.isIsAgency()) {
//            brd.setOverTime(0.0);
//            brd.setHolidayPay(0.0);
//            brd.setMonthlyLoan(0.0);
//            brd.setAirFare(0.0);
//        } else {// Exit Maid
//            brd.setOverTime(308.0);
//            brd.setHolidayPay(64.0);
//            brd.setMonthlyLoan(55.0);
//            brd.setAirFare(83.0);
//        }
//        return brd;
//    }
    //************************** OfficeStaff payslips***************************

    @Autowired
    private OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2;
    @Autowired
    private LockDateService lockDateService;

    private int getOfficeStaffPayslipBeans(String type, Date date,
                                           List<OfficeStaffPayrollBean> result,
                                           List<Long> employeesIds,
                                           Pageable pageable) {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollMonth = dt.withDayOfMonth(1);

        List<OfficeStaffPayrollLog> staffPayrollLogs;

        if(employeesIds != null && !employeesIds.isEmpty())
            staffPayrollLogs = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findReadyToGeneratePayslipsLogs(new java.sql.Date(payrollMonth.toDate().getTime()), employeesIds);
        else {
            staffPayrollLogs = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findReadyToGeneratePayslipsLogs(new java.sql.Date(payrollMonth.toDate().getTime()));
        }

        staffPayrollLogs = staffPayrollLogs.stream().filter(log -> {
            PayrollAccountantTodoType todoType = PayrollAccountantTodoType
                    .valueOf(log.getPayrollAccountantTodo().getTaskName());

            if (todoType == PayrollAccountantTodoType.PENSION_AUTHORITY
                    || todoType == PayrollAccountantTodoType.SENDING_TRANSACTIONS) {
                return false;
            }

            if (!type.equals("All")) {
                return log.getOfficeStaff().getEmployeeType().equals(OfficeStaffType.valueOf(type));
            }
            return true;
        }).collect(Collectors.toList());

        int total = staffPayrollLogs.size();

        if(pageable != null) {
            int start = pageable.getPageSize() * pageable.getPageNumber();
            int end = Math.min(total, start + pageable.getPageSize());
            staffPayrollLogs = staffPayrollLogs.subList(start, end);
        }

        for (OfficeStaffPayrollLog log : staffPayrollLogs) {

            OfficeStaffPayrollBean bean = log.getOfficeStaffPayrollBean();

            result.add(bean);
        }

        return total;
    }

    @PreAuthorize("hasPermission('payslip','officeStaffPaySlip')")
    @RequestMapping("/officeStaffPaySlip")
    public ResponseEntity<?> officeStaffPaySlip(HttpServletResponse response,
                                                @RequestParam(value = "type") String type,
                                                @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                @RequestBody(required = false) List<Integer> employeesIds,
                                                Pageable pageable) throws FileNotFoundException {

        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();

        if(repository.countByUserAndForOfficeStaffsTrue(user) <= 0) {
            return this.unauthorizedReponse();
        }

        List<Long> employeesLongIds = (employeesIds == null || employeesIds.isEmpty())
                ? null
                : employeesIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList());

        List<OfficeStaffPayrollBean> result = new ArrayList<>();
        int total = getOfficeStaffPayslipBeans(type, date, result, employeesLongIds, pageable);

        PageImpl page = new PageImpl(result, pageable, total);
        return new ResponseEntity<>(page.map(staff
                -> projectionFactory.createProjection(OfficeStaffPaymentSlipProjection.class,
                staff)), HttpStatus.OK);
    }

    //************************** Housemaid Payslips v2*********************
//    @PreAuthorize("hasPermission('paySlip','generateHousemaidsFianlPayslips')")
//    @RequestMapping("/generateHousemaidsFianlPayslips")
//    public ResponseEntity generateHousemaidsFianlPayslips(
//            @RequestParam(required = true, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) throws URISyntaxException, IOException, Exception {
//        LocalDate dt;
//        if (date != null) {
//            dt = new LocalDate(date);
//        } else {
//            dt = new LocalDate();
//        }
//        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
//        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
//        if (!payrollDocumentRep.findByPayrollDateAndTypeOrderByCreationDateDesc(new java.sql.Date(payrollEnd.toDate().getTime()), MonthlyPayrollDocumentType.PAYSLIPS).isEmpty()) {
//            throw new RuntimeException("Payslips for this month were generated, you can't generate them again");
//        }
//        List<MonthlyPayroll> payrolls = payrollRep.findByPayrollDate(new java.sql.Date(payrollEnd.toDate().getTime()));
//        if (payrolls.isEmpty()) {
//            throw new RuntimeException("Payroll wasnt' generated yet");
//        }
//        List<HousemaidPayslip> paySlips = generateHousemaidPayslip(dt.toDate(), payrolls);
//
//        Thread one = new Thread() {
//            public void run() {
//
//                String s = "";
//                for (HousemaidPayslip paySlip : paySlips) {
//                    try {
//                        List<String> payslipLanguages = PayslipTranslateService.getHousemaidPayslipLanguages(paySlip.getHousemaid());
//                        List<TranslatedPayslip> translatedPayslips = new ArrayList<>();
//                        for (String lang : payslipLanguages) {
//                            File input;
//                            logger.log(Level.INFO, "Creating payslip word document for housemaid " + paySlip.getHousemaid().getName());
//                            input = PayrollGenerationLibrary.createHouseMaidPayslipAsWordDocument(paySlip, lang);
//                            logger.log(Level.INFO, "Creating payslip photo for housemaid " + paySlip.getHousemaid().getName());
//                            File file = PayrollGenerationLibrary.generateHouseMaidPayslipPhoto(new FileInputStream(input));
//                            logger.log(Level.INFO, "Saving payslip to DB for housemaid " + paySlip.getHousemaid().getName());
//                            translatedPayslips.add(new TranslatedPayslip(file, lang));
//                        }
//                        PayrollGenerationLibrary.savePayslipPhoto(translatedPayslips, payrollEnd, paySlip.getHousemaid(), paySlip.getMonthlyPayroll(), "");
//                        logger.log(Level.INFO, "************************************************************");
//                    } catch (URISyntaxException ex) {
//                        Logger.getLogger(PaySlipsController.class.getName()).log(Level.SEVERE, null, ex);
//                    } catch (IOException ex) {
//                        Logger.getLogger(PaySlipsController.class.getName()).log(Level.SEVERE, null, ex);
//                    } catch (Exception ex) {
//
//                        s = "error in generating payslip for housemaid " + paySlip.getHousemaid().getName() + " " + ex.getStackTrace().toString();
//                    }
//
//                }
//                String emails = Setup.getParameter(Setup.getCurrentModule(),
//                        PayrollManagementModule.PARAMETER_PAYROLL_EMAILS);
////                Parameter parm=Setup.getRepository(ParameterRepository.class).findByModuleAndCode(null, s)
//                List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
//                TextEmail mail = new TextEmail("Payslips generation result of " + DateUtil.formatMonth(payrollEnd.toDate()), s.equals("") ? "All payslips were generated successuflly!" : s);
//                Setup.getMailService().sendEmail(recipients, mail, null);
//
//            }
//        };
//        one.start();
//
//        return new ResponseEntity("Your request was registered, we will inform you by email once done!", HttpStatus.OK);
//    }

    @PreAuthorize("hasPermission('paySlip','housemaidPayslips')")
    @RequestMapping("/housemaidPayslips")
    public ResponseEntity housemaidPayslips(
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(value = "Name", required = false) String maidName,
            @RequestParam(value = "linkedToYayaBot", required = false) String linkedToYayaBot,
            @RequestParam(value = "Nationality", required = false) PicklistItem nationality,
            Pageable pageable) {
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollMonth = dt.withDayOfMonth(1);

//        List<MonthlyPayroll> payrolls = payrollRep.findByPayrollDate(new java.sql.Date(payrollEnd.toDate().getTime()));
//        if (payrolls.isEmpty()) {
//            throw new RuntimeException("Payroll wasnt' generated yet");
//        }
//        List<MonthlyPayrollDocument> documents = payrollDocumentRep.findByPayrollDateAndTypeOrderByCreationDateDesc(new java.sql.Date(payrollEnd.toDate().getTime()), MonthlyPayrollDocumentType.PAYSLIPS);
//        if (documents.isEmpty()) {
//            throw new RuntimeException("Payslips for this month weren't generated yet, ask accounting admin to generate them");
//        }

        Parameter payslipsGenerationInProgress = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_PAYSLIPS_ARE_BEING_GENRATED);

        if (payslipsGenerationInProgress.getValue().equals("1"))
            throw new BusinessException("Payslips are already being generated, please wait until the job finishes");

        SelectQuery<MonthlyPayrollDocument> query = new SelectQuery<>(MonthlyPayrollDocument.class);

        // Old and new payroll systems
        query.filterBy(new SelectFilter("payrollDate", "=", new java.sql.Date(payrollMonth.toDate().getTime()))
        .or("payrollDate", "=", new java.sql.Date(payrollMonth.withDayOfMonth(26).toDate().getTime())));

        query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);
        SelectQuery<HousemaidPsid> query2 = new SelectQuery<>(HousemaidPsid.class);
        List<HousemaidPsid> psids = query2.execute();

        if (maidName != null) {
            query.filterBy("housemaid.name", "LIKE", "%" + maidName + "%");
            //documents = documents.stream().filter(x -> x.getHousemaid().getName().toLowerCase().contains(maidName.toLowerCase())).collect(Collectors.toList());
        }
        if (linkedToYayaBot != null && linkedToYayaBot.equals("LINKED")) {
            query.filterBy("housemaid.id", "IN", psids.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList()));
            //documents = documents.stream().filter(x -> !x.getHousemaid().getHousemaidPsids().isEmpty()).collect(Collectors.toList());
        } else if (linkedToYayaBot != null && linkedToYayaBot.equals("NOT_LINKED")) {
            query.filterBy("housemaid.id", "NOT IN", psids.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList()));
            //documents = documents.stream().filter(x -> x.getHousemaid().getHousemaidPsids().isEmpty()).collect(Collectors.toList());
        }
        if (nationality != null) {
            query.filterBy("housemaid.nationality.id", "=", nationality.getId());
            //documents = documents.stream().filter(x -> x.getHousemaid().getNationality() != null && x.getHousemaid().getNationality().getId().equals(nationality.getId())).collect(Collectors.toList());
        }
//        int size = documents.size();
//        PageImpl<MonthlyPayrollDocument> page = new PageImpl(documents, pageable, size);

        //ListToPage
//        int start = (int)pageable.getOffset();
//        int end = (start + (pageable).getPageSize()) > documents.size()
//                ? documents.size() : (start + (pageable).getPageSize());
//
//        PageImpl<MonthlyPayrollDocument> page = new PageImpl<>(
//                start > end ? new ArrayList<>()
//                        : documents.subList(start, end),
//                pageable, documents.size());

        return new ResponseEntity<>(query.execute(pageable), HttpStatus.OK);
    }

//    //The following API is for testing only
//    @PreAuthorize("hasPermission('paySlip','getHousemaidPayslips')")
//    @RequestMapping("/getHousemaidPayslips/{id}")
//    public ResponseEntity getHousemaidPayslips(HttpServletResponse response,
//                                               @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
//                                               @PathVariable Long id) throws IOException, FileNotFoundException, NoSuchAlgorithmException, Exception {
//
//        LocalDate dt;
//        if (date != null) {
//            dt = new LocalDate(date);
//        } else {
//            dt = new LocalDate();
//        }
//        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
//        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
//        Housemaid housemaid = housemaidRep.findOne(id);
//        List<MonthlyPayroll> payroll = monthlyPayrollRep.findByHousemaidAndPayrollDate(housemaid, new java.sql.Date(payrollEnd.toDate().getTime()));
//        if (payroll.isEmpty()) {
//            throw new RuntimeException("The requested Housemaid isn't inculded in the payroll or it hasn't been generated yet!");
//        }
////        List<MonthlyPayrollDocument> payslip=month
//        List<HousemaidPayslip> paySlips = generateHousemaidPayslip(dt.toDate(), payroll);
//        File input = PayrollGenerationLibrary.createHouseMaidPayslipAsWordDocument( paySlips.get(0), "en");
//        File file = PayrollGenerationLibrary.generateHouseMaidPayslipPhoto(new FileInputStream(input));
//
//        createDownloadResponse(response, "test.jpeg", new FileInputStream(file));
//////            PayrollGenerationLibrary.savePayslipPhoto(file, new LocalDate());
//////            String emails = Setup.getParameter(Setup.getCurrentModule(),
//////                AccountingModule.PARAMETER_ERROR_EMAIL);
//////        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
//////        TextEmail mail = new TextEmail("payslip","test");
//////        mail.addAttachement(file);
//////        mail.addAttachement(input);
//////        Setup.getMailService().sendEmail(recipients, mail);
////            createDownloadResponse(response,"test.jpeg", new FileInputStream(file));
////        return new ResponseEntity<>(payslip,HttpStatus.OK);
//        return new ResponseEntity(HttpStatus.OK);
//    }

    //Jirra ACC-1270
    @PreAuthorize("hasPermission('payslip','sendOfficeStaffPayslips')")
    @RequestMapping("/sendOfficeStaffPayslips")
    public ResponseEntity<?> sendOfficeStaffPayslipsViaEmail(@RequestParam(value = "type") String type,
                                                             @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) throws FileNotFoundException {


        Thread one = new Thread() {
            public void run() {

                List<OfficeStaffPayrollBean> result = new ArrayList<>();
                getOfficeStaffPayslipBeans(type, date, result, null,null);

                if (result == null || result.isEmpty()) {
                    throw new BusinessException("Payroll isn't generated yet");
                }


                for (OfficeStaffPayrollBean officeStaffPayrollBean : result) {
                    try {
                        if (officeStaffPayrollBean.getOfficeStaffEMail() == null || officeStaffPayrollBean.getOfficeStaffEMail().trim().equals(""))
                            continue;
                        File input = PayrollGenerationLibrary.createOfficeStaffPayslipAsWordDocument(officeStaffPayrollBean);
                        File file = PayrollGenerationLibrary.generateOfficeStaffPayslipPhoto(new FileInputStream(input), officeStaffPayrollBean.getOfficeStaffName());
                        List<EmailRecipient> recipients = Recipient.parseEmailsString(officeStaffPayrollBean.getOfficeStaffEMail());
                        TextEmail email = new TextEmail(officeStaffPayrollBean.getOfficeStaffName() +
                                " Payslip of the month " + officeStaffPayrollBean.getPaySlipMonth(), "please find attached files");
                        email.addAttachement(file);
                        mailService.sendEmail(new MailObject.builder(email, EmailReceiverType.Office_Staff)
                                .recipients(recipients)
                                .secure()
                                .build());
                    } catch (Exception e) {
                        logger.log(Level.SEVERE, "exception while generating payslip for office staff named: " + officeStaffPayrollBean.getOfficeStaffName());
                    }
                }
            }
        };
        one.start();

        return new ResponseEntity("Email will be sent for each office staff", HttpStatus.OK);
    }

    public String validateLanguage(Housemaid housemaid, String lang) {
        if(lang == null || lang.isEmpty()) {
            return "en";
        }
        if(PayslipTranslateService.getHousemaidPayslipLanguages(housemaid).contains(lang)) {
            return lang;
        }

        return "en";
    }

    //The following API is for testing only UPD: it's used by Yayabot project
    @PreAuthorize("hasPermission('paySlip','generateHousemaidPayslip')")
    @RequestMapping("/generateHousemaidPayslip/{id}")
    public ResponseEntity<?> generateHousemaidPayslip(HttpServletResponse response, @PathVariable Long id,
                                                      @RequestParam(required = false, value = "lang") String lang,
                                                      @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) throws IOException, FileNotFoundException, NoSuchAlgorithmException, Exception {


        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
//        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);

        Housemaid housemaid = housemaidRep.findOne(id);


        lang = validateLanguage(housemaid, lang);

        return ResponseEntity.ok(generatePayslipPublicURLWithoutShorten(housemaid, date, lang));

//        List<MonthlyPayroll> payroll = monthlyPayrollRep.findByHousemaidAndPayrollDate(housemaid, new java.sql.Date(payrollEnd.toDate().getTime()));
//        if (payroll.isEmpty()) {
//            throw new RuntimeException("The requested Housemaid isn't inculded in the payroll or it hasn't been generated yet!");
//        }
//        List<MonthlyPayrollDocument> payslip=month 5555
//        List<MonthlyPayrollDocument> documents = payrollDocumentRep.findByHousemaidAndPayrollDate(housemaid, new java.sql.Date(payrollEnd.toDate().getTime()));
//        documents.addAll(payrollDocumentRep.findByHousemaidAndPayrollDate(housemaid, new java.sql.Date(dt.withDayOfMonth(1).toDate().getTime())));
//        if (documents.size() > 0 && documents.get(0).getAttachments().size() > 0) {
//            Attachment att = documents.get(0).getAttachment("Payslip-" + lang);
//            if (att == null) {
//                att = documents.get(0).getAttachments().get(0);
//            }
//            InputStream in = Storage.getStream(att);
//            createDownloadResponse(response, housemaid.getName() + ".jpeg", in);
//        } else {
//            throw new RuntimeException("Payslip for this month is not generated yet!");
//        }
////        } else {
////            List<HousemaidPayslip> paySlips = generateHousemaidPayslip(dt.toDate(), payroll);
////            File input = PayrollGenerationLibrary.createHouseMaidPayslipAsWordDocument(paySlips.get(0), lang);
////            File file = PayrollGenerationLibrary.generateHouseMaidPayslipPhoto(new FileInputStream(input));
////            PayrollGenerationLibrary.savePayslipPhoto(Arrays.asList(new TranslatedPayslip(file, lang)), payrollEnd, paySlips.get(0).getHousemaid(), paySlips.get(0).getMonthlyPayroll(), "");
////            payrollDocumentRep.delete(documents);
////            createDownloadResponse(response, housemaid.getName() + ".jpeg", new FileInputStream(file));
////        }
//        return new ResponseEntity(HttpStatus.OK);

    }


    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    public List<HousemaidPayslip> generateHousemaidPayslip(List<HousemaidPayrollLog> logs) {
        //Calculate start date and end date of payroll
        if (logs.isEmpty()) {
            throw new BusinessException("Payroll wasnt' generated yet");
        }

        List<HousemaidPayslip> payslips = new ArrayList<>();
        for (HousemaidPayrollLog log : logs) {

            PayrollAccountantTodo todo = log.getPayrollAccountantTodo();
            MonthlyPaymentRule monthlyPaymentRule = todo.getMonthlyPaymentRule();

            LocalDate payrollStart = new LocalDate(
                    housemaidPayrollPaymentServiceV2.getPayrollStartLockDate(monthlyPaymentRule));

            LocalDate payrollEnd = new LocalDate(
                    housemaidPayrollPaymentServiceV2.getPayrollEndLockDate(monthlyPaymentRule));


            LocalDate payrollMonth = new LocalDate(todo.getPayrollMonth());

//            DebugHelper.sendMail("<EMAIL>",
//                    String.format("payrollStart: %s, payrollEnd: %s, payrollMonth: %S",
//                            DateUtil.formatDateSlashed(payrollStart.toDate()),
//                            DateUtil.formatDateSlashed(payrollEnd.toDate()),
//                            DateUtil.formatDateSlashed(payrollMonth.toDate())));

            HousemaidPayslip bean = new HousemaidPayslip();
            Housemaid h = log.getHousemaid();
            bean.setHousemaid(h);
            bean.setHousemaidPayrollLog(log);
            bean.setPaySlipDate(log.getPayrollMonth());
            bean.setMohreSalary(log.getPrimarySalary());
            bean.setOverTime(log.getOverTime());
            bean.setHolidays(log.getHoliday());
            bean.setCashAdvanceLoan(log.getMonthlyLoan());
            bean.setBasicMonthlyPay(log.getBasicSalary());
            bean.setAdditionToBalanceDeductionLimit(log.getAdditionToCoverDeductionLimit());

            if (log.getStartDateDeduction() > 0) {
                int numOfDays = 0;
                LocalDate a = new LocalDate(h.getStartDate());
                LocalDate b = payrollMonth.dayOfMonth().withMaximumValue();
                numOfDays = Math.abs(Days.daysBetween(a, b).getDays() + 1);
                bean.setOfWorkingDaysThisMonth(numOfDays);
                bean.setBasicPayThisMonth(log.getBasicSalary() - log.getStartDateDeduction());
            } else {
                bean.setOfWorkingDaysThisMonth(0);
                bean.setBasicPayThisMonth(0.0);
            }

            bean.setFoodAllowance(log.getFoodAllowance());
            bean.setHousingAllowance(log.getHousingAllowance());
            bean.setManagerAdditions(log.getManagerAdditions());
            bean.setVacationAirFare(0d);

            // Calculate Earnings
            Double totalEarning = 0.0;
            totalEarning += (bean.getBasicPayThisMonth() == 0.0 ? bean.getBasicMonthlyPay() : bean.getBasicPayThisMonth())
                    + bean.getHousingAllowance() + bean.getFoodAllowance() + bean.getManagerAdditions() + bean.getVacationAirFare();

            bean.setTotalEarnings(Math.round(totalEarning * 100) / 100.0);
            bean.setRepaymentThisMonth(log.getLoanRepayment());
            bean.setRemainingLoan(log.getRemainingLoan());


            // Calculate Deductions
            List<WarningLetter> warningLetters = new ArrayList<WarningLetter>();
            //logger.log(Level.WARNING, "Housemaid Name " + h.getName());
            if (h.getWarningLetters() != null) {
                warningLetters = h.getWarningLetters().stream().filter(x -> x.getDate() != null
                        && !x.getDate().before(payrollStart.toDate())
                        && x.getDate().before(payrollEnd.toDate()))
                        .collect(Collectors.toList());
                bean.setWarningLetters(warningLetters);
                bean.setTotalComplaintsDeductions(
                        Math.round(bean.getWarningLetters().stream()
                                .mapToDouble(x -> x.getDeduction()).sum() * 100) / 100.0);
            } else {
                bean.setWarningLetters(warningLetters);
                bean.setTotalComplaintsDeductions(0.0);
            }

            List<PayrollManagerNote> managerDeductions = new ArrayList<>();
            List<PayrollManagerNote> adjusments = new ArrayList<>();
            List<PayrollManagerNote> deductions = new ArrayList<>();
            List<PayrollManagerNote> additions = new ArrayList<>();
            if (h.getManagerNotes() != null) {
                deductions = h.getManagerNotes().stream().filter(x -> x.getNoteDate() != null
                        && !x.getNoteDate().before(payrollStart.toDate())
                        && x.getNoteDate().before(payrollEnd.toDate())
                        && x.getNoteType() == PayrollManagerNote.ManagerNoteType.DEDUCTION).collect(Collectors.toList());

                additions =  h.getManagerNotes().stream().filter(x -> x.getNoteDate() != null
                        && !x.getNoteDate().before(payrollStart.toDate())
                        && x.getNoteDate().before(payrollEnd.toDate())
                        && x.getNoteType() == PayrollManagerNote.ManagerNoteType.ADDITION).collect(Collectors.toList());

            }


//            jira ACC-1537

            for (PayrollManagerNote payrollManagerNote : deductions) {
                if (payrollManagerNote.getDeductionReason() != null && !payrollManagerNote.getDeductionReason().getTags().isEmpty() && payrollManagerNote.getDeductionReason().hasTag("adjusment")) {
                    adjusments.add(payrollManagerNote);
                } else {
                    managerDeductions.add(payrollManagerNote);
                }
            }
            bean.setManagerDeductions(managerDeductions);
            bean.setAdditionsList(additions);

            bean.setTotalManagerDeductions(
                    Math.round(bean.getManagerDeductions().stream()
                            .mapToDouble(x -> x.getAmount()).sum() * 100) / 100.0);
            bean.setAdjusments(adjusments);
            bean.setTotalAdjusmentsDeductions(
                    Math.round(bean.getAdjusments().stream()
                            .mapToDouble(x -> x.getAmount()).sum() * 100) / 100.0);

            bean.setDeduction(deductions);

//            end jira ACC-1537

            bean.setTotalDeduction(Math.round((bean.getRepaymentThisMonth()
                    + bean.getTotalComplaintsDeductions()
                    + bean.getTotalAdjusmentsDeductions()
                    + bean.getTotalManagerDeductions()) * 100) / 100.0);
            bean.setNetPayAnsari(bean.getTotalEarnings() - bean.getTotalDeduction());
            payslips.add(bean);

//            DebugHelper.sendMail("<EMAIL>",
//                    String.format("Name: %s, Deductions: %s, Additions: %S",
//                            h.getName(),
//                            bean.getDeduction().size(),
//                            bean.getAdditionsList().size()));
        }
        return payslips;
    }

    //**************************Send payslip via FB Messenger ****************************************
    public final String endPoint = "http://maidsae-chatbot-env.karetmke2n.us-east-2.elasticbeanstalk.com/PayslipsWebhook.php";

    @NoPermission
    @RequestMapping("/SendPayslipViaFB")
    @ResponseBody
    public ResponseEntity SendPayslipViaFB(@RequestBody PayslipImage image) throws IOException {
//        try {
        image.setCleanerType("FullTime");
        image.setPayrollMonth(DateUtil.formatMonth(new Date()));
        List<PayslipImage> toSend = Arrays.asList(image);

        debug("endPoint= " + endPoint);
        if (image.getPayslipImageBody() != null && image.getPayslipImageBody() != "") {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset=UTF-8");
            ObjectMapper objectMapper = new ObjectMapper();

            String result = postRequest(endPoint, headers, objectMapper.writeValueAsString(toSend), 120000);

            if (result.startsWith("?>")) {
                result = result.substring(2);
            }
            System.out.println(result);
            if (result.contains("Sending Failure")) {
                throw new BusinessException("Housemaid with id " + image.getCleanerId() + " doesn't exist");
            }
            return new ResponseEntity<>(objectMapper.readTree(result), HttpStatus.OK);
//            }
//        } catch (Exception e) {
//            return new ResponseEntity<>(e.getStackTrace(), HttpStatus.BAD_REQUEST);
//        }
//        return null;
        }
        return null;
    }

    private static String postRequest(String url,
                                      Map<String, String> headers,
                                      String body,
                                      int timeout
    ) throws IOException {
        BufferedReader in = null;
        OutputStreamWriter out = null;
        HttpURLConnection con = null;
        try {
            StringBuffer response = new StringBuffer();

            URL obj = new URL(url);
            con = (HttpURLConnection) obj.openConnection();
            con.setDoOutput(true);
            con.setRequestMethod("POST");
            for (Iterator<String> iterator = headers.keySet().iterator(); iterator.hasNext(); ) {
                String key = (String) iterator.next();
                con.setRequestProperty(key, headers.get(key));
            }
            con.setConnectTimeout(timeout);
            con.connect();
            out = new OutputStreamWriter(con.getOutputStream());

            if (body != null) {
                out.write(body);
            }
            out.close();
            in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            return response.toString();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @PreAuthorize("hasPermission('payslip','generatePayslipsFBErrorReport')")
    @RequestMapping(path = "/generatePayslipsFBErrorReport",
            method = RequestMethod.GET)
    public void generatePayslipsFBErrorReport(
            @RequestParam(name = "notExist", required = false) Boolean notExist,
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            HttpServletResponse response) {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
        LocalDate payrollMonth = dt.withDayOfMonth(1);

        List<MonthlyPayroll> payrolls = payrollRep.findByPayrollDate(new java.sql.Date(payrollEnd.toDate().getTime()));

        List<HousemaidPayrollLog> logs = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findByPayslipGenerated(new java.sql.Date(payrollMonth.toDate().getTime()));

        if (payrolls.isEmpty() && logs.isEmpty()) {
            throw new BusinessException("Payroll wasnt' generated yet");
        }
        //Jirra YB-32
        SelectQuery<MonthlyPayrollDocument> query
                = new SelectQuery<>(MonthlyPayrollDocument.class);


        query.filterBy(new SelectFilter("payrollDate", "=", payrollEnd.toDate())
                .or("payrollDate", "=", payrollMonth.toDate()));

        query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);

        List<MonthlyPayrollDocument> documents = query.execute();

        if (documents.isEmpty()) {
            throw new BusinessException("Payslips for this month weren't generated yet, ask accounting admin to generate them");
        }

        List<FbPaySlipsLog> paySlipsLogs = new ArrayList<>();
        paySlipsLogs.addAll(fbPaySlipsLogRepository.findByPayrollDate(payrollEnd.toDate()));
        paySlipsLogs.addAll(fbPaySlipsLogRepository.findByPayrollDate(payrollMonth.toDate()));
        try {
            createDownloadResponse(response, "PayslipsFBErrorReport.csv",
                    PayrollGenerationLibrary.generatePayslipsFBErrorReport(paySlipsLogs));
        } catch (FileNotFoundException ex) {
            Logger.getLogger(PaySlipsController.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @PreAuthorize("hasPermission('payslip','checkIfPayslipsWereGenerated')")
    @RequestMapping(path = "/checkIfPayslipsWereGenerated",
            method = RequestMethod.GET)
    public ResponseEntity<?> checkIfPayslipsWereGenerated(
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(name = "id", required = false) Long hid,
            HttpServletResponse response) {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
            date = new java.sql.Date(dt.withDayOfMonth(1).toDate().getTime());
        }

        if (java.sql.Date.valueOf("2020-08-01").after(date))
            throw new BusinessException("can't fetch old payslips before August 2020");

        LocalDate payrollMonth = dt.withDayOfMonth(1);

        List<HousemaidPayrollLog> logs;
        if(hid == null) {
            logs = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .findLogByPayrollMonthAndTransferredTrue(new java.sql.Date(payrollMonth.toDate().getTime()));
        } else {
            logs = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .findLogByPayrollMonthAndTransferredTrueAndHousemaid(new java.sql.Date(payrollMonth.toDate().getTime()), housemaidRep.findOne(hid));
        }

        if(logs.isEmpty()) {
            throw new BusinessException("Payslips for this month weren't generated yet, ask accounting admin to generate them");
        }
        return this.okResponse();

//        List<MonthlyPayroll> payrolls = payrollRep.findByPayrollDate(new java.sql.Date(payrollEnd.toDate().getTime()));
//
//        List<HousemaidPayrollLog> logs = Setup.getRepository(HousemaidPayrollLogRepository.class)
//                .findByPayslipGenerated(new java.sql.Date(payrollMonth.toDate().getTime()));
//
//        if (payrolls.isEmpty() && logs.isEmpty()) {
//            throw new RuntimeException("Payroll wasnt' generated yet");
//        }
//        //Jirra YB-32
//        SelectQuery<MonthlyPayrollDocument> query
//                = new SelectQuery<>(MonthlyPayrollDocument.class);
//
//        Housemaid housemaid = null;
//        if (hid != null) {
//            housemaid = housemaidRep.findOne(hid);
//            if (housemaid == null) {
//                return new ResponseEntity<>("Housemaid does not found.", HttpStatus.BAD_REQUEST);
//            }
//        }
//
//        if (housemaid != null) {
//            query.filterBy("housemaid.id", "=", housemaid.getId());
//        }
//
//        query.filterBy(new SelectFilter("payrollDate", "=", payrollEnd.toDate())
//        .or("payrollDate", "=", payrollMonth.toDate()));
//
//        query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);
//
//        List<MonthlyPayrollDocument> documents = query.execute();
////        List<MonthlyPayrollDocument> documents = payrollDocumentRep
////                .findByPayrollDateAndTypeOrderByCreationDateDesc(
////                        new java.sql.Date(payrollEnd.toDate().getTime()),
////                        MonthlyPayrollDocumentType.PAYSLIPS);
//        if (documents.isEmpty()) {
//            throw new RuntimeException("Payslips for this month weren't generated yet, ask accounting admin to generate them");
//        }
//        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('payslip','sendtosinglehousamaidsviafb')")
    @RequestMapping(path = "/sendtosinglehousamaidsviafb",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> sendToSingleHousamaidsViaFB(
            @RequestParam(required = false, name = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(name = "id", required = true) Long hid) {

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
        try {

            List<MonthlyPayroll> payrolls = payrollRep.findByPayrollDate(new java.sql.Date(payrollEnd.toDate().getTime()));
            if (payrolls.isEmpty()) {
                throw new BusinessException("Payroll wasnt' generated yet");
            }
            Housemaid housemaid = null;
            if (hid != null) {
                housemaid = housemaidRep.findOne(hid);
                if (housemaid == null) {
                    return new ResponseEntity<>("Housemaid does not found.", HttpStatus.BAD_REQUEST);
                }
            }

            SelectQuery<MonthlyPayrollDocument> query
                    = new SelectQuery<>(MonthlyPayrollDocument.class);

            //query.filterBy("payrollMonth", "LIKE", (month != null && !month.isEmpty()) ? "%" + month + "%" : dt.toString("MMMyyyy"));
            if (housemaid != null) {
                query.filterBy("housemaid.id", "=", housemaid.getId());
            }
            query.filterBy("payrollDate", "=", payrollEnd.toDate());
            query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);

            List<MonthlyPayrollDocument> docs = query.execute();

            if (docs.size() > 0) {
                docs.sort(Comparator.comparing(MonthlyPayrollDocument::getCreationDate).reversed());
                MonthlyPayrollDocument targetPayslips = docs.get(docs.size() - 1);

                List<String> botIdsArray = new ArrayList<>();

                List<FbPaySlipsLog> logs = fbPaySlipsLogRepository.findByHousemaidAndPayrollDate(
                        targetPayslips.getHousemaid(), payrollEnd.toDate());


                if (targetPayslips.getHousemaid().getHousemaidPsids().size() > 0) {
                    for (HousemaidPsid housemaidPsid : targetPayslips.getHousemaid().getHousemaidPsids()) {
                        //JSONObject dataItem = new JSONObject();
                        botIdsArray.add(housemaidPsid.getPsid());
                    }
                } else {
                    FbPaySlipsLog log = new FbPaySlipsLog();
                    log.setHousemaid(targetPayslips.getHousemaid());
                    log.setSendDate(new java.sql.Date(new Date().getTime()));
                    log.setStatus(FbPaySlipsLogStatus.DOES_NOT_EXIST);
                    log.setPayrollDate(payrollEnd.toDate());
                    fbPaySlipsLogRepository.save(log);
                    return new ResponseEntity<>("Housemaid is not linked.", HttpStatus.BAD_REQUEST);
                }

                String url
                        = Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.YAYA_CHAT_BOT_API)
                        + Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.YAYA_CHAT_BOT_PaySlipsSender_Sender_API);

                JSONObject data = new JSONObject();
                JSONObject requestBody = new JSONObject();
                requestBody.put("botIds", botIdsArray);
                requestBody.put("url", Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.YAYA_CHAT_BOT_PaySlips_Photoes_Server)
                        + "/payroll/payslip/getPayslipPhoto/"
                        + targetPayslips.getId());
                data.put("single", requestBody);

                Map<String, Object> mappedResult = new HashMap<>();
                ObjectMapper mapper = new ObjectMapper();
                TypeReference<Map<String, Object>> typeRef
                        = new TypeReference<Map<String, Object>>() {
                };

                try {
                    System.out.println("Single Request Body------>\n" + data.toString());
                    String s = apisHelper.postRequest(url, data.toString(), 0);
                    System.out.println("Response ------>\n" + s);
                    mappedResult = mapper.readValue(s, typeRef);
                    if (mappedResult.get("status").toString().equals("success")) {

                        FbPaySlipsLog log = new FbPaySlipsLog();
                        log.setHousemaid(housemaid);
                        log.setSendDate(new java.sql.Date(new Date().getTime()));
                        log.setStatus(FbPaySlipsLogStatus.RECEIVED);
                        log.setPayrollDate(payrollEnd.toDate());
                        fbPaySlipsLogRepository.save(log);

                        return new ResponseEntity<>("Housemaid Payslips sended successfuly.", HttpStatus.OK);

                    } else {
                        FbPaySlipsLog log = new FbPaySlipsLog();
                        if (!logs.isEmpty()) {
                            log = logs.get(0);
                        }
                        log.setHousemaid(housemaid);
                        log.setSendDate(new java.sql.Date(new Date().getTime()));
                        log.setStatus(FbPaySlipsLogStatus.CONNECTION_ERROR);
                        log.setPayrollDate(payrollEnd.toDate());
                        fbPaySlipsLogRepository.save(log);
                        return new ResponseEntity<>("Failed, " + mappedResult.get("message"), HttpStatus.BAD_REQUEST);
                    }
                } catch (Exception ex) {
                    String subject = "FAILURE - Handling YAYA Response " + DateUtil.formatMonth(payrollEnd.toDate());
                    sendWarningEmail(subject, "", recipients, ex, null);
                    return new ResponseEntity<>(ex.getMessage(), HttpStatus.BAD_REQUEST);
                }
            } else {
                return new ResponseEntity<>("Housemaid Payslips is not found.", HttpStatus.BAD_REQUEST);
            }

        } catch (Exception ex) {
            String subject = "FAILURE - Handling YAYA Response " + DateUtil.formatMonth(payrollEnd.toDate());
            sendWarningEmail(subject, "", recipients, ex, null);
            return new ResponseEntity(ex.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @NoPermission
    @RequestMapping(path = "/getPayslipPhoto/{id}",
            method = RequestMethod.GET)
    public void getPayslipPhoto(@PathVariable Long id, HttpServletResponse response,
                                @RequestParam(name = "lang", required = false) String lang) {
        if (lang == null) {
            lang = "en";
        }

        MonthlyPayrollDocument file = payrollDocumentRep.findOne(id);
        if (file == null) {
            throw new BusinessException("No such file");
        }
        if (file.getAttachments().size() > 0) {
            Attachment att = file.getAttachment("Payslip-" + lang);
            if(att == null) {
                att = file.getAttachments().get(0);
            }
            InputStream in = Storage.getStream(att);
            createDownloadResponse(response, file.getHousemaid().getName() + ".jpeg", in);
        }
    }

    @PreAuthorize("hasPermission('payslip','test2')")
    @RequestMapping(path = "/test2",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> test2() {
        try {
            String sourcePath = "D://test.docx";
            Document doc = new Document(sourcePath);
            ImageSaveOptions options = new ImageSaveOptions(SaveFormat.JPEG);
            options.setJpegQuality(100);
            options.setResolution(400);
            options.setUseHighQualityRendering(true);
            for (int i = 0; i < doc.getPageCount(); i++) {
                String imageFilePath = "D://" + "img_" + i + ".jpeg";
                options.setPageSet(new PageSet(i));
                doc.save(imageFilePath, options);

                ///
                BufferedImage image = ImageIO.read(new File(imageFilePath));
                BufferedImage out = image.getSubimage(0, 280, 3400, 4100);

                ImageIO.write(out, "jpg", new File(imageFilePath));
            }
            System.out.println("Done...");

        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

//    @RequestMapping(path = "/triggerHousemaidPayslipsGeneration",
//            method = RequestMethod.GET)
//    @ResponseBody
//    //Jirra ACC-317
//    public ResponseEntity<?> triggerHousemaidPayslipsGeneration(
//            @RequestParam(name = "id", required = false) Long hid) {
//
//        LocalDate dt = new LocalDate();
//        boolean forThisMonth = false;
//        boolean forPrevMonth = false;
//
//        Housemaid housemaid = null;
//        if (hid != null) {
//            housemaid = housemaidRep.findOne(hid);
//            if (housemaid == null) {
//                return new ResponseEntity<>("Housemaid does not found.", HttpStatus.BAD_REQUEST);
//            }
//        }
//
//        payslipsScheduledJob = new PayslipsScheduledJob();
//        forThisMonth = payslipsScheduledJob.triggerPayslipsGeneration(dt, housemaid);
//        if (!forThisMonth)
//            forPrevMonth = payslipsScheduledJob.triggerPayslipsGeneration(dt.minusMonths(1), housemaid);
//
//        String message;
//        if (!forThisMonth && !forPrevMonth)
//            message = "Payslips generation job was not triggered.";
//        else
//            message = "Payslips generation job was triggered for "
//                    + (forThisMonth ? "this month." : "previous month.");
//
//        return new ResponseEntity<>(message, HttpStatus.OK);
//    }

    public void debug(String message) {
        System.out.println(message);
    }


////////////////////////////////////////////////////////////////////////////////
//////////////////////// Sending Payslips via Messenger ////////////////////////

    @Autowired
    private YayapotApisHelper apisHelper;

    //Jirra ACC-299
    /*@NoPermission
    @RequestMapping(path = "/getmaidPayslipPhoto/{id}",
            method = RequestMethod.GET)
    public void getMaidPayslipPhoto(@PathVariable("id") Housemaid housemaid, HttpServletResponse response) {

        LocalDate dt = new LocalDate();
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt.minusMonths(1));

        Parameter payslipsMessengerJob = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING);

        Parameter payslipsGenerationInProgress = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(), "Are paySlips being generated");


        if (payslipsGenerationInProgress.getValue().equals("1"))
            throw new BusinessException("Payslips are already being generated, please wait until the job finishes");

        if (payslipsMessengerJob.getValue().equals("1"))
            throw new BusinessException("Payslips are already being sent by YAYA Bot");


        List<MonthlyPayroll> payrolls = payrollRep.findByPayrollDate(new java.sql.Date(payrollEnd.toDate().getTime()));
        if (payrolls.isEmpty())
            throw new BusinessException("Payroll wasn't generated yet");

        SelectQuery<MonthlyPayrollDocument> query
                = new SelectQuery<>(MonthlyPayrollDocument.class);

        query.filterBy("payrollDate", "=", payrollEnd.toDate());
        query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);
        query.filterBy("housemaid", "=", housemaid);


        List<MonthlyPayrollDocument> docs = query.execute();

        MonthlyPayrollDocument file = docs.size() > 0 ? docs.get(0) : null;
        if (file == null) {
            throw new BusinessException("No such file");
        }
        if (file.getAttachments().size() > 0) {
            InputStream in = Storage.getStream(file.getAttachments().get(0));
            createDownloadResponse(response, file.getHousemaid().getName() + ".jpeg", in);
        }
    }*/

    @PreAuthorize("hasPermission('payslip','issendingpayslipsrunning')")
    @RequestMapping(path = "/issendingpayslipsrunning",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> isSendingPayslipsRunning() {

        Parameter payslipsMessengerJob = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING);
        return new ResponseEntity<>(
                payslipsMessengerJob != null ?
                        payslipsMessengerJob.getValue().equals("1")
                        : false,
                HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('payslip','sendToAllHousamaidsViaFB')")
    @RequestMapping(path = "/sendToAllHousamaidsViaFB",
            method = RequestMethod.GET)
    @ResponseBody
    @Transactional
    public ResponseEntity<?> sendToAllHousamaidsViaFB(
            @RequestParam(required = false, name = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(name = "id", required = false) Long hid) throws ParseException {



        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

       /* java.sql.Date minimumPayrollMonth = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findMinimumLogDate();

        if (minimumPayrollMonth == null || new java.sql.Date(dt.toDate().getTime()).before(minimumPayrollMonth)) {
            throw new BusinessException("Can't send payslips that are generated by the old payroll system!");
        }*/

        Date minPayrollDate = DateUtil.parseDateDashed("2020-07-01");

        if (new java.sql.Date(dt.toDate().getTime()).before(new java.sql.Date(minPayrollDate.getTime())))
            throw new BusinessException("Can't send payslips that are generated by the old payroll system!");


        Housemaid housemaid = null;
        if (hid != null) {
            housemaid = housemaidRep.findOne(hid);
            if (housemaid == null) {
                return new ResponseEntity<>("Housemaid does not found.", HttpStatus.BAD_REQUEST);
            }
        }

        LocalDate payrollMonth = dt.withDayOfMonth(1);

        Parameter payslipsGenerationInProgress = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(), "Are paySlips being generated");


        if (payslipsGenerationInProgress.getValue().equals("1"))
            throw new BusinessException("Payslips are already being generated, please wait until the job finishes");

        if (payslipService.isTaskRunning(SENDING_PAYSLIPS_TASK_NAME))
            throw new BusinessException("Payslips are already being sent by YAYA Bot");

       /* Integer logsCount = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .countGeneratedPayslips(new java.sql.Date(payrollMonth.toDate().getTime()));
        if (logsCount == null)
            logsCount = 0;

        List<HousemaidPayrollLog> housemaidPayrollLogs = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findReadyToSentPayslips(new java.sql.Date(payrollMonth.toDate().getTime()));

        if (logsCount == 0)
            throw new BusinessException("Payroll wasn't generated yet");
        if (logsCount > 0 && housemaidPayrollLogs.isEmpty())
            throw new BusinessException("Payslips of " + DateUtil.formatSimpleMonthYear(payrollMonth.toDate()) + " are already sent, we can't send them again");*/

        boolean existTransferredLog =  Setup.getRepository(HousemaidPayrollLogRepository.class).existTransferredLog(new java.sql.Date(payrollMonth.toDate().getTime()));
        if(!existTransferredLog)
            throw new BusinessException("Payroll wasn't generated yet");

        boolean existTransferredAndPayslipNotSentLog =  Setup.getRepository(HousemaidPayrollLogRepository.class).existTransferredAndPayslipNotSentLog(new java.sql.Date(payrollMonth.toDate().getTime()));
        if(!existTransferredAndPayslipNotSentLog)
            throw new BusinessException("Payslips of " + DateUtil.formatSimpleMonthYear(payrollMonth.toDate()) + " are already sent, we can't send them again");
        //Create background task for sending payslips:
        BackgroundTask backgroundTask = new BackgroundTask.builder(SENDING_PAYSLIPS_TASK_NAME,
                Setup.getCurrentModule().getCode(), "paySlipsController",
                "sendPayslipsProcess")
                .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                .withParameters(new Class<?>[]{Date.class}, date)
                .build();
        Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);


        return new ResponseEntity("Request successfully sent to YAYA Bot and SMS API, and you will recieve details Emails for each step.", HttpStatus.OK);
    }

    //Jirra ACC-286
    public static String normalizePhoneNumber(String number) {

        if (number != null && !number.isEmpty()) {
            String NormalizedNumber = number;
            NormalizedNumber = NormalizedNumber.replace("-", "");
            NormalizedNumber = NormalizedNumber.replace("+", "");
            NormalizedNumber = NormalizedNumber.replace(")", "");
            NormalizedNumber = NormalizedNumber.replace("(", "");
            NormalizedNumber = NormalizedNumber.replace(" ", "");

            NormalizedNumber = removeFirst(NormalizedNumber, "00");

            if (NormalizedNumber.startsWith("0")) {
                NormalizedNumber = removeFirst(NormalizedNumber, "0");
                NormalizedNumber = "971" + NormalizedNumber;
            }
            if (NormalizedNumber.startsWith("9710")) {
                NormalizedNumber = removeFirst(NormalizedNumber, "9710");
                NormalizedNumber = "971" + NormalizedNumber;
            }
            return NormalizedNumber;
        }
        return number;
    }

    public static String removeFirst(String s,
                                     String toRemove) {
        int x = 0;
        if (s.startsWith(toRemove)) {
            s = s.substring(toRemove.length());
        }
        return s;
    }

    @NoPermission
    @RequestMapping(path = "/setPayslipsFBJobToRunning",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> sendYAYABotJobWarningEmail(
            @RequestParam(required = false, name = "jobid") String jobId) {

        setParameterValue(PayrollManagementModule.
                PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING, "1");
        System.out.println(jobId);
        String subject = "WARNING - Sending Payslips Via FB Messenger Job-" + jobId + " Started";
        String nowDate = DateUtil.formatFullDateWithTime(DateUtil.now());
        String body = "YAYA Bot started sending payslips to housemaids at " + nowDate + "<br/>";
        body += "Job ID: " + jobId;
        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

        sendWarningEmail(subject, body, recipients, null, null);

        return new ResponseEntity("Noted", HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(
            value = "/insertPayslipsLogs",
            method = RequestMethod.POST, consumes = "application/json")
    public void insertPayslipsLogs(
            @RequestBody Map<String, Object> requestBody)
            throws IOException, IOException {

        System.out.println(requestBody);

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS);

        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        Parameter payslipsMessengerJob =
                Setup.getRepository(ParameterRepository.class)
                        .findByModuleAndCode(
                                Setup.getCurrentModule(),
                                PayrollManagementModule.PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING);

        if (payslipsMessengerJob.getValue().equals("0"))
            throw new BusinessException("Payslips Sending Job was not requested by system");

        LocalDate today = new LocalDate();
        LocalDate payrollEnd = today.getDayOfMonth() <= 26 ? today.minusMonths(1).withDayOfMonth(26)
                : today.withDayOfMonth(26);
        List<FbPaySlipsLog> logs = new ArrayList<>();
        List<Housemaid> toBeSend = new ArrayList<>();
        Integer receivedCount = 0;
        Integer connectionErrorCount = 0;

        try {
            if (requestBody.get("status").equals("success")) {
                System.out.println("Success");
                //JSONArray jsonArray = new JSONArray(readlocationFeed);
                JSONArray array = new JSONArray(requestBody.get("list").toString());
                List<String> successed = new ArrayList<>();
                for (int i = 0; i < array.length(); i++)
                    successed.add(array.getString(i));

                List<Housemaid> successList = new ArrayList<>();

                System.out.println(successed);

                if (!successed.isEmpty()) {
                    SelectQuery<Housemaid> query2
                            = new SelectQuery<>(Housemaid.class);
                    query2.join("housemaidPsids");
                    query2.filterBy("housemaidPsids.psid", "in", successed);

                    successList = query2.execute();

                    successList = successList.stream().distinct().collect(Collectors.toList());
                }

                for (Housemaid h : successList) {
                    FbPaySlipsLog log = new FbPaySlipsLog();
                    log.setHousemaid(h);
                    log.setSendDate(new java.sql.Date(new Date().getTime()));
                    log.setStatus(FbPaySlipsLogStatus.RECEIVED);
                    log.setPayrollDate(payrollEnd.toDate());
                    logs.add(log);
                    receivedCount++;
                }


                SelectQuery<MonthlyPayrollDocument> query
                        = new SelectQuery<>(MonthlyPayrollDocument.class);
                query.filterBy("payrollDate", "=", payrollEnd.toDate());
                query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);

                List<MonthlyPayrollDocument> docs = query.execute();
                for (MonthlyPayrollDocument doc : docs) {
                    if (fbPaySlipsLogRepository.findByHousemaidAndPayrollDate(doc.getHousemaid(), payrollEnd.toDate())
                            .stream().filter(x ->
                                    x.getStatus() == FbPaySlipsLogStatus.RECEIVED ||
                                            x.getStatus() == FbPaySlipsLogStatus.RECEIVED_SMS)
                            .collect(Collectors.toList()).isEmpty()) {
                        if (doc.getHousemaid().getHousemaidPsids().size() > 0)
                            toBeSend.add(doc.getHousemaid());
                    }
                }


                List<Long> tt = successList.stream().
                        map(x -> x.getId()).collect(Collectors.toList());
                List<Housemaid> distictList = toBeSend.stream().distinct().
                        collect(Collectors.toList());
                List<Housemaid> notSend = toBeSend.stream().
                        filter(x -> !tt.contains(x.getId())).collect(Collectors.toList());

                for (Housemaid h : notSend) {
                    FbPaySlipsLog log = new FbPaySlipsLog();
                    log.setHousemaid(h);
                    log.setSendDate(new java.sql.Date(new Date().getTime()));
                    log.setStatus(FbPaySlipsLogStatus.CONNECTION_ERROR);
                    log.setPayrollDate(payrollEnd.toDate());
                    logs.add(log);
                    connectionErrorCount++;
                }
            } else {
                setParameterValue(PayrollManagementModule.
                        PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING, "0");
                String subject = "FAILURE - Handling YAYA Bot Response - " + DateUtil.formatMonth(payrollEnd.toDate());
                String body = "<b>Request body received from YAYA Bot: </b><br/><br/>";
                body += "<pre>" + requestBody + "</pre>";
                sendWarningEmail(subject, body, recipients, null, null);
            }
        } catch (Exception ex) {
            setParameterValue(PayrollManagementModule.
                    PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING, "0");
            String subject = "FAILURE - Insert Payslips Logs Received from YAYA Bot - " + DateUtil.formatMonth(payrollEnd.toDate());
            sendWarningEmail(subject, "", recipients, ex, null);
        }


        System.out.println("Logs size is " + logs.size());

        for (FbPaySlipsLog temp : logs) {
            fbPaySlipsLogRepository.save(temp);
        }

        FileOutputStream out = null;
        InputStream input = null;
        try {
            input = PayrollGenerationLibrary.generatePayslipsFBErrorReport(
                    fbPaySlipsLogRepository.findByPayrollDate(payrollEnd.toDate()));

            File file = Paths.get(System.getProperty("java.io.tmpdir"),
                    "PayslipsErrroReport.csv")
                    .toFile();

            out = new FileOutputStream(file);
            int read = 0;
            byte[] bytes = new byte[1024];

            while ((read = input.read(bytes)) != -1) {
                out.write(bytes, 0, read);
            }

            String subject = "Sending Payslips via FB report of " + DateUtil.formatMonth(payrollEnd.toDate());
            String body = "";
            body += "<b>Preparing request body is done. The following logs have been inserted:</b> <br/>";
            body += "-RECEIVED: " + receivedCount + "<br/>";
            body += "-CONNECTION_ERROR: " + connectionErrorCount;
            sendWarningEmail(subject, body, recipients, null, file);
            setParameterValue(PayrollManagementModule.
                    PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING, "0");

        } catch (FileNotFoundException ex) {
            setParameterValue(PayrollManagementModule.
                    PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING, "0");
            String subject = "FAILURE - Send Final Summary Email - Payroll of " + DateUtil.formatMonth(payrollEnd.toDate());
            sendWarningEmail(subject, "", recipients, ex, null);
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @PreAuthorize("hasPermission('paySlip','getHousemaidPayslips')")
    @RequestMapping("/getHousemaidPayslips")
    public ResponseEntity getHousemaidPayslips(
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(value = "Name", required = false) String maidName,
            @RequestParam(value = "linkedToYayaBot", required = false) String linkedToYayaBot,
            @RequestParam(value = "Nationality", required = false) PicklistItem nationality,
            @RequestParam(value = "id", required = false) Long housemaidId,
            Pageable pageable) {

        try {
            if (java.sql.Date.valueOf("2020-08-01").after(date)) {
                LocalDate dt = new LocalDate(date);
                LocalDate payrollMonth = dt.withDayOfMonth(1);

                SelectQuery<MonthlyPayrollDocument> query = new SelectQuery<>(MonthlyPayrollDocument.class);
                // Old  payslips
                query.filterBy(new SelectFilter("payrollDate", "=", new java.sql.Date(payrollMonth.toDate().getTime()))
                        .or("payrollDate", "=", new java.sql.Date(payrollMonth.withDayOfMonth(26).toDate().getTime())));

                query.filterBy("type", "=", MonthlyPayrollDocumentType.PAYSLIPS);
                SelectQuery<HousemaidPsid> query2 = new SelectQuery<>(HousemaidPsid.class);
                List<HousemaidPsid> psids = query2.execute();

                if (maidName != null) {
                    query.filterBy("housemaid.name", "LIKE", "%" + maidName + "%");
                }
                if (linkedToYayaBot != null && linkedToYayaBot.equals("LINKED")) {
                    query.filterBy("housemaid.id", "IN", psids.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList()));
                } else if (linkedToYayaBot != null && linkedToYayaBot.equals("NOT_LINKED")) {
                    query.filterBy("housemaid.id", "NOT IN", psids.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList()));
                }
                if (nationality != null) {
                    query.filterBy("housemaid.nationality.id", "=", nationality.getId());
                }

                return new ResponseEntity<>(query.execute(pageable), HttpStatus.OK);

            } else {
                Page<HousemaidPayslipProjection> payslipProjections = payslipService.getHousemaidPayslips(date, maidName, linkedToYayaBot, nationality, housemaidId, "EN", pageable);

                return ResponseEntity.ok(payslipProjections);
            }
        } catch (Exception e) {
            DebugHelper.sendExceptionMail(
                    "<EMAIL>", e,
                    "Exception occurred while generating getHousemaidPayslips", false);
            throw e;
        }
    }

////////////////////////////////////////////////////////////////////////////////

    void sendWarningEmail(String subject, String body,
                          List<EmailRecipient> recipients, Exception ex, File file) {


        if (ex != null) {
            String methodName = "<b>Method Name: </b>" + ex.getStackTrace()[0].getMethodName() + "<br/>";
            String timeStamp = "<b>Time Stamp: </b>" + DateUtil.formatFullDateWithTime(DateUtil.now()) + "<br/>";
            String className = "<b>Class Name: </b>" + ex.getStackTrace()[0].getClassName() + "<br/>";
            String errorMessage = "<b>Error Message: </b>" + ex.getMessage() + "<br/>";
            String lineNumber = "<b>Error Line: </b>" + ex.getStackTrace()[0].getLineNumber() + "<br/>";
            String stackTraceBody = "<b>StackTrace Body: </b><br/>" + getStackTraceMessage(ex);
            body = errorMessage + timeStamp + className + methodName + lineNumber + stackTraceBody;

        }
        String thisMonth = DateUtil.formatMonth(DateUtil.now());
        //subject += " / Payroll of " + thisMonth; 
        //String payrollMonth = "<b>Payroll of " + thisMonth+"</b><br/><br/>";
        TextEmail mail = new TextEmail(subject, body);
        if (file != null)
            mail.addAttachement(file);
        Setup.getMailService().sendEmail(recipients, mail, true, null);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setParameterValue(String code, String value) {
        Parameter param = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(), code);
        param.setValue(value);
        Setup.getRepository(ParameterRepository.class).save(param);
    }

    String getStackTraceMessage(Exception e) {
        StringWriter errors = new StringWriter();
        e.printStackTrace(new PrintWriter(errors));
        String errorsString = errors.toString();
        return "<pre>" + errorsString + "</pre>";
    }


    @Autowired
    private PublicPageHelper publicPageHelper;


    public String generatePayslipPublicURL(Housemaid housemaid, Date date, String lang) {
        return publicPageHelper.generatePublicURL(PublicPageHelper.PAYSLIPS,
                housemaid.getId().toString() + "#" + DateUtil.formatDateDashed(date) + "#" + lang);
    }

    public String generatePayslipPublicURLWithoutShorten(Housemaid housemaid, Date date, String lang) {
        return publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.PAYSLIPS,
                housemaid.getId().toString() + "#" + DateUtil.formatDateDashed(date) + "#" + lang);
    }


    private void insertFbLog(Housemaid housemaid, FbPaySlipsLogStatus status,
                                      LocalDate payrollMonth, List<FbPaySlipsLog> logs) {
        FbPaySlipsLog log = new FbPaySlipsLog();
        log.setHousemaid(housemaid);
        log.setSendDate(new java.sql.Date(new Date().getTime()));
        log.setStatus(status);
        log.setPayrollDate(payrollMonth.toDate());
        logs.add(log);
    }

    public File copyPayslipFile() throws IOException {
        FileInputStream fis = new FileInputStream(PaySlipsController.class.getResource("/payslip.html").getPath());

        File payslipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                "payslipCopy" + new Date().getTime() + ".html")
                .toFile();

        try (FileOutputStream out = new FileOutputStream(payslipFile)) {
            int b;
            while ((b = fis.read()) != -1)
                out.write(b);
            fis.close();
        }

        return payslipFile;
    }


    @NoPermission
    @RequestMapping(value = "/directDownload", method = RequestMethod.POST)
    public ResponseEntity<?> getHousemaidPayslips(@RequestBody byte [] body, HttpServletResponse response) throws IOException {


        String payslipDiv = new String(body);
        String htmlEnding = "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</div>\n" +
                "\n" +
                "</body></html>";

        File f1 = copyPayslipFile();

        FileWriter fileWritter = new FileWriter(f1,true);
        try (BufferedWriter bw = new BufferedWriter(fileWritter)) {
            bw.write(payslipDiv);
            bw.write(htmlEnding);
        }


        String fileName = "payslip-" + new Date().getTime() + ".pdf";
        File payslipFile = Paths.get(System.getProperty("java.io.tmpdir"),
                fileName)
                .toFile();

        try (FileOutputStream out = new FileOutputStream(payslipFile)) {
            HtmlConverter.convertToPdf(new FileInputStream(f1),
                    out);

            createDownloadResponse(response, fileName, new FileInputStream(payslipFile));
//            Attachment att = Storage.storeTemporary(fileName, new FileInputStream(payslipFile), fileName, false);
//
//            response.setContentType(this.getMimeType(att.getExtension()));
//            response.addHeader("Content-Disposition", "attachment; filename=\"" + att.getName() + "\"");
//
//            try {
//                IOUtils.copy(Storage.getStream(att), response.getOutputStream());
//                response.getOutputStream().flush();
//            } catch (IOException var4) {
//                logger.log(Level.SEVERE, var4.getMessage(), var4);
//            }

        }

        return this.okResponse();
    }

    public void sendPayslipsProcess(Date date){
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollMonth = dt.withDayOfMonth(1);

        List<Long> housemaidPayrollLogsIds = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findReadyToSentPayslips(new java.sql.Date(payrollMonth.toDate().getTime()));

        //Send payslips in chunks
        int chunkSize = 50;
        try {
            chunkSize = Integer.parseInt(
                    Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_PAYROLL_SEND_PAYSLIP_CHUNK_SIZE));

        } catch (Exception e) {
            logger.log(Level.SEVERE,  "Error parsing payroll payslip chunk size parameter, using default value: " + chunkSize);
        }

        BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);
        if (housemaidPayrollLogsIds != null && !housemaidPayrollLogsIds.isEmpty()){
            List<String> housemaidPayrollLogsIdsAsString = housemaidPayrollLogsIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            List<List<String>> batches = ListHelper.divideList(housemaidPayrollLogsIdsAsString, chunkSize);

            int i = 0;
            for (List<String> batch: batches) {
                BackgroundTask task = new BackgroundTask
                        .builder("sendPayslipForChunk batch #" + i, Setup.getCurrentModule().getCode(),
                        "paySlipsController", "sendPayslipForChunk")
                        .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                        .withParameters(new Class<?>[]{List.class, Date.class}, batch, date)
                        .build();

                backgroundTaskService.create(task);
                i++;
            }
        }

    }

    // Called as BGT
    public void sendPayslipForChunk(List<String> housemaidPayrollLogsIds, Date date) {

        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollMonth = dt.withDayOfMonth(1);
        List<FbPaySlipsLog> logs = new ArrayList<>();

        int doesNotExistCount = 0;
        int noPayslipsFile = 0;
        int sentViaSms = 0;
        int connectionErrorSms = 0;
        int serverException = 0;
        int sentViaYayaBot = 0;

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);

        List<Long> housemaidPayrollLogsIdsLong = new ArrayList<>();
        try {
            housemaidPayrollLogsIdsLong = housemaidPayrollLogsIds.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.info("housemaidPayrollLogsIdsLong exception: " +e.getMessage() + Arrays.toString(e.getStackTrace()));
        }

        List<HousemaidPayrollLog> housemaidPayrollLogs = Setup.getRepository(HousemaidPayrollLogRepository.class).findDistinctHousemaidPayrollLogByIds(housemaidPayrollLogsIdsLong);

        for (HousemaidPayrollLog doc : housemaidPayrollLogs) {
            try {
                List<HousemaidPsid> housemaidPsids =
                        doc.getHousemaid().getHousemaidPsids().stream()
                                .filter(x -> !x.getDisabled())
                                .collect(Collectors.toList());

                // prepare languages and template
                List<String> langs = PayslipTranslateService.getHousemaidPayslipLanguages(doc.getHousemaid());
                String template = PayslipTranslateService.getPayslipSmsTemplate(doc.getHousemaid());
                // prepare params
                String url = generatePayslipPublicURL(doc.getHousemaid(), date, "en");

                String yayaNotificationUrl = url;
                String yayaLanguage = doc.getHousemaid().getYayaAppNotificationLang();

                if (yayaLanguage != null && yayaLanguage.equalsIgnoreCase("az"))
                    yayaLanguage = "om";
                else if (yayaLanguage != null && yayaLanguage.equalsIgnoreCase("am"))
                    yayaLanguage = "amh";
                Map<String, String> paramValues = new HashMap<>();
                url = shortener.shorten(url);
                paramValues.put("url", url);

                for (String lang : langs) {
                    paramValues.put("payslip_" + lang, generatePayslipPublicURL(doc.getHousemaid(), date, lang));
                    if(lang.equalsIgnoreCase(yayaLanguage))
                        yayaNotificationUrl = generatePayslipPublicURL(doc.getHousemaid(), date, lang);
                }

                boolean sentByYayaAppNotification = false;
                if (doc.getHousemaid().getYayaLastLoginDate() != null && doc.getHousemaid().getPhoneNumber() != null &&
                        !doc.getHousemaid().getPhoneNumber().isEmpty()) {

                    PayrollNotificationsService payrollNotificationsService = Setup.getApplicationContext().getBean(PayrollNotificationsService.class);
                    Setup.getApplicationContext().getBean(MessagingService.class).notifyMaidAboutHerSalaryOnYaya(doc.getHousemaid(), "AED " + NumberFormatter.formatNumber(doc.getTotalSalary()), HousemaidStatus.WITH_CLIENT.equals(doc.getHousemaid().getStatus()), yayaNotificationUrl);
                    sentByYayaAppNotification = true;
                    insertFbLog(doc.getHousemaid(), FbPaySlipsLogStatus.RECEIVED_SMS, payrollMonth, logs);

                }

                if (!sentByYayaAppNotification && doc.getHousemaid().getPhoneNumber() != null &&
                        !doc.getHousemaid().getPhoneNumber().isEmpty()) {
                    SmsResponse smsResponse =
                            smsService.send(
                                    "Housmaids Payslips Sending",
                                    normalizePhoneNumber(doc.getHousemaid().getPhoneNumber()),
                                    SmsReceiverType.Housemaid,
                                    doc.getHousemaid().getId(),
                                    doc.getHousemaid().getName(),
                                    template,
                                    paramValues,
                                    null);
                    if (smsResponse != null && smsResponse.isSuccess()) {
                        insertFbLog(doc.getHousemaid(), FbPaySlipsLogStatus.RECEIVED_SMS, payrollMonth, logs);
                        sentViaSms++;
                    } else {
                        insertFbLog(doc.getHousemaid(), FbPaySlipsLogStatus.CONNECTION_ERROR_SMS, payrollMonth, logs);
                        connectionErrorSms++;
                    }
                } else {
                    insertFbLog(doc.getHousemaid(), FbPaySlipsLogStatus.DOES_NOT_EXIST, payrollMonth, logs);
                    doesNotExistCount++;
                }

            } catch (Exception ex) {
                insertFbLog(doc.getHousemaid(), FbPaySlipsLogStatus.SERVER_EXCEPTION, payrollMonth, logs);
                serverException++;
            }
        }

        fbPaySlipsLogRepository.save(logs);

        for(HousemaidPayrollLog payrollLog: housemaidPayrollLogs) {
            payrollLog.setPayslipSent(true);
        }
        Setup.getRepository(HousemaidPayrollLogRepository.class)
                .save(housemaidPayrollLogs);

        // validate finished all BGT
        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("name", "like", "sendPayslipForChunk batch #%");
        query.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
        List<BackgroundTask> activeTasks = query.execute();

        // Check if this BGT is the last in the set Or there is still <= 5 activeTasks
        if (activeTasks == null || activeTasks.size() <= 5) {

            List<Long> accountantTodosIds = Setup.getRepository(HousemaidPayrollLogRepository.class).findDistinctAccountantTodosForLogs(housemaidPayrollLogsIdsLong);

            // Create BGT to set housemaidsPayslips to sent
            BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);
            if (accountantTodosIds != null && !accountantTodosIds.isEmpty()){
                List<String> accountantTodosIdsAsString = accountantTodosIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());

                BackgroundTask task = new BackgroundTask
                        .builder("markHousemaidsPayslipsAsSent", Setup.getCurrentModule().getCode(),
                        "paySlipsController", "markHousemaidsPayslipsAsSent")
                        .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                        .withParameters(new Class<?>[]{List.class}, accountantTodosIdsAsString)
                        .build();

                backgroundTaskService.create(task);

            }


            //Send Summary Email
            String subject = "DONE - Sending Payslips to all housemaids - Payroll of " + DateUtil.formatMonth(payrollMonth.toDate());
            String body = "Done sending all payslips.";
            sendWarningEmail(subject, body, recipients, null, null);
        }
    }

    // Called as BGT
    public void markHousemaidsPayslipsAsSent(List<String> accountantTodosIds) {
        List<Long> accountantTodosIdsLong = new ArrayList<>();
        try {
            accountantTodosIdsLong = accountantTodosIds.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.info("accountantTodosIdsLong exception: " +e.getMessage() + Arrays.toString(e.getStackTrace()));
        }

        for (Long accountantTodoId : accountantTodosIdsLong){
            PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(accountantTodoId);
            if(accountantTodo.getHousemaidsPayslipsSent() == null || !accountantTodo.getHousemaidsPayslipsSent().equals(true)) {
                accountantTodo.setHousemaidsPayslipsSent(true);
                Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);
            }
        }
    }

    @PreAuthorize("hasPermission('payslip','fetchHousemaidPayslipUrls')")
    @RequestMapping(value = "/fetchHousemaidPayslipUrls", method = RequestMethod.GET)
    public ResponseEntity<?> fetchHousemaidPayslipUrls(
            @RequestParam(name = "mobileNumber",required = false) String mobileNumber,
            @RequestParam(name = "firstName",required = false) String firstName,
            @RequestParam(name = "middleName",required = false) String middleName,
            @RequestParam(name = "lastName",required = false) String lastName,
            @RequestParam(name = "dates") String dates) {

        Map<String, Object> result = new HashMap<>();

        if(mobileNumber == null && firstName == null && middleName == null && lastName == null){
            result.put("errorReason", "Housemaid's name or mobile number is required.");
            return ResponseEntity.ok(result);
        }

        Housemaid housemaid = null;
        List<Housemaid> housemaids = Setup.getApplicationContext().getBean(HousemaidController.class).getHousemaid(mobileNumber, null, firstName, middleName, lastName, null, null);

        if(housemaids != null && !housemaids.isEmpty()) {
            housemaid = housemaids.get(0);
        } else{
            result.put("errorReason", "No housemaid found");
            return ResponseEntity.ok(result);
        }

        List<Date> months = new ArrayList<>();
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            for (String month : dates.split(",")) {
                months.add(dateFormat.parse(month.trim()));
            }
        } catch (ParseException e) {
            result.put("errorReason", "Invalid date format in 'dates' parameter.");
            return ResponseEntity.ok(result);
        }

        int i = 1;
        for (Date month : months) {
            Boolean logExists = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .existsByHousemaidAndPayrollMonthAndTransferredTrue(housemaid, new java.sql.Date(month.getTime()));

            if(logExists.equals(true)) {
                String url = generatePayslipPublicURL(housemaid, month, "en");
                result.put("payslip" + i, url);
            } else {
                result.put("payslip" + i, "");
            }
            i++;
        }

        return ResponseEntity.ok(result);
    }
}
