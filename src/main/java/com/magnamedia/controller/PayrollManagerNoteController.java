package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.projection.HousemaidNoteProjection;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.MaidType;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.logging.Level;

/**
 *
 * <AUTHOR> Abbas <<EMAIL>>
 */

@RequestMapping("/ManagerNotes")
@RestController
public class PayrollManagerNoteController extends BaseRepositoryController<PayrollManagerNote> {

    @Autowired
    private PayrollManagerNoteRepository noteRep;

    @Autowired
    private HousemaidRepository housemaidRep;

    @Autowired
    private OfficeStaffRepository staffRep;

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    @Override
    public BaseRepository<PayrollManagerNote> getRepository() {
        return noteRep;
    }

    @Override
    public ResponseEntity<?> createEntity(PayrollManagerNote entity){
        try {
            if(entity.getOfficeStaff() != null) {
                throw new BusinessException("Can't add an officeStaff's manager note without a final manager approval!");
            }

            LocalDate dt = new LocalDate();
            String lockPayrollEnabled = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_ENABLED);
            Integer lockPayrollStart = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_START));
            Integer lockPayrollEnd = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_END));

            if ((!lockPayrollEnabled.equals("1"))
                    || (dt.getDayOfMonth()<lockPayrollStart && dt.getDayOfMonth()>lockPayrollEnd)
                    || (entity.getNoteType().equals(PayrollManagerNote.ManagerNoteType.DEDUCTION))
                    || (entity.getNoteType().equals(PayrollManagerNote.ManagerNoteType.PENALTY_DEDUCTION))){
                getRepository().save(entity);
                return new ResponseEntity<>(entity, HttpStatus.OK);
            }
            else{
                //Jirra ACC-309
                return new ResponseEntity<>("Manager Note Could not be added, because of the payroll lock.", HttpStatus.BAD_REQUEST);
            }
        }catch (Exception ex){
            throw new BusinessException(ex.getMessage().contains("salary rule not found")
                ? "Sorry we can't proceed with this action, salary rule not found." : ex.getMessage());
        }


    }

    @NoPermission
    @RequestMapping(value = "/customdelete/{id}",
            method = RequestMethod.DELETE)
    public ResponseEntity<?> customDelete(@PathVariable("id") PayrollManagerNote entity) {
        entity.setLogActionRequired(true);
        return super.delete(entity);
    }

    @NoPermission
    @RequestMapping(value = "/bulkcreate",
            method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> bulkCreate(
            @RequestBody List<PayrollManagerNote> entities) {

        List<PayrollManagerNote> successNotes = new ArrayList<>();
        List<PayrollManagerNote> failedNotes = new ArrayList<>();
        if (checkPermission("create")) {
            for (PayrollManagerNote entity : entities){
                if (entity.getId() != null) {
                    failedNotes.add(entity);
                }
                else{
                    try{
                        createEntity(entity);
                        successNotes.add(entity);
                    }
                    catch (Exception e){
                        failedNotes.add(entity);
                    }
                }
            }
        } else {
            return unauthorizedReponse();
        }

        Map<String, List<PayrollManagerNote>> result = new HashMap<>();
        result.put("success", successNotes);
        result.put("failed", failedNotes);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/bulkrefund",
            method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> bulkRefund(
            @RequestBody List<Long> entitiesIds) {

        PicklistItem refundDeduction =
                getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_REFUND_DEDUCTION_ADDITION_CODE);
        PicklistItem refundAddition =
                getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_REFUND_DEDUCTION_ADDITION_CODE);
        List<PayrollManagerNote> result = new ArrayList<>();
        List<PayrollManagerNote> notes = noteRep.findAll(entitiesIds);
        for (PayrollManagerNote note : notes){
            PayrollManagerNote noteToAdd = new PayrollManagerNote();
            noteToAdd.setHousemaid(note.getHousemaid());
            noteToAdd.setOfficeStaff(note.getOfficeStaff());
            noteToAdd.setAmount(note.getAmount());
            noteToAdd.setIsRefund(true);
            noteToAdd.setRefundedNote(note);
            if (note.getOfficeStaff() != null) {
                if (note.getNoteType().equals(PayrollManagerNote.ManagerNoteType.DEDUCTION)) {
                    noteToAdd.setNoteType(PayrollManagerNote.ManagerNoteType.BONUS);
                    noteToAdd.setAdditionReason(refundAddition);
                } else if (note.getNoteType().equals(PayrollManagerNote.ManagerNoteType.BONUS)) {
                    noteToAdd.setNoteType(PayrollManagerNote.ManagerNoteType.DEDUCTION);
                    noteToAdd.setDeductionReason(refundDeduction);
                }
            } else {
                if (note.getNoteType().equals(PayrollManagerNote.ManagerNoteType.DEDUCTION)) {
                    noteToAdd.setNoteType(PayrollManagerNote.ManagerNoteType.ADDITION);
                    noteToAdd.setAdditionReason(refundAddition);
                } else if (note.getNoteType().equals(PayrollManagerNote.ManagerNoteType.ADDITION)) {
                    noteToAdd.setNoteType(PayrollManagerNote.ManagerNoteType.DEDUCTION);
                    noteToAdd.setDeductionReason(refundDeduction);
                }
            }
            noteToAdd.setFromManager(note.getFromManager());
            noteToAdd.setNoteDate(new Date());
            result.add(noteToAdd);
        }

        return bulkCreate(result);
    }

    @NoPermission
    @RequestMapping("/getHousemaidManagerNotes/{id}")
    public ResponseEntity<?> getHousemaidManagerNotes(@PathVariable Long id,
                                                      @RequestParam(required = false, defaultValue = "false") boolean lastSixMonths){
        /*
         * <AUTHOR> Qazzaz
         * @reason PAY-117
         * start
         */
        Housemaid housemaid =housemaidRep.findOne(id);
        Map<String , Object> result = new HashMap<>();
        SalariesAccess salariesAccess = Setup.getRepository(SalariesAccessRepository.class).findTopByUser(CurrentRequest.getUser());
        boolean isAdmin = salariesAccess != null && salariesAccess.getForHousemaids();

        if(housemaid!=null){
            List<PayrollManagerNote.ManagerNoteType> managerNotesTypes = new ArrayList<>();
            managerNotesTypes.add(PayrollManagerNote.ManagerNoteType.ADDITION);
            managerNotesTypes.add(PayrollManagerNote.ManagerNoteType.DEDUCTION);
            Date beforeSixMonthsDate = lastSixMonths ? new DateTime().minusMonths(6).toDate() : null; // SMM-2371
            List<PayrollManagerNote> notes = noteRep.findByHousemaidAndNoteTypesAndNoteDateAfter(housemaid,
                    managerNotesTypes, beforeSixMonthsDate);
            if(notes.size() > 0)
            {
               result.put("managerNotes", project(notes , HousemaidNoteProjection.class ));
               result.put("isAdmin", isAdmin);

               return new ResponseEntity<>(result, HttpStatus.OK);
            }
              
            else
            {
              result = new HashMap<>();
              result.put("managerNotes", new ArrayList<>());
               result.put("isAdmin", isAdmin);
              return new ResponseEntity<>(result, HttpStatus.OK);
            }
        }
            result = new HashMap<>();
            result.put("managerNotes", new ArrayList<>());
            result.put("isAdmin", isAdmin);
            return new ResponseEntity<>(result,HttpStatus.OK);

         /*
         * end
         */
    }

    @NoPermission
    @RequestMapping("/getOfficeStaffManagerNotes/{id}")
    public ResponseEntity<?> getOfficeStaffManagerNotes(@PathVariable Long id){
        OfficeStaff staff=staffRep.findOne(id);
        if(staff!=null)
            return new ResponseEntity<>(noteRep.findByOfficeStaff(staff),HttpStatus.OK);
        return new ResponseEntity<>("Please check the passed OfficeStaff's id",HttpStatus.BAD_REQUEST);

    }

    /**
     * used for deductions only
     * @param managerNote
     * @return
     */
    @NoPermission
    @RequestMapping("/getManagerNotePaymentDate/{id}")
    public ResponseEntity<?> getManagerNotePaymentDate(@PathVariable("id") PayrollManagerNote managerNote){
        Map<String, Object> result = new HashMap<>();

        if(managerNote == null || managerNote.getHousemaid() == null)
            throw new BusinessException("it's not available for this note");

        java.sql.Date paymentDate = lockDateService.getPaymentDate(new java.sql.Date(managerNote.getNoteDate().getTime()), PaymentRuleEmployeeType.HOUSEMAIDS, PayrollType.PRIMARY);

        HousemaidPayrollLog log = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonth(managerNote.getHousemaid(), new java.sql.Date(new LocalDate(managerNote.getNoteDate()).withDayOfMonth(1).toDate().getTime()));
        if(paymentDate == null) {
            result.put("status", "UNKNOWN");
            result.put("paymentDate", null);
        }else if (paymentDate.after(new java.sql.Date(System.currentTimeMillis())) || log == null) {
            result.put("status", "UNPAID");
            result.put("paymentDate", paymentDate);
        }else{
            result.put("status", "PAID");
            result.put("paymentDate", paymentDate);
        }

        return ResponseEntity.ok(result);
    }

    // used in staff SMM-2420
    @NoPermission
    @RequestMapping("/getnextpaymentdate/{id}")
    public java.sql.Date getNextPaymentDate(@PathVariable("id") PayrollManagerNote payrollManagerNote){

        if(payrollManagerNote == null)
            throw new BusinessException("Manager note not found.");

        List<MonthlyPaymentRule> monthlyPaymentRules = monthlyPaymentRuleRepository
                .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(
                        new java.sql.Date(payrollManagerNote.getNoteDate().getTime()),
                        payrollManagerNote.getNoteType().equals(AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION)
                                ? PayrollType.PRIMARY : null);

        Optional<MonthlyPaymentRule> filteredRules = monthlyPaymentRules.stream().filter(
                mpr -> mpr.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)
                        && mpr.getHousemaidTypeList()
                        .contains(MaidType.MAIDS_CC)).findFirst();

        MonthlyPaymentRule monthlyPaymentRule = filteredRules.orElse(null);

        return monthlyPaymentRule != null ? monthlyPaymentRule.getPaymentDate() : null;
    }

    @Transactional
    @RequestMapping(value = "/create-deduction-manager-note", method = RequestMethod.POST)
    public ResponseEntity<?> createDeductionManagerNote(@RequestBody ObjectNode requestBody) {
        logger.log(Level.SEVERE, "Start testBGT");
        String housemaidId = requestBody.get("housemaidId").textValue();
        String amount = requestBody.get("amount").textValue();
        String reasonId = requestBody.get("reasonId").textValue();
        String date = requestBody.get("date").textValue();
        String noteReason = requestBody.get("noteReason").textValue();

        logger.log(Level.SEVERE, "values: " + housemaidId + " || " +
                amount + " || " + reasonId + " || " + date + " || " + noteReason);

        BackgroundTask backgroundTask = new BackgroundTask.builder("createUnemploymentInsuranceDeductionForMaid",
                Setup.getCurrentModule().getCode(),
                "managerNoteService",
                "createUnemploymentInsuranceDeductionForMaid")
                .withParameters(new Class[]{Long.class, Double.class, Long.class, java.sql.Date.class, String.class},
                        Long.parseLong(housemaidId), Double.parseDouble(amount), Long.parseLong(reasonId), java.sql.Date.valueOf(date), noteReason).build();
        Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);

        logger.log(Level.SEVERE, "End testBGT");
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping("/getAdditionsForMaid")
    public ResponseEntity<?> getAdditionsForMaid(
            @RequestParam Long id,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date payrollMonthDate){
        Housemaid housemaid = housemaidRep.findById(id)
                .orElseThrow(() -> new BusinessException("Housemaid does not exist"));

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(payrollMonthDate);

        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        if (dayOfMonth != 1) {
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            payrollMonthDate = calendar.getTime();
        }

        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("payrollMonth", "=", new java.sql.Date(payrollMonthDate.getTime()));
        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        List<MonthlyPaymentRule> listOfRelatedRules = selectQuery.execute();

        if(listOfRelatedRules.isEmpty()) {return ResponseEntity.ok("");}

        Date payrollEnd = new LocalDate(housemaidPayrollPaymentServiceV2.getPayrollEndLockDate(listOfRelatedRules.get(0))).toDate();
        Date payrollStart = new LocalDate(housemaidPayrollPaymentServiceV2.getPayrollStartLockDate(listOfRelatedRules.get(0))).toDate();

        SelectQuery<PayrollManagerNote> selectQueryy = new SelectQuery<>(PayrollManagerNote.class);
        selectQueryy.filterBy("housemaid", "=", housemaid);
        selectQueryy.filterBy("noteDate", ">=", payrollStart);
        selectQueryy.filterBy("noteDate", "<", payrollEnd);
        selectQueryy.filterBy("noteType", "=", AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
        List<PayrollManagerNote> notes = selectQueryy.execute();

        List<HousemaidNoteProjection> result;

        result = project(notes, HousemaidNoteProjection.class);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}