package com.magnamedia.controller;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.EmployeeLoanApprove;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.repository.EmployeeLoanApproveRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.service.EmployeeLoanService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PendingManagerApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Haj <PERSON> <<EMAIL>>
 * Created At 5/16/2020
 **/
@RequestMapping("/loanApprove")
@RestController
public class EmployeeLoanApproveController extends BaseRepositoryController<EmployeeLoanApprove> {

    @Autowired
    private EmployeeLoanApproveRepository employeeLoanApproveRepository;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    @Autowired
    private EmployeeLoanService employeeLoanService ;

    @Override
    public BaseRepository<EmployeeLoanApprove> getRepository() {
        return employeeLoanApproveRepository;
    }

    @Override
    protected ResponseEntity<?> createEntity(EmployeeLoanApprove entity) {
        // check if less than Zero
        if (entity.getAmount() <= 0)
            throw new BusinessException("Loan's amount can't be zero or negative!");
        if (entity.getMonthlyRepaymentAmount() <= 0)
            throw new BusinessException("Loan's repayment amount can't be zero or negative!");
        if(entity.getDeductFromSameMonth() != null && entity.getDeductFromSameMonth()){
            if (!employeeLoanService.canDeductFromSameMonth(entity)){
                throw new BusinessException("Payroll is already paid, you can't add a loan deducted from the same month!");
            }
        }
        entity.setRemainingAmount(entity.getAmount());
        entity.setLoanApproveType(EmployeeLoanApprove.LoanApproveType.NEW_LOAN);
        entity = (EmployeeLoanApprove) super.createEntity(entity).getBody();
        OfficeStaff staff = officeStaffRepository.getOne(entity.getOfficeStaff().getId());
        OfficeStaff finalManager = staff.getFinalManager();
        if (finalManager == null || finalManager.getPhoneNumber() == null)
            throw new BusinessException("Can't find Final Manager or the Manager doesn't have phone number!");
        String url = "";
        url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, entity.getId().toString() + "#Payroll_Add_Loan_Approval", String.valueOf(finalManager.getUser().getId()));
        pendingManagerApprovalService.insertNewPendingApprovalRequest(staff.getName(), DateUtil.formatFullDate(staff.getStartingDate()),staff.getSalaryWithCurrency(), staff.getJobTitle() != null ? staff.getJobTitle().getName() : "", staff.getEmployeeManager(), finalManager, (staff.getSalaryCurrency() != null ? staff.getSalaryCurrency().name() + " " :"") + NumberFormatter.formatNumber(entity.getAmount()), "New Loan", entity.getNotes(), url, entity.getId().toString() + "#Payroll_Add_Loan_Approval");

//        Map<String, String> paramValues = new HashMap<>();
//        paramValues.put("employee_name", staff.getFirstLastName());
//        paramValues.put("value", NumberFormatter.formatNumber(entity.getAmount()) + " " + staff.getSalaryCurrency());
//        paramValues.put("url", url);
//
//        SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                "Adding new loan",
//                normalizePhoneNumber(finalManager.getPhoneNumber()),
//                finalManager.getEmail(),
//                SmsReceiverType.Office_Staff,
//                finalManager.getId(),
//                finalManager.getName(),
//                "Payroll_Add_Loan_Approval",
//                paramValues,
//                null,
//                finalManager.getPreferredCommunicationMethod());
//
//        if (smsResponse == null || !smsResponse.isSuccess())
//            throw new RuntimeException("Failed to send the Approval Message to the Final Manager");
        return ResponseEntity.ok("Your request was received, waiting the employee's final manager approval!");
    }

    @NoPermission
    @RequestMapping("/getOfficeStaffLoansApproves/{id}")
    public ResponseEntity<?> officeStaffLoansApproves(@PathVariable Long id) {
        OfficeStaff staff = officeStaffRepository.findOne(id);
        if (staff != null) {
            return new ResponseEntity<>(employeeLoanApproveRepository.getLoansApprovesByOfficeStaffAndActionTaken(staff.getId(), false), HttpStatus.OK);
        }
        return new ResponseEntity<>("Please check the passed officeStaff's id", HttpStatus.BAD_REQUEST);

    }
}
