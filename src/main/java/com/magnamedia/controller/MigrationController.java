package com.magnamedia.controller;

import com.magnamedia.core.RunModule;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.Module;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.ibanvalidator.SwiftCodeValidationResponse;
import com.magnamedia.core.helper.ibanvalidator.SwiftCodeValidationResult;
import com.magnamedia.core.helper.ibanvalidator.ValidateIBANService;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.master.repository.JobDefinitionRepository;
import com.magnamedia.core.master.repository.JobInstanceRepository;
import com.magnamedia.core.repository.*;
import com.magnamedia.entity.*;
import com.magnamedia.entity.accessmgmt.ExternalAccess;
import com.magnamedia.entity.accessmgmt.OfficeStaffAccess;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.extra.payroll.init.HousemaidVisaInfoProjection;
import com.magnamedia.helper.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.Auditor.AsyncService;
import com.magnamedia.service.EmployeeLoanService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollExceptionsReportService;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import com.magnamedia.service.payroll.generation.newversion.MonthlyPaymentRuleService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.TransferFilesService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.LocalDate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.time.Month;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.extra.StringUtils.*;
import static com.magnamedia.helper.StringHelper.isNotEmpty;
import static java.util.logging.Level.SEVERE;

/**
 * This is an temp controller and should be removed after first deploy on production
 * Purposes: to migrated data from accounting module to this module
 */
@RestController
@RequestMapping("/migrate")
public class MigrationController {
    private final Logger logger = Logger.getLogger(MigrationController.class.getName());

    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;
    @Autowired
    private OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2;
    @Autowired
    private PayrollExceptionsReportService payrollExceptionsReportService;
    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;
    @Autowired
    private TransferDestinationRepository transferDestinationRepository;
    @Autowired
    private BackgroundTaskService backgroundTaskService;

    @Autowired
    private OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository;

    @Autowired
    private ValidateIBANService validateIBANService;

    private final RunModule runModule;
    private final EmployeeLoanService employeeLoanService;

    public MigrationController(RunModule runModule, EmployeeLoanService employeeLoanService) {
        this.runModule = runModule;
        this.employeeLoanService = employeeLoanService;
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'parameters')")
    @RequestMapping(value = "/parameters", method = RequestMethod.GET)
    public ResponseEntity<?> parameters() {

        List<String> exclude = Arrays.asList();

        List<Parameter> parameters = this.runModule.getCustomParameters();

        Module accountingModule = Setup.getModule(PayrollManagementModule.ACCOUNTING);
        Module thisModule = Setup.getCurrentModule();

        int total = parameters.size() - exclude.size();
        int size = 0;

        for(Parameter param: parameters) {
            String code = param.getCode();
            if (!exclude.contains(code)) {
                Parameter accParam = Setup.getRepository(ParameterRepository.class)
                .findByModuleAndCode(accountingModule, code);

                Parameter payrollParam = Setup.getRepository(ParameterRepository.class)
                        .findByModuleAndCode(thisModule, code);

                if(accParam != null && payrollParam != null) {
                    payrollParam.setValue(accParam.getValue());
                    payrollParam.setDescription(accParam.getDescription());
                    payrollParam.setActive(accParam.getActive());

                    Setup.getRepository(ParameterRepository.class).save(payrollParam);
                    size++;
                }

            }
        }
        return ResponseEntity.ok(String.format("%s/%s parameters migrated successfully!", size, total));
    }


    @Transactional
    @PreAuthorize("hasPermission('migration', 'jobInstances')")
    @RequestMapping(value = "/jobInstances", method = RequestMethod.GET)
    public ResponseEntity<?> jobInstances() {


        List<JobDefinition> jobDefinitions = this.runModule.getCustomJobDefinitions();

        Module accountingModule = Setup.getModule(PayrollManagementModule.ACCOUNTING);
        Module thisModule = Setup.getCurrentModule();

        int total = jobDefinitions.size();
        int size = 0;

        for(JobDefinition definition: jobDefinitions) {
            JobDefinition accountingJobDefinition = Setup.getRepository(JobDefinitionRepository.class)
                    .findByModuleAndCode(accountingModule, definition.getCode());

            JobDefinition thisModuleDefinition = Setup.getRepository(JobDefinitionRepository.class)
                    .findByModuleAndCode(thisModule, definition.getCode());

            if(accountingJobDefinition != null && thisModuleDefinition != null) {
                SelectQuery<JobInstance> query = new SelectQuery<>(JobInstance.class);
                query.filterBy("definition", "=", accountingJobDefinition);

                for(JobInstance jobInstance: query.execute()) {
                    jobInstance.setDefinition(thisModuleDefinition);
                    Setup.getRepository(JobInstanceRepository.class).save(jobInstance);
                    ++size;
                }
            }
        }

        return ResponseEntity.ok(String.format("%s/%s job instances migrated successfully!", size, total));
    }


    /************************************ V1.0 ************************************/
    @Transactional
    @PreAuthorize("hasPermission('migration', 'officeStaffInfo')")
    @RequestMapping(value = "/officeStaffInfo", method = RequestMethod.GET)
    public ResponseEntity<?> officeStaffInfo() {
        PicklistItem dubaiCity = PicklistHelper.getItem("cities", "dubai");
        PicklistItem emiratiNationality = PicklistHelper.getItem("nationalities", "emirati");

        List<OfficeStaff> activeStaff = Setup.getRepository(OfficeStaffRepository.class)
                .findActiveOfficeStaff();

        Picklist countriesList = Setup.getRepository(PicklistRepository.class)
                .findByCode("countries");

        int totalActive = activeStaff.size(), emirati = 0, expat = 0, overseas = 0;

        for(OfficeStaff officeStaff: activeStaff) {
            // set city name
            if(officeStaff.getCity() != null) {
                officeStaff.setCityName(officeStaff.getCity().getName());
                PicklistItem country = officeStaff.getCity().getRoot();
                if(country == null) {
                    country = Setup.getRepository(PicklistItemRepository.class)
                            .findByListAndCodeIgnoreCase(countriesList, officeStaff.getCity().getName());
                }

                officeStaff.setCountry(country);
            }

            // set office staff type
            if(officeStaff.getNationality() != null && officeStaff.getNationality().getId().equals(emiratiNationality.getId())) {
                officeStaff.setEmployeeType(OfficeStaffType.DUBAI_STAFF_EMARATI);
                emirati++;
                continue;
            }

            if(officeStaff.getCity() != null && officeStaff.getCity().getId().equals(dubaiCity.getId())) {
                officeStaff.setEmployeeType(OfficeStaffType.DUBAI_STAFF_EXPAT);
                expat++;
                continue;
            }
            officeStaff.setEmployeeType(OfficeStaffType.OVERSEAS_STAFF);
            overseas++;
        }
        Setup.getRepository(OfficeStaffRepository.class).save(activeStaff);

        Map<String, Object> result = new HashMap<>();
        result.put("Total Active", totalActive);
        result.put("Emirati", emirati);
        result.put("Expat", expat);
        result.put("Overseas", overseas);
        return ResponseEntity.ok(result);
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'officeStaffAdditionManagerNotes')")
    @RequestMapping(value = "/officeStaffAdditionManagerNotes", method = RequestMethod.GET)
    public ResponseEntity<?> officeStaffAdditionManagerNotes() {
        SelectQuery<PayrollManagerNote> query = new SelectQuery<>(PayrollManagerNote.class);
        query.filterBy("officeStaff", "IS NOT NULL", null)
                .and("housemaid", "IS NULL", null)
                .and("noteType", "=", AbstractPayrollManagerNote.ManagerNoteType.ADDITION);

        List<PayrollManagerNote> notes = query.execute();

        for(PayrollManagerNote note: notes) {
            note.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.BONUS);
        }
        if(!notes.isEmpty()) {
            Setup.getRepository(PayrollManagerNoteRepository.class).save(notes);
        }
        return ResponseEntity.ok(String.format("%s addition notes migrated!", notes.size()));
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'officeStaffLoans')")
    @RequestMapping(value = "/officeStaffLoans", method = RequestMethod.GET)
    public ResponseEntity<?> officeStaffLoans() {

        int size = 0;
        List<OfficeStaff> activeStaff = Setup.getRepository(OfficeStaffRepository.class)
                .findActiveOfficeStaff();

        for(OfficeStaff officeStaff: activeStaff) {
            List<Repayment> repayments = Setup.getRepository(RepaymentRepository.class)
                    .findByOfficestaff(officeStaff);
            Double loansAmount = Setup.getRepository(EmployeeLoanRepository.class)
                    .sumOldLoansByOfficeStaff(officeStaff);
            if(loansAmount == null) loansAmount = 0.0;

            Double paidRepayments = 0.0;
            for (Repayment r : repayments) {
                if (r.getPaidRepayment() != null && r.getPaidRepayment())
                    paidRepayments += r.getAmount();
            }

            double newLoanAmount = loansAmount - paidRepayments;

            Double defaultRepaymentMethod = officeStaff.getDefaulMonthlyRepayment();

            if (defaultRepaymentMethod == null
                    || defaultRepaymentMethod.equals(0.0)) {
                defaultRepaymentMethod = 500d;
            }

            if(newLoanAmount > 0.0) {

                EmployeeLoan newLoan = new EmployeeLoan();
                newLoan.setOfficeStaff(officeStaff);
                newLoan.setAmount(newLoanAmount);
                newLoan.setMonthlyRepaymentAmount(defaultRepaymentMethod);
                newLoan.setEditable(true);
                newLoan.setShown(false);
                newLoan.setLoanDate(new Date());
                newLoan.setLoanType(AbstractEmployeeLoan.LoanType.CASH_ADVANCE);
                newLoan.setNotes("Data migration for Payroll V1.0");
                Setup.getRepository(EmployeeLoanRepository.class).save(newLoan);

                this.employeeLoanService.generateAllRepayments(newLoan.getMonthlyRepaymentAmount(), newLoan.getLoanDate(), newLoan);

                Double loanBalance = officeStaff.getLoanBalance();
                loanBalance = loanBalance == null ? 0.0 : loanBalance;
                if(newLoanAmount != loanBalance)  {
                    throw new RuntimeException("Failed to migrate loans for OfficeStaff #" + officeStaff.getId());
                }

                size++;
            }

        }
        Setup.getRepository(OfficeStaffRepository.class).save(activeStaff);

        return ResponseEntity.ok(String.format("%s new loans created!", size));
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'officeStaffSalaryTransferInfo')")
    @RequestMapping(value = "/officeStaffSalaryTransferInfo", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> officeStaffSalaryTransferInfo(@RequestBody(required = false) MultipartFile file) {
        try {

            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                Row row = iterator.next();
                if(rowNum++ == 0) {
                    continue;
                }
                String employeeName = formatter.formatCellValue(row.getCell(0));
                String employeeType = formatter.formatCellValue(row.getCell(1));
                String receiverName = formatter.formatCellValue(row.getCell(2));
                String receiverMobileNumber = formatter.formatCellValue(row.getCell(3));
                String paymentMethod = formatter.formatCellValue(row.getCell(4));
                String bankName = formatter.formatCellValue(row.getCell(5));
                String accountNumber = formatter.formatCellValue(row.getCell(6));
                String iban = formatter.formatCellValue(row.getCell(7));
                String accountHolderName = formatter.formatCellValue(row.getCell(8));
                String swiftCode = formatter.formatCellValue(row.getCell(9));
                String nearestCenter = formatter.formatCellValue(row.getCell(13));
                if(!notEmpty(nearestCenter)) {
                    nearestCenter = formatter.formatCellValue(row.getCell(14));
                }

                if(!notEmpty(iban)) {
                    iban = null;
                }

                List<OfficeStaff> staffs = Setup.getRepository(OfficeStaffRepository.class).findByNameContaining(employeeName);
                if(staffs.isEmpty()) continue;
                OfficeStaff officeStaff = staffs.get(0);

                if(employeeType.equals("Emirati") || employeeType.equals("Expat")) {
                    TransferDestination destination = new TransferDestination();
                    destination.setOfficeStaff(officeStaff);
                    destination.setSelfReceiver(true);
                    destination.setReceiveMoneyMethod(ReceiveMoneyMethod.BANK_TRANSFER);
                    destination.setAccountHolderName(accountHolderName);
                    destination.setBankName(bankName);
                    destination.setAccountNumber(accountNumber);
                    destination.setIban(iban);
                    Setup.getRepository(TransferDestinationRepository.class).save(destination);


                    officeStaff.setSelectedTransferDestination(destination);
                    officeStaff.getTransferDestinations().add(destination);

                    OfficeStaffType type = employeeType.equals("Emirati") ? OfficeStaffType.DUBAI_STAFF_EMARATI : OfficeStaffType.DUBAI_STAFF_EXPAT;

                    officeStaff.setEmployeeType(type);

                    Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
                } else {

                    TransferDestination destination = new TransferDestination();
                    destination.setOfficeStaff(officeStaff);
                    destination.setSelfReceiver(officeStaff.getName().equalsIgnoreCase(receiverName));
                    destination.setReceiveMoneyMethod(paymentMethod.equals("Bank Transfer")
                            || notEmpty(swiftCode) || notEmpty(iban) ? ReceiveMoneyMethod.BANK_TRANSFER : ReceiveMoneyMethod.MONEY_TRANSFER_CENTER);

                    destination.setName(receiverName);
                    destination.setPhoneNumber(receiverMobileNumber);
                    destination.setAccountHolderName(accountHolderName);
                    destination.setIban(iban);
                    destination.setBankName(bankName);
                    destination.setAccountNumber(accountNumber);
                    destination.setSwiftCode(swiftCode);
                    destination.setNearestCenter(nearestCenter);
                    Setup.getRepository(TransferDestinationRepository.class).save(destination);

                    officeStaff.setSelectedTransferDestination(destination);
                    officeStaff.getTransferDestinations().add(destination);
                    officeStaff.setEmployeeType(OfficeStaffType.OVERSEAS_STAFF);

                    Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
                }

            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    /************************************ V2.1 ************************************/

    @Transactional
    @PreAuthorize("hasPermission('migration', 'accesses')")
    @RequestMapping(value = "/accesses", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> accesses(@RequestBody(required = false) MultipartFile file) throws IOException, InvalidFormatException {

        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();
        DataFormatter formatter = new DataFormatter();
        OfficeStaffRepository repository = Setup.getRepository(OfficeStaffRepository.class);
        ExternalAccessRepository accessRepository = Setup.getRepository(ExternalAccessRepository.class);
        OfficeStaffAccessRepository officeStaffAccessRepository = Setup.getRepository(OfficeStaffAccessRepository.class);

        OfficeStaff adeeb = repository.findByNameContaining("ADIB NABIL KHAL").get(0);

        List<OfficeStaff> candidatesToBeWessam = repository.findByNameContaining("Wessam Tannos");
        if(candidatesToBeWessam.isEmpty()) candidatesToBeWessam = repository.findByNameContaining("Wessam Tannos Tannos");

        OfficeStaff wessam = null;
        if(!candidatesToBeWessam.isEmpty()) {
            wessam = candidatesToBeWessam.get(0);
        }

        OfficeStaff sulaiman = repository.findByNameContaining("Sulaiman Imad Aldeen Almoghrabi").get(0);
        OfficeStaff malek = repository.findByNameContaining("Malek Elia Elie Barghout").get(0);
        OfficeStaff mugy = repository.findByNameContaining("Karim Ahmed Hussein Aly Elmoghraby").get(0);
        OfficeStaff leen = repository.findByNameContaining("Leen Osamah Taha").get(0);

        int saved = 0, deleted = 0;

        int rowNum = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if (rowNum++ <= 1) {
                continue;
            }

            String accessName = formatter.formatCellValue(row.getCell(0));
            String handlerName = formatter.formatCellValue(row.getCell(2));
            if (accessName == null || handlerName == null) continue;


            accessName = accessName.trim();
            handlerName = handlerName.trim();

            List<ExternalAccess> accesses = accessRepository.findByNameContainingAndAdminIsNull(accessName.trim());
            if (accesses.isEmpty()) continue;

            for (ExternalAccess access : accesses) {
                boolean save = true;
                if (handlerName.equalsIgnoreCase("adeeb")) {
                    access.setAdmin(adeeb);
                } else if (handlerName.equalsIgnoreCase("malek")) {
                    access.setAdmin(malek);
                } else if (handlerName.equalsIgnoreCase("sulaiman")) {
                    access.setAdmin(sulaiman);
                } else if (handlerName.equalsIgnoreCase("wesam")) {
                    access.setAdmin(wessam);
                } else if (handlerName.equalsIgnoreCase("mugy")) {
                    access.setAdmin(mugy);
                } else if (handlerName.equalsIgnoreCase("leen")) {
                    access.setAdmin(leen);
                } else if (handlerName.equalsIgnoreCase("delete")) {
                    save = false;
                    if (!access.getEmployees().isEmpty()) {
                        List<OfficeStaff> employeesWithAccess = access.getEmployees().stream().map(OfficeStaffAccess::getEmployee)
                                .collect(Collectors.toList());
                        for (OfficeStaffAccess officeStaffAccess : access.getEmployees()) {
                            officeStaffAccessRepository.delete(officeStaffAccess);
                        }

                        for (OfficeStaff staff : employeesWithAccess) {
                            staff.getEmployeeAccesses().remove(access);
                            repository.save(staff);
                        }
                    }

                    accessRepository.delete(access);
                }

                if (save) {
                    accessRepository.save(access);
                    saved++;
                } else {
                    deleted++;
                }
            }
        }
        return ResponseEntity.ok(String.format("Updated: %s, Deleted: %s", saved, deleted));
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'syrianEmployeesAddress')")
    @RequestMapping(value = "/syrianEmployeesAddress", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> syrianEmployeesAddress(@RequestBody(required = false) MultipartFile file) throws IOException, InvalidFormatException {

        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();
        DataFormatter formatter = new DataFormatter();
        OfficeStaffRepository repository = Setup.getRepository(OfficeStaffRepository.class);
        PicklistItem syria = PicklistHelper.getItem("countries", "syria");

        int migrated = 0;
        int rowNum = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if (rowNum++ < 1) {
                continue;
            }

            String staffName = formatter.formatCellValue(row.getCell(1));
            String fullAddress = formatter.formatCellValue(row.getCell(2));
            String city = formatter.formatCellValue(row.getCell(4));

            if (staffName == null || fullAddress == null || city == null) continue;

            staffName = staffName.trim();
            fullAddress = fullAddress.trim();
            city = city.trim();

            if(staffName.isEmpty()) continue;

            List<OfficeStaff> allStaff = repository.findByNameContaining(staffName);
            if(allStaff.isEmpty()) continue;

            OfficeStaff staff = allStaff.get(0);

            staff.setCityName(city);
            staff.setFullAddress(fullAddress);
            staff.setCountry(syria);
            repository.save(staff);
            migrated++;
        }
        return ResponseEntity.ok(String.format("%s office staff updated", migrated));
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'teamsAndManagers')")
    @RequestMapping(value = "/teamsAndManagers", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> teamsAndManagers(@RequestBody(required = false) MultipartFile file) throws IOException, InvalidFormatException {
        Picklist jobTitles = Setup.getRepository(PicklistRepository.class).findByCode(PayrollManagementModule.PICKLIST_JOB_TITLE);
        Picklist teams = Setup.getRepository(PicklistRepository.class).findByCode("OfficestaffTeam");


        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();
        DataFormatter formatter = new DataFormatter();
        OfficeStaffRepository repository = Setup.getRepository(OfficeStaffRepository.class);

        OfficeStaff adeeb = repository.findByNameContaining("ADIB NABIL KHAL").get(0);

        List<OfficeStaff> candidatesToBeWessam = repository.findByNameContaining("Wessam Tannos");
        if(candidatesToBeWessam.isEmpty()) candidatesToBeWessam = repository.findByNameContaining("Wessam Tannos Tannos");

        OfficeStaff wessam = null;
        if(!candidatesToBeWessam.isEmpty()) {
            wessam = candidatesToBeWessam.get(0);
        }

        OfficeStaff sulaiman = repository.findByNameContaining("Sulaiman Imad Aldeen Almoghrabi").get(0);
        OfficeStaff malek = repository.findByNameContaining("Malek Elia Elie Barghout").get(0);
        OfficeStaff mugy = repository.findByNameContaining("Karim Ahmed Hussein Aly Elmoghraby").get(0);
        OfficeStaff leen = repository.findByNameContaining("Leen Osamah Taha").get(0);
        OfficeStaff jad = repository.findByNameContaining("JAD ELIE BARGHOUT").get(0);
        OfficeStaff jessica = repository.findByNameContaining("Jessica Joao Abdallah").get(0);
        OfficeStaff hassan = repository.findByNameContaining("Hassan Rudwan Sundouk").get(0);
        OfficeStaff Omer = repository.findByNameContaining("Omer Ihsan Ibrahim Awad").get(0);
        OfficeStaff hussein = repository.findByNameContaining("Hussein Ibrahim Al Ahmad").get(0);
        OfficeStaff mario = repository.findByNameContaining("Mario Gharios Gharios").get(0);

        OfficeStaff georges = repository.findByNameContaining("Georges Aref Chaoul").get(0);
        OfficeStaff rami = repository.findByNameContaining("Rami Nazih Abboud").get(0);
        OfficeStaff sara = repository.findByNameContaining("Sara Samir Alaloul").get(0);
        OfficeStaff georgeAbboud = repository.findByNameContaining("George Nazih Abboud").get(0);
        OfficeStaff homam = repository.findByNameContaining("Mohamad Homam Sadeldien Yasminah").get(0);
        OfficeStaff rosetteKhalilNabky = repository.findByNameContaining("Rosette Khalil Nabky").get(0);
        OfficeStaff ward = repository.findByNameContaining("Ward Hameed Shiban").get(0);
        OfficeStaff ali = repository.findByNameContaining("Ali Hamdar Hamdar").get(0);
        OfficeStaff omair = repository.findByNameContaining("Omair Faez Aldebes").get(0);
        OfficeStaff mira = repository.findByNameContaining("Mira Michel Chammas").get(0);

        OfficeStaff rita = repository.findByNameContaining("Rita Narsis Tashejyan").get(0);
        OfficeStaff monique = repository.findByNameContaining("MONIQUE FAYE LIM MENDOZA").get(0);


        int migrated = 0;
        int rowNum = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if (rowNum++ < 1) {
                continue;
            }

            String staffName = formatter.formatCellValue(row.getCell(1));
            String manager = formatter.formatCellValue(row.getCell(2));
            String jobTitle = formatter.formatCellValue(row.getCell(4));
            String team = formatter.formatCellValue(row.getCell(5));

            if (staffName == null) continue;

            staffName = staffName.trim();

            if(staffName.isEmpty()) continue;

            List<OfficeStaff> allStaff = repository.findByNameContaining(staffName);
            if (allStaff.isEmpty()) continue;
            OfficeStaff staff = allStaff.get(0);

            if (jobTitle != null && !jobTitle.isEmpty()) {
                if (jobTitles.getItemByCode(PicklistItem.getCode(jobTitle)) == null) {
                    jobTitles.addItem(jobTitle);
                    Setup.getRepository(PicklistRepository.class).save(jobTitles);
                }
                staff.setJobTitle(jobTitles.getItemByName(jobTitle));
            }

            if (team != null && !team.isEmpty()) {
                if (teams.getItemByCode(PicklistItem.getCode(team)) == null) {
                    teams.addItem(team);
                    Setup.getRepository(PicklistRepository.class).save(teams);
                }
                staff.setTeam(teams.getItemByName(team));
            }

            OfficeStaff managerItem = null;

            if (manager != null && !manager.isEmpty()) {
                switch (manager) {
                    case "Jad":
                        managerItem = jad;
                        break;
                    case "Malek":
                        managerItem = malek;
                        break;
                    case "Jessica Abdallah":
                        managerItem = jessica;
                        break;
                    case "Adeeb":
                        managerItem = adeeb;
                        break;
                    case "Hassan Sundouk":
                        managerItem = hassan;
                        break;
                    case "Omer Awad":
                        managerItem = Omer;
                        break;
                    case "Hussein":
                        managerItem = hussein;
                        break;
                    case "Mario Gharios":
                        managerItem = mario;
                        break;
                    case "Georges CHaoul":
                        managerItem = georges;
                        break;
                    case "Rami Abboud":
                        managerItem = rami;
                        break;
                    case "Sara Aloul":
                        managerItem = sara;
                        break;
                    case "George Abboud":
                        managerItem = georgeAbboud;
                        break;
                    case "Homam":
                        managerItem = homam;
                        break;
                    case "Rosette Nabky":
                        managerItem = rosetteKhalilNabky;
                        break;
                    case "Ward Shaiban":
                        managerItem = ward;
                        break;
                    case "Ali Hamdar":
                        managerItem = ali;
                        break;
                    case "Mugy":
                        managerItem = mugy;
                        break;
                    case "Omair Faez Aldebes":
                        managerItem = omair;
                        break;
                    case "Mira Chammas":
                        managerItem = mira;
                        break;
                    case "Rita Tashjian":
                        managerItem = rita;
                        break;
                    case "Sulaiman Almoughrabi":
                        managerItem = sulaiman;
                        break;
                    case "Wessam Tannos":
                        managerItem = wessam;
                        break;
                    case "Monique":
                        managerItem = monique;
                        break;
                }
                staff.setEmployeeManager(managerItem);
            }
            repository.save(staff);
            ++migrated;
        }

        return ResponseEntity.ok(String.format("%s office staff updated!", migrated));
    }


    public boolean notEmpty(String value) {
        return value != null && !value.trim().isEmpty() && !value.trim().equals("-") && value.length() > 0;
    }


    @Transactional
    @PreAuthorize("hasPermission('migration', 'allOfficeStaffInfo')")
    @RequestMapping(value = "/allOfficeStaffInfo", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> allOfficeStaffInfo(@RequestBody(required = false) MultipartFile file) {
        try {

            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                Row row = iterator.next();
                if (rowNum++ == 0) {
                    continue;
                }

                String employeeName = getCellValue(row, 1, formatter);
                if(employeeName == null) continue;;


                String employeeArabicName = getCellValue(row, 2, formatter);
                String employeeType = getCellValue(row, 3, formatter);
                // Reciever info
                String receiverName = getCellValue(row, 4, formatter);
                String receiverArabicName = getCellValue(row, 5, formatter);
                String receiverMobileNumber = getCellValue(row, 6, formatter);
                String receiverCountry = getCellValue(row, 7, formatter);
                String receiverCity = getCellValue(row, 8, formatter);


                String bankName = getCellValue(row, 9, formatter);
                String accountNumber = getCellValue(row, 10, formatter);
                String iban = getCellValue(row, 11, formatter);
                String accountHolderName = getCellValue(row, 12, formatter);
                String swiftCode = getCellValue(row, 13, formatter);


                String countryOfResidence = getCellValue(row, 14, formatter);
                String city = getCellValue(row, 15, formatter);


                boolean selfReceiver = receiverName == null;
                boolean bankTransfer = bankName != null || accountHolderName != null || accountNumber != null
                        || swiftCode != null || iban != null;


                List<OfficeStaff> staffs = Setup.getRepository(OfficeStaffRepository.class).findByNameContaining(employeeName);
                if (staffs.isEmpty()) continue;
                OfficeStaff officeStaff = staffs.get(0);


                TransferDestination destination = new TransferDestination();
                destination.setOfficeStaff(officeStaff);
                destination.setSelfReceiver(selfReceiver);

                PicklistItem receiverCountryItem = null;

                if (receiverCountry != null) {
                    if (receiverCountry.equalsIgnoreCase("Philippine")) {
                        receiverCountry = "philippines";
                    }
                    receiverCountryItem = Setup.getItem("countries", receiverCountry);
                }

                if (!selfReceiver) {
                    destination.setName(receiverName);
                    destination.setFullNameInArabic(receiverArabicName);
                    destination.setPhoneNumber(receiverMobileNumber);
                    destination.setCountry(receiverCountryItem);
                    destination.setCityName(receiverCity);
                }

                if (bankTransfer) {
                    destination.setReceiveMoneyMethod(ReceiveMoneyMethod.BANK_TRANSFER);
                } else {
                    destination.setReceiveMoneyMethod(employeeType.equals("Overseas") ? ReceiveMoneyMethod.MONEY_TRANSFER_CENTER :
                            ReceiveMoneyMethod.MONEY_TRANSFER);
                }

                destination.setAccountHolderName(accountHolderName);
                destination.setBankName(bankName);
                destination.setAccountNumber(accountNumber);
                destination.setIban(iban);
                destination.setSwiftCode(swiftCode);
                Setup.getRepository(TransferDestinationRepository.class).save(destination);

                if(officeStaff.getFullNameInArabic() == null) {
                    officeStaff.setFullNameInArabic(employeeArabicName);
                }

                if(officeStaff.getCityName() == null) {
                    officeStaff.setCityName(city);
                }

                if(officeStaff.getCountry() == null && countryOfResidence != null) {
                    officeStaff.setCountry(Setup.getItem("countries", countryOfResidence));
                }

                officeStaff.setSelectedTransferDestination(destination);
                officeStaff.getTransferDestinations().add(destination);


                if(!selfReceiver) {
                    TransferDestination selfDestination = new TransferDestination();
                    selfDestination.setSelfReceiver(true);
                    selfDestination.setOfficeStaff(officeStaff);
                    selfDestination.setReceiveMoneyMethod(ReceiveMoneyMethod.MONEY_TRANSFER_CENTER);
                    Setup.getRepository(TransferDestinationRepository.class).save(selfDestination);
                    officeStaff.getTransferDestinations().add(selfDestination);
                }
                Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'syrianBankAccounts')")
    @RequestMapping(value = "/syrianBankAccounts", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> syrianBankAccounts(@RequestBody(required = false) MultipartFile file) {
        try {

            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                Row row = iterator.next();
                if (rowNum++ == 0) {
                    continue;
                }

                String employeeName = getCellValue(row, 1, formatter);

                if(employeeName == null) continue;;

                String bankName = getCellValue(row, 3, formatter);
                String iban = getCellValue(row, 4, formatter);
                String accountNumber = getCellValue(row, 5, formatter);
                String accountHolderName = getCellValue(row, 6, formatter);
                String swiftCode = getCellValue(row, 7, formatter);



                List<OfficeStaff> staffs = Setup.getRepository(OfficeStaffRepository.class).findByNameContaining(employeeName);
                if (staffs.isEmpty()) continue;
                OfficeStaff officeStaff = staffs.get(0);


                TransferDestination destination =  officeStaff.getSelectedTransferDestination() != null ?
                        officeStaff.getSelectedTransferDestination() : new TransferDestination();


                destination.setOfficeStaff(officeStaff);
                destination.setSelfReceiver(false);

                destination.setAccountHolderName(accountHolderName);
                destination.setBankName(bankName);
                destination.setAccountNumber(accountNumber);
                destination.setIban(iban);
                destination.setSwiftCode(swiftCode);
                Setup.getRepository(TransferDestinationRepository.class).save(destination);

                if(officeStaff.getSelectedTransferDestination() == null) {
                    officeStaff.setSelectedTransferDestination(destination);
                    officeStaff.getTransferDestinations().add(destination);
                }

                Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'onHoldStaff')")
    @RequestMapping(value = "/onHoldStaff", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> onHoldStaff(@RequestBody(required = false) MultipartFile file) {
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                Row row = iterator.next();
                if (rowNum++ == 0) {
                    continue;
                }


                String employeeName = getCellValue(row, 1, formatter);
                if(employeeName == null) continue;;

                List<OfficeStaff> staffs = Setup.getRepository(OfficeStaffRepository.class).findByNameContaining(employeeName);
                if (staffs.isEmpty()) continue;
                OfficeStaff officeStaff = staffs.get(0);

                officeStaff.setExcludedFromPayroll(true);

                Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'jobTitleManagers')")
    @RequestMapping(value = "/jobTitleManagers", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> jobTitleManagers(@RequestBody(required = false) MultipartFile file) {
        try {
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                Row row = iterator.next();
                if (rowNum++ == 0) {
                    continue;
                }


                String jobTitleName  = getCellValue(row, 0, formatter);
                String managerName = getCellValue(row, 1, formatter);
                if(jobTitleName == null || managerName == null) continue;;

                jobTitleName = jobTitleName.trim();
                managerName = managerName.trim();
                if(jobTitleName.isEmpty() || managerName.isEmpty()) continue;


                List<OfficeStaff> staffs = Setup.getRepository(OfficeStaffRepository.class).findByNameContaining(managerName);
                if (staffs.isEmpty()) continue;
                OfficeStaff officeStaff = staffs.get(0);

                PicklistItem item = Setup.getItem("JOB_TITLE", PicklistItem.getCode(jobTitleName));

                if(item == null) continue;

                JobTitleManager titleManager = new JobTitleManager();
                titleManager.setJobTitle(item);
                titleManager.setManager(officeStaff);
                Setup.getRepository(JobTitleManagerRepository.class).save(titleManager);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }


    String getCellValue(Row row, int cellNumber, DataFormatter formatter) {
        Cell cell = row.getCell(cellNumber);
        if (cell == null || cell.getCellType() != CellType.STRING) return null;
        String value = formatter.formatCellValue(cell);
        if(value.trim().isEmpty() || value.trim().equalsIgnoreCase("-")) return null;
        return value.trim();
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'internetAllowance')")
    @RequestMapping(value = "/internetAllowance", method = RequestMethod.GET)
    public ResponseEntity<?> internetAllowance() {

        List<OfficeStaff> allStaff = Setup.getRepository(OfficeStaffRepository.class)
                .findAll();

        for (OfficeStaff staff : allStaff) {
            double internetAllowance = staff.getInternetAllowance() == null ? 0d : staff.getInternetAllowance();

            staff.setInternetAllowance(0d);
            staff.setOldInternetAllowance(internetAllowance);

            double newBasicSalary = staff.getBasicSalary() + internetAllowance;

            staff.setBasicSalary(newBasicSalary);

            double components = 0.0;
            if (staff.getBasicSalary() != null) components += staff.getBasicSalary();
            if (staff.getHousingAllowance() != null) components += staff.getHousingAllowance();
            if (staff.getTrasnportation() != null) components += staff.getTrasnportation();

            if (components > 0.0) {
                staff.setSalary(components);
            }
        }
        return ResponseEntity.ok("Success");
    }

    /************************************ V1.1 ************************************/
    @Transactional
    @PreAuthorize("hasPermission('migration', 'housemaidScheduledAnnualVacations')")
    @RequestMapping(value = "/housemaidScheduledAnnualVacations", method = RequestMethod.GET)
    public ResponseEntity<?> housemaidScheduledAnnualVacations() {
        String code = "airfare";

        SelectQuery<ScheduledAnnualVacation> query = new SelectQuery<>(ScheduledAnnualVacation.class);
        query.filterBy("housemaid", "IS NOT NULL", null);
        query.filterBy("payrollDueDate", ">=", PayrollGenerationLibrary.getPayrollStartDate(LocalDate.now()).toDate());
        query.filterBy("type", "IS NOT NULL", null)
                .and("type.code", "LIKE", "%" + code + "%");

        List<ScheduledAnnualVacation> vacations = query.execute();
        int size = 0;

        for(ScheduledAnnualVacation entity: vacations) {
            if(Setup.getRepository(PayrollManagerNoteRepository.class)
                    .findTopByScheduledAnnualVacationOrderByNoteDateDesc(entity) != null) {
                continue;
            }

            PayrollManagerNote note = new PayrollManagerNote();
            note.setHousemaid(entity.getHousemaid());
            note.setScheduledAnnualVacation(entity);
            note.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
            note.setNoteDate(entity.getPayrollDueDate());
            note.setNoteReasone(entity.getInformation());
            note.setAmount(entity.getAmount());
            note.setAdditionReason(PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                    PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_AIRFARE_TICKET_ADDITION_CODE));
            note.setFromManager(PicklistHelper.getItem(
                    "managers", "jad"));
            Setup.getRepository(PayrollManagerNoteRepository.class)
                    .save(note);
            size++;
        }

        return ResponseEntity.ok(String.format("%s / %s airfare addition notes created!", size, vacations.size()));
    }
    /************************************ V2.0 ************************************/

    @Transactional
    @PreAuthorize("hasPermission('migration', 'eidNumbers')")
    @RequestMapping(value = "/eidNumbers", method = RequestMethod.GET)
    public ResponseEntity<?> eidNumbers() {

        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.filterBy("employeeType", "=", OfficeStaffType.DUBAI_STAFF_EMARATI);

        int size = 0;
        for(OfficeStaff staff: query.execute()) {
            NewRequest visaRequest = staff.getVisaNewRequest();
            if(visaRequest != null && visaRequest.getEidApplicationNumber() != null && staff.getEidNumber() == null) {
                staff.setEidNumber(visaRequest.getEidApplicationNumber());
                size++;
            }
        }
        return ResponseEntity.ok(String.format("%s EID numbers migrated!", size));
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'monthlyRules')")
    @RequestMapping(value = "/monthlyRules", method = RequestMethod.GET)
    public ResponseEntity<?> monthlyRules() {

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByFinished(false);

        for(MonthlyPaymentRule monthlyPaymentRule: rules) {
            if(monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.SECONDARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                monthlyPaymentRule.setPaymentMethod(PaymentRulePaymentMethod.WPS);
                monthlyPaymentRule.setHousemaidTypeList(Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC));
            }
        }

        Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .save(rules);
        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'mohreSalary')")
    @RequestMapping(value = "/mohreSalary", method = RequestMethod.GET)
    public ResponseEntity<?> mohreSalary() {
        List<PicklistItem> africansNationalities = PicklistHelper.getItemsByListAndTag("nationalities", "african_deduction_limit");
        List<PicklistItem> asiansNationalities = PicklistHelper.getItemsByListAndTag("nationalities", "other_asians");
        long mohreSalaryCount = Setup.getRepository(NationalityMohreSalaryRepository.class).count();
        if (mohreSalaryCount == 0L) {
            List<NationalityMohreSalary> mohreSalaryList = new ArrayList<>();

            //add philippines
            mohreSalaryList.add(new NationalityMohreSalary("philippines", 1500D));
            //add ethiopian
            mohreSalaryList.add(new NationalityMohreSalary("ethiopian", 546D));

            //add asians
            for (PicklistItem nationality : asiansNationalities)
                mohreSalaryList.add(new NationalityMohreSalary(nationality.getCode(),646D));

            //add africans
            for (PicklistItem nationality : africansNationalities)
                mohreSalaryList.add(new NationalityMohreSalary(nationality.getCode(),546D));

            Setup.getRepository(NationalityMohreSalaryRepository.class)
                    .save(mohreSalaryList);
            return ResponseEntity.ok("Success!");
        }
        throw new RuntimeException("Already added before!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'onVacationMaids')")
    @RequestMapping(value = "/onVacationMaids", method = RequestMethod.GET)
    public ResponseEntity<?> onVacationMaids() {
        return ResponseEntity.ok("Success!");
    }


    @Transactional
    @PreAuthorize("hasPermission('migration', 'markMaidsSalariesAsPaid')")
    @RequestMapping(value = "/markMaidsSalariesAsPaid", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> markMaidsSalariesAsPaid(@RequestBody(required = false) MultipartFile file) {
        try {
            HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
            HousemaidPayrollLogRepository housemaidPayrollLogRepo = Setup.getRepository(HousemaidPayrollLogRepository.class);

            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                String debugMsg = "";
                Row row = iterator.next();
                if(rowNum++ == 0) {
                    continue;
                }
                String nm = formatter.formatCellValue(row.getCell(0));
                String employeeName = formatter.formatCellValue(row.getCell(2));
                String amount = formatter.formatCellValue(row.getCell(5));
                String month = formatter.formatCellValue(row.getCell(7));
                debugMsg += "\nemployeeName: " + employeeName
                        + ", amount: " + amount
                        + ", month: " + month;
                Housemaid housemaid = housemaidRepository.findFirstByName(employeeName);
                if (housemaid != null){
                    debugMsg += "\n housemaid is not null";
                    month = month.replace(" Salary","").trim().toUpperCase();
                    debugMsg += "\n edited month: " + month;
                    Month salaryMonth = Month.valueOf(month);
                    LocalDate payrollMonth = new LocalDate(new Date());
                    payrollMonth = payrollMonth.withMonthOfYear(salaryMonth.getValue());
                    payrollMonth = payrollMonth.withDayOfMonth(1);
                    java.sql.Date payrollMonthDate = new java.sql.Date(payrollMonth.toDate().getTime());
                    debugMsg += "\n payrollMonth: " + payrollMonthDate;

                    //check for log
                    HousemaidPayrollLog log = housemaidPayrollLogRepo.findTopByHousemaidAndPayrollMonth(housemaid, payrollMonthDate);
                    if(log == null){
                        debugMsg += "\n HousemaidPayrollLog is null : ";
                        log = new HousemaidPayrollLog();
                        String employeeUniqueId = "";
                        String agentId = "";
                        String employeeAccountWithAgent = "";

                        List<HousemaidVisaInfoProjection> housemaidVisaInfo = housemaidRepository.findVisaInfo(Arrays.asList(housemaid.getId()));
                        for(HousemaidVisaInfoProjection projection: housemaidVisaInfo) {
                            employeeUniqueId = projection.getEmployeeUniqueId();
                            employeeAccountWithAgent = projection.getAccountWithAgent();
                            agentId = projection.getAgentId();
                        }

                        log.setHousemaid(housemaid);
                        log.setStatus(housemaid.getRealStatus());
                        log.setRecordType("EDR");
                        log.setEmployeeName(housemaid.getName());

                        log.setEmployeeUniqueId(employeeUniqueId);
                        log.setAgentId(agentId);
                        log.setEmployeeAccountWithAgent(employeeAccountWithAgent);

                        log.setPayrollMonth(payrollMonthDate);
                        log.setPayStartDate(new java.sql.Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime()));
                        log.setPayEndDate(new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime()));
                        log.setDaysInPeriod(DateUtil.getDaysBetween(log.getPayStartDate(), log.getPayEndDate()) + 1);

                        log.setTotalEarnings(0.0);
                        log.setTotalAddition(0.0);
                        log.setTotalDeduction(0.0);

                        log.setTotalSalary(Double.valueOf(amount.replace(",","")));
                    }
                    debugMsg += "\n HousemaidPayrollLog current status is : " + log.getTransferred();

                    //set it as transferred
                    log.setTransferred(true);
                    log.setPaidOnDate(DateUtil.formatFullDate(new Date()));

                    housemaidPayrollLogRepo.save(log);
                }
//                DebugHelper.sendMail("<EMAIL>", debugMsg);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'migrateOldMaids')")
    @RequestMapping(value = "/migrateOldMaids", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> migrateOldMaids(
            @RequestParam(required = false, defaultValue = "false") Boolean forMaidCC,
            @RequestParam(required = false, defaultValue = "false") Boolean isPaid,
            @RequestBody(required = false) MultipartFile file) {

        String debugMsg = "";
//        DebugHelper.sendMail("<EMAIL>", "start migrateOldMaids API --> forMaidCC is: " + forMaidCC + ", isPaid is: " + isPaid);
        try {
            HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
            HousemaidPayrollLogRepository housemaidPayrollLogRepo = Setup.getRepository(HousemaidPayrollLogRepository.class);
//            HousemaidBeanInfoRepository housemaidBeanInfoRepo = Setup.getRepository(HousemaidBeanInfoRepository.class);

            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                debugMsg = "";
                Row row = iterator.next();
                if(rowNum++ == 0) {
                    continue;
                }
                String housemaidId = formatter.formatCellValue(row.getCell(0));
                String employeeName = formatter.formatCellValue(row.getCell(1));
                String amount = formatter.formatCellValue(row.getCell(2));
                String month = formatter.formatCellValue(row.getCell(3));
                String yearSt = formatter.formatCellValue(row.getCell(4));
                String currentStatus = formatter.formatCellValue(row.getCell(5));
                debugMsg += "\nhousemaidId: " + housemaidId
                        + ", employeeName: " + employeeName
                        + ", amount: " + amount
                        + ", month: " + month
                        + ", year: " + yearSt
                        + ", currentStatus: " + currentStatus;
                Housemaid housemaid = housemaidRepository.findOne(Long.parseLong(housemaidId));
                if (housemaid != null){
                    debugMsg += "\n housemaid is not null";
                    month = month.trim().toUpperCase();
                    debugMsg += "\n edited month: " + month;
                    Month salaryMonth = Month.valueOf(month);
                    int year = yearSt != null && !yearSt.isEmpty() ? Integer.valueOf(yearSt) : new Date().getYear();
//                    LocalDate payrollMonth = new LocalDate(new Date()).withYear(2020);
                    LocalDate payrollMonth = new LocalDate(new Date());
                    payrollMonth = payrollMonth.withYear(year).withMonthOfYear(salaryMonth.getValue()).withDayOfMonth(1);
                    java.sql.Date payrollMonthDate = new java.sql.Date(payrollMonth.toDate().getTime());
                    debugMsg += "\n payrollMonth: " + payrollMonthDate;

                    //check for log
                    HousemaidPayrollLog log = housemaidPayrollLogRepo.findTopByHousemaidAndPayrollMonth(housemaid, payrollMonthDate);
                    if(log == null){
                        debugMsg += "\n HousemaidPayrollLog is null : ";
                        log = new HousemaidPayrollLog();
                        String employeeUniqueId = "";
                        String agentId = "";
                        String employeeAccountWithAgent = "";

                        List<HousemaidVisaInfoProjection> housemaidVisaInfo = housemaidRepository.findVisaInfo(Arrays.asList(housemaid.getId()));
                        for(HousemaidVisaInfoProjection projection: housemaidVisaInfo) {
                            employeeUniqueId = projection.getEmployeeUniqueId();
                            employeeAccountWithAgent = projection.getAccountWithAgent();
                            agentId = projection.getAgentId();
                        }

                        log.setHousemaid(housemaid);
                        log.setStatus(housemaid.getRealStatus());
                        log.setRecordType("EDR");
                        log.setEmployeeName(housemaid.getName());

                        log.setEmployeeUniqueId(employeeUniqueId);
                        log.setAgentId(agentId);
                        log.setEmployeeAccountWithAgent(employeeAccountWithAgent);

                        log.setPayrollMonth(payrollMonthDate);
                        log.setPayStartDate(new java.sql.Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime()));
                        log.setPayEndDate(new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime()));
                        log.setDaysInPeriod(DateUtil.getDaysBetween(log.getPayStartDate(), log.getPayEndDate()) + 1);

                        log.setTotalEarnings(0.0);
                        log.setTotalAddition(0.0);
                        log.setTotalDeduction(0.0);

                        log.setReceiverName(housemaid.getName());
                        log.setDestinationOfTransfer("UAE");
                        log.setMobileNumber(housemaid.getPhoneNumber());

                        log.setTotalSalary(Double.valueOf(amount.replace(",","")));
                    }
                    debugMsg += "\n HousemaidPayrollLog current status is : " + log.getTransferred();

                    //set it as transferred and included
                    if(isPaid) {
                        log.setTransferred(true);
                        log.setWillBeIncluded(true);
                        log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                        log.setLogStatus(HousemaidPayrollLog.HousemaidPayrollLogStatus.FINAL);
                    }else if(forMaidCC){
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.valueOf(currentStatus));
                    } else {
                        log.setHousemaidUnpaidStatus(HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
                    }
//                    HousemaidBeanInfo housemaidBeanInfo = HousemaidPayrollPaymentServiceV2.createBeanInfoDetails(housemaid, log);
                    housemaidPayrollLogRepo.save(log);
//                    housemaidBeanInfoRepo.save(housemaidBeanInfo);
                }
                DebugHelper.sendMail("<EMAIL>", "finish migrateOldMaids API : " + debugMsg);
            }

        } catch (Exception e) {
//            DebugHelper.sendMail("<EMAIL>","migrateOldMaids in Catch Exception: " + debugMsg);
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in migrateOldMaids API", false);
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'migrateOldStaffs')")
    @RequestMapping(value = "/migrateOldStaffs", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> migrateOldStaffs(
            @RequestParam(required = false, defaultValue = "false") Boolean isPaid,
            @RequestBody(required = false) MultipartFile file) {

        String debugMsg = "";
//        DebugHelper.sendMail("<EMAIL>", "start migrateOldStaffs API --> isPaid is: " + isPaid);
        try {
            OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);
            OfficeStaffPayrollLogRepository officeStaffPayrollLogRepo = Setup.getRepository(OfficeStaffPayrollLogRepository.class);

            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> iterator = sheet.iterator();
            DataFormatter formatter = new DataFormatter();

            int rowNum = 0;
            while (iterator.hasNext()) {
                debugMsg = "";
                Row row = iterator.next();
                if(rowNum++ == 0) {
                    continue;
                }
                String nm = formatter.formatCellValue(row.getCell(0));
                String employeeName = formatter.formatCellValue(row.getCell(1));
                String amount = formatter.formatCellValue(row.getCell(2));
                String month = formatter.formatCellValue(row.getCell(3));
                String currentStatus = formatter.formatCellValue(row.getCell(4));
                debugMsg += "\nemployeeName: " + employeeName
                        + ", amount: " + amount
                        + ", month: " + month
                        + ", currentStatus: " + currentStatus;
                OfficeStaff staff = officeStaffRepository.findFirstByName(employeeName);
                if (staff != null){
                    debugMsg += "\n officeStaff is not null";
                    month = month.trim().toUpperCase();
                    debugMsg += "\n edited month: " + month;
                    Month salaryMonth = Month.valueOf(month);
//                    LocalDate payrollMonth = new LocalDate(new Date()).withYear(2020);
                    LocalDate payrollMonth = new LocalDate(new Date());
                    payrollMonth = payrollMonth.withMonthOfYear(salaryMonth.getValue());
                    payrollMonth = payrollMonth.withDayOfMonth(1);
                    java.sql.Date payrollMonthDate = new java.sql.Date(payrollMonth.toDate().getTime());
                    debugMsg += "\n payrollMonth: " + payrollMonthDate;

                    //check for log
                    OfficeStaffPayrollLog log = officeStaffPayrollLogRepo.findTopByOfficeStaffAndPayrollMonthAndForEmployeeLoanFalse(staff, payrollMonthDate);
                    if(log == null){
                        debugMsg += "\n HousemaidPayrollLog is null : ";
                        log = new OfficeStaffPayrollLog();

                        log.setOfficeStaff(staff);
                        log.setLogStatus(OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus.FINAL);
                        log.setRecordType("EDR");
                        log.setEmployeeName(staff.getName());
                        log.setReceiverName(staff.getName());


                        log.setPayrollMonth(payrollMonthDate);
                        log.setPayStartDate(new java.sql.Date(new LocalDate(payrollMonth).withDayOfMonth(1).toDate().getTime()));
                        log.setPayEndDate(new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime()));
                        log.setDaysInPeriod(DateUtil.getDaysBetween(log.getPayStartDate(), log.getPayEndDate()) + 1);
                        log.setCurrency(staff.getSalaryCurrency() != null ? staff.getSalaryCurrency().toString() : "");
                        String countryName = staff.getCountry() != null ? staff.getCountry().getName() : "";
                        log.setDestinationOfTransfer(countryName);

                        TransferDestination transferDestination = staff.getSelectedTransferDestination();
                        if(transferDestination != null) {
                            log.setFullNameEnglish(transferDestination.getName());
                            log.setFullNameArabic(transferDestination.getFullNameInArabic());
                            log.setDestinationOfTransfer(transferDestination.getDetails());
                            log.setMobileNumber(transferDestination.getPhoneNumber());

                            log.setIban(transferDestination.getIban());
                            log.setAccountHolderName(transferDestination.getAccountHolderName());
                            log.setAccountNumber(transferDestination.getAccountNumber());
                            log.setBankName(transferDestination.getBankName());
                            log.setSwiftCode(transferDestination.getSwiftCode());
                            log.setBeneficiaryAddress(transferDestination.getFullAddress());
                        }

                        NewRequest visaNewRequest = staff.getVisaNewRequest();
                        if(visaNewRequest != null) {
                            log.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
                            log.setAgentId(visaNewRequest.getAgentId());
                            log.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
                        }

                        log.setTotalSalary(Double.valueOf(amount.replace(",","")));
                    }
                    debugMsg += "\n OfficeStaffPayrollLog current status is : " + log.getTransferred();

                    //set it as transferred and included
                    if(isPaid) {
                        log.setTransferred(true);
                        log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                        log.setWillBeIncluded(true);
                    }
                    officeStaffPayrollLogRepo.save(log);
                }
//                DebugHelper.sendMail("<EMAIL>", "finish migrateOldStaffs API : " + debugMsg);
            }

        } catch (Exception e) {
//            DebugHelper.sendMail("<EMAIL>","migrateOldStaffs in Catch Exception: " + debugMsg);
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in migrateOldStaffs API", false);
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }


    @Transactional
    @PreAuthorize("hasPermission('migration', 'migrateOfficeStaffLogs')")
    @RequestMapping(value = "/migrateOfficeStaffLogs", method = RequestMethod.GET)
    public ResponseEntity<?> migrateOfficeStaffLogs() {

//        DebugHelper.sendMail("<EMAIL>", "start migrateOfficeStaffLogs API");
        try {
            OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);
            OfficeStaffPayrollLogRepository officeStaffPayrollLogRepo = Setup.getRepository(OfficeStaffPayrollLogRepository.class);

            Date neededDate = new LocalDate().withDayOfMonth(27).withMonthOfYear(1).toDate();
            Date oneDayAfter = new LocalDate().withDayOfMonth(28).withMonthOfYear(1).toDate();
            List<OfficeStaffPayrollLog> payrollLogs = officeStaffPayrollLogRepo.findByCreatorAndCreationDateAndTransferred(330L, neededDate, oneDayAfter);

            for(OfficeStaffPayrollLog log : payrollLogs) {
                OfficeStaff staff = officeStaffRepository.findOne(log.getOfficeStaff().getId());
//                DebugHelper.sendMail("<EMAIL>", "migrateOfficeStaffLogs for: " + staff.getName());
                if (OfficeStaffType.DUBAI_STAFF_EXPAT.equals(staff.getEmployeeType()) && staff.getVisaNewRequest() != null) {
                    NewRequest visaNewRequest = Setup.getRepository(NewVisaRequestRepository.class).findOne(staff.getVisaNewRequest().getId());
                    log.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
                    log.setAgentId(visaNewRequest.getAgentId());
                    log.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
                } else {
                    TransferDestination transferDestination = staff.getSelectedTransferDestination();
                    if (transferDestination != null) {
                        log.setFullNameEnglish(transferDestination.getName());
                        log.setFullNameArabic(transferDestination.getFullNameInArabic());
                        log.setDestinationOfTransfer(transferDestination.getDetails());
                        log.setMobileNumber(transferDestination.getPhoneNumber());

                        log.setIban(transferDestination.getIban());
                        log.setAccountHolderName(transferDestination.getAccountHolderName());
                        log.setAccountNumber(transferDestination.getAccountNumber());
                        log.setBankName(transferDestination.getBankName());
                        log.setSwiftCode(transferDestination.getSwiftCode());
                        log.setBeneficiaryAddress(transferDestination.getFullAddress());
                    }

                }
            }
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(payrollLogs);
        } catch (Exception e) {
//            DebugHelper.sendMail("<EMAIL>","migrateOfficeStaffLogs in Catch Exception: ");
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in migrateOfficeStaffLogs API", false);
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'generatePayrollEcxeptionsReport')")
    @RequestMapping(value = "/generatePayrollEcxeptionsReport/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> generatePayrollEcxeptionsReport(@PathVariable("id") MonthlyPaymentRule monthlyPaymentRule) {
        List<Housemaid> includedHousemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(monthlyPaymentRule);
        List<Housemaid> ccMaids = includedHousemaids.stream().filter(x -> !x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
        List<Housemaid> visaMaids = includedHousemaids.stream().filter(x -> x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
        List<OfficeStaff> officeStaffs = officeStaffPayrollPaymentServiceV2.getTargetList(monthlyPaymentRule, PayrollAccountantTodoType.WPS);

        return ResponseEntity.ok(payrollExceptionsReportService.generatePayrollExceptionsReport(ccMaids,visaMaids,officeStaffs, monthlyPaymentRule, monthlyPaymentRule.getPaymentMethod().toString()));

    }

    @NoPermission
    @Transactional
    @RequestMapping(value = "/generateForMissingHousemaids/{id}/{secondaryId}", method = RequestMethod.GET)
    public ResponseEntity<?> generateForMissingHousemaids(@PathVariable("id") MonthlyPaymentRule rule, @PathVariable("secondaryId") MonthlyPaymentRule secondaryRule) {
        HousemaidPayrollAuditService housemaidPayrollAuditService = Setup.getApplicationContext().getBean(HousemaidPayrollAuditService.class);
        HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);

//        DebugHelper.sendMail("<EMAIL>", "start generateForMissingHousemaids API");
        try {
            PayrollAuditTodo auditTodo = Setup.getRepository(PayrollAuditTodoRepository.class).getOne(126L);
            List<Housemaid> targetList = housemaidPayrollAuditService.getTargetList(secondaryRule);
            targetList = targetList.stream().filter(x->!x.getId().equals(25214)).collect(Collectors.toList());
            housemaidPayrollPaymentServiceV2.generatePayrollLogsBasedOnAllAsync(targetList, rule, auditTodo, null, false, true );
        } catch (Exception e) {
//            DebugHelper.sendMail("<EMAIL>","generateForMissingHousemaids in Catch Exception: ");
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in generateForMissingHousemaids API", false);
            throw new RuntimeException(e);
        }

        return ResponseEntity.ok("Success!");
    }

    @PreAuthorize("hasPermission('migration', 'sendNoEIDFile')")
    @RequestMapping(  value = "/sendNoEIDFile/{id}" , method = RequestMethod.GET)
    public ResponseEntity<?> sendNoEIDFile(@PathVariable(name = "id") PayrollAccountantTodo todo){
        if (todo != null)
            Setup.getApplicationContext().getBean(AsyncService.class).sendNoEIDFile(todo);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('migration', 'setAccommodationSalaryToSalaryRules')")
    @RequestMapping(  value = "/setAccommodationSalaryToSalaryRules" , method = RequestMethod.GET)
    public ResponseEntity<?> setAccommodationSalaryToSalaryRules(){
        SalaryRuleRepository salaryRuleRepository = Setup.getRepository(SalaryRuleRepository.class);
        SalaryRuleDetailsRepository salaryRuleDetailsRepository = Setup.getRepository(SalaryRuleDetailsRepository.class);
        List<SalaryRule> salaryRules = salaryRuleRepository.findAll();
        PicklistItem accommodationSalaryItem = PicklistHelper.getItem("salary_rules_components", "accommodationSalary");
        for (SalaryRule salaryRule : salaryRules) {
            SalaryRuleDetails salaryRuleDetails = new SalaryRuleDetails();
            salaryRuleDetails.setSalaryComponent(accommodationSalaryItem);
            salaryRuleDetails.setSalaryRule(salaryRule);
            salaryRuleDetails.setValue(0.0);
            salaryRuleDetailsRepository.save(salaryRuleDetails);
        }
        return ResponseEntity.ok("Done");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'addLowExchangeRateAdditionsForSyrianStaffsMigration')")
    @RequestMapping(  value = "/addLowExchangeRateAdditionsForSyrianStaffsMigration" , method = RequestMethod.GET)
    public ResponseEntity<?> addLowExchangeRateAdditionsForSyrianStaffsMigration(){
        java.sql.Date previousPayrollMonth = new java.sql.Date(new LocalDate().withDayOfMonth(1).plusMonths(-1).toDate().getTime());
        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("payrollMonth", "=", previousPayrollMonth);
        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.OVERSEAS);
        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE));
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.sortBy("paymentDate", true);
        List<MonthlyPaymentRule> rules = selectQuery.execute();

        MonthlyPaymentRule previousMonthlyPaymentRule = rules != null && rules.size() > 0 ? rules.get(0) : null;

        if (previousMonthlyPaymentRule != null) {
            java.sql.Date payrollStart = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).getPayrollStartLockDate(previousMonthlyPaymentRule);
            java.sql.Date payrollEnd = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).getPayrollEndLockDate(previousMonthlyPaymentRule);

            SelectQuery<OfficeStaffPayrollLog> query = new SelectQuery<>(OfficeStaffPayrollLog.class);
            query.filterBy("monthlyPaymentRule", "=", previousMonthlyPaymentRule);
            query.join("payrollAccountantTodo");
            query.filterBy("payrollAccountantTodo.taskName", "=", PayrollAccountantTodoType.INTERNATIONAL_TRANSFER.toString());
            query.filterBy("transferred", "=", true);

            List<OfficeStaffPayrollLog> transferredLogs = query.execute();
            transferredLogs = transferredLogs.stream().filter(x -> x.getDestinationOfTransfer() != null && x.getDestinationOfTransfer().toLowerCase().startsWith("syria")).collect(Collectors.toList());
            for (OfficeStaffPayrollLog officeStaffPayrollLog : transferredLogs) {
                OfficeStaff officeStaff = officeStaffPayrollLog.getOfficeStaff();
                if (OfficeStaffStatus.ACTIVE.equals(officeStaff.getStatus())) {
                    Double totalSalary = officeStaffPayrollLog.getTotalSalary();
                    PicklistItem bonusItem = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, "bonus");
                    PicklistItem lowExchangeItem = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_LOW_EXCHANGE_RATE_COMPENSATION_ADDITION_CODE);
                    List<PayrollManagerNote> previousMonthAdditions = payrollManagerNoteRepository.findTopByOfficeStaffAndNoteTypeAndNoteDateAndAdditionReasonAndNoteReasone(
                            officeStaff, AbstractPayrollManagerNote.ManagerNoteType.BONUS,
                            payrollStart, payrollEnd, bonusItem, "low exchange rate to Syria");
                    if (previousMonthAdditions.size() > 0) {
                        PayrollManagerNote previousMonthAddition = previousMonthAdditions.get(0);
                        totalSalary -= previousMonthAddition.getAmount() != null ? previousMonthAddition.getAmount() : 0.0;
                        previousMonthAddition.setAdditionReason(lowExchangeItem);
                        previousMonthAddition.setNoteReasone("5% low exchange rate compensation for " + StringHelper.enumToCapitalizedFirstLetter(DateUtil.formatSimpleMonth(previousPayrollMonth)) + " salary transfer (Migration)");
                        payrollManagerNoteRepository.save(previousMonthAddition);
                    }

                    String additionPercentageST = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_SYRIAN_LOW_EXCHANGE_RATE_PERCENTAGE);
                    Double additionPercentage = additionPercentageST != null && !additionPercentageST.isEmpty() ? Double.parseDouble(additionPercentageST) : 0.0;
                    totalSalary = Math.round(totalSalary * additionPercentage / 100.0) * 1.0;

                    if (totalSalary > 0.0) {
                        List<PayrollManagerNote> thisMonthAdditions = payrollManagerNoteRepository.findTopByOfficeStaffAndNoteTypeAndNoteDateAndAdditionReasonAndNoteReasone(
                                officeStaff, AbstractPayrollManagerNote.ManagerNoteType.BONUS,
                                payrollEnd, new Date(), bonusItem, "low exchange rate to Syria");
                        if (thisMonthAdditions.size() > 0) {
                            PayrollManagerNote payrollManagerNote = thisMonthAdditions.get(0);
                            payrollManagerNote.setAdditionReason(lowExchangeItem);
                            payrollManagerNote.setNoteReasone("5% low exchange rate compensation for " + StringHelper.enumToCapitalizedFirstLetter(DateUtil.formatSimpleMonth(previousPayrollMonth)) + " salary transfer (Migration)");
                            payrollManagerNoteRepository.save(payrollManagerNote);
                        } else {
                            PayrollManagerNote payrollManagerNote = new PayrollManagerNote();
                            payrollManagerNote.setOfficeStaff(officeStaff);
                            payrollManagerNote.setAmount(totalSalary);
                            payrollManagerNote.setNoteDate(new java.sql.Date(System.currentTimeMillis()));
                            payrollManagerNote.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.BONUS);
                            payrollManagerNote.setAdditionReason(lowExchangeItem);
                            payrollManagerNote.setNoteReasone(additionPercentageST + "% low exchange rate compensation for " + StringHelper.enumToCapitalizedFirstLetter(DateUtil.formatSimpleMonth(previousPayrollMonth)) + " salary transfer (Migration)");
                            payrollManagerNote.setApplied(true);
                            //get last salary
                            OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class)
                                    .findOne(payrollManagerNote.getOfficeStaff().getId());
                            Double lastSalary = staff != null ? staff.getSalary() : 0D;
                            payrollManagerNote.setLastSalary(lastSalary);
                            payrollManagerNoteRepository.save(payrollManagerNote);
                        }
                    }
                }
            }
        }
        return ResponseEntity.ok("Done");
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'markOldManagerNotesAsPaidByType')")
    @RequestMapping(  value = "/markOldManagerNotesAsPaidByType" , method = RequestMethod.GET)
    public ResponseEntity<?> markOldManagerNotesAsPaidByType(@RequestParam("type") String type) {
        PayrollManagerNoteRepository payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);

        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("finished", "=", true);
        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
        selectQuery.filterBy("paymentDate", "<", new java.sql.Date(System.currentTimeMillis()));
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.sortBy("id", false);
        List<MonthlyPaymentRule> monthlyPaymentRules = selectQuery.execute();

        if (monthlyPaymentRules.size() > 0){
            MonthlyPaymentRule rule = monthlyPaymentRules.get(0);
            java.sql.Date lockDate = rule.getLockDate();

            SelectQuery<PayrollManagerNote> query = new SelectQuery<>(PayrollManagerNote.class);
            query.filterBy("housemaid", "IS NOT NULL", null);
            query.filterBy("noteDate", "<", lockDate);
            query.filterBy("noteType", "=", AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
            query.filterBy("additionReason", "=", PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, type));
            query.filterBy("paid", "=", false);

            List<PayrollManagerNote> managerNotes = query.execute();

            for (PayrollManagerNote managerNote : managerNotes) {
                managerNote.setPaid(true);
                payrollManagerNoteRepository.save(managerNote);
            }

        }
        return ResponseEntity.ok("Done");
    }

    @Transactional
    @NoPermission
    @RequestMapping(  value = "/sendApprovalMailsForOneTime" , method = RequestMethod.GET)
    public ResponseEntity<?> sendApprovalMailsForOneTime(){
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "sendApprovalMails", "payrollRosterApprovalService", "payroll",
                        "sendApprovalMails",
                        null, null, false,
                        false, new Class[]{Boolean.class, Boolean.class}, new Object[]{true, false});
        return ResponseEntity.ok("Done");
    }

    @NoPermission
    @RequestMapping(value = "/createHousemaidPayrollEvents", method = RequestMethod.GET)
    public ResponseEntity<?> createHousemaidPayrollEvents(@RequestParam(required = false, name = "ids") String ids) {
        List<Long> housemaidsIds = null;
        if (ids != null && !ids.isEmpty()) {
            housemaidsIds = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        HistorySelectQuery<Housemaid> selectQuery = new HistorySelectQuery<>(Housemaid.class);
        if (housemaidsIds != null && !housemaidsIds.isEmpty()) {
            selectQuery.filterBy("id", "IN", housemaidsIds);
        }
        selectQuery.filterByChanged("liveOut");
        selectQuery.sortBy("lastModificationDate", true);
        List<Housemaid> housemaids = selectQuery.execute();
        if (housemaids != null && !housemaids.isEmpty()) {
            for (Housemaid housemaid : housemaids) {
                if (housemaid.getCreationDate().getTime() == housemaid.getLastModificationDate().getTime()) {
                    continue;
                }
                Setup.getApplicationContext().getBean(PayrollGroupService.class)
                        .createHousemaidPayrollEvent(housemaid, housemaid.getLastModificationDate());
            }
        }

        return ResponseEntity.ok("event was created " + housemaids.size() + " events");
    }


    @NoPermission
    @RequestMapping(value = "/exportMigrationFile1474", method = RequestMethod.GET)
    public ResponseEntity<?> exportMigrationFile1474() {

        List<TransferDestination> transferDestinations = Setup.getRepository(TransferDestinationRepository.class)
                .findBankTransferDestinationsForActiveOverseasStaff();

        if (transferDestinations == null || transferDestinations.isEmpty()) {
            return new ResponseEntity<>("transfer destinations list is empty", HttpStatus.OK);
        }
        File file = null;
        try {
            URL resource = PayrollGenerationLibrary.class.getResource("/transferfiles/migrationFile1474.xlsx");
            file = new File(resource.toURI());
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        try (FileInputStream inputStream = new FileInputStream(file)) {
            Workbook workbook = new XSSFWorkbook(inputStream);

            Sheet spreadsheet = workbook.getSheetAt(0);

            CellStyle style = TransferFilesService.createTextStyle(workbook);

            int rowCount = 2;
            for (TransferDestination destination : transferDestinations) {
                OfficeStaff staff = destination.getOfficeStaff();

                if(staff == null) continue;

                Row row = spreadsheet.createRow(rowCount++);
                int cellCount = 0;
                row.createCell(cellCount++).setCellValue(staff.getId());
                row.createCell(cellCount++).setCellValue(destination.getId());
                row.createCell(cellCount++).setCellValue(staff.getName());
                row.createCell(cellCount++).setCellValue(staff.getCityName());
                row.createCell(cellCount++).setCellValue(staff.getFullAddress());
                row.createCell(cellCount++).setCellValue(destination.getCityName());
                row.createCell(cellCount++).setCellValue(destination.getFullAddress());
                row.createCell(cellCount++).setCellValue(destination.getBankName());
                row.createCell(cellCount++).setCellValue(destination.getSwiftCode());

                for (int i = 0; i < cellCount; i++) {
                    row.getCell(i).setCellStyle(style);
                }
            }

            String fileName = "office staff transfer destination details.xls";

            File directory = Paths.get(System.getProperty("java.io.tmpdir"),
                    "payroll").toFile();

            directory.mkdir();

            File tempFile = Paths.get(System.getProperty("java.io.tmpdir"),
                            "payroll/" + fileName)
                    .toFile();

            try (FileOutputStream out = new FileOutputStream(tempFile)) {
                workbook.write(out);
            }

            Attachment attachment = Storage.storeTemporary("payroll/" + fileName, new FileInputStream(tempFile), "OfficeStaffTransferDestinationDetails", true);
            return new ResponseEntity<>(attachment, HttpStatus.OK);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @NoPermission
    @Transactional
    @RequestMapping(value = "/importMigrationFile1474", method = RequestMethod.POST)
    public ResponseEntity<?> importMigrationFile1474(@RequestBody MultipartFile file) {
        Workbook workbook = null;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (Exception e) {
            try {
                workbook = new HSSFWorkbook(file.getInputStream());
            } catch (Exception e1) {
                throw new RuntimeException("Error while parsing file " + file.getName());
            }
        }
        int i = 0;
        Sheet sheet = workbook.getSheetAt(0);
        DataFormatter formatter = new DataFormatter();
        Long employeeId = null;
        for (Row row : sheet) {
            if (i++ <= 1) {
                continue;
            }
            if (formatter.formatCellValue(row.getCell(0)).trim().isEmpty()) {
                continue;
            }
            try {
                employeeId = Long.parseLong(formatter.formatCellValue(row.getCell(0)).trim());
            } catch (NumberFormatException e) {
                continue;
            }
            OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(employeeId);
            if (officeStaff == null) {
                throw new BusinessException("employeeId #" + employeeId + "is not found");
            }
            String swiftCode = formatter.formatCellValue(row.getCell(8)).trim();

            String cityCode = formatter.formatCellValue(row.getCell(10)).trim();
            if (!cityCode.isEmpty()) {
                PicklistItem city = PicklistHelper.getItem("payroll_cities", cityCode);
                if (city != null) {
                    officeStaff.setCity(city);
                    officeStaff.setCityName(city.getName());
                    OfficeStaffCandidate candidate = officeStaff.getOfficeStaffCandidate();
                    if (candidate != null) {
                        candidate.setCity(city);
                        candidate.setCityName(city.getName());
                        Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
                    }
                } else {
                    throw new BusinessException("please fill city employeeId #" + employeeId);
                }
            }

            String staffFullAddress = formatter.formatCellValue(row.getCell(12)).trim();
            if (!staffFullAddress.isEmpty()) {
                if (staffFullAddress.length() > 35) {
                    throw new BusinessException("limit the full address field to 35 characters, please fix Staff Full Address employeeId #" + employeeId);
                }
                officeStaff.setFullAddress(staffFullAddress);
                OfficeStaffCandidate candidate = officeStaff.getOfficeStaffCandidate();
                if (candidate != null) {
                    candidate.setFullAddress(staffFullAddress);
                    Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
                }
            }

            if (officeStaff.getSelectedTransferDestination() == null) {
                throw new BusinessException("Selected Transfer Destination is null, employeeId #" + employeeId);
            }

            // get transfer destination
            Long transferDestinationId;
            try {
                transferDestinationId = Long.parseLong(formatter.formatCellValue(row.getCell(1)).trim());
            } catch (NumberFormatException e) {
                continue;
            }
            TransferDestination transferDestination = Setup.getRepository(TransferDestinationRepository.class).findOne(transferDestinationId);
            if (transferDestination == null) {
                throw new BusinessException("transferDestinationId #" + transferDestinationId + "is not found");
            }

            if (!swiftCode.isEmpty()) {
                transferDestination.setSwiftCode(swiftCode);
                SwiftCodeValidationResponse response = Setup.getApplicationContext().getBean(ValidateIBANService.class).validateSwiftCode(swiftCode);
                if (response == null || response.getData() == null || !response.isSuccess()) {
                    throw new BusinessException("Swift code is not valid : " + swiftCode);
                }
                transferDestination.setBankName(response.getData().getBank().getName());
                transferDestination.setBranchCityName(response.getData().getCity().getName());
                transferDestination.setBranchCountryName(response.getData().getCountry().getName());
            }

            String cityCodeTD = formatter.formatCellValue(row.getCell(11)).trim();
            if (!cityCodeTD.isEmpty()) {
                PicklistItem city = Setup.getItem("payroll_cities", cityCodeTD);
                if (city != null) {
                    transferDestination.setCity(city);
                }
            }

            String fullAddress = formatter.formatCellValue(row.getCell(13)).trim();
            if (!fullAddress.isEmpty()) {
                if (fullAddress.length() > 35) {
                    throw new BusinessException("limit the full address field to 35 characters, please fix Receiver Full Address employeeId #" + employeeId);
                }
                transferDestination.setFullAddress(fullAddress);
            }

            Boolean insideUAE = formatter.formatCellValue(row.getCell(14)).trim().contains("1") || formatter.formatCellValue(row.getCell(14)).trim().toLowerCase().contains("yes") || formatter.formatCellValue(row.getCell(11)).trim().toLowerCase().contains("inside");
            transferDestination.setInsideUAE(insideUAE);
            Setup.getRepository(TransferDestinationRepository.class).save(transferDestination);
            Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
        }
        return ResponseEntity.ok("Done");
    }

    @NoPermission
    @RequestMapping(value = "/importISOCode", method = RequestMethod.POST)
    public ResponseEntity<?> importISOCode(@RequestBody MultipartFile file) {
        Workbook workbook = null;
        StringBuilder result = new StringBuilder("");
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (Exception e) {
            try {
                workbook = new HSSFWorkbook(file.getInputStream());
            } catch (Exception e1) {
                throw new RuntimeException("Error while parsing file " + file.getName());
            }
        }
        int i = 0;
        Sheet sheet = workbook.getSheetAt(0);
        DataFormatter formatter = new DataFormatter();
        for (Row row : sheet) {
            if (i++ <= 0) {
                continue;
            }
            if (formatter.formatCellValue(row.getCell(0)).trim().isEmpty()) {
                continue;
            }
            String country = formatter.formatCellValue(row.getCell(0)).trim();
            String isoCode = formatter.formatCellValue(row.getCell(1)).trim();
            if (country.isEmpty() || isoCode.isEmpty()) {
                continue;
            }
            try {
                PicklistItem item = PicklistHelper.getItem("countries", country);
                if (!item.hasTag("iso_code_payroll")) {
                    Map<String, Object> body = new HashMap<>();
                    body.put("key", "iso_code_payroll");
                    body.put("value", isoCode);
                    Setup.getApplicationContext().getBean(InterModuleConnector.class).postJson("/admin/picklistItem/addComplexTag/" + item.getId(), body, Object.class);
                } else {
                    result.append(country).append(" has ISO : ").append(item.getTagValue("iso"));
                }
            } catch (IllegalArgumentException | BeansException ignored) {
                Logger.getLogger(MigrationController.class.getName()).info(country + " is not found");
                result.append(country).append(" is not found, ");
            }
        }

        return ResponseEntity.ok(result.toString().isEmpty() ? "Done" : result.toString());
    }

    @NoPermission
    @RequestMapping(value = "/createPayrollCityPickList", method = RequestMethod.POST)
    public ResponseEntity<?> createPayrollCityPickList(@RequestBody MultipartFile file) {
        PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        Picklist payrollCitiesList = picklistRepository.findByCode("payroll_cities");
        Picklist countries = picklistRepository.findByCode("countries");
        if (payrollCitiesList == null) {
            payrollCitiesList = new Picklist("payroll_cities", "Payroll Cities");
            payrollCitiesList.setRoot(countries);
            payrollCitiesList = picklistRepository.save(payrollCitiesList);
        }

        Workbook workbook = null;
        try {
            workbook = new XSSFWorkbook(file.getInputStream());
        } catch (Exception e) {
            try {
                workbook = new HSSFWorkbook(file.getInputStream());
            } catch (Exception e1) {
                throw new RuntimeException("Error while parsing file " + file.getName());
            }
        }
        int i = 0;
        Sheet sheet = workbook.getSheetAt(0);
        DataFormatter formatter = new DataFormatter();
        for (Row row : sheet) {
            if (i++ <= 0) {
                continue;
            }
            if (formatter.formatCellValue(row.getCell(0)).trim().isEmpty()) {
                continue;
            }
            String countryNameAsString = formatter.formatCellValue(row.getCell(0)).trim();
            String cityNameAsString = formatter.formatCellValue(row.getCell(1)).trim();
            List<PicklistItem> countryRoot = picklistItemRepository.findByListAndNameLike(countries, countryNameAsString);
            List<PicklistItem> cityItem = picklistItemRepository.findByListAndNameLike(payrollCitiesList, cityNameAsString);
            if (cityItem == null || cityItem.isEmpty()) {
                PicklistItem picklistItem = new PicklistItem(payrollCitiesList, cityNameAsString, countryRoot != null && !countryRoot.isEmpty() ? countryRoot.get(0) : null);
                picklistItemRepository.save(picklistItem);
            }
        }
        return new ResponseEntity<>(i + " line was migrated", HttpStatus.OK);
    }




    /**
     * Normalizes phone numbers for all office staff and their transfer destinations to follow E.164 format.
     * This method processes:
     * 1. Phone numbers in transfer destinations for each staff member
     * 2. Direct phone numbers of staff members
     * 3. Emergency Contact phone numbers of staff members
     *
     * The normalization removes special characters, spaces, and ensures proper international format.
     * Uses PhoneNumberUtil.normalizeInternationalPhoneNumber() for standardization.
     *
     * @return ResponseEntity with count of normalized phone numbers and IDs of staff not updated
     */
    @Transactional
    @PreAuthorize("hasPermission('migrate', 'normalizePhoneNumbers')")
    @RequestMapping(value = "/normalizePhoneNumbers", method = RequestMethod.GET)
    public ResponseEntity<?> normalizePhoneNumbers() {
        List<OfficeStaff> officeStaffs = Setup.getRepository(OfficeStaffRepository.class)
                .findByEmployeeTypeIn(Arrays.asList(OfficeStaffType.OVERSEAS_STAFF, OfficeStaffType.DUBAI_STAFF_EXPAT, OfficeStaffType.DUBAI_STAFF_EMARATI));

        List<Long> notUpdatedIds = new ArrayList<>();
        int updatedCount = 0;

        for (OfficeStaff staff : officeStaffs) {
            boolean updated = false;
            try {
                if (staff.getTransferDestinations() != null && !staff.getTransferDestinations().isEmpty()) {
                    List<TransferDestination> destinations = staff.getTransferDestinations();

                    for (TransferDestination destination : destinations) {
                        if (destination.getPhoneNumber() != null && !destination.getPhoneNumber().isEmpty()) {
                            String normalizedNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(destination.getPhoneNumber());
                            if (!normalizedNumber.equals(destination.getPhoneNumber())) {
                                destination.setPhoneNumber(normalizedNumber);
                                 transferDestinationRepository.silentSave(destination);
                                updated = true;
                            }
                        }
                    }
                }
                if (staff.getEmergencyContactPhoneNumber() != null && !staff.getEmergencyContactPhoneNumber().isEmpty()) {
                    String emergencyContactPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(staff.getEmergencyContactPhoneNumber());
                    if (!emergencyContactPhoneNumber.equals(staff.getEmergencyContactPhoneNumber())) {
                        staff.setEmergencyContactPhoneNumber(emergencyContactPhoneNumber);
                        updated = true;
                    }
                }
                if (staff.getPhoneNumber() != null && !staff.getPhoneNumber().isEmpty()) {
                    String normalizedNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(staff.getPhoneNumber());
                    if (!normalizedNumber.equals(staff.getPhoneNumber())) {
                        staff.setPhoneNumber(normalizedNumber);
                        updated = true;
                    }
                }
                // Save each OfficeStaff after normalization
                Setup.getRepository(OfficeStaffRepository.class).silentSave(staff);
            } catch (Exception e) {
                // Log the exception and continue with the next staff
                logger.log(SEVERE, "Error normalizing phone numbers for staff ID: " + staff.getId(), e);
                e.printStackTrace();
            }

            if (!updated) {
                notUpdatedIds.add(staff.getId());
                logger.log(SEVERE, "officeStaff with ID = : " + staff.getId() + "is not updated");
            } else{
                updatedCount++;
            }
        }

        return ResponseEntity.ok(String.format("Success! Updated OfficeStaffs count = " + updatedCount + ", IDs not updated: %s", notUpdatedIds));
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'staffProfileCorrection')")
    @RequestMapping(value = "/staffProfileCorrection", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> staffProfileCorrection(@RequestBody MultipartFile file) {
        try {
            // Parse Excel file and extract only the needed data
            Map<String, Map<String, String>> staffData = new HashMap<>();

            try (InputStream inputStream = file.getInputStream();
                 Workbook workbook = WorkbookFactory.create(inputStream)) {

                Sheet sheet = workbook.getSheetAt(0);
                Iterator<Row> iterator = sheet.iterator();
                DataFormatter formatter = new DataFormatter();

                // Skip header row
                if (iterator.hasNext()) iterator.next();

                // Extract data from each row
                while (iterator.hasNext()) {
                    Row row = iterator.next();

                    String email = getCellValue(row, 0, formatter);
                    if (email == null || email.isEmpty()) continue;

                    Map<String, String> rowData = new HashMap<>();
                    rowData.put("photoUrl", getCellValue(row, 1, formatter));
                    rowData.put("departments", getCellValue(row, 2, formatter));
                    rowData.put("zohoExactJobTitle", getCellValue(row, 3, formatter));

                    staffData.put(email, rowData);
                }
            }

            // Process the extracted data in a background task
            BackgroundTask housemaidTask = new BackgroundTask
                    .builder("staffProfileCorrection",
                    Setup.getCurrentModule().getCode(),
                    "migrationService",
                    "processStaffProfileData")
                    .withParameters(new Class[]{Map.class}, staffData)
                    .build();
            backgroundTaskService.create(housemaidTask);

            return ResponseEntity.ok(String.format("Processing %d staff records in background.", staffData.size()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing file: " + e.getMessage());
        }
    }

    @Transactional
    @PreAuthorize("hasPermission('migration', 'createNewPicklist')")
    @RequestMapping(value = "/createNewPicklist", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> createNewPicklist(
            @RequestParam("picklistName") String picklistName,
            @RequestBody MultipartFile file) throws IOException, InvalidFormatException {

        List<String> errors = new ArrayList<>();
        List<String> successes = new ArrayList<>();

        // Check if picklist name is provided
        if (picklistName.isEmpty()) {
            return ResponseEntity.badRequest().body("Picklist name is required");
        }

        // Generate picklist code from name (lowercase with underscores)
        String picklistCode = PicklistItem.getCode(picklistName);

        // Check if picklist already exists
        PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
        Picklist picklist = picklistRepository.findByCode(picklistCode);

        // Create new picklist if it doesn't exist
        if (picklist == null) {
            picklist = new Picklist(picklistCode, picklistName);
            picklist = picklistRepository.save(picklist);
            successes.add("Created new picklist: " + picklistName + " with code: " + picklistCode);
        } else {
            successes.add("Using existing picklist: " + picklistName + " with code: " + picklistCode);
        }

        // Read Excel file
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        int rowCount = 0;
        int itemsAdded = 0;

        // Process each row in the file
        for (Row row : sheet) {
            if (rowCount == 0) { // Skip header row
                rowCount++;
                continue;
            }

            try {
                // Get item name from the first column
                Cell cell = row.getCell(0);
                if (cell == null) {
                    errors.add("Row " + rowCount + ": Empty cell");
                    rowCount++;
                    continue;
                }

                String itemName = cell.getStringCellValue().trim();
                if (itemName.isEmpty()) {
                    errors.add("Row " + rowCount + ": Empty item name");
                    rowCount++;
                    continue;
                }

                // Generate item code (lowercase with underscores)
                String itemCode = PicklistItem.getCode(itemName);

                // Check if item already exists in the picklist
                PicklistItem existingItem = picklist.getItemByCode(itemCode);
                if (existingItem != null) {
                    errors.add("Row " + rowCount + ": Item already exists: " + itemName);
                    rowCount++;
                    continue;
                }

                // Add new item to the picklist
                picklist.addItem(itemName);
                itemsAdded++;
                successes.add("Added item: " + itemName + " with code: " + itemCode);

            } catch (Exception e) {
                errors.add("Row " + rowCount + ": Error - " + e.getMessage());
            }

            rowCount++;
        }

        // Save the picklist with all new items
        picklistRepository.save(picklist);

        // Prepare response
        Map<String, Object> response = new HashMap<>();
        response.put("picklistName", picklistName);
        response.put("picklistCode", picklistCode);
        response.put("totalRows", rowCount - 1);
        response.put("itemsAdded", itemsAdded);
        response.put("successes", successes);
        response.put("errors", errors);

        return ResponseEntity.ok(response);
    }

    @NoPermission
    @Transactional
    @RequestMapping(value = "/fillOfficeStaffExactJobTitle", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> fillOfficeStaffExactJobTitle(@RequestBody(required = false) MultipartFile file) {
        try {
            OfficeStaffRepository repository = Setup.getRepository(OfficeStaffRepository.class);
            List<OfficeStaff> toFillExactJobTitleForOfficeStaffList = new ArrayList<>();
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            int totalSheets = workbook.getNumberOfSheets();

            for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);

                Iterator<Row> iterator = sheet.iterator();
                DataFormatter formatter = new DataFormatter();


                int rowNum = 0;
                while (iterator.hasNext()) {
                    Row row = iterator.next();
                    if (rowNum++ == 0) {
                        continue; // Skip header row
                    }

                    String email = getCellValue(row, 0, formatter);
                    String exactJobTitle = getCellValue(row, 1, formatter);

                    if (email == null || exactJobTitle == null) continue;

                    OfficeStaff officeStaff = repository.findFirstByEmail(email);
                    if (officeStaff == null) continue;

                    officeStaff.setZohoExactJobTitle(exactJobTitle);
                    toFillExactJobTitleForOfficeStaffList.add(officeStaff);
                }
            }

            if (!toFillExactJobTitleForOfficeStaffList.isEmpty())
                repository.save(toFillExactJobTitleForOfficeStaffList);

            return ResponseEntity.ok(String.format("%d office staff records updated with exact job title", toFillExactJobTitleForOfficeStaffList.size()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Migration API to update SWIFT codes in Transfer Destinations according to new format requirements
     * - If SWIFT code is 8 characters, append "XXX" to make it 11 characters
     * - If SWIFT code is 11 characters but doesn't end with "XXX", replace last 3 characters with "XXX"
     */
    @Transactional
    @PreAuthorize("hasPermission('migration', 'updateSwiftCodes')")
    @RequestMapping(value = "/updateSwiftCodes", method = RequestMethod.GET)
    public ResponseEntity<?> updateSwiftCodes() {
        try {
            // Get all records with SWIFT codes
            List<TransferDestination> transferDestinations = transferDestinationRepository.findAllWithSwiftCode();
            List<OfficeStaffPayrollLog> officeStaffPayrollLogs = officeStaffPayrollLogRepository.findUnTransferredWithSwiftCode();

            List<String> errorRecordIds = new ArrayList<>();

            // Prepare lists for batch save
            List<TransferDestination> toUpdateDestinations = new ArrayList<>();
            for (TransferDestination destination : transferDestinations) {
                try {
                    String originalSwiftCode = destination.getSwiftCode();
                    String updatedSwiftCode = StringHelper.normalizeSwiftCode(originalSwiftCode);

                    if (!originalSwiftCode.equals(updatedSwiftCode)) {
                        destination.setSwiftCode(updatedSwiftCode);
                        toUpdateDestinations.add(destination);
                    }
                } catch (Exception e) {
                    errorRecordIds.add("TransferDestination : " + destination.getId());
                }
            }

            // Save all updated transfer destinations at once
            if (!toUpdateDestinations.isEmpty()) {
                transferDestinationRepository.saveAll(toUpdateDestinations);
            }

            // OfficeStaffPayrollLogs
            List<OfficeStaffPayrollLog> toUpdateLogs = new ArrayList<>();
            for (OfficeStaffPayrollLog log : officeStaffPayrollLogs) {
                try {
                    String originalSwiftCode = log.getSwiftCode();
                    String updatedSwiftCode = StringHelper.normalizeSwiftCode(originalSwiftCode);

                    if (!originalSwiftCode.equals(updatedSwiftCode)) {
                        log.setSwiftCode(updatedSwiftCode);
                        toUpdateLogs.add(log);
                    }
                } catch (Exception e) {
                    errorRecordIds.add("OfficeStaffPayrollLog : " + log.getId());
                }
            }

            // Save all updated logs at once
            if (!toUpdateLogs.isEmpty()) {
                officeStaffPayrollLogRepository.saveAll(toUpdateLogs);
            }

            // Prepare response
            Map<String, Object> response = new HashMap<>();
            if(!errorRecordIds.isEmpty()) response.put("errorRecordIds", errorRecordIds);
            response.put("message", String.format("SWIFT codes migration completed!"));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error during SWIFT codes migration: " + e.getMessage());
        }
    }

    /**
     * Migration API to validate SWIFT codes and fill missing bank details
     * (Branch Country Name, Branch City Name, Bank Name) for bank transfer destinations
     */
    @Transactional
    @PreAuthorize("hasPermission('migration', 'fixBankDetails')")
    @RequestMapping(value = "/fixBankDetails", method = RequestMethod.GET)
    public ResponseEntity<?> fixBankDetails() {
        try {
            // Get all TransferDestination records that need bank details filled
            List<TransferDestination> transferDestinations = transferDestinationRepository
                    .findBankTransferDestinationsWithMissingBranchDetails();

            List<Long> errorRecordIds = new ArrayList<>();
            List<TransferDestination> toUpdate = new ArrayList<>();

            for (TransferDestination destination : transferDestinations) {
                try {
                    String swiftCode = destination.getSwiftCode();

                    SwiftCodeValidationResponse swiftResponse = validateIBANService.validateSwiftCode(swiftCode);
                    if (swiftResponse == null || swiftResponse.getData() == null || !swiftResponse.isSuccess()) {
                        continue;
                    }

                    SwiftCodeValidationResult data = swiftResponse.getData();
                    boolean updated = false;

                    String bankName = data.getBank() != null ? data.getBank().getName() : "";
                    String cityName = data.getCity() != null ? data.getCity().getName() : "";
                    String countryName = data.getCountry() != null ? data.getCountry().getName() : "";

                    if (isEmpty(destination.getBankName()) && isNotEmpty(bankName)) {
                        destination.setBankName(bankName);
                        updated = true;
                    }

                    if (isEmpty(destination.getBranchCityName()) && isNotEmpty(cityName)) {
                        destination.setBranchCityName(cityName);
                        updated = true;
                    }

                    if (isEmpty(destination.getBranchCountryName()) && isNotEmpty(countryName)) {
                        destination.setBranchCountryName(countryName);
                        updated = true;
                    }

                    if (updated) {
                        toUpdate.add(destination);
                    }

                } catch (Exception e) {
                    errorRecordIds.add(destination.getId());
                }
            }

            if (!toUpdate.isEmpty()) {
                transferDestinationRepository.saveAll(toUpdate);
            }

            Map<String, Object> response = new HashMap<>();
            if(!errorRecordIds.isEmpty()) response.put("errorRecordIds", errorRecordIds);
            response.put("message", String.format("Bank details fixing completed!"));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error during bank details fixing migration: " + e.getMessage());
        }
    }

}
