package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.maidsatv2.MaidsAtCandidateWA;
import com.magnamedia.extra.EmployeeLoansBean;
import com.magnamedia.extra.LoanConnector;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.report.HousemaidLoans;
import com.magnamedia.repository.*;
import com.magnamedia.service.EmployeeLoanService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PendingManagerApprovalService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.projection.HousemaidLoanProjection;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/loans")
@RestController
public class LoansController extends BaseReportRepositoryController<EmployeeLoan> {

    @Autowired
    HousemaidForgivenessRepository housemaidForgivenessRepository;
    @Autowired
    ProjectionFactory projectionFactory;
    @Autowired
    private EmployeeLoanRepository employeeLoanRepository;

    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private RepaymentRepository repaymentRepository;

    @Autowired
    private OfficeStaffRepository staffRep;

    @Autowired
    private EmployeeLoanService employeeLoanService;

    @Autowired
    private EmployeeLoanApproveRepository employeeLoanApproveRepository;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    @Override
    public BaseRepository<EmployeeLoan> getRepository() {
        return employeeLoanRepository;
    }

    @Override
    protected ResponseEntity<?> createEntity(EmployeeLoan entity) {
        System.out.println("com.magnamedia.controller.LoansController.createEntity()");
        System.out.println(entity.toString());
        try {
            return super.createEntity(entity);
//            getRepository().save(entity);
        } catch (Exception e) {
            System.out.println("errors: " + e.toString() + " " + e.getMessage());
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/customdelete/{id}",
            method = RequestMethod.DELETE)
    public ResponseEntity<?> customDelete(@PathVariable("id") EmployeeLoan entity) {
        entity.setLogActionRequired(true);
        if (entity.getHousemaid() != null) {
            Housemaid housemaid = entity.getHousemaid();
            if (housemaid.getIsMaidsAt()) {
                MaidsAtCandidateWA maidsAtCandidateWA = Setup.getRepository(MaidsAtCandidateWARepository.class).findTop1ByHousemaid(housemaid);
                if (maidsAtCandidateWA != null) {
                    BackgroundTask backgroundTask = new BackgroundTask.builder("deleteExitLoan",
                            "recruitment",
                            "maidsAtHustlerActionService",
                            "deleteExitLoan")
                            .withParameters(new Class[]{Long.class}, maidsAtCandidateWA.getId())
                            .withRelatedEntity(housemaid.getEntityType(), housemaid.getId())
                            .build();

                    Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);
                }
            }
        }
        return super.delete(entity);
    }

    @PreAuthorize("hasPermission('loans','addloan')")
    @RequestMapping("/addloan/{housemaidid}")
    @Transactional
    @ResponseBody
    public ResponseEntity<?> addloan(@PathVariable Long housemaidid,
                                     @RequestBody LoanConnector loanConnector) {
        Housemaid maid = housemaidRepository.findOne(housemaidid);
        if (maid == null) {
            throw new BusinessException("Maid doesn't exist");
        }
        EmployeeLoan employeeLoan = new EmployeeLoan();
        employeeLoan.setAmount(loanConnector.getAmount());
        employeeLoan.setHousemaid(maid);
        employeeLoan.setLoanType(loanConnector.getType());
        employeeLoan.setLoanDate(new java.sql.Date(System.currentTimeMillis()));
        employeeLoan.setNotes("Auto inserted from ERP in Exit");

//        super.createEntity(employeeLoan);
        employeeLoanRepository.save(employeeLoan);
        return okResponse();
    }

    @PreAuthorize("hasPermission('loans','getLoans')")
    @RequestMapping(value = "/loans/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getLoans(@PathVariable("id") Housemaid housemaid) {

        return new ResponseEntity<>(getLoansBean(housemaid), HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('loans','getLoansPdf')")
    @RequestMapping(value = "/loans/{id}/pdf", method = RequestMethod.GET,
            produces = "application/pdf")
    @ResponseBody
    public ResponseEntity<?> getLoansPdf(@PathVariable("id") Housemaid housemaid) {

        HousemaidLoans loans = new HousemaidLoans();
        loans.setHousemaid(housemaid);
        loans.setLoans(getLoansBean(housemaid));
        attachPdfTo(housemaid, loans, "loansAgreement.pdf", "loan", false);
        housemaidRepository.save(housemaid);
        return createPdfResponse("Loan Agreement '" + housemaid.getName() + "'.pdf", loans, true);

    }

    private EmployeeLoansBean getLoansBean(Object object) {
        Housemaid housemaid = null;
        OfficeStaff officeStaff = null;
        //the result bean
        EmployeeLoansBean result = new EmployeeLoansBean();
        Set<HousemaidStatus> rejectedStatuses = Housemaid.rejectedStatuses;

        if (object instanceof Housemaid) {
            housemaid = (Housemaid) object;
            result.setTotal(housemaid.getLoanBalance());
            result.setLostLoans(housemaid.getLostLoans());
        } else {
            officeStaff = (OfficeStaff) object;
            result.setTotal(officeStaff.getLoanBalance());
//            result.setTotal(officeStaff.getLoanBalance());

        }
//        if (housemaid!=null&&rejectedStatuses.contains(housemaid.getStatus())) {
//            return result;
//        }
        //the loans
//        result.setTotal(housemaid.getLoanBalance());
        List<EmployeeLoan> loans;
        if (housemaid != null) {
            loans = employeeLoanRepository.findByHousemaid(housemaid);
        } else {
            loans = employeeLoanRepository.getLoansByOfficeStaffFullEntity(officeStaff.getId());
        }
        for (EmployeeLoan loan : loans) {
            switch (loan.getLoanType()) {

                case SMARTPHONE:
                    result.setSmartPhone(result.getSmartPhone() + loan.getAmount());
                    break;
                case MALAYSIA_MEDICAL_TEST:
                    result.setMalaysiaMedicalTest(result.getMalaysiaMedicalTest() + loan.getAmount());
                    break;
                case TICKET_TO_EXIT:
                    result.setTicketToExit(result.getTicketToExit() + loan.getAmount());
                    break;
                case TICKET_TO_MANILA:
                    result.setRefundedKlManillaTicket(result.getRefundedKlManillaTicket() + loan.getAmount());
                    break;
                case ACCOMODATION_IN_EXIT:
                    result.setAccomodationInExit(result.getAccomodationInExit() + loan.getAmount());
                    break;
                case HOTEL_EXPENSES:
                    result.setHotelExponses(result.getHotelExponses() + loan.getAmount());
                    break;
                case PROCESSING_FEE:
                    result.setProcessingFees(result.getProcessingFees() + loan.getAmount());
                    break;
                case ALLOWANCE_IN_EXIT:
                    result.setAllownceInExit(result.getAllownceInExit() + loan.getAmount());
                    break;
                case TICKET_TO_DUBAI:
                    result.setTicketToDubai(result.getTicketToDubai() + loan.getAmount());
                    break;
                case CASH_ADVANCE:
                    result.setCashAdvance(result.getCashAdvance() + loan.getAmount());
                    break;
                case VACATION:
                    result.setVacation(result.getVacation() + loan.getAmount());
                    break;
                case MEDICAL_APPROVAL_IN_MALAYSIA:
                    result.setMedicalApprovalInMalaysia(result.getMedicalApprovalInMalaysia() + loan.getAmount());
                    break;
                case TICKET_TO_PREWORK_VACATION:
                    result.setTicketToPreWorkVacation(result.getTicketToPreWorkVacation() + loan.getAmount());
                    break;
                //Jirra ACC-501
                case INITIAL_CASH_ADVANCE:
                    result.setInitialCashAdvance(result.getInitialCashAdvance() + loan.getAmount());
                    break;
                case FOURTEEN_DAYS_WITHOUT_A_CLIENT_AFTER_THE_INITIAL_CASH_ADVANCE:
                    result.setFourteenDaysWithoutAClientAfterTheInitialPeriod(result.getFourteenDaysWithoutAClientAfterTheInitialPeriod() + loan.getAmount());
                    break;
                case NON_INSURANCE_MEDICAL_ASSISTANCE:
                    result.setNonInsuranceMedicalAssistance(result.getNonInsuranceMedicalAssistance() + loan.getAmount());
                    break;
                case MEDICAL_ASSISTANCE:
                    result.setMedicalAssistance(result.getMedicalAssistance() + loan.getAmount());
                    break;
                case MANAGER_DECISION:
                    result.setManagerDecision(result.getManagerDecision() + loan.getAmount());
                    break;
                //Jirra ACC-629
                case CASH_ADVANCE_FOR_TAXI:
                    result.setCashAdvanceForTaxi(result.getCashAdvanceForTaxi() + loan.getAmount());
                    break;
                case COVER_DEDUCTION_LIMIT:
                    result.setCoverDeductionLimit(result.getCoverDeductionLimit() + loan.getAmount());
                    break;
                case COVER_NEGATIVE_SALARY:
                    result.setCoverNegativeSalary(result.getCoverNegativeSalary() + loan.getAmount());
                    break;
                case OVERSTAY_FINES_FEES:
                    result.setOverstayFinesFees(result.getOverstayFinesFees() + loan.getAmount());
                    break;
                case EXIT_LOAN:
                    result.setExitLoan(result.getExitLoan() + loan.getAmount());
                    break;
                case BONUS:
                    result.setBonus(result.getBonus() + loan.getAmount());
                    break;
                case SALARY_DISPUTE:
                    result.setSalaryDispute(result.getSalaryDispute() + loan.getAmount());
                    break;
                case MEDICAL_ASSISTANT:
                    result.setMedicalAssistance(result.getMedicalAssistant() + loan.getAmount());
                    break;
                case PAY_VACATION_DAYS:
                    result.setPayVacationDays(result.getPayVacationDays() +  loan.getAmount());
                    break;
                case TAXI_REIMBURSEMENT:
                    result.setTaxiReimbursement(result.getTaxiReimbursement() + loan.getAmount());
                    break;
                case LOST_LUGGAGE_COMPENSATION:
                    result.setLostLuggageCompensation(result.getLostLuggageCompensation() + loan.getAmount());
                    break;
                case FINAL_SETTLEMENT_ABOVE_AED_1500:
                    result.setFinalSettlementaboveAED(result.getFinalSettlementaboveAED() + loan.getAmount());
                    break;
                case FINAL_SETTLEMENT_BELOW_AED_1500:
                    result.setFinalSettlementbelowAED(result.getFinalSettlementbelowAED() + loan.getAmount());
                    break;
                case OVERSTAY:
                    result.setOverstay(result.getOverstay() + loan.getAmount());
                    break;
            }
        }
        //forgiveness
        if (housemaid != null) {
            List<HousemaidForgiveness> forgivnesss = housemaidForgivenessRepository.findByHousemaid(housemaid);
            double forgivenessAmount = 0;
            for (HousemaidForgiveness item : forgivnesss) {
                forgivenessAmount += item.getAmount();
            }
            result.setForgiveness(-forgivenessAmount);
        } else {
            result.setForgiveness(-0.0);
        }

        //total repayments still needed
        List<Repayment> repayments;
        if (housemaid != null) {
            repayments = repaymentRepository.findByHousemaid(housemaid);
        } else {
            repayments = repaymentRepository.findByOfficestaff(officeStaff);
        }
        double repaymentAmount = 0;
        for (Repayment item : repayments) {
            if (item.getPaidRepayment() == true) {
                repaymentAmount += item.getAmount();
            }
        }
        result.setTotlaRepayments(-repaymentAmount);

        return result;
    }

    @NoPermission
    @RequestMapping("/getHousemaidLoans/{id}")
    public ResponseEntity<?> getHousemaidLoans(@PathVariable Long id) {
        /*
         * <AUTHOR> Qazzaz
         * @reason PAY-117
         * start
         */
        Housemaid housemaid = housemaidRepository.findOne(id);
        if (housemaid != null) {
            List<EmployeeLoan> loans = employeeLoanRepository.findByHousemaid(housemaid);
            if(loans.size() > 0)
              return new ResponseEntity<>(project(loans , HousemaidLoanProjection.class), HttpStatus.OK);
            else
            {
              return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
            }
        }
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);

        /*
         * end
         */
    }

    @NoPermission
    @RequestMapping("/maidloans/{id}")
    public ResponseEntity<?> maidloans(@PathVariable Long id) {
        Housemaid housemaid = housemaidRepository.findOne(id);
        if (housemaid != null) {
            return new ResponseEntity<>(employeeLoanRepository.findByHousemaid(housemaid), HttpStatus.OK);
        }
        return new ResponseEntity<>("Please check the passed houseamid's id", HttpStatus.BAD_REQUEST);

    }

    @NoPermission
    @RequestMapping("/getOfficeStaffLoans/{id}")
    public ResponseEntity<?> officeStaffLoans(@PathVariable Long id) {
        OfficeStaff staff = staffRep.findOne(id);
        if (staff != null) {
            return new ResponseEntity<>(employeeLoanRepository.getLoansByOfficeStaff(staff.getId()), HttpStatus.OK);
        }
        return new ResponseEntity<>("Please check the passed officeStaff's id", HttpStatus.BAD_REQUEST);

    }

    @PreAuthorize("hasPermission('loans','getStaffLoans')")
    @RequestMapping(value = "/officeStaffLoans/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getStaffLoans(@PathVariable("id") OfficeStaff staff) {

        return new ResponseEntity<>(getLoansBean(staff), HttpStatus.OK);

    }


    @Transactional
    @PreAuthorize("hasPermission('loans','updateLoan')")
    @RequestMapping(value = "/updateLoan")
    public ResponseEntity<?> updateLoan(@RequestBody EmployeeLoanApprove loanApprove) {
        EmployeeLoan loan = getRepository().findOne(loanApprove.getOriginalLoan().getId());
        OfficeStaff staff = officeStaffRepository.getOne(loan.getOfficeStaff().getId());

        if (loanApprove.getUpdatedAmount() <= 0 || loanApprove.getUpdatedRepaymentAmount() <= 0)
            throw new BusinessException("Can't update for given amounts!");

        //if nothing changed, show exception
        if (loanApprove.getUpdatedAmount().equals(loan.getAmount())
                && loanApprove.getUpdatedRepaymentAmount().equals(loan.getMonthlyRepaymentAmount()))
            return ResponseEntity.ok("");

        //calculate paid amount
        Double paidAmount = repaymentRepository.sumAmountByEmployeeLoanAndPaidRepayment(loan, true);
        Double remainingAmount = loan.getAmount() - (paidAmount != null ? paidAmount : 0d);

        //make sure that the new amount > paid amount
        if (loanApprove.getUpdatedAmount() < (paidAmount != null ? paidAmount : 0d))
            throw new BusinessException("new loan amount can't be less than the paid amount");

        //save the EmployeeLoanApprove
        loanApprove.setAmount(loan.getAmount());
        loanApprove.setLoanType(loan.getLoanType());
        loanApprove.setLoanDate(loan.getLoanDate());
        loanApprove.setMonthlyRepaymentAmount(loan.getMonthlyRepaymentAmount());
        loanApprove.setRemainingAmount(remainingAmount);
        loanApprove.setOfficeStaff(loan.getOfficeStaff());
        loanApprove.setLoanType(loan.getLoanType());
        loanApprove.setLoanApproveType(EmployeeLoanApprove.LoanApproveType.UPDATED_LOAN);
        loanApprove = employeeLoanApproveRepository.save(loanApprove);

        //generate public page url and send it to manager
        String url = "";
        String notes = "";

//        Map<String, String> paramValues = new HashMap<>();
//        paramValues.put("employee_name", staff.getFirstLastName());

//        String messageTemplateCode = "";
        OfficeStaff finalManager = staff.getFinalManager();

        // only repayment is changed
        if(loan.getAmount().equals(loanApprove.getUpdatedAmount())){
//            messageTemplateCode = "Payroll_Change_Loan_repayments_Approval";
            url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, loanApprove.getId().toString() + "#Payroll_Change_Loan_repayments_Approval", String.valueOf(finalManager.getUser().getId()));
            Date startingDate = null;
            Repayment lastPaidRepayment = repaymentRepository.findTopByEmployeeLoanAndPaidRepaymentOrderByRepaymentDateDesc(loan, true);
            if (lastPaidRepayment != null)
                startingDate = DateUtil.addMonths(lastPaidRepayment.getRepaymentDate(), 1);
            else { //new loan
                if (loan.getDeductFromSameMonth())
                    startingDate = loan.getLoanDate();
                else
                    startingDate = DateUtil.addMonths(loan.getLoanDate(), 1);
            }
            notes = "Change the loan repayment amount of " + staff.getName() + " from " + loan.getHousemaidRepaymentAmountWithCurrency() + " to " + loanApprove.getUpdatedRepaymentAmountWithCurrency() + " starting from " + DateUtil.formatFullDate(startingDate) + ".";
            pendingManagerApprovalService.insertNewPendingApprovalRequest(staff.getName(), DateUtil.formatFullDate(staff.getStartingDate()),staff.getSalaryWithCurrency(), staff.getJobTitle() != null ? staff.getJobTitle().getName() : "", staff.getEmployeeManager(), finalManager, loanApprove.getUpdatedRepaymentAmountWithCurrency(), "Edit Loan Repayment", notes, url, loanApprove.getId().toString() + "#Payroll_Change_Loan_repayments_Approval");
//            paramValues.put("old_value", NumberFormatter.formatNumber(loan.getMonthlyRepaymentAmount()) + " " + staff.getSalaryCurrency());
//            paramValues.put("new_value", NumberFormatter.formatNumber(loanApprove.getUpdatedRepaymentAmount()) + " " + staff.getSalaryCurrency());
        }else{
//            messageTemplateCode = "Payroll_Edit_Loan_Approval";
            url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, loanApprove.getId().toString() + "#Payroll_Edit_Loan_Approval", String.valueOf(finalManager.getUser().getId()));

            notes = "Change the loan amount of " + staff.getName() + " from " + loan.getAmountWIthCurrency() + " to " + loanApprove.getUpdatedAmountWithCurrency() + " on " + DateUtil.formatFullDate(loanApprove.getCreationDate()) + ".";
            pendingManagerApprovalService.insertNewPendingApprovalRequest(staff.getName(), DateUtil.formatFullDate(staff.getStartingDate()),staff.getSalaryWithCurrency(), staff.getJobTitle() != null ? staff.getJobTitle().getName() : "", staff.getEmployeeManager(), finalManager, loanApprove.getUpdatedAmountWithCurrency(), "Edit Loan Amount", notes, url, loanApprove.getId().toString() + "#Payroll_Edit_Loan_Approval");
//            paramValues.put("old_value", NumberFormatter.formatNumber(loanApprove.getAmount()) + " " + staff.getSalaryCurrency());
//            paramValues.put("new_value", NumberFormatter.formatNumber(loanApprove.getUpdatedAmount()) + " " + staff.getSalaryCurrency());
//            paramValues.put("repayment_amount", NumberFormatter.formatNumber(loanApprove.getUpdatedRepaymentAmount()) + " " + staff.getSalaryCurrency());
        }
//        paramValues.put("url", url);

//        SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                "Editing Loans",
//                normalizePhoneNumber(finalManager.getPhoneNumber()),
//                finalManager.getEmail(),
//                SmsReceiverType.Office_Staff,
//                finalManager.getId(),
//                finalManager.getName(),
//                messageTemplateCode,
//                paramValues,
//                null,
//                finalManager.getPreferredCommunicationMethod());
//
//        if (smsResponse == null || !smsResponse.isSuccess())
//            throw new RuntimeException("Failed to send the Approval Message to the Final Manager");


        return ResponseEntity.ok("Your request was received, waiting the employee's final manager approval!");
    }

//    @Override
//    protected ResponseEntity<?> deleteEntity(EmployeeLoan entity) {
//
//        entity= employeeLoanRepository.getOne(entity.getId());
//
//        //if office staff loan so we need to check paid amount and delete repayments before calling super
//        if (entity.getOfficeStaff() != null) {
//            //check if there is any paid repayments
//            Date currentDate = new java.sql.Date(PayrollGenerationLibrary.getPayrollEndDate(new LocalDate()).withDayOfMonth(1).toDate().getTime());
//            Double paidAmount = repaymentRepository.sumAmountByEmployeeLoanAndPaidRepayment(entity, currentDate);
//            if (paidAmount != null && paidAmount > 0)
//                throw new RuntimeException("can't delete this loan, it had paid repayments");
//
//            //delete all related repayments
//            repaymentRepository.deleteByEmployeeLoan(entity);
//            repaymentRepository.flush();
//
//            // delete all related loan approves
//            Setup.getRepository(EmployeeLoanApproveRepository.class)
//                    .deleteByOriginalLoan(entity);
//            Setup.getRepository(EmployeeLoanApproveRepository.class).flush();
//        }
//
//        //call super
//        return super.deleteEntity(entity);
//    }

    @NoPermission
    @RequestMapping(value = "/deleteLoanByApprove/{id}", method = RequestMethod.DELETE)
    public ResponseEntity<?> deleteLoanByApprove(@PathVariable Long id) {
        EmployeeLoan loan = employeeLoanRepository.findOne(id);
        OfficeStaff staff = officeStaffRepository.getOne(loan.getOfficeStaff().getId());

        //calculate paid amount
        Double paidAmount = repaymentRepository.sumAmountByEmployeeLoanAndPaidRepayment(loan, true);
        if(paidAmount != null  && paidAmount > 0)
            throw new BusinessException("can't delete this loan, it had paid repayments");

        Double remainingAmount = loan.getAmount() - (paidAmount != null ? paidAmount : 0d);

        //save new employeeLoanApprove entity
        EmployeeLoanApprove loanApprove = new EmployeeLoanApprove();
        loanApprove.setAmount(loan.getAmount());
        loanApprove.setLoanType(loan.getLoanType());
        loanApprove.setLoanDate(loan.getLoanDate());
        loanApprove.setMonthlyRepaymentAmount(loan.getMonthlyRepaymentAmount());
        loanApprove.setRemainingAmount(remainingAmount);
        loanApprove.setOfficeStaff(loan.getOfficeStaff());
        loanApprove.setOriginalLoan(loan);
        loanApprove.setLoanApproveType(EmployeeLoanApprove.LoanApproveType.DELETE_LOAN);
        loanApprove= employeeLoanApproveRepository.save(loanApprove);

        //generate public page url and send it to manager
        String url = "";
        OfficeStaff finalManager = staff.getFinalManager();
        url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, loanApprove.getId().toString() + "#Payroll_Delete_Loan_Approval", String.valueOf(finalManager.getUser().getId()));

        pendingManagerApprovalService.insertNewPendingApprovalRequest(staff.getName(), DateUtil.formatFullDate(staff.getStartingDate()),staff.getSalaryWithCurrency(), staff.getJobTitle() != null ? staff.getJobTitle().getName() : "", staff.getEmployeeManager(), finalManager, loan.getAmountWIthCurrency(), "Delete Loan", null, url, loanApprove.getId().toString() + "#Payroll_Delete_Loan_Approval");
//        Map<String, String> paramValues = new HashMap<>();
//        paramValues.put("employee_name", staff.getFirstLastName());
//        paramValues.put("loan_amount", NumberFormatter.formatNumber(loan.getAmount()) + " " + staff.getSalaryCurrency());
//        paramValues.put("url", url);
//
//        SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                "Deleting Loan",
//                normalizePhoneNumber(finalManager.getPhoneNumber()),
//                finalManager.getEmail(),
//                SmsReceiverType.Office_Staff,
//                finalManager.getId(),
//                finalManager.getName(),
//                "Payroll_Delete_Loan_Approval",
//                paramValues,
//                null,
//                finalManager.getPreferredCommunicationMethod());
//
//        if (smsResponse == null || !smsResponse.isSuccess())
//            throw new RuntimeException("Failed to send the Approval Message to the Final Manager");

        return ResponseEntity.ok("Your request was received, waiting the employee's final manager approval!");
    }

    @Transactional
    public Boolean createEmployeeLoanBT(Long housemaidId, Double amount, Date date, String loanType) {
        Boolean employeeLoanExists = employeeLoanRepository.existsByHousemaidIdAndAmountAndLoanType(housemaidId, amount, EmployeeLoan.LoanType.valueOf(loanType));
        if(!employeeLoanExists) {
            EmployeeLoan employeeLoan = new EmployeeLoan();
            employeeLoan.setHousemaid(housemaidRepository.findOne(housemaidId));
            employeeLoan.setLoanType(EmployeeLoan.LoanType.valueOf(loanType));
            employeeLoan.setLoanDate(date);
            employeeLoan.setAmount(amount);
            employeeLoan.setCashLoanDescription("");
            employeeLoanRepository.save(employeeLoan);
            return true;
        }
        else
            throw new BusinessException("Housemaid's loan already created");
    }
}
