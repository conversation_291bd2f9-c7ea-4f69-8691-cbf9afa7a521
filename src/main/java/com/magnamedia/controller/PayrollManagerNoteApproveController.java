package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AbstractPayrollManagerNote;
import com.magnamedia.entity.Repayment;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.entity.PayrollManagerNoteApprove;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.PayrollManagerNoteApproveRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.repository.RepaymentRepository;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PendingManagerApprovalService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/managerNoteApprove")
public class PayrollManagerNoteApproveController extends BaseRepositoryController<PayrollManagerNoteApprove> {

    @Autowired
    private PayrollManagerNoteApproveRepository noteRep;

    @Autowired
    private OfficeStaffRepository staffRep;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Override
    public BaseRepository<PayrollManagerNoteApprove> getRepository() {
        return noteRep;
    }

    @Autowired
    private RepaymentRepository repaymentRepository;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    @Override
    protected ResponseEntity<?> createEntity(PayrollManagerNoteApprove entity) {

        if (entity.getHousemaid() != null) {
            throw new BusinessException("Housemaid's manager note doesn't need a final manager approval!");
        }

        if(entity.getOfficeStaff() == null) {
            throw new BusinessException("OfficeStaff doesn't exist!");
        }

        AbstractPayrollManagerNote.ManagerNoteType noteType = entity.getNoteType();

        if ((noteType.equals(AbstractPayrollManagerNote.ManagerNoteType.SALARY_RAISE) || noteType.equals(AbstractPayrollManagerNote.ManagerNoteType.REDUCTION))
                && !entity.getAmount().equals(entity.getBasicSalary() + entity.getHousing() + entity.getTransportation())) {
            throw new BusinessException("Amount must be equals to Basic Salary + Housing + Transportation");
        }

        OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(entity.getOfficeStaff().getId());

        if (noteType.equals(AbstractPayrollManagerNote.ManagerNoteType.REDUCTION)
                && (entity.getAmount() > officeStaff.getSalary()
                    || entity.getBasicSalary() > officeStaff.getBasicSalary()
                    || entity.getTransportation() > officeStaff.getTrasnportation()
                    || entity.getHousing() > officeStaff.getHousingAllowance())) {
            throw new BusinessException("Reduction amount can't be more than the employee's salary!");
        }

        if (noteType.equals(AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION)) {
            double salary = officeStaff.getSalary();
            java.sql.Date repaymentDate = new java.sql.Date(new LocalDate().withDayOfMonth(1).toDate().getTime());

            List<Repayment> repayments = repaymentRepository.getCurrentMonthRepayments(officeStaff, repaymentDate);
            Double amount = 0d;
            for(Repayment repayment: repayments) {
                amount += repayment.getAmount();
            }
            salary -= entity.getAmount();
            salary -= amount;
            if (salary < 0) {
                throw new BusinessException("Salary can't be negative!");
            }
        }

        OfficeStaff finalManager = officeStaff.getFinalManager();


        if (finalManager == null || finalManager.getPhoneNumber() == null) {
            throw new BusinessException("Can't find the employee's final manager or the manager doesn't have a phone number!");
        } else {
            super.createEntity(entity);

            String templateName = "";
            String type = "";

            switch (noteType) {
                case BONUS:
                    templateName = "Payroll_Bonus_Approval";
                    type = "Manager Note - Bonus";
                    break;
                case DEDUCTION:
                    templateName = "Payroll_Salary_Deduction_Approval";
                    type = "Manager Note - Deduction";
                    break;
                case SALARY_RAISE:
                    templateName = "Payroll_Salary_Raise_Approval";
                    type = "Manager Note - Raise";
                    break;
                case REDUCTION:
                    templateName = "Payroll_Salary_Reduction_Approval";
                    type = "Manager Note - Reduction";
                    break;
                default:
                    throw new BusinessException("This note type '" + noteType + "' is not supported!");
            }

            String url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, entity.getId().toString() + "#" + templateName, String.valueOf(finalManager.getUser().getId()));
            pendingManagerApprovalService.insertNewPendingApprovalRequest(officeStaff.getName(), DateUtil.formatFullDate(officeStaff.getStartingDate()),officeStaff.getSalaryWithCurrency(), officeStaff.getJobTitle() != null ? officeStaff.getJobTitle().getName() : "", officeStaff.getEmployeeManager(), finalManager, (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().name() + " " : "") +  NumberFormatter.formatNumber(entity.getAmount()), type, entity.getNoteReasone(), url, entity.getId().toString() + "#" + templateName);
//            Date date = PayrollGenerationLibrary.getPayrollEndDate(new LocalDate(entity.getNoteDate())).toDate();
//
//            Map<String, String> paramValues = new HashMap<>();
//            paramValues.put("employee_name", officeStaff.getFirstLastName());
//            paramValues.put("value", NumberFormatter.formatNumber(entity.getAmount()));
//            paramValues.put("url", url);
//            paramValues.put("currency", officeStaff.getSalaryCurrency().toString());
////            paramValues.put("note_date", DateUtil.formatSimpleMonthYear(date));
//
//            SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                    type,
//                    normalizePhoneNumber(finalManager.getPhoneNumber()),
//                    finalManager.getEmail(),
//                    SmsReceiverType.Office_Staff,
//                    finalManager.getId(),
//                    finalManager.getName(),
//                    templateName,
//                    paramValues,
//                    null,
//                    finalManager.getPreferredCommunicationMethod());
//
//            if (smsResponse == null || !smsResponse.isSuccess())
//                throw new RuntimeException("Failed to send the Approval Message to the Final Manager");
        }
        return ResponseEntity.ok("Your request received, waiting the employee's final manager approval!");
    }

    @NoPermission
    @RequestMapping(
            value = {"/update"},
            method = {RequestMethod.POST}
    )
    @ResponseBody
    @Transactional
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        PayrollManagerNoteApprove updated = this.parse(objectNode);
        PayrollManagerNoteApprove origin = this.getRepository().findOne(updated.getId());
        this.update(origin, updated, objectNode);
        return this.updateEntity(origin);
    }

    @Override
    protected ResponseEntity<?> updateEntity(PayrollManagerNoteApprove entity) {
        AbstractPayrollManagerNote.ManagerNoteType type = entity.getNoteType();

        if ((type.equals(AbstractPayrollManagerNote.ManagerNoteType.SALARY_RAISE) || type.equals(AbstractPayrollManagerNote.ManagerNoteType.REDUCTION))
                && !entity.getAmount().equals(entity.getBasicSalary() + entity.getHousing() + entity.getTransportation())) {
            throw new BusinessException("Amount must be equals to Basic Salary + Housing + Transportation");
        }
        return super.updateEntity(entity);
    }

    @NoPermission
    @RequestMapping("/getOfficeStaffManagerNotesApproves/{id}")
    public ResponseEntity<?> getOfficeStaffManagerNotesApproves(@PathVariable Long id){
        OfficeStaff staff=staffRep.findOne(id);
        if(staff!=null)
            return new ResponseEntity<>(noteRep.findByOfficeStaffAndIsActionTaken(staff,false), HttpStatus.OK);
        return new ResponseEntity<>("Please check the passed OfficeStaff's id",HttpStatus.BAD_REQUEST);

    }

}
