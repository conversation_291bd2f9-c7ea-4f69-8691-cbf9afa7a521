package com.magnamedia.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.HousemaidPayslipProjection;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.OcrHelper;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.PublicPageService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.OfficeStaffFinalSettlementService;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Saker Ali
 * Creation Date 09/05/2020
 */
@RestController
@RequestMapping("/public")
public class PublicPageController {

    private final PublicPageHelper helper;

    @Autowired
    OfficeStaffFinalSettlementService officeStaffFinalSettlementService ;
    @Autowired
    private PublicPageService publicPageService;

    @Autowired
    private PayrollNotificationsService payrollNotificationsService;

    @Autowired
    private OcrHelper ocrHelper;

    @Autowired
    OfficeStaffFinalSettlementRepository officeStaffFinalSettlementRepository ;
    @Autowired
    FinalSettlementNoteRepository finalSettlementNoteRepository ;

    @Autowired
    OfficeStaffRepository officeStaffRepository ;

    @Autowired
    OfficeStaffTodoRepository officeStaffTodoRepository ;

    @Autowired
    PayrollNotificationsService notificationsService;

    public PublicPageController(PublicPageHelper helper) {
        this.helper = helper;
    }

    @RequestMapping(value = "/meta", method = RequestMethod.GET)
    public ResponseEntity<?> pageInformation(@RequestParam(value = "token") String token) {
        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = this.helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];
            long generateTime = Long.parseLong(decryptedToken[2]);
            long userId = Long.parseLong(decryptedToken[3]);

            String additionalUserIdsParamValue = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ACCESS_LINK_USER_ID_RECIPIENTS);
            List<Long> additionalUserIds = (additionalUserIdsParamValue != null && !additionalUserIdsParamValue.isEmpty()) ?
                    Arrays.stream(additionalUserIdsParamValue.split(","))
                            .map(s -> Long.parseLong(s.trim()))
                            .collect(Collectors.toList()) :
                    Collections.emptyList();  // Return an empty list instead of throwing an exception


            // Get the current user ID
            Long currentUserId = CurrentRequest.getUser().getId();

            // Check if the current user is authorized
            if (!currentUserId.equals(userId) && !additionalUserIds.contains(currentUserId)) {
                throw new RuntimeException("Unauthorized.");
            }

            switch (pageId) {
                case PublicPageHelper.FINAL_MANAGER_APPROVE:
                    Map<String, Object> infoMap = publicPageService.getFinalManagerApprovePageInfo(tokenLoad);
                    return ResponseEntity.ok(infoMap);
                case PublicPageHelper.PENDING_APPROVAL_REQUESTS:
                    return publicPageService.getPendingApprovalPageInfo(tokenLoad);
                case PublicPageHelper.FINAL_SETTLEMENT_FINAL_MANAGER:
                case PublicPageHelper.FINAL_SETTLEMENT_CFO:
                    Map<String, Object> finalSettlement = publicPageService.getFinalSettlementForExpatAndEmirati(tokenLoad, generateTime);
                    return ResponseEntity.ok(finalSettlement);
                case PublicPageHelper.FINAL_SETTLEMENT_QUESTION:
                    Map<String, Object> question = publicPageService.getCFOQuestion(tokenLoad);
                    return ResponseEntity.ok(question);
                case PublicPageHelper.FINAL_SETTLEMENT:
                    Map<String, Object> finalSettlementForOverseas = publicPageService.getFinalSettlementForOverseas(tokenLoad);
                    return ResponseEntity.ok(finalSettlementForOverseas);
//                case PublicPageHelper.ACCESS_INFORMATION:
//                    return ResponseEntity.ok(publicPageService.getAccessInformationPageInfo(tokenLoad));
//                case PublicPageHelper.REVOKE_ACCESS:
//                    return ResponseEntity.ok(publicPageService.getRevokeAccessPageInfo(tokenLoad));
                case PublicPageHelper.INFORM_TERMINATED_STAFF:
                    return ResponseEntity.ok(publicPageService.getInformTerminatedStaffPageInfo(tokenLoad));
                case PublicPageHelper.QUESTION_ABOUT_MAID_SALARY:
                    return ResponseEntity.ok(publicPageService.getQuestionAboutMaidSalary(tokenLoad));
                default:
                    break;
            }
        } catch (Exception ex) {
            if (ex.getMessage().equals("Unauthorized.")) {
                throw new RuntimeException("Unauthorized.");
            } else {
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while calling meta API!", false);
                Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
                throw new RuntimeException(!"".equals(PublicPageService.errorMessage) ?
                        PublicPageService.errorMessage : "Link has expired!");
            }
        }

        return ResponseEntity.ok("Success!");
    }

    @NoPermission
    @RequestMapping(value = "/metaWithoutAuthorization", method = RequestMethod.GET)
    public ResponseEntity<?> pageInformationWithoutAuthorization(@RequestParam(value = "token") String token) {
        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = this.helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];

            switch (pageId) {
                case PublicPageHelper.NEW_STAFF_QUESTIONNAIRE:
                    Map<String, Object> staffInfoMap = publicPageService.getNewStaffQuestionnairePageInfo(tokenLoad);
                    return ResponseEntity.ok(staffInfoMap);
                case PublicPageHelper.PAYSLIPS:
                    HousemaidPayslipProjection housemaidPayslip = publicPageService.getHousemaidPayslip(tokenLoad);
                    return ResponseEntity.ok(housemaidPayslip);
                default:
                    break;
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while calling meta With Authorization API!",false);
            Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException(!"".equals(PublicPageService.errorMessage) ?
                    PublicPageService.errorMessage : "Link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }

    @NoPermission
    @RequestMapping(value = "/approveFinalManager", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> approveFinalManager(
            @RequestParam(value = "token") String token,
            @RequestParam(value = "action") Boolean action,
            @RequestParam(value = "notes", required = false) String notes) {

        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];
            switch (pageId) {
                case PublicPageHelper.FINAL_MANAGER_APPROVE:
                    if (!publicPageService.approveCaseByFinalManager(tokenLoad, action, notes)) {
                        throw new BusinessException(PublicPageService.errorMessage="Failed to approve, Link has expired!");
                    }
                    break;
//                case PublicPageHelper.ACCESS_INFORMATION:
//                    if (!publicPageService.approveGrantAccess(tokenLoad)) {
//                        throw new RuntimeException(PublicPageService.errorMessage="Failed to approve, Link has expired!");
//                    }
//                    break;
//                case PublicPageHelper.REVOKE_ACCESS:
//                    if (!publicPageService.approveRevokeAccess(tokenLoad)) {
//                        throw new RuntimeException(PublicPageService.errorMessage="Failed to approve, Link has expired!");
//                    }
//                    break;
                case PublicPageHelper.INFORM_TERMINATED_STAFF:
                    if (!publicPageService.approveInformTerminatedStaff(tokenLoad)) {
                        throw new BusinessException(PublicPageService.errorMessage="Failed to approve, Link has expired!");
                    }
                    break;
                default:
                    throw new BusinessException(PublicPageService.errorMessage="Page Id is not correct here");
            }
        } catch (Throwable ex) {
            Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException(!"".equals(PublicPageService.errorMessage) ?
                    PublicPageService.errorMessage : "Link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }

    @NoPermission
    @RequestMapping(value = "/replayQuestionAboutMaidSalary", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> replayQuestionAboutMaidSalary(
            @RequestParam(value = "token") String token,
            @RequestParam(value = "answer", required = false) String answer) {

        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];

            String[] tokenLoadParts = tokenLoad.split("#");
            if (tokenLoadParts.length != 1)
                throw new RuntimeException("missing data in the token.");
            String questionAboutSalaryId = tokenLoadParts[0];

            QuestionAboutMaidSalary questionAboutMaidSalary = Setup.getRepository(QuestionAboutMaidSalaryRepository.class).getOne(Long.parseLong(questionAboutSalaryId));
            if (questionAboutMaidSalary.getActionTaken())
                throw new BusinessException("Action has been already taken!");

            Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).getOne(questionAboutMaidSalary.getHousemaid().getId());
            payrollNotificationsService.replayToAuditorsAboutMaidSalaryQuestion(housemaid, answer);

            questionAboutMaidSalary.setActionTaken(true);
            questionAboutMaidSalary.setAnswer(answer);
            Setup.getRepository(QuestionAboutMaidSalaryRepository.class).save(questionAboutMaidSalary);

        } catch (Throwable ex) {
            Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException(!"".equals(PublicPageService.errorMessage) ?
                    PublicPageService.errorMessage : "Link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }

    @NoPermission
    @RequestMapping(value = "/submitQuestionnaire", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> submitQuestionnaire(@RequestParam(value = "token") String token, @RequestBody OfficeStaffCandidate officeStaffCandidate) {
        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];
            switch (pageId) {
                case PublicPageHelper.NEW_STAFF_QUESTIONNAIRE:
                    if (!publicPageService.submitQuestionnaire(tokenLoad, officeStaffCandidate)) {
                        throw new RuntimeException(PublicPageService.errorMessage="Failed to submit!");
                    }
                    break;
                default:
                    throw new BusinessException(!"".equals(PublicPageService.errorMessage) ?
                            PublicPageService.errorMessage : "Page Id is not correct here!");

            }
        } catch (Throwable ex) {
            Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException(!"".equals(PublicPageService.errorMessage) ?
                    PublicPageService.errorMessage : "link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }

    @RequestMapping(value = "/submitAnswerForCFO", method = RequestMethod.GET)
    @Transactional
    @NoPermission
    public ResponseEntity<?> submitAnswerForCFO(@RequestParam(value = "token") String token,
                                                @RequestParam(value = "answer") String answer) {
        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];
            switch (pageId) {
                case PublicPageHelper.FINAL_SETTLEMENT_QUESTION:
                    if (!publicPageService.submitAnswerForCFO(tokenLoad, answer)) {
                        throw new RuntimeException(PublicPageService.errorMessage="Failed to submit!");
                    }
                    break;
                default:
                    throw new RuntimeException(!"".equals(PublicPageService.errorMessage) ?
                            PublicPageService.errorMessage : "Page Id is not correct here!");

            }
        } catch (Throwable ex) {
            Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
            throw new BusinessException(!"".equals(PublicPageService.errorMessage) ?
                    PublicPageService.errorMessage : "link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }


    @NoPermission
    @RequestMapping(value = "/checkiban/", method = RequestMethod.GET)
    public ResponseEntity<?> checkIBAN() {
        return ResponseEntity.ok("");
    }

    @NoPermission
    @RequestMapping(value = "/checkiban/{IBAN_NUM}", method = RequestMethod.GET)
    public ResponseEntity<?> checkIBAN(@PathVariable("IBAN_NUM") String IBANNumber)
            throws IOException, JSONException {

        String url = "https://api.iban.com/clients/api/v4/iban/";
        URL obj = new URL(url);
        HttpsURLConnection con = null;
        DataOutputStream wr = null;
        BufferedReader in = null;
        Map bankInfo = new HashMap();
        try {
            con = (HttpsURLConnection) obj.openConnection();

            //add reuqest header
            con.setRequestMethod("POST");
            con.setRequestProperty("User-Agent", HttpHeaders.USER_AGENT);
            con.setRequestProperty("Accept-Language", "en-US,en;q=0.5");

            StringBuilder sb = new StringBuilder();
            sb.append("api_key=")
                    .append(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_IBAN_CHECK_API_KEY))
                    .append("&format=json").append("&iban=").append(IBANNumber);

            // Send post request
            con.setDoOutput(true);
            wr = new DataOutputStream(con.getOutputStream());
            wr.writeBytes(sb.toString());
            wr.flush();
            wr.close();

            in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;
            StringBuffer response = new StringBuffer();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }


            bankInfo = new ObjectMapper().readValue(response.toString(), HashMap.class);
            String bankName = (String) ((HashMap) bankInfo.get("bank_data")).get("bank");
            PicklistItem picklistItemInfo = null;
            if ((bankName != null) && (!bankName.isEmpty())) {
                List<PicklistItem> items = Setup.getRepository(PicklistRepository.class).findByCode("BankName")
                        .getItemsWithTag("IBAN_BANK_NAME", bankName);
                if (!items.isEmpty()) {
                    picklistItemInfo = items.get(0);
                }
            }
            bankInfo.put("picklistItemInfo", picklistItemInfo);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (wr != null) {
                try {
                    wr.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (con != null) {
                try {
                    con.disconnect();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return ResponseEntity.ok(bankInfo);
    }

    @NoPermission
    @RequestMapping(value = "/getEidNumberFromOCR", method = RequestMethod.POST)
    public ResponseEntity<?> getEidNumberFromOCR(@RequestBody Attachment attachment) {

        attachment = Setup.getRepository(AttachementRepository.class).findOne(attachment.getId());

        String eidNumber = ocrHelper.getMatcherByPatternFromOcr(attachment, OcrHelper.OCR_EID_PATTERN);
        return ResponseEntity.ok(eidNumber);
    }

    @NoPermission
    @RequestMapping(value = "/approveFinalSettlement", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> approveFinalSettlement(@RequestParam(value = "token") String token, @RequestBody OfficeStaffFinalSettlement finalSettlement,
                                                    @RequestParam(value = "action") Boolean action,
                                                    @RequestParam(value = "notes", required = false) String notes,
                                                    @RequestParam(value = "finalManagerNotes" ,required = false) String finalManagerNotes) {
        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = helper.decryptToken(token);
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];
            if (!Objects.equals(pageId, PublicPageHelper.FINAL_SETTLEMENT)){
                throw new RuntimeException("Page Id is not correct here");
            }
            if (!publicPageService.approveFinalSettlementForOverseas(tokenLoad, finalSettlement, action, notes)) {
                throw new RuntimeException("Failed to approve!");
            }
        } catch (Throwable ex) {
            Logger.getLogger(PublicPageHelper.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException("Link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }

    @NoPermission
    @RequestMapping(value = "/goInFlowFinalSettlement", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> goInFlowFinalSettlement(
            @RequestParam(value = "token") String token,
            @RequestParam(value = "action") String action,
            @RequestParam(value = "questionedUser" ,required = false) Long questionedUser,
            @RequestParam(value = "note", required = false) String note,
            @RequestBody(required = false) List<Attachment> attachments) {

        FinalSettlementNote finalSettlementNote = null;
        OfficeStaffCandidate candidate = null;
        if (note != null) {
            finalSettlementNote = new FinalSettlementNote();
            finalSettlementNote.setText(note);
        }

        PublicPageService.errorMessage = "";
        try {
            String[] decryptedToken = helper.decryptToken(token);
            if(decryptedToken.length != 4){
                throw new RuntimeException("URL is not valid now");
            }
            String pageId = decryptedToken[0];
            String tokenLoad = decryptedToken[1];
            long generateTime = Long.parseLong(decryptedToken[2]);
            OfficeStaffFinalSettlement finalSettlement = officeStaffFinalSettlementRepository.findOne(Long.parseLong(tokenLoad));
            if(finalSettlement == null){
                throw new RuntimeException("Final Settlement is null");
            }
            OfficeStaff terminatedStaff = officeStaffRepository.findOne(finalSettlement.getOfficeStaff().getId());
            List<OfficeStaffDocument> officeStaffDocuments = null ;
            switch (action) {
                //case "generateDocumentToSign":
                //    return ResponseEntity.ok(publicPageService.generationDocumentsAndAddToFinalSettlement(finalSettlement));
                case "sendBackToPayrollManager":
                    if(finalSettlementNote == null){
                        throw new RuntimeException("the note is required in case send back to payroll manager");
                    }
                    if(!finalSettlement.isValidURL(generateTime)){
                        throw new RuntimeException(" Action is already taken");
                    }
                    if (attachments!= null && !attachments.isEmpty()){
                        publicPageService.addAttachmentsToFS(attachments, finalSettlement);
                    }
                    String managerName = "" ;
                    if(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_CFO.equals(finalSettlement.getCurrentStatus())){
                        OfficeStaff cfo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
                        managerName = cfo.getFirstLastName();
                    }else {
                        managerName = terminatedStaff.getLastManagerOtherThanCFO().getFirstLastName() ;
                    }
                    String todoLabel = "Check notes from "+managerName+" on the final settlement of "+terminatedStaff.getFirstLastName();
                    candidate = terminatedStaff.getOfficeStaffCandidate();
                    if (candidate == null) {
                        candidate = new OfficeStaffCandidate();
                        candidate.setMockCandidate(true);
                    }
                    candidate.setEmployeeName(terminatedStaff.getName());
                    candidate.setFirstName(terminatedStaff.getFirstName());
                    candidate.setMiddleName(terminatedStaff.getMiddleName());
                    candidate.setLastName(terminatedStaff.getLastName());
                    candidate.setJobTitle(terminatedStaff.getJobTitle());
                    if (terminatedStaff.getJobTitle() != null)
                        candidate.setJazzHRJobTitle(terminatedStaff.getJobTitle().getName());
                    candidate.setPhoneNumber(terminatedStaff.getPhoneNumber());
                    candidate.setEmail(terminatedStaff.getEmail());
                    candidate = Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
                    terminatedStaff.setOfficeStaffCandidate(candidate);
                    terminatedStaff = Setup.getRepository(OfficeStaffRepository.class).save(terminatedStaff);

                    OfficeStaffTodo officeStaffTodo = new OfficeStaffTodo(OfficeStaffTodoType.CHECK_NOTES_FROM_MANAGER.toString());
                    officeStaffTodo.setLabel(todoLabel);
                    officeStaffTodo.setCandidate(candidate);
                    officeStaffTodo.setRelatedEntityId(finalSettlement.getId());
                    officeStaffTodo.setRelatedEntityType(finalSettlement.getEntityType());
                    officeStaffTodo = officeStaffTodoRepository.save(officeStaffTodo);
                    Setup.getApplicationContext().getBean(MessagingService.class).notifyPayrollTrustee(officeStaffTodo);
                    finalSettlementNote.setFinalSettlement(finalSettlement);
                    finalSettlementNote.setNoteType(finalSettlement.getCurrentStatus()==OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_FINAL_MANAGER ?
                            FSNoteType.FINAL_MANAGER : FSNoteType.CFO);
                    finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_PAYROLL_MANAGER);
                    finalSettlementNoteRepository.save(finalSettlementNote);
                    officeStaffFinalSettlementRepository.save(finalSettlement);
                    break;

                case "sendToCFO":
                    if(note == null){
                        throw new RuntimeException("the note is required in case send to cfo");
                    }
                    if(!finalSettlement.isValidURL(generateTime)){
                        throw new RuntimeException(" Action is already taken");
                    }
                    finalSettlementNote.setFinalSettlement(finalSettlement);
                    finalSettlementNote.setNoteType(FSNoteType.FINAL_MANAGER);
                    finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_CFO);
                    publicPageService.sendFinalSettlementToCFO(finalSettlement);
                    finalSettlementNoteRepository.save(finalSettlementNote);
                    if (attachments!= null && !attachments.isEmpty()){
                        publicPageService.addAttachmentsToFS(attachments, finalSettlement);
                    }
                    officeStaffFinalSettlementRepository.save(finalSettlement);

                    break;
                //hidden after PAY-1254
//                case "reject":
//                    if(finalSettlementNote == null){
//                        throw new RuntimeException("the note is required in case reject final settlement");
//                    }
//                    if(!finalSettlement.isValidURL(generateTime)){
//                        throw new RuntimeException(" Action is already taken");
//                    }
//                    finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.REJECT);
//                    officeStaffFinalSettlementService.sendRejectEmail(finalSettlement, terminatedStaff, finalSettlementNote.getText());
//                    finalSettlementNote.setFinalSettlement(finalSettlement);
//                    finalSettlementNote.setNoteType(FSNoteType.REJECTION);
//                    finalSettlementNoteRepository.save(finalSettlementNote);
//                    finalSettlement = officeStaffFinalSettlementRepository.save(finalSettlement);
//                    finalSettlementNoteRepository.deleteByFinalSettlement(finalSettlement);
//                    officeStaffFinalSettlementRepository.delete(finalSettlement);
//                    terminatedStaff.setTerminationDate(null);
//                    terminatedStaff.setTerminationNotes(null);
//                    terminatedStaff.setTerminationReason(null);
//                    terminatedStaff.setTerminationType(null);
//                    terminatedStaff.setWhoRequestedTermination(null);
//                    terminatedStaff.setNoticePeriodStartDate(null);
//                    terminatedStaff.setNoticePeriodEndDate(null);
//                    terminatedStaff.setExcludedFromPayroll(false);
//                    terminatedStaff.setStatus(OfficeStaffStatus.ACTIVE);
//                    officeStaffRepository.save(terminatedStaff);
//                    break;
                case "question":
                    if(questionedUser == null){
                        throw new RuntimeException("User is required") ;
                    }
                    if(finalSettlementNote == null){
                        throw new RuntimeException("the note is required in case send back to payroll manager");
                    }
                    User user = Setup.getRepository(UserRepository.class).findOne(questionedUser);
                    if(user == null){
                        throw new RuntimeException("User is not found") ;
                    }
                    finalSettlementNote.setNoteType(FSNoteType.QUESTION);
                    finalSettlementNote.setFinalSettlement(finalSettlement);
                    finalSettlementNote.setUser(user);
                    finalSettlementNoteRepository.save(finalSettlementNote);
                    officeStaffFinalSettlementService.sendCFOQuestionTOUser(finalSettlementNote);
                    break;
                case "confirm":
                    if(!finalSettlement.isValidURL(generateTime)){
                        throw new RuntimeException(" Action is already taken");
                    }

                    if (attachments!= null && !attachments.isEmpty()){
                        publicPageService.addAttachmentsToFS(attachments, finalSettlement);
                    }

                    finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.CONFIRM);
                    finalSettlement.setApprovedByCFO(true);
                    finalSettlement.setActionTaken(true);
                    officeStaffFinalSettlementRepository.save(finalSettlement);
                    candidate = terminatedStaff.getOfficeStaffCandidate();
                    if (candidate == null) {
                        candidate = new OfficeStaffCandidate();
                        candidate.setMockCandidate(true);
                    }
                    candidate.setEmployeeName(terminatedStaff.getName());
                    candidate.setFirstName(terminatedStaff.getFirstName());
                    candidate.setMiddleName(terminatedStaff.getMiddleName());
                    candidate.setLastName(terminatedStaff.getLastName());
                    candidate.setJobTitle(terminatedStaff.getJobTitle());
                    if (terminatedStaff.getJobTitle() != null)
                        candidate.setJazzHRJobTitle(terminatedStaff.getJobTitle().getName());
                    candidate.setPhoneNumber(terminatedStaff.getPhoneNumber());
                    candidate.setEmail(terminatedStaff.getEmail());
                    candidate = Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
                    terminatedStaff.setOfficeStaffCandidate(candidate);
                    terminatedStaff = Setup.getRepository(OfficeStaffRepository.class).save(terminatedStaff);
                    String todoPayLabel = "Pay the final settlement of "+finalSettlement.getOfficeStaff().getFirstLastName();
                    OfficeStaffTodo officeStaffPayTodo = new OfficeStaffTodo(OfficeStaffTodoType.PAY_THE_FINAL_SETTLEMENT.toString());
                    officeStaffPayTodo.setLabel(todoPayLabel);
                    officeStaffPayTodo.setCandidate(candidate);
                    officeStaffPayTodo.setRelatedEntityId(finalSettlement.getId());
                    officeStaffPayTodo.setRelatedEntityType(finalSettlement.getEntityType());
                    officeStaffPayTodo = officeStaffTodoRepository.save(officeStaffPayTodo);
                    Setup.getApplicationContext().getBean(MessagingService.class).notifyPayrollTrustee(officeStaffPayTodo);
                    break;
                default:
                    throw new BusinessException(PublicPageService.errorMessage="action is not correct here");
            }
        } catch (Throwable ex) {
            Logger.getLogger(PublicPageController.class.getName()).log(Level.SEVERE, null, ex);
            throw new BusinessException(!"".equals(PublicPageService.errorMessage) ?
                    PublicPageService.errorMessage : "Link has expired!");
        }

        return ResponseEntity.ok("Success!");
    }

    @NoPermission
    @RequestMapping(value = "/getMoneyTransferCenter/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> getMoneyTransferCenter(@PathVariable("id") PicklistItem country) {
        if(country.getCode().equals("philippines")) {
            return ResponseEntity.ok(
                    Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PHILIPPINE_MONEY_TRANSFER_CENTER));
        }
        if(country.getCode().equals("lebanon")) {
            return ResponseEntity.ok(
                    Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LEBANON_MONEY_TRANSFER_CENTER));
        }
        return ResponseEntity.ok("");
    }
}
