package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.OfficeStaffPayrollPaymentProjection;
import com.magnamedia.entity.projection.v2.OfficeStaffListV2;
import com.magnamedia.entity.projection.v2.OfficeStaffRoster;
import com.magnamedia.entity.projection.SalaryChangeProjection;
import com.magnamedia.entity.projection.v2.OfficeStaffList;
import com.magnamedia.entity.projection.v2.OfficeStaffReport;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.report.RosterTableReport;
import com.magnamedia.report.TableReport;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.OfficeStaffSalaryTransaction;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.OfficeStaffUpdateService;
import com.magnamedia.service.ScheduledMonthlyService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import com.magnamedia.service.payroll.generation.newversion.PayrollRosterApprovalService;
import com.opencsv.CSVWriter;
import com.opencsv.bean.BeanToCsv;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import org.joda.time.LocalDate;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Abbas <<EMAIL>>
 * <AUTHOR> Kanaan <<EMAIL>>
 */
@RequestMapping("/OfficestaffPayroll")
@RestController
public class OfficestaffPayrollController extends BaseRepositoryController<OfficeStaff> {

    @Autowired
    private OfficeStaffRepository staffRep;

    @Autowired
    private PicklistRepository picklistRepository;

    @Autowired
    ProjectionFactory projectionFactory;

    @Autowired
    public PicklistItemRepository itemRep;

    @Autowired
    private YayapotApisHelper apisHelper;

    @Autowired
    private ScheduledMonthlyService scheduledMonthlyService;

    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;
    @Autowired
    private OfficeStaffUpdateService officeStaffUpdateService;

    @Autowired
    private TransferDestinationController transferDestinationController;

    @Autowired
    private OfficeStaffPayrollPaymentServiceV2 payrollPaymentServiceV2;

    @Autowired
    HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    @Autowired
    private AccountantToDoService accountantToDoService;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private BaseControllerHelper baseControllerHelper;

    @Autowired
    private InterModuleConnector interModuleConnector;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private UserRepository userRepository;

    @Override
    public BaseRepository<OfficeStaff> getRepository() {
        return staffRep;
    }

    @Override
    protected ResponseEntity<?> createEntity(OfficeStaff entity) {
        return super.createEntity(entity);
    }

    @NoPermission
    @RequestMapping(value = "/filterOfficeStaff", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> filterOfficeStaff(@RequestBody OfficeStaff staff,
                                               Pageable pageable) {

        String name = (staff.getName() == null || staff.getName().isEmpty()) ? null : staff.getName();
        String moneyReceiverName = staff.moneyReceiverName == null || staff.moneyReceiverName.isEmpty() ? null : staff.moneyReceiverName;

        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("team");
        query.leftJoin("jobTitle");

        if(name != null) {
            String likeExpression;
            if (name.split(" ").length != 2) {
                likeExpression = name.trim();
            } else {
                String[] parts = name.split(" ");
                String firstName = parts[0];
                String lastName = parts[1];
                likeExpression = firstName.trim() + "%" + lastName.trim();
            }
            query.filterBy("name", "LIKE", "%" + likeExpression + "%");
        }

        if(staff.getEmployeeType() != null) {
            query.filterBy("employeeType", "=", staff.getEmployeeType());
        }

        if(staff.getStatus() != null) {
            query.filterBy("status", "=", staff.getStatus());
        }

        if(staff.getTeam() != null) {
            query.filterBy(new SelectFilter("team", "IS NOT NULL", null).and("team.id", "=", staff.getTeam().getId()));
        }

        if(staff.getJobTitle() != null) {
            query.filterBy(new SelectFilter("jobTitle", "IS NOT NULL", null).and("jobTitle.id", "=", staff.getJobTitle().getId()));
        }

        if(staff.getExcludedFromPayroll()) {
            query.filterBy("excludedFromPayroll", "=", true);
            if(staff.getStatus() == null) {
                query.filterBy("status", "=", OfficeStaffStatus.ACTIVE);
            }
        }

        if(moneyReceiverName != null){
            String likeExpression;
            if (moneyReceiverName.split(" ").length != 2) {
                likeExpression = moneyReceiverName.trim();
            } else {
                String[] parts = moneyReceiverName.split(" ");
                String firstName = parts[0];
                String lastName = parts[1];
                likeExpression = firstName.trim() + "%" + lastName.trim();
            }

            query.leftJoin("selectedTransferDestination");
            query.filterBy(new SelectFilter(new SelectFilter(new SelectFilter("selectedTransferDestination", "IS NULL", null).or(new SelectFilter("selectedTransferDestination", "IS NOT NULL", null).and("selectedTransferDestination.selfReceiver", "=", true))).and("name", "LIKE", "%" + likeExpression + "%"))
                    .or(new SelectFilter("selectedTransferDestination", "IS NOT NULL", null).and("selectedTransferDestination.selfReceiver", "=", false).and("selectedTransferDestination.name", "LIKE", "%" + likeExpression + "%")));
        }

        if(staff.getTerminationDateFilter() != null && !staff.getTerminationDateFilter().isEmpty()) {
            switch (staff.getTerminationDateFilter()) {
                case "Equal":
                    if (staff.getTerminationDateFrom() != null) {
                        query.filterBy("terminationDate", ">=", staff.getTerminationDateFrom());
                        query.filterBy("terminationDate", "<", DateUtil.addDays(staff.getTerminationDateFrom(), 1));
                    }
                    break;
                case "Between":
                    if (staff.getTerminationDateFrom() != null)
                        query.filterBy("terminationDate", ">=", staff.getTerminationDateFrom());
                    if (staff.getTerminationDateTo() != null)
                        query.filterBy("terminationDate", "<", DateUtil.addDays(staff.getTerminationDateTo(), 1));
                    break;
                case "Greater":
                    if (staff.getTerminationDateFrom() != null)
                        query.filterBy("terminationDate", ">=", DateUtil.addDays(staff.getTerminationDateFrom(), 1));
                    break;
                case "Lesser":
                    if (staff.getTerminationDateTo() != null)
                        query.filterBy("terminationDate", "<", staff.getTerminationDateTo());
                    break;
            }
        }

        if(staff.getStartDateFilter() != null && !staff.getStartDateFilter().isEmpty()) {
            switch (staff.getStartDateFilter()) {
                case "Equal":
                    if (staff.getStartDateFrom() != null) {
                        query.filterBy("startingDate", ">=", staff.getStartDateFrom());
                        query.filterBy("startingDate", "<", DateUtil.addDays(staff.getStartDateFrom(), 1));
                    }
                    break;
                case "Between":
                    if (staff.getStartDateFrom() != null)
                        query.filterBy("startingDate", ">=", staff.getStartDateFrom());
                    if (staff.getStartDateTo() != null)
                        query.filterBy("startingDate", "<", DateUtil.addDays(staff.getStartDateTo(), 1));
                    break;
                case "Greater":
                    if (staff.getStartDateFrom() != null)
                        query.filterBy("startingDate", ">=", DateUtil.addDays(staff.getStartDateFrom(), 1));
                    break;
                case "Lesser":
                    if (staff.getStartDateTo() != null)
                        query.filterBy("startingDate", "<", staff.getStartDateTo());
                    break;
            }
        }


        User currentUser = CurrentRequest.getUser();

        if(currentUser == null) throw new BusinessException("Unauthorized request. Can't fetch the office staff list.");

        if (!currentUser.hasPosition("payroll_trustee") && !currentUser.hasPosition("hr_manager") && !currentUser.hasPosition("hr_manager_assistant") && !currentUser.hasPosition("office_staff_documents")) {
            List<OfficeStaff> employees = new ArrayList<>();
            OfficeStaff currentOfficeStaff = staffRep.findFirstByUser(currentUser);
            if (messageTemplateService.isManager(currentOfficeStaff)) {
                employees = currentOfficeStaff.getAllLevelEmployees();
            }
            List<Long> employeIds = employees.stream().map(OfficeStaff::getId).collect(Collectors.toList());
            if(employeIds.isEmpty()) {
                return ResponseEntity.ok(employees);
            }

            query.filterBy("id", "in", employeIds);
        }


        return ResponseEntity.ok(project(query.execute(pageable), OfficeStaffList.class));
        /*
        *end
        */

        //Jirra PAY-16
    /*    SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        if (staff.getName() != null) {
            query.filterBy("name", "Like", "%" + staff.getName() + "%");
        }
        if (staff.getStatus() != null) {
            query.filterBy("status", "=", staff.getStatus());
        }
        if (staff.getManager() != null) {
            query.filterBy("manager", "=", staff.getManager());
        }
        //Jirra ACC-785
        if (staff.getCity() != null) {
            query.filterBy("city", "=", staff.getCity());
        }
        if (staff.getBasicSalarySearch() != null) {
            query.filterBy("basicSalary", "=", staff.getBasicSalarySearch());
        }
        if (staff.getStartingDate() != null) {
            query.filterBy("startingDate", "=", staff.getStartingDate());
        }
        if (staff.getPnl() != null) {
            query.filterBy("pnl", "=", staff.getPnl());
        }
        if (staff.getTeam() != null) {
            query.filterBy("team", "=", staff.getTeam());
        }

        return new ResponseEntity<>(query.execute(pageable).map(officestaff -> projectionFactory.createProjection(OfficeStaffJobTitleList.class,
                officestaff)), HttpStatus.OK);*/

    }

    @NoPermission
    @RequestMapping(value = "/listActiveOfficeStaffs", method = RequestMethod.GET)
    public ResponseEntity<?> listActiveOfficeStaffs(@RequestParam(value = "name", required = false) String name,
                                                    Pageable pageable) {

        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);

        if(name != null && !name.equals("")) {
            String likeExpression;
            if (name.split(" ").length != 2) {
                likeExpression = name.trim();
            } else {
                String[] parts = name.split(" ");
                String firstName = parts[0];
                String lastName = parts[1];
                likeExpression = firstName.trim() + "%" + lastName.trim();
            }
            query.filterBy("name", "LIKE", "%" + likeExpression + "%");
        }

        return ResponseEntity.ok(project(query.execute(pageable), OfficeStaffListV2.class));
    }

    @PreAuthorize("hasPermission('OfficestaffPayroll','salaries')")
    @RequestMapping(value = "/salaries/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> salaries(
            @PathVariable("id") OfficeStaff officeStaff,
            @RequestParam(value = "unpaidOnly", defaultValue = "false") boolean unpaidOnly) {

        List<OfficeStaffPayrollPaymentProjection> response = new ArrayList<>();
            List<OfficeStaffPayrollLog> logs;
            if(unpaidOnly)
                logs = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findByOfficeStaffWithinMonthAndTransferredFalse(officeStaff, false);
            else
                logs = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                        .findByOfficeStaffWithinMonth(officeStaff);

            for (OfficeStaffPayrollLog log : logs){
                response.add(baseControllerHelper.project(log, OfficeStaffPayrollPaymentProjection.class));
            }

        Collections.reverse(response);
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasPermission('OfficestaffPayroll','paySalaryToCurrentPayroll')")
    @RequestMapping(value = "/paySalaryToCurrentPayroll/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> paySalaryToCurrentPayroll(
            @PathVariable("id") OfficeStaff officeStaff,
            @RequestBody List<java.sql.Date> dates) {

        try {
            if (dates == null || dates.isEmpty()) return this.okResponse();

            List<java.sql.Date> payrollMonths = new ArrayList<>();
            for(Date date :dates)
                payrollMonths.add(new java.sql.Date(date.getTime()));

            List<OfficeStaffPayrollLog> logs = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findByPayrollMonths(officeStaff, payrollMonths);

            for (OfficeStaffPayrollLog log : logs)
                log.setWillBeIncluded(true);

            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(logs);
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error on paying salaries to current month", false);
        }
        return this.okResponse();
    }

    @PreAuthorize("hasPermission('OfficestaffPayroll','paySalaries')")
    @RequestMapping(value = "/paySalaries/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> paySalaries(
            @PathVariable("id") OfficeStaff officeStaff,
            @RequestParam(value = "selectedDestination", defaultValue = "true") boolean selectedDestination,
            @RequestParam(value = "withAccountantTodo", defaultValue = "true") boolean withAccountantTodo,
            @RequestParam(value = "releaseByCash", defaultValue = "false", required = false) boolean releaseByCash,
            @RequestParam(value = "releaseByCashMoneyReceiverName", defaultValue = "", required = false) String releaseByCashMoneyReceiverName,
            @RequestParam(value = "notes", defaultValue = "", required = false) String notes,
            @RequestBody List<java.sql.Date> dates) {

        try {
            if (dates == null || dates.isEmpty()) return this.okResponse();

            Collections.reverse(dates);

            TransferDestination prevTransferDestination = officeStaff.getSelectedTransferDestination();
            if (!selectedDestination && officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF &&
                    !officeStaff.getTransferDestinations().isEmpty()) {
                int size = officeStaff.getTransferDestinations().size();
                officeStaff.setSelectedTransferDestination(officeStaff.getTransferDestinations().get(size - 1));
            }

            List<OfficeStaffPayrollLog> logs = new ArrayList<>();

            for(java.sql.Date date : dates){

                LocalDate current = new LocalDate(date).withDayOfMonth(1);
                java.sql.Date currentPayrollMonth = new java.sql.Date(current.toDate().getTime());
                    OfficeStaffPayrollLog log = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findTopByOfficeStaffAndPayrollMonthAndForEmployeeLoanFalse(officeStaff, currentPayrollMonth);
                    if(log == null)
                        throw new BusinessException("couldn't find a payroll in " + DateUtil.formatSimpleMonthYear(date));
                    if (log.getTransferred())
                        throw new BusinessException("the payroll of " + DateUtil.formatSimpleMonthYear(date) + " is already paid and transferred");

                    logs.add(log);
            }

            //PAY-903
            if(releaseByCash) {
                Double amount = logs.stream().map(x -> x.getTotalSalary()).collect(Collectors.summingDouble(Double::doubleValue));
                String description = "Money handed to " + releaseByCashMoneyReceiverName + ", and the payroll manager noted the following: " + notes;
                addExpenseRequest(amount, officeStaff, description, CurrentRequest.getUser());
                accountantToDoService.createPaymentToDoForSingleOfficeStaff(accountantToDoService.createMonthlyRuleForSingleOfficeStaff(officeStaff),
                        officeStaff, logs, false);
            } else
                accountantToDoService.createPaymentToDoForSingleOfficeStaff(accountantToDoService.createMonthlyRuleForSingleOfficeStaff(officeStaff),
                        officeStaff, logs, withAccountantTodo);

            officeStaff.setSelectedTransferDestination(prevTransferDestination);
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error on paying salaries", false);
        }
        return this.okResponse();
    }

    public void addExpenseRequest(Double amount, OfficeStaff officeStaff, String description, User creator) {

        Map<String, Object> data = new HashMap<>();
        PicklistItem currency = PicklistHelper.getItem("EXPENSE_CURRENCY", officeStaff.getSalaryCurrency() == null ? "aed" : officeStaff.getSalaryCurrency().toString().toLowerCase());

        final String expenseCode = "SC 11";
        String type = "OFFICE_STAFF";

        data.put("paymentMethod", "CASH");
        data.put("beneficiaryId", officeStaff.getId());
        data.put("amount", amount);
        data.put("beneficiaryType", type);
        data.put("beneficiaryName", officeStaff.getName());
        data.put("currency", new HashMap<String, Long>() {{ put("id", currency.getId()); }});
        data.put("expense", new HashMap<String, String>() {{ put("code", expenseCode); }});
        data.put("expenseRequestType", "NEW_REQUEST");
        data.put("invoiceNumber", null);
        data.put("notes", description);
        data.put("description", description);

        if (creator == null) {
            creator = Setup.getRepository(UserRepository.class).findAdmin();
        }
        User finalUser = creator;
        data.put("requestedBy", new HashMap<String, Long>() {{
            put("id", finalUser.getId());
        }});

        interModuleConnector.postJsonAsync("/accounting/expenseRequestTodo/create", data);
    }



    @Autowired
    LockDateService lockDateService;

    public void decrementMonthlyRuleDates(OfficeStaff officeStaff, MonthlyPaymentRule rule) {
        rule.setPayrollMonth(new java.sql.Date(new LocalDate(rule.getPayrollMonth()).minusMonths(1).toDate().getTime()));
        if(officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            rule.setLockDate(lockDateService.getLockDate(rule.getPayrollMonth(), 0, PaymentRuleEmployeeType.OVERSEAS));
        } else if (officeStaff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EXPAT) {
            rule.setLockDate(lockDateService.getLockDate(rule.getPayrollMonth(), 0, PaymentRuleEmployeeType.EXPATS));
        } else {
            rule.setLockDate(lockDateService.getLockDate(rule.getPayrollMonth(), 0, PaymentRuleEmployeeType.EMIRATI));
        }
    }

    @PreAuthorize("hasPermission('OfficestaffPayroll','getsalarychanges')")
    @RequestMapping(value = "/getsalarychanges/{id}")
    public ResponseEntity<?> getSalaryChanges(@PathVariable Long id) {

        HistorySelectQuery<OfficeStaff> historySelectQuery = new HistorySelectQuery(OfficeStaff.class);
        historySelectQuery.filterBy("id", "=", id);
        historySelectQuery.filterByChanged("salary");
        historySelectQuery.sortBy("lastModificationDate", false, true);
        return new ResponseEntity<>(project(historySelectQuery.execute(), SalaryChangeProjection.class), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('OfficestaffPayroll','canaddtickettype')")
    @RequestMapping(value = "/canaddtickettype/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> canAddTicketType(
            @PathVariable("id") OfficeStaff officeStaff,
            @RequestParam(required = true, value = "employeeType") OfficeStaffType employeeType){
        return new ResponseEntity<>(officeStaff.getCanAddTicketType(employeeType), HttpStatus.OK);
    }
    
    @NoPermission
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    @Override
    public ResponseEntity<?> update(
            @RequestBody ObjectNode objectNode) throws IOException {

        if (checkPermission("update")) {


            OfficeStaff updated = parse(objectNode);

            OfficeStaff origin = getRepository().findOne(updated.getId());

            // Normalize phone numbers
            if (updated.getPhoneNumber() != null && !updated.getPhoneNumber().isEmpty()) {
                String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(updated.getPhoneNumber());
                if (!normalizedPhoneNumber.equals(updated.getPhoneNumber())) {
                    updated.setPhoneNumber(normalizedPhoneNumber);
                }
            }

            if (updated.getEmergencyContactPhoneNumber() != null && !updated.getEmergencyContactPhoneNumber().isEmpty()) {
                String normalizedEmergencyNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(updated.getEmergencyContactPhoneNumber());
                if (!normalizedEmergencyNumber.equals(updated.getEmergencyContactPhoneNumber())) {
                    updated.setEmergencyContactPhoneNumber(normalizedEmergencyNumber);
                }
            }

            if (origin != null && OfficeStaffStatus.ACTIVE.equals(origin.getStatus()) &&
                    (OfficeStaffType.DUBAI_STAFF_EMARATI.equals(origin.getEmployeeType()) ||
                            OfficeStaffType.DUBAI_STAFF_EXPAT.equals(origin.getEmployeeType()))) {
                duplicateIBanDetection(origin, updated.getSelectedTransferDestination());
            }

            if (updated.getAirfareTicketType() != null)

                throw new BusinessException("can't add ticket type to staff");


            LocalDate dt = new LocalDate();
            String lockPayrollEnabled = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_ENABLED);
            Integer lockPayrollStart = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_START));
            Integer lockPayrollEnd = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_END));

            Boolean salaryChanged = true;
            if (origin.getBasicSalary() == null && updated.getBasicSalary() == null)
                salaryChanged = false;
            else if (origin.getBasicSalary() != null && origin.getBasicSalary().equals(updated.getBasicSalary()))
                salaryChanged = false;

            if ((!salaryChanged) || (!lockPayrollEnabled.equals("1"))
                    || (dt.getDayOfMonth() < lockPayrollStart && dt.getDayOfMonth() > lockPayrollEnd)) {

               //officeStaffUpdateService.checkBankDetailsAndSendAnsariEmail(origin, updated, objectNode);
                officeStaffUpdateService.checkSalaryAndSendApproveMessages(origin, updated, objectNode);

                // new transfer destination add it to transfer destinations
                TransferDestination destination = updated.getSelectedTransferDestination();
                if (destination != null){

                    // Create ChangeBankInfoHistoryLog record when new TransferDestination is selected
                    if(origin.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI) || origin.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT)){

                        TransferDestination newDestination = updated.getSelectedTransferDestination();
                        TransferDestination oldDestination = origin.getSelectedTransferDestination();

                        // Create history log only if there are actual changes in the selectedTransferDestination
                        if (oldDestination == null || !oldDestination.equals(newDestination)) {
                            newDestination = Setup.getRepository(TransferDestinationRepository.class).findOne(newDestination.getId());

                            ChangeBankInfoHistoryLog bankHistoryLog = new ChangeBankInfoHistoryLog(
                                    origin,
                                    newDestination.getReceiveMoneyMethod(),
                                    origin.getVisaNewRequest().getEmployeeAccountWithAgent(),
                                    (newDestination.getReceiveMoneyMethod() != null && newDestination.getReceiveMoneyMethod().equals(ReceiveMoneyMethod.BANK_TRANSFER)) ? newDestination.getIban() : "DXB-LULU BARSHA",
                                    newDestination.getBankName(),
                                    BankInfoChangeStatus.PENDING
                            );

                            bankHistoryLog = Setup.getRepository(ChangeBankInfoHistoryLogRepository.class).save(bankHistoryLog);
                        }
                    }

                    if(destination.getId() == null) {

                        if (!destination.getSelfReceiver()
                                && destination.getPhoneNumber() != null && !destination.getPhoneNumber().isEmpty()) {
                            String normalizedPhoneNumber = PhoneNumberUtil.normalizeInternationalPhoneNumber(destination.getPhoneNumber());

                            if (!normalizedPhoneNumber.equals(destination.getPhoneNumber())) {
                                destination.setPhoneNumber(normalizedPhoneNumber);
                            }
                        }

                        destination.setOfficeStaff(origin);
                        Setup.getRepository(TransferDestinationRepository.class)
                                .save(destination);
                        origin.getTransferDestinations().add(destination);
                    } else {
                        TransferDestination originalDestination = Setup.getRepository(TransferDestinationRepository.class)
                                .findOne(destination.getId());

                        origin.setSelectedTransferDestination(originalDestination);
                    }
                }


                if (objectNode.has("transferDestinations")) objectNode.remove("transferDestinations");
                if (objectNode.has("selectedTransferDestination")) objectNode.remove("selectedTransferDestination");
                update(origin, updated, objectNode);

                return super.updateEntity(origin);
            } else {
                return new ResponseEntity<>("Officestaff Salary Could not be updated because payroll is locked.", HttpStatus.BAD_REQUEST);
            }
        } else {
            return unauthorizedReponse();
        }
    }

    public void duplicateIBanDetection(OfficeStaff officeStaff, TransferDestination selectedTransferDestination) {
        if (officeStaff != null
                && selectedTransferDestination != null
                && selectedTransferDestination.getIban() != null) {
            List<OfficeStaff> staffs = Setup.getRepository(OfficeStaffRepository.class)
                    .findDuplicateIbanOfficeStaff(selectedTransferDestination.getIban(), officeStaff.getId(), OfficeStaffStatus.ACTIVE, Arrays.asList(OfficeStaffType.DUBAI_STAFF_EMARATI, OfficeStaffType.DUBAI_STAFF_EXPAT));
            if (staffs != null && !staffs.isEmpty()) {
                OfficeStaff existStaff = staffs.get(0);
                if (existStaff != null) {
                    Setup.getApplicationContext().getBean(OfficeStaffUpdateService.class).sendDuplicateIBanEmail(officeStaff, existStaff);

                    throw new BusinessException("Duplicate IBAN detected. This IBAN is already assigned to another employee. Please enter a unique IBAN.");
                }
            }
        }
    }

    //Jirra ACC-325
    public static void sendMail(File file, String mailTitle) throws FileNotFoundException, IOException {

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_PAYROLL_EMAILS);
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
        TextEmail mail = new TextEmail(mailTitle, "please find attached files");
        mail.addAttachement(file);
        Setup.getMailService().sendEmail(recipients, mail, null);
    }

    public File generateCSVFromData(List<OfficeStaffPayrollBean> data, String fileName, String type) throws FileNotFoundException {
        CSVWriter csvWriter = null;
        File file = Paths.get(System.getProperty("java.io.tmpdir"), fileName).toFile();

        try {

            csvWriter = new CSVWriter(new FileWriter(file));

            BeanToCsv bc = new BeanToCsv();

            ColumnPositionMappingStrategy mappingStrategy
                    = new ColumnPositionMappingStrategy();
            mappingStrategy.setType(OfficeStaffPayrollBean.class);
            String[] columns = null;
            if (type.equals("SYR"))//SYR_LEBANON_STAFF
            {

                columns = new String[]{"indexV", "officeStaffName", "city", "remainingLoanBalance", "loanRepaymentV",
                        "startDateDeductionV", "complaintsDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "internetAllowanceV",
                        "managerAdditionV", "extraShiftsV", "paymentMethod", "balanceV", "currencyUsed"};

            } else if (type.equals("UAE"))//UAE_NON_H_C ,PT_OFFICE_STAFF,STORAGE_STAFF
            {
                columns = new String[]{"indexV", "employeeUniqueId", "agentId", "employeeAccountWithAgent", "officeStaffName", "city", "pLcode", "remainingLoanBalance", "loanRepaymentV",
                        "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "internetAllowanceV", "housing", "transportation", "managerAdditionV", "extraShiftsV", "paymentMethod", "balanceV", "currencyUsed"};
            } else if (type.equals("FT_FILIPINO"))//FT_PHILIPPINES_STAFF
            {
                columns = new String[]{"indexV", "officeStaffName", "city", "pLcode", "remainingLoanBalance", "loanRepaymentV",
                        "startDateDeductionV", "penaltyDeductionV", "finalSettelmentDeduciontV", "managerDeductionV", "basicSalaryV", "officeStaffPaidOffDaysAmount", "officeStaffTicketValue", "housing", "transportation", "managerAdditionV", "extraShiftsV", "paymentMethod", "balanceV", "currencyUsed"};
            }
            //Setting the colums for mappingStrategy
            mappingStrategy.setColumnMapping(columns);
            //Writing empList to csv file
            bc.write(mappingStrategy, csvWriter, data);

            System.out.println("CSV File written successfully!!!");

        } catch (Exception ee) {
            ee.printStackTrace();
        } finally {
            try {
                //closing the writer
                csvWriter.close();
            } catch (Exception ee) {
                ee.printStackTrace();
            }
        }
        //Jirra ACC-325
        return file;
        //return new FileInputStream(file);
    }

    enum RosterFormat {HTML, CSV, PDF}

    @NoPermission
    @RequestMapping("/payrollRoster/{filter}/{format}")
    public ResponseEntity<?> payrollRoster(HttpServletResponse response,
                                           @PathVariable("filter") OfficeStaffStatus filter,
                                           @PathVariable(value = "format", required = false) RosterFormat format) throws Exception {
        if (format == null)
            format = RosterFormat.HTML;
        List<OfficeStaff> result = null;
        switch (filter) {
            case TERMINATED:
                result = staffRep.findTerminatedOfficeStaffOrdered();
                break;
            case ACTIVE:
                result = staffRep.findActiveOfficeStaffOrdered();
                break;
        }
        if (result == null)
            throw new BusinessException("Invalid filter: " + filter.name());
        String title = String.format("Payroll Roster %s", DateUtil.formatFullDate(new Date()));
        if (format.equals(RosterFormat.CSV)) {
            String[] headers = new String[]{"name", "city", "manager", "departments", "companySalaryAed", "companySalaryUsd"};
            OfficeStaffRoster.Counter.resetCounter();
            try (InputStream is = generateCsv(result, OfficeStaffRoster.class, headers)) {

                createDownloadResponse(response,
                        title + ".csv",
                        is);
            }
        }
        String[] headers = new String[]{"Employee Name", "City", "Manager", "Departments", "Company Salary(AED)", "Company Salary(USD)"};
        List<String[]> values = result.stream().map(c -> new String[]
                {

                        c.getName(),
                        c.getCity() == null ? "N\\A" : c.getCityName(), //c.getCity().getName(),
                        c.getManager() == null ? "N\\A" : c.getManager().getName(),
                        c.getDepartments() == null ? "N\\A" : c.getDepartmentNames(),
                        c.getBasicSalary() == null ? "-" : c.getSalaryCurrency() == SalaryCurrency.AED ? String.format("%,.2f", c.getBasicSalary()) : "-",
                        c.getBasicSalary() == null ? "-" : c.getSalaryCurrency() == SalaryCurrency.USD ? String.format("%,.2f", c.getBasicSalary()) : "-"
                }).collect(Collectors.toList());
        TableReport tableReport = new TableReport(title, filter.name(), values, headers);
        if (format == RosterFormat.HTML) {
            return new ResponseEntity<>(tableReport.render(), HttpStatus.OK);
        }
        if (format == RosterFormat.PDF) {
            response.setContentType(getMimeType("pdf"));
            response.addHeader("Content-Disposition",
                    "attachment; filename=\"" + title + ".pdf\"");
            tableReport.exportPdf(response.getOutputStream());
            response.flushBuffer();
        }
        return null;

    }

    @Autowired
    private PayrollRosterApprovalService payrollRosterApprovalService;

    @NoPermission
    @RequestMapping("/exportPayrollRoster")
    public void exportPayrollRoster(HttpServletResponse response) {
        User currentUser = CurrentRequest.getUser();
        if (!currentUser.hasPosition("payroll_trustee")) {
            throw new BusinessException("You don't have the permission to export the payroll roster!");
        }

        java.sql.Date payrollMonth = new java.sql.Date(new LocalDate().withDayOfMonth(1).toDate().getTime());
        Attachment attachment = payrollRosterApprovalService.generateOfficeStaffDetailedPayrollFile(payrollRosterApprovalService.getAllEmployees(payrollMonth), " for All", null);

        this.createDownloadResponse(response, "Payroll roster of " + DateUtil.formatSimpleMonthYear(payrollMonth) + ".xlsx", Storage.getStream(attachment));
    }
    

    @NoPermission
    @RequestMapping("/payrollRoster2/{filter}/{format}")
    public ResponseEntity<?> payrollRoster2(
            HttpServletResponse response,
            @PathVariable("filter") OfficeStaffStatus filter,
            @PathVariable(value = "format", required = false) RosterFormat format,
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) throws Exception {

        User currentUser = CurrentRequest.getUser();

        if (currentUser == null) throw new BusinessException("Unauthorized request. Can't fetch the office staff list.");

        if (!currentUser.hasPosition("payroll_trustee")) {
            throw new BusinessException("You don't have the permission to export the payroll roster!");
        }

        if (format == null)
            format = RosterFormat.HTML;
        List<OfficeStaffRoster> result = null;
        RosterTableReport tableReport = null;
        String title = "Payroll Roster - " + DateUtil.formatFullDate(new Date());
        title += " - " + filter.toString();
        //Jirra ACC-279
        String[] headers = new String[]{
                "Employee Name", "City", "Manager", "Team", "Company Salary(AED)", "Company Salary(USD)"};
        switch (filter) {
            case TERMINATED:
                if (date != null)
                    result = staffRep.findByStatusAndTerminationDateAfterOrderByTerminationDate(filter, date);
                else
                    result = staffRep.findByStatusOrderByTerminationDate(filter);

                if (result == null)
                    throw new BusinessException("Invalid filter: " + filter.name());
                List<String[]> values = mapOfficeStaff(result);

                tableReport = new RosterTableReport(filter, title, values, headers);
                break;
            case ACTIVE:
                result = staffRep.findByStatusOrderByManager_Name(filter);

                if (result == null)
                    throw new BusinessException("Invalid filter: " + filter.name());
                List<String[]> values2 = mapOfficeStaff(result);

                //Jirra 1223
                LocalDate dt;
                if (date != null) {
                    dt = new LocalDate(date);
                } else {
                    dt = new LocalDate();
                }
                java.sql.Date payrollMonth = new java.sql.Date(dt.withDayOfMonth(1).toDate().getTime());


                MonthlyPaymentRule rule = new MonthlyPaymentRule();
                rule.setPayrollMonth(payrollMonth);
                rule.setEmployeeTypeList(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS));
                rule.setMolType(AbstractPaymentRule.MolType.WITH_AND_WITHOUT);
                rule.setHousemaidStatusList(Arrays.asList(PaymentRuleMaidStatus.IN_ACCOMMODATION, PaymentRuleMaidStatus.WITH_CLIENT));
                rule.setHousemaidTypeList(Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC));
                rule.setPayrollType(PayrollType.PRIMARY);
//
//                LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
//                LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt);
//                List<Housemaid> targetHousemaids = PayrollGenerationLibrary.getPayrollHousemaids(payrollStart, payrollEnd);//getTargetHousemaids();

                List<Housemaid> targetHousemaids = housemaidPayrollPaymentServiceV2.getTargetList(rule);

                List<String[]> totals = new ArrayList<>();
                totals.add(new String[]
                        {"",
                                "Housemaids (" + targetHousemaids.size() + ")",
                                "UAE",
                                "Jad",
                                "",
                                "" + targetHousemaids != null ?
                                        "" + (targetHousemaids.stream()
                                                .mapToDouble(x -> x.getBasicSalary() != null ?
                                                        x.getBasicSalary() : 0.0).sum()) :
                                        "0.0",
                                ""});

                String url = Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.SALESFORCE_APIS_BASE_URL)
                        + Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.SALESFORCE_APIS_CLEANERS_SALARIES);

                Map<String, Object> mappedResult = new HashMap<>();
                ObjectMapper mapper = new ObjectMapper();
                TypeReference<Map<String, Object>> typeRef
                        = new TypeReference<Map<String, Object>>() {
                };

                try {
                    //UAE
                    String uaeUrl = url + "?type=uae&date=" + DateUtil.formatDateSlashedV2(new Date());
                    String s = apisHelper.getRequest(uaeUrl);
                    mappedResult = mapper.readValue(s, typeRef);
                    if (mappedResult.get("status").toString().equals("success")) {
                        totals.add(new String[]
                                {"",
                                        "UAE Cleaners (" + mappedResult.get("totalnumber").toString() + ")",
                                        "UAE",
                                        "Jad",
                                        "",
                                        "" + (Double) mappedResult.get("totalSalaries"),
                                        ""});

                    } else {
                        totals.add(new String[]
                                {"",
                                        "UAE Cleaners (API " + mappedResult.get("status").toString() + ")",
                                        "UAE",
                                        "Jad",
                                        "",
                                        "",
                                        ""});
                    }

                    //QTR
                    String qtrUrl = url + "?type=qtr&date=" + DateUtil.formatDateSlashedV2(new Date());
                    s = apisHelper.getRequest(qtrUrl);
                    mappedResult = mapper.readValue(s, typeRef);
                    if (mappedResult.get("status").toString().equals("success")) {
                        totals.add(new String[]
                                {"",
                                        "Qatar Cleaners (" + mappedResult.get("totalnumber").toString() + ")",
                                        "Qatar",
                                        "Jad",
                                        "",
                                        "" + (Double) mappedResult.get("totalSalaries"),
                                        ""});

                    } else {
                        totals.add(new String[]
                                {"",
                                        "Cleaners (API " + mappedResult.get("status").toString() + ")",
                                        "Qatar",
                                        "Jad",
                                        "",
                                        "",
                                        ""});
                    }
                } catch (Exception ex) {
                    totals.add(new String[]
                            {"",
                                    "UAE Cleaners (API Failed)",
                                    "UAE",
                                    "Jad",
                                    "",
                                    "",
                                    ""});
                    totals.add(new String[]
                            {"",
                                    "Cleaners (API Failed)",
                                    "Qatar",
                                    "Jad",
                                    "",
                                    "",
                                    ""});
                }
                tableReport = new RosterTableReport(filter, title, values2, totals, headers);
                break;
        }

        if (format.equals(RosterFormat.CSV)) {
            String[] csvHeaders = new String[]{"name", "city", "manager", "team", "companySalaryAed", "companySalaryUsd"};
            OfficeStaffRoster.Counter.resetCounter();
            try (InputStream is = generateCsv(result, OfficeStaffRoster.class, csvHeaders)) {

                createDownloadResponse(response,
                        title + ".csv",
                        is);
            }
        }

        if (format == RosterFormat.HTML) {
            if (tableReport != null)
                return new ResponseEntity<>(tableReport.render(), HttpStatus.OK);
        }
        if (format == RosterFormat.PDF) {
            if (tableReport != null) {
                response.setContentType(getMimeType("pdf"));
                response.addHeader("Content-Disposition",
                        "attachment; filename=\"" + title + ".pdf\"");
                tableReport.exportPdf(response.getOutputStream());
                response.flushBuffer();
            }
        }
        return null;

    }

    private List<String[]> mapOfficeStaff(List<OfficeStaffRoster> result) {
        //Jirra ACC-279
        return result.stream().map(c -> new String[]
                {
                        c.getTerminationDate() != null ? DateUtil.formatFullDate(c.getTerminationDate()) : "",
                        c.getName(),
                        c.getCity() == null ? "N\\A" :  c.getCity(), //c.getCity().getName(),
                        c.getManager() == null ? "N\\A" : c.getManager(),
                        c.getTeam() == null ? "N\\A" : c.getTeam(),
                        c.getCompanySalaryAed() > 0d ? String.format("%,.2f", c.getCompanySalaryAed()) : "-",
                        c.getCompanySalaryUsd() > 0d ? String.format("%,.2f", c.getCompanySalaryUsd()) : "-"
                }).collect(Collectors.toList());
    }

    //ACC-1223
//    public List<Housemaid> getTargetHousemaids() {
//
//        List<Housemaid> maids;
//        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
//        query.filterBy(
//                new SelectFilter()
//                        .or("status", "=", HousemaidStatus.WITH_CLIENT)
//                        .or("status", "=", HousemaidStatus.VIP_RESERVATIONS)
//                        .or("status", "=", HousemaidStatus.AVAILABLE)
//                        .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
//                        .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
//                        .or("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
//                        .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
//                        .or("status", "=", HousemaidStatus.ON_VACATION)
//                        .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
//                        .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
//                        .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
//        );
//        query.filterBy(
//                new SelectFilter()
//                        .or("excludedFromPayroll", "=", false)
//                        .or("excludedFromPayroll", "IS NULL", null)
//        );
//        maids = query.execute();
//        return maids;
//    }

    /**
     * @param response
     * @param manager
     * @param type
     * @param date
     * @param isSentByEmail Check manger existence
     *                      Payroll start and end date
     *                      Get Tag name for staff with local nationalities
     *                      Get target staff
     *                      Calculate staff Salary and set their beans fields values
     *                      Return office staff payroll file
     */
    //Jirra ACC-677
    @PreAuthorize("hasPermission('OfficestaffPayroll','generatePayrollOfCurrentMonth')")
    @RequestMapping("/generatePayrollOfCurrentMonth")
    @Transactional
    public void generatePayrollOfCurrentMonthV2(
            HttpServletResponse response,
            @RequestParam(value = "manager", required = false) Long manager,
            @RequestParam(value = "type") String type,
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = false, value = "sentByEmail") boolean isSentByEmail) throws FileNotFoundException, IOException, URISyntaxException {

        PicklistItem employeeType = null;
        if (manager != null) {
            employeeType = itemRep.findOne(manager);
            if (employeeType == null) {
                throw new BusinessException(
                        "please check the passed manager name, possible values are: "
                                + "rhiz, adeeb, malek, jad, riad_sonbo, rawan , "
                                + "sam, meredith, q-riad, samer_dofesh, samer_d");
            }
        }

        //Payroll start and end date
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        LocalDate payrollStart =
                PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd =
                PayrollGenerationLibrary.getPayrollEndDate(dt);

        String localStaffTagName = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_lOCAL_STAFF_NATIONALITY_TAG);

        List<OfficeStaff> staffs =
                getTargetOfficeStaff(manager, employeeType, type, localStaffTagName, payrollEnd)
                        .stream()
//                        .filter(x->!checkVacationForOfficeStaff(payrollStart, payrollEnd, x))
                        .collect(Collectors.toList());

        int counter = 0;
        Double totalAnsari = 0.0;
        //PicklistItem vacationType = getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE, "pre-paid_vacation");
        List<OfficeStaffPayrollBean> result = new ArrayList<>();

        //Jirra ACC-1085
        // START COMMENTED PAY-2
//        Integer jobStartDate =
//                Integer.parseInt(
//                        Setup.getParameter(Setup.getCurrentModule(),
//                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_START));
//        Integer jobEndDate =
//                Integer.parseInt(
//                        Setup.getParameter(Setup.getCurrentModule(),
//                                PayrollManagementModule.PARAMETER_PAYROLL_JOBS_END));
//
//        LocalDate todayDT = new LocalDate();
//        if ((todayDT.getDayOfMonth() >= jobStartDate && todayDT.getMonthOfYear() == dt.getMonthOfYear())
//                || (todayDT.getDayOfMonth() <= jobEndDate && todayDT.getMonthOfYear() == dt.getMonthOfYear() + 1)) {
//            scheduledMonthlyService.officeStaffRepayments(staffs, dt.withDayOfMonth(1));
//        }
        // END COMMENTED PAY-2

        for (OfficeStaff staff : staffs) {
            // START COMMENTED PAY-2
//            if (staff.getStatus().equals(OfficeStaffStatus.TERMINATED) && staff.getTerminationDate() == null) {
//                continue;
//            }
//            if (staff.getStatus().equals(OfficeStaffStatus.TERMINATED)) {
//                if (staff.getTerminationDate() != null) {
//                    LocalDate termination = new LocalDate(staff.getTerminationDate());
//                    if (termination.isBefore(payrollEnd.withDayOfMonth(1))) {
//                        continue;
//                    }
//                }
//
//            }
            // END COMMENTED PAY-2

//            boolean flag = false;
//            List<ScheduledAnnualVacation> annualVacations = staff.getScheduledAnnualVacations();
//            if (annualVacations != null) {
//
//                for (ScheduledAnnualVacation vacation : annualVacations) {
//                    LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                    if ((payrollDueDate.equals(payrollStart.minusMonths(1)) || payrollDueDate.isAfter(payrollStart.minusMonths(1)))
//                            && (payrollDueDate.equals(payrollEnd.minusMonths(1)) || payrollDueDate.isBefore(payrollEnd.minusMonths(1)))
//                            && vacation.getType().getCode().equals(vacationType.getCode()) && vacation.getAmount() > 0.0) {
//                        flag = true;
//                        break;
//                    }
//                }
//            }
//            if (!flag){

            OfficeStaffPayrollBean bean =
                    setOfficeStaffPayrollBeanValues(
                            null, staff, payrollStart, payrollEnd, payrollEnd.withDayOfMonth(1));
            bean = setOfficeStaffInfo(bean, staff, localStaffTagName, dt);
            //index
            bean.setIndexNum(++counter);
            result.add(bean);
            totalAnsari += bean.getBalance() != null ? bean.getBalance() : 0;
//            }
        }

        // START COMMENTED PAY-2
//        //Jirra ACC-427
//        String fileName = "";
//        if (employeeType != null)
//            fileName += employeeType.getName() + " ";
//        //Jirra ACC-431
//        if (type.contains("LOCAL")) {
//            fileName += "Local - ";
//            type = type.replace("LOCAL_", "");
//        }
        String fileName = "";
        if (!type.equals("All")) {
            fileName += OfficeStaffType.valueOf(type).getLabel() + " ";
        } else {
            fileName = "All Staff - ";
        }
        fileName += "Payroll of " + DateUtil.formatSimpleMonth(payrollEnd.toDate());

        //Jirra ACC-479
        PayrollGenerationLibrary.generateOfficeStaffPayrollFile(
                response, fileName, result, totalAnsari, isSentByEmail, type);
    }

    public static OfficeStaffPayrollBean setOfficeStaffPayrollBeanValues(
            OfficeStaffPayrollBean bean,
            OfficeStaff staff,
            LocalDate payrollStart,
            LocalDate payrollEnd,
            LocalDate payrollMonth) {

        if (bean == null)
            bean = new OfficeStaffPayrollBean();
        Reflections reflections =
                new Reflections("com.magnamedia.salarycalculation.officestaff");
        Set<Class<? extends OfficeStaffSalaryTransaction>> allClasses =
                reflections.getSubTypesOf(OfficeStaffSalaryTransaction.class);

        for (Class<?> clazz : allClasses) {
            try {
                Method calculateMethod =
                        clazz.getMethod(
                                "calculate",
                                OfficeStaff.class,
                                LocalDate.class,
                                LocalDate.class,
                                LocalDate.class);

                Method setInSalaryObjectMethod =
                        clazz.getMethod(
                                "setInSalaryObject",
                                OfficeStaffPayrollBean.class,
                                Double.class);
                OfficeStaffSalaryTransaction t = (OfficeStaffSalaryTransaction) clazz.newInstance();
                bean = (OfficeStaffPayrollBean) setInSalaryObjectMethod.invoke(t, bean, (Double) calculateMethod.invoke(t, staff, payrollStart, payrollEnd, payrollMonth));
            } catch (NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
                Logger.getLogger(OfficestaffPayrollController.class.getName()).log(Level.SEVERE, null, ex);
            } catch (InstantiationException | IllegalAccessException ex) {
                Logger.getLogger(OfficeStaffPayrollBean.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

        if(payrollStart != null)
        bean.setPayStartDate(new java.sql.Date(payrollStart.toDate().getTime()));
        bean.setPayEndDate(new java.sql.Date(payrollEnd.toDate().getTime()));
        return bean;
    }

    /**
     * Set OfficeStaff information:
     * name, team, city, local nationality, payment method, salary currency,
     * employee unique id, agent id, employee account with agent, pLcode
     * Return OfficeStaffPayrollBean
     *
     * @param bean
     * @param staff
     * @param localStaffTagName
     * @param dt
     * @return
     */
    public static OfficeStaffPayrollBean setOfficeStaffInfo(
            OfficeStaffPayrollBean bean,
            OfficeStaff staff,
            String localStaffTagName,
            LocalDate dt) {

        //Staff name
        bean.setOfficeStaffName(staff.getName());
        bean.setTeam(staff.getTeam() != null ? staff.getTeam().getName() : "");
        bean.setDepartments((staff.getDepartments() != null && !staff.getDepartments().isEmpty()) ? staff.getDepartmentNames() : ""); //todo: check with Team leader if needed

        //staff city
        if (staff.getCity() != null) {
            bean.setCity(staff.getCityName());//.getName());
        }

        //staff mail
        if (staff.getEmail() != null && !staff.getEmail().trim().equals("")) {
            bean.setOfficeStaffEMail(staff.getEmail());
        }

        //Jirra ACC-431
        if (staff.getNationality() != null
                && !localStaffTagName.isEmpty()
                && staff.getNationality().hasTag(localStaffTagName))
            bean.setLocal("Yes");

        //Payment Method
        if (staff.getSalaryPayementMethod() != null) {
            bean.setPaymentMethod(staff.getSalaryPayementMethod().getName());
        }
        if (staff.getSalaryCurrency() != null) {
            bean.setCurrencyUsed(staff.getSalaryCurrency().toString());
        }

        //visa info for UAE_NON_H_C ,PT_OFFICE_STAFF,STORAGE_STAFF
        NewRequest visaNewRequest = staff.getVisaNewRequest();
        if (visaNewRequest != null) {
            if (visaNewRequest.getEmployeeUniqueId() != null) {
                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
            }
            if (visaNewRequest.getAgentId() != null) {
                bean.setAgentId(visaNewRequest.getAgentId());
            }
            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
            }
        }

        if (staff.getPnl() != null) {
            bean.setpLcode(staff.getPnl());
        }

        if (staff.getStartingDate() != null) {
            LocalDate da = new LocalDate(staff.getStartingDate());
            bean.setStartDate(DateUtil.formatFullDate(da.toDate()));
        }

        //Jirra ACC-1066
        if (staff.getStatus() != null) {
            bean.setStatus(staff.getStatus().toString());
        }

        //Jirra ACC-353
        //PAY-2
        //bean.setMohoreSalary(staff.getPrimarySalary());
        bean.setMohoreSalary(staff.getBasicSalary());

        //PAY-2
        //bean.setMonthlyCashAdvance(staff.getMonthlyLoan());
        bean.setMonthlyCashAdvance(0.0);
        bean.setPaySlipMonth(DateUtil.formatSimpleMonthYear(dt.toDate()));
        return bean;
    }

    /**
     * Build OfficeStaff query
     * Left join with manager
     * Set query filters: status, excludedFromPayroll, manager, local nationalities, type
     * Return query result list
     */
    private List<OfficeStaff> getTargetOfficeStaff(
            Long manager,
            PicklistItem employeeType,
            String type,
            String localStaffTagName,
            LocalDate payrollEnd) {
        //Get Office Staff
        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        //query.filterBy("id", "=", 358L);
        query.leftJoin("manager");
        query.leftJoin("finalSettlement");

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null);

        // Active and not excluded from payroll
        SelectFilter activeAndNotExcludedFromPayroll = new SelectFilter("status", "=", OfficeStaffStatus.ACTIVE)
                .and(notExcludedFromPayroll);

        // Terminated within payroll
        SelectFilter terminatedWithinPayrollDate = new SelectFilter("terminationDate", "IS NOT NULL", null)
                .and("terminationDate", ">=", payrollEnd.withDayOfMonth(1).toDate())
                .and("terminationDate", "<=", payrollEnd.dayOfMonth().withMaximumValue().toDate());


        // Terminated within payroll date and not excluded form payroll (Old case)
        SelectFilter terminatedWithinPayrollNotExcluded =   new SelectFilter("status", "=", OfficeStaffStatus.TERMINATED)
                .and("finalSettlement", "IS NULL", null)
                .and(terminatedWithinPayrollDate)
                .and(notExcludedFromPayroll);

        // Terminated within payroll date and Final settlement is approved before lock date (New case)
        SelectFilter terminatedWithinPayrollFinalSettlementApproved =   new SelectFilter("finalSettlement", "IS NOT NULL", null)
                .and("finalSettlement.approvedByCFO", "=", true)
                .and("finalSettlement.includeInPayroll", "=", true)
                .and(terminatedWithinPayrollDate);

        query.filterBy(new SelectFilter(activeAndNotExcludedFromPayroll).or(terminatedWithinPayrollNotExcluded)
        .or(terminatedWithinPayrollFinalSettlementApproved));
        // START COMMENTED PAY-2
//        query.filterBy(
//                new SelectFilter()
//                        .or("status", "=", OfficeStaffStatus.ACTIVE)
//                        .or(new SelectFilter("terminationDate", ">=", payrollEnd.withDayOfMonth(1).toDate())
//                                .and(new SelectFilter("status", "=", OfficeStaffStatus.TERMINATED)))
//        );
//        query.filterBy(
//                new SelectFilter()
//                        .or("excludedFromPayroll", "=", false)
//                        .or("excludedFromPayroll", "IS NULL", null)
//        );
        if (manager != null) {
            query.filterBy("manager.id", "=", employeeType.getId());
        }

        // START COMMENTED PAY-2
//        if (type.equalsIgnoreCase("LOCAL_UAE_NON_H_C")
//                || type.equalsIgnoreCase("UAE_NON_H_C")) {
//
//            List<PicklistItem> localNationalitiesItems =
//                    picklistRepository.findByCode(Picklist.NATIONALITIES)
//                            .getItemsWithTag(localStaffTagName);
//            if (type.equalsIgnoreCase("LOCAL_UAE_NON_H_C") && localNationalitiesItems.isEmpty())
//                throw new RuntimeException("There is no local nationalities.");
//            else if (!localNationalitiesItems.isEmpty()) {
//                if (type.equalsIgnoreCase("LOCAL_UAE_NON_H_C"))
//                    query.filterBy("nationality", "in", localNationalitiesItems);
//                else if (type.equalsIgnoreCase("UAE_NON_H_C")) {
//                    query.filterBy(
//                            new SelectFilter()
//                                    .or("nationality", "not in", localNationalitiesItems)
//                                    .or("nationality", "IS NULL", null));
//                }
//            }
//            type = "UAE_NON_H_C";
//        }

        if (!type.equals("All")) {
            query.filterBy("employeeType", "=", OfficeStaffType.valueOf(type));
        }
        return query.execute();
    }

    //Jirra ACC-1085
    //Check Annual Vacations
//    public boolean checkVacationForOfficeStaff(
//            LocalDate payrollStart,
//            LocalDate payrollEnd,
//            OfficeStaff officeStaff){
//
//        Filter maids not on vacation this month
//        PicklistItem vacationType =
//                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
//                        "pre-paid_vacation");
//        List<ScheduledAnnualVacation> annualVacations = officeStaff.getScheduledAnnualVacations();
//        boolean flag = false;
//        if (annualVacations != null) {
//
//            for (ScheduledAnnualVacation vacation : annualVacations) {
//                LocalDate payrollDueDate = new LocalDate(vacation.getPayrollDueDate());
//                if ((payrollDueDate.equals(payrollStart.minusMonths(1)) || payrollDueDate.isAfter(payrollStart.minusMonths(1)))
//                        && (payrollDueDate.equals(payrollEnd.minusMonths(1)) || payrollDueDate.isBefore(payrollEnd.minusMonths(1)))
//                        && vacation.getType().getCode().equals(vacationType.getCode()) && vacation.getAmount() > 0.0) {
//                    flag = true;
//                    break;
//                }
//            }
//        }
//        return flag;
//    }

    /*
    * <AUTHOR> Qazzaz
    * @reason PAY-142
    */
    @NoPermission
    @RequestMapping("/exportOfficeStaffsToCSV")
    public ResponseEntity<?> exportOfficeStaffsToCSV(HttpServletResponse response , @RequestBody OfficeStaff staff) throws Exception {

        String name = (staff.getName() == null || staff.getName().isEmpty()) ? null : staff.getName();

        SelectQuery<OfficeStaff> query = new SelectQuery<>(OfficeStaff.class);
        query.leftJoin("team");
        query.leftJoin("jobTitle");

        if(name != null) {
            String likeExpression;
            if (name.split(" ").length != 2) {
                likeExpression = name.trim();
            } else {
                String[] parts = name.split(" ");
                String firstName = parts[0];
                String lastName = parts[1];
                likeExpression = firstName.trim() + "%" + lastName.trim();
            }
            query.filterBy("name", "LIKE", "%" + likeExpression + "%");
        }

        if(staff.getEmployeeType() != null) {
            query.filterBy("employeeType", "=", staff.getEmployeeType());
        }

        if(staff.getStatus() != null) {
            query.filterBy("status", "=", staff.getStatus());
        }

        if(staff.getTeam() != null) {
            query.filterBy(new SelectFilter("team", "IS NOT NULL", null).and("team.id", "=", staff.getTeam().getId()));
        }

        if(staff.getJobTitle() != null) {
            query.filterBy(new SelectFilter("jobTitle", "IS NOT NULL", null).and("jobTitle.id", "=", staff.getJobTitle().getId()));
        }


        if(staff.getExcludedFromPayroll()) {
            query.filterBy("excludedFromPayroll", "=", true);
            if(staff.getStatus() == null) {
                query.filterBy("status", "=", OfficeStaffStatus.ACTIVE);
            }
        }

        User currentUser = CurrentRequest.getUser();

        if(currentUser == null) throw new BusinessException("Unauthorized request. Can't fetch the office staff list.");

        if (!currentUser.hasPosition("payroll_trustee") && !currentUser.hasPosition("hr_manager") && !currentUser.hasPosition("hr_manager_assistant") && !currentUser.hasPosition("office_staff_documents")) {
            List<OfficeStaff> employees = new ArrayList<>();
            OfficeStaff currentOfficeStaff = staffRep.findFirstByUser(currentUser);
            if (messageTemplateService.isManager(currentOfficeStaff)) {
                employees = currentOfficeStaff.getAllLevelEmployees();
            }
            List<Long> employeIds = employees.stream().map(OfficeStaff::getId).collect(Collectors.toList());
            if(employeIds.isEmpty()) {
                return ResponseEntity.ok(employees);
            }

            query.filterBy("id", "in", employeIds);
        }


        List<OfficeStaffReport> result = project(query.execute(), OfficeStaffReport.class);

        String title = String.format("Office Staffs %s", DateUtil.formatFullDate(new Date()));
        String[] headers = new String[]{"name", "statusReport", "jobTitle", "departments", "employeeType", "startDateReport", "manager"};
        //OfficeStaffRoster.Counter.resetCounter();
        OfficeStaffReport.Counter.resetCounter();
        try (InputStream is = generateCsv(result, OfficeStaffReport.class, headers)) {

            createDownloadResponse(response,
                    title + ".csv",
                    is);
        }
        return null;

    }

    @PreAuthorize("hasPermission('OfficeStaffPayrollLog','markPayrollUnpaid')")
    @RequestMapping(value = "/mark-payroll-unpaid/{id}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> markPayrollUnpaid(@PathVariable("id") OfficeStaffPayrollLog officeStaffPayrollLog,
                                               @RequestParam(value = "notes") String notes) {
        String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MARK_PAYROLL_UNPAID_EMAIL_SUBJECT);
        subject = subject.replace("@employee_name@", officeStaffPayrollLog.getEmployeeName() != null ?
                officeStaffPayrollLog.getEmployeeName() : officeStaffPayrollLog.getOfficeStaff().getName());
        subject = subject.replace("@payroll_date@", DateUtil.formatFullMonthDashedFullYear(officeStaffPayrollLog.getPayrollMonth()));

        String usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MARK_PAYROLL_UNPAID_USER_ID_RECIPIENTS);
        List<String> idsList = Arrays.asList(usersIds.split(";"));

        boolean emailSent = false;

        Map<String, String> paramValues = new HashMap<>();
        paramValues.put("payroll_date", DateUtil.formatFullMonthDashedFullYear(officeStaffPayrollLog.getPayrollMonth()));
        paramValues.put("employee_name", officeStaffPayrollLog.getEmployeeName() != null ?
                officeStaffPayrollLog.getEmployeeName() : officeStaffPayrollLog.getOfficeStaff().getName());
        paramValues.put("salary_amount", NumberFormatter.formatNumber(officeStaffPayrollLog.getTotalSalary()) + " " + officeStaffPayrollLog.getCurrency());
        paramValues.put("notes", notes);

        for(String userId : idsList) {
            User user = userRepository.findOne(Long.parseLong(userId));

            EmailRecipient recipient = new Recipient(user.getEmail() != null ? user.getEmail() : null , user.getName() != null ? user.getName() : null);
            List<EmailRecipient> recipients = new ArrayList<>();
            recipients.add(recipient);

            //Create new url for every user & send him the email
            String url = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_MANAGER_APPROVE,
                    officeStaffPayrollLog.getId().toString() + "#Payroll_Mark_Unpaid_Approval", userId);

            paramValues.put("url", url);

            Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(recipients, null, "Payroll_Mark_Unpaid_Approval", subject
                            , paramValues, null, null);

            emailSent = true;
        }

        if(emailSent){
            officeStaffPayrollLog.setMarkUnpaidNotes(notes);
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(officeStaffPayrollLog);
        }
        return new ResponseEntity<>("Success", HttpStatus.OK);
    }

    /*
    *end
    */
}
