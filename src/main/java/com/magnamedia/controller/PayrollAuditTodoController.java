package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.PayrollAuditSubTodo;
import com.magnamedia.entity.projection.payrollAudit.ExcludedMVInfoProjection;
import com.magnamedia.entity.projection.payrollAudit.PayrollAuditFile;
import com.magnamedia.entity.projection.payrollAudit.PayrollAuditMissingField;
import com.magnamedia.extra.PayrollAuditTodoTaskNameType;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.PayrollAuditTodoJob;
import com.magnamedia.service.Auditor.AsyncService;
import com.magnamedia.service.Auditor.Housemaids.HousemaidCheckListService;
import com.magnamedia.service.Auditor.Housemaids.HousemaidsGoogleReviews;
import com.magnamedia.service.Auditor.Housemaids.HousemaidsMissingFields;
import com.magnamedia.service.Auditor.OfficeStaffs.OfficeStaffsExceptions;
import com.magnamedia.service.Auditor.OfficeStaffs.OfficeStaffsMissingFields;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.PayrollAuditTodoService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollExceptionsReportService;
import com.magnamedia.service.payroll.generation.newversion.AuditFilesService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.OfficeStaffPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 6/20/2020
 **/
@RestController
@RequestMapping("/payrollAuditTodo")
public class PayrollAuditTodoController extends BaseAdminRepositoryController<PayrollAuditTodo> {

    @Autowired
    MailService emailService;
    @Autowired
    private PayrollNotificationsService payrollNotificationsService;
    @Autowired
    private PayrollAuditTodoRepository payrollAuditTodoRepository;
    @Autowired
    private PayrollRosterApproveRequestRepository payrollRosterApproveRequestRepository;
    @Autowired
    private OfficeStaffsExceptions officeStaffsExceptions;
    @Autowired
    private OfficeStaffsMissingFields officeStaffsMissingFields;
    @Autowired
    private HousemaidsMissingFields housemaidsMissingFields;
    @Autowired
    private HousemaidCheckListService housemaidCheckListService;
    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;
    @Autowired
    private AttachementRepository attachementRepository;
    @Autowired
    private AuditSetupRepository auditSetupRepository;
    @Autowired
    private HousemaidsGoogleReviews housemaidsGoogleReviews;
    @Autowired
    private AuditFilesService auditFilesService;
    @Autowired
    private PayrollAuditHousemaidExceptionRepository housemaidExceptionRepository;
    @Autowired
    private PayrollAuditOfficeStaffExceptionRepository officeStaffExceptionRepository;
    @Autowired
    private AccountantToDoService accountantToDoService;
    @Autowired
    private HousemaidPayrollAuditService housemaidPayrollAuditService;
    @Autowired
    private OfficeStaffPayrollAuditService officeStaffPayrollAuditService;
    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private OfficeStaffRepository officeStaffRepository;
    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private PayrollAuditTodoService payrollAuditTodoService;
    @Autowired
    private PayrollExceptionsReportService payrollExceptionsReportService;
    @Autowired
    private PublicPageHelper publicPageHelper;
    @Autowired
    private CcMaidSwitchedToMvRepository ccMaidSwitchedToMvRepository;

    @Override
    public BaseRepository<PayrollAuditTodo> getRepository() {
        return payrollAuditTodoRepository;
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','listPayrollAuditTodo')")
    @RequestMapping("/listPayrollAuditTodo")
    public ResponseEntity<?> listPayrollAuditTodo() {
        List<PayrollAuditTodo> auditTodoList = payrollAuditTodoRepository.findByCompleted(false);
        return ResponseEntity.ok(auditTodoList);
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','listPayrollAuditSubTodos')")
    @RequestMapping("/listPayrollAuditSubTodos/{todoId}")
    public ResponseEntity<?> listPayrollAuditSubTodos(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {
        List<PayrollAuditSubTodo> subTodoList = new ArrayList<>();
        int count = 0;
        if (payrollAuditTodo == null)
            throw new BusinessException("Payroll Audit Todo is not Existed, please go back to the Auditors page.");

        //HOUSEMAID
        if (payrollAuditTodo.getTaskName().equals(PaymentRuleEmployeeType.HOUSEMAIDS.toString())) {
            List<Housemaid> targetList = housemaidPayrollAuditService.getTargetList(payrollAuditTodo.getMonthlyPaymentRule());
            //check if there is an exceptions
            count = housemaidExceptionRepository.countByAuditTodoAndConfirmed(payrollAuditTodo, false);
            if (count > 0) {
                subTodoList.add(new PayrollAuditSubTodo("Check " + count + " Exceptions", PayrollAuditSubTodo.SubTodoType.EXCEPTION, null, false));
            }

            // check Google Reviews Addition
            int countAllGoogleReviews = housemaidsGoogleReviews.countAllGoogleReviews(payrollAuditTodo, targetList);
            int countNotConfirmedGoogleReviews = housemaidsGoogleReviews.countNotConfirmedGoogleReviews(payrollAuditTodo, targetList);

            if (countAllGoogleReviews > 0)
                subTodoList.add(new PayrollAuditSubTodo("Confirm Google Reviews Addition", PayrollAuditSubTodo.SubTodoType.GOOGLE_REVIEWS, null, countNotConfirmedGoogleReviews > 0 ? false : true));

            //check if there is missing fields
            boolean missingFieldsExisted = housemaidsMissingFields.checkExistHousemaidMissingFields(payrollAuditTodo, targetList);
            if (missingFieldsExisted) {
                subTodoList.add(new PayrollAuditSubTodo("Missing Fields in Payroll File", PayrollAuditSubTodo.SubTodoType.MISSING_FIELDS, null, true));
            }

            //Payroll Checklist
            subTodoList.add(new PayrollAuditSubTodo("Payroll Checklist", PayrollAuditSubTodo.SubTodoType.PAYROLL_CHECKLIST, null, true));


            //CC Maids who have salaries different
            subTodoList.add(new PayrollAuditSubTodo("CC Maids who have salaries different from others of their nationality", PayrollAuditSubTodo.SubTodoType.MAIDS_SALARY_OVER_NATIONALITY, null, true));

//            if (payrollAuditTodo.getAttachment("audit_report") == null) {
//                //generate the Audit Report And finish processing
//                try {
//                    asyncService.generateHousemaidAuditReport(payrollAuditTodo, targetList);
//
//                } catch (Exception ex) {
//                    DebugHelper.sendExceptionMail(
//                            "<EMAIL>", ex,
//                            "Exception occurred while trying to generate audit report in generateHousemaidExceptions", false);
//                }
//            }

        } else {//OVERSEAS, EXPAT, EMARATI

            //check if there is an exceptions
            count = officeStaffExceptionRepository.countByAuditTodoAndConfirmed(payrollAuditTodo, false);
            if (count > 0) {
                subTodoList.add(new PayrollAuditSubTodo("Check " + count + " Exceptions", PayrollAuditSubTodo.SubTodoType.EXCEPTION, null, false));
            }

            //check if there is missing fields
            List<PayrollAuditMissingField> missingFields = officeStaffsMissingFields.getOfficeStaffMissingFields(payrollAuditTodo);
            if (!missingFields.isEmpty()) {
                subTodoList.add(new PayrollAuditSubTodo("Missing Fields in Payroll File", PayrollAuditSubTodo.SubTodoType.MISSING_FIELDS, null, false));
            }

            // add ChangeLog
            subTodoList.add(new PayrollAuditSubTodo("Check all payroll changes from last month", PayrollAuditSubTodo.SubTodoType.CHANGE_LOG, null, true));

//            if (payrollAuditTodo.getAttachment("audit_report") == null) {
//                //generate the Audit Report And finish processing
//                try {
//                    asyncService.generateOfficeStaffAuditReport(payrollAuditTodo);
//                } catch (Exception ex) {
//                    DebugHelper.sendExceptionMail(
//                            "<EMAIL>", ex,
//                            "Exception occurred while trying to generate audit report in generateOfficeStaffExceptions", false);
//                }
//            }

        }
        //add payroll files
        subTodoList.add(new PayrollAuditSubTodo("Review payroll files", PayrollAuditSubTodo.SubTodoType.PAYROLL_FILES, null, true));

        //jira PAY-512 Payroll - Stop sending the payroll audit report
//        //add Audit Report file
//        List<Attachment> attachmentList = new ArrayList<>();
//        attachmentList.add(payrollAuditTodo.getAttachment("audit_report"));
//        subTodoList.add(new PayrollAuditSubTodo("View Audit Report", PayrollAuditSubTodo.SubTodoType.AUDIT_REPORT, attachmentList, true));

        return ResponseEntity.ok(subTodoList);
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','confirmPayrollAuditTodo')")
    @RequestMapping("/confirmPayrollAuditTodo/{todoId}")
    public ResponseEntity<?> confirmPayrollAuditTodo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");

        //check if there is another opened todos related to the same monthly rule
        if(PaymentRuleEmployeeType.HOUSEMAIDS.toString().equals(payrollAuditTodo.getTaskName())){
            List<String> taskNames = new ArrayList<>();
            taskNames.add(PayrollAuditTodoTaskNameType.HOUSEMAID_WITH_DIFFERENT_SALARIES.toString());
            taskNames.add(PayrollAuditTodoTaskNameType.HOUSEMAID_MANUALLY_EXCLUDED_FROM_PROFILE.toString());
            taskNames.add(PayrollAuditTodoTaskNameType.CC_HOUSEMAIDS_INCLUDED_BY_MANAGER.toString());
            taskNames.add(PayrollAuditTodoTaskNameType.MV_HOUSEMAIDS_INCLUDED_BY_MANAGER.toString());
            Integer openedTodosCount = payrollAuditTodoRepository.countByMonthlyPaymentRuleAndTaskNameInAndCompletedFalse(payrollAuditTodo.getMonthlyPaymentRule(), taskNames);
            if (openedTodosCount != null && openedTodosCount > 0)
                throw new BusinessException("please close the other opened to-dos before closing this one !");
        }
        // in case office staff --> check if there are any pending roster requests
        else {
            OfficeStaffType officeStaffType = payrollAuditTodo.getTargetedOfficeStaffType();
            Boolean isEmarati = OfficeStaffType.DUBAI_STAFF_EMARATI.equals(officeStaffType);
            Integer pendingRequestsCount = payrollRosterApproveRequestRepository.countByPayrollMonthAndApprovedFalseAndForEmarati(payrollAuditTodo.getPayrollMonth(), isEmarati);
            if (pendingRequestsCount != null && pendingRequestsCount > 0)
                throw new BusinessException("Please close pending roster requests before closing this to-do !");
        }

        //set the to-do as completed
        payrollAuditTodo.setCompleted(true);
        payrollAuditTodo = payrollAuditTodoRepository.save(payrollAuditTodo);

        MonthlyPaymentRule monthlyPaymentRule = monthlyPaymentRuleRepository.findOne(payrollAuditTodo.getMonthlyPaymentRule().getId());

        if (PaymentRuleEmployeeType.HOUSEMAIDS.toString().equals(payrollAuditTodo.getTaskName())) {

            Setup.getApplicationContext()
                    .getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "processHousemaidPayrollAuditTodo", "payrollAuditTodoService", "payroll",
                            "processHousemaidPayrollAuditTodo",
                            "PayrollAuditTodo", payrollAuditTodo.getId(), false,
                            false, new Class[]{Long.class}, new Object[]{payrollAuditTodo.getId()});


        }else {
            checkMonthlyRuleAuditTodos(monthlyPaymentRule);
        }


        //jira PAY-512 Payroll - Stop sending the payroll audit report
//        //sending the report
//        try {
//            Attachment auditReportAttachment = payrollAuditTodo.getAttachment("audit_report");
//
//            String recipientsParam = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS);
//            String subjectParam = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT);
//            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(recipientsParam);
//            TextEmail email = new TextEmail(subjectParam, "please find attached files");
//            email.addAttachement(auditReportAttachment);
//            Setup.getMailService().sendEmail(recipients, email, false, MessageTemplateService.getMaidsCcSenderName(), null);
//        } catch (Exception ex) {
//            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating housemaids report", false);
//        }



        return ResponseEntity.ok("Done");
    }

//    public List<Housemaid> getExcludedMaidsDueToEVisaIssued(List<Housemaid> housemaids) {
//        return housemaidRepository.findByHousemaidsAndEntryVisaExpiryDate(
//                housemaids,
//                HousemaidType.MAID_VISA,
//                new LocalDate().minusMonths(1).withDayOfMonth(1).toDate());
//    }


    public List<Housemaid> getExcludedMaidsDueToMedicalTestIsNotDone(List<Housemaid> targetList) {
        return targetList.stream()
                .filter(housemaid -> HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType()) && (Objects.isNull(housemaid.getVisaNewRequest()) ||
                        Objects.isNull(housemaid.getVisaNewRequest().findTaskMoveOutDate("Waiting for the maid to go to medical test and EID fingerprinting"))))
                .collect(Collectors.toList());
    }

    public List<Housemaid> getExcludedMaidsDueToNotHasMedicalCertificate(List<Housemaid> targetList) {
        return housemaidRepository.findMVMaidsWithNoMedicalCertificate(targetList, "medicalCertificate", HousemaidType.MAID_VISA);
    }


    @PreAuthorize("hasPermission('payrollAuditTodo','confirmGeneralAuditTodo')")
    @RequestMapping("/confirmGeneralAuditTodo/{todoId}")
    public ResponseEntity<?> confirmGeneralAuditTodo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");
        //set the to-do as completed
        payrollAuditTodo.setCompleted(true);
        payrollAuditTodo = payrollAuditTodoRepository.save(payrollAuditTodo);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','remindManagerAboutExcludedMaids')")
    @RequestMapping("/remindManagerAboutExcludedMaids/{todoId}")
    public ResponseEntity<?> remindManagerAboutExcludedMaids(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");

        List<Housemaid> ccExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(payrollAuditTodo.getMonthlyPaymentRule(), true);
        List<Housemaid> mvExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(payrollAuditTodo.getMonthlyPaymentRule(), false);

        //no need to send reminder
        if (ccExcludedMaids.size() == 0 && mvExcludedMaids.size() == 0)
            return ResponseEntity.ok("Done");

        //send emails to cc and mv managers
        payrollAuditTodoService.sendManuallyExcludedMaidsEmails(payrollAuditTodo.getMonthlyPaymentRule(), ccExcludedMaids, mvExcludedMaids);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getMaidsExcludedManuallyInfo')")
    @RequestMapping("/getMaidsExcludedManuallyInfo/{todoId}")
    public ResponseEntity<?> getMaidsExcludedManuallyInfo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");

        List<Housemaid> allExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(payrollAuditTodo.getMonthlyPaymentRule(), null);

        //no need to send reminder
        if (allExcludedMaids.size() == 0)
            return ResponseEntity.ok("Done");

        return ResponseEntity.ok(housemaidRepository.getExcludedIncludedMaidsInfos(allExcludedMaids));
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getMaidsIncludedManuallyInfo')")
    @RequestMapping("/getMaidsIncludedManuallyInfo/{todoId}")
    public ResponseEntity<?> getMaidsIncludedManuallyInfo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        if(payrollAuditTodo.getCompleted() != null & payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");
        payrollAuditTodo = payrollAuditTodoRepository.getOne(payrollAuditTodo.getId());

        return ResponseEntity.ok(housemaidRepository.getExcludedIncludedMaidsInfos(payrollAuditTodo.getManuallyIncludedMaids()));
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getMaidsExcludedManuallyInfoByType')")
    @RequestMapping("/getMaidsExcludedManuallyInfoByType/{ruleId}")
    public ResponseEntity<?> getMaidsExcludedManuallyInfoByType(@PathVariable(name = "ruleId") MonthlyPaymentRule rule, @RequestParam Boolean ccMaids) {

        Map<String, Object> resultMap = new HashMap<>();

        if (ccMaids) {
            List<Housemaid> ccExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(rule, true);
            resultMap.put("submittedBefore", rule.getCcMaidsIncludedBefore() || rule.getAuditingFinished());
            resultMap.put("maidsInfo", housemaidRepository.getExcludedIncludedMaidsInfos(ccExcludedMaids));
        }else {
            List<Housemaid> mvExcludedMaids = housemaidPayrollAuditService.getManuallyExcludedTargetListForHousemaidType(rule, false);
            resultMap.put("submittedBefore", rule.getMvMaidsIncludedBefore() || rule.getAuditingFinished());
            resultMap.put("maidsInfo", housemaidRepository.getExcludedIncludedMaidsInfos(mvExcludedMaids));
        }

        return ResponseEntity.ok(resultMap);
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','confirmManuallyExcludedMaidsTodo')")
    @RequestMapping(value = "/confirmManuallyExcludedMaidsTodo/{ruleId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> confirmManuallyExcludedMaidsTodo(@PathVariable(name = "ruleId") MonthlyPaymentRule rule, @RequestBody List<Map<String, Object>> includedHousemaidsInfo, @RequestParam Boolean ccMaids) {

        try {
            PayrollAuditTodo auditTodo;
            if ((rule.getAuditingFinished() != null && rule.getAuditingFinished())
                    || (ccMaids && rule.getCcMaidsIncludedBefore() != null && rule.getCcMaidsIncludedBefore())
                    || !ccMaids && rule.getMvMaidsIncludedBefore() != null && rule.getMvMaidsIncludedBefore())
                throw new BusinessException("Already Submitted before !");

            List<Housemaid> includedMaids = new ArrayList<>();
            //first include the maids
            for (Map<String, Object> housemaidInfo : includedHousemaidsInfo) {
                Housemaid housemaid = housemaidRepository.getOne(Long.parseLong(housemaidInfo.get("id").toString()));
                if (housemaid.getExcludedFromPayroll() != null && housemaid.getExcludedFromPayroll()) {
                    HousemaidExcludeHistory excludeHistory = new HousemaidExcludeHistory(housemaid, false, (String) housemaidInfo.get("notes"));
                    Setup.getRepository(HousemaidExcludeHistoryRepository.class).save(excludeHistory);
                    HousemaidLastExcludeDetails lastExcludeDetails = housemaid.getHousemaidLastExcludeDetails();
                    if (lastExcludeDetails == null) {
                        lastExcludeDetails = new HousemaidLastExcludeDetails(housemaid, false, (String) housemaidInfo.get("notes"), new Date());
                    } else {
                        lastExcludeDetails.setHousemaid(housemaid);
                        lastExcludeDetails.setExcludedManuallyFromProfile(false);
                        lastExcludeDetails.setNotes((String) housemaidInfo.get("notes"));
                        lastExcludeDetails.setLastExclusionManuallyDate(new Date());
                    }
                    lastExcludeDetails = Setup.getRepository(HousemaidLastExcludeDetailsRepository.class).save(lastExcludeDetails);
                    housemaid.setExcludedFromPayroll(false);
                    housemaid = housemaidRepository.save(housemaid);
                    includedMaids.add(housemaid);
                }
            }


            if (ccMaids) {
                rule.setCcMaidsIncludedBefore(true);
                rule = monthlyPaymentRuleRepository.save(rule);

                //create audit todo for auditor
                auditTodo = new PayrollAuditTodo();
                auditTodo.setTaskName(PayrollAuditTodoTaskNameType.CC_HOUSEMAIDS_INCLUDED_BY_MANAGER.toString());
                auditTodo.setMonthlyPaymentRule(rule);
                auditTodo.setPayrollMonth(rule.getPayrollMonth());
                auditTodo.setManuallyIncludedMaids(includedMaids);
                auditTodo.setLabel("Maids that CC manager included in payroll");
            } else {
                rule.setMvMaidsIncludedBefore(true);
                rule = monthlyPaymentRuleRepository.save(rule);

                //create audit todo for auditor
                auditTodo = new PayrollAuditTodo();
                auditTodo.setTaskName(PayrollAuditTodoTaskNameType.MV_HOUSEMAIDS_INCLUDED_BY_MANAGER.toString());
                auditTodo.setMonthlyPaymentRule(rule);
                auditTodo.setPayrollMonth(rule.getPayrollMonth());
                auditTodo.setManuallyIncludedMaids(includedMaids);
                auditTodo.setLabel("Maids that MV manager included in payroll");
            }
            auditTodo = payrollAuditTodoRepository.save(auditTodo);

            Setup.getApplicationContext().getBean(MessagingService.class)
                    .notifyAuditorsAboutIncludedMaidsTodo(auditTodo);
        }catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Exception in confirmManuallyExcludedMaidsTodo for rule #" + rule.getId(), false);
            throw ex;
        }
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getExcludedMVList')")
    @RequestMapping("/getExcludedMVList/{todoId}")
    public ResponseEntity<?> getExcludedMVList(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {
        payrollAuditTodo = payrollAuditTodoRepository.findOne(payrollAuditTodo.getId());

        List<ExcludedMVInfo> excludedMVInfoList = payrollAuditTodo.getExcludedMVInfos();

        return ResponseEntity.ok(excludedMVInfoList);
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','confirmExcludedMVTodo')")
    @RequestMapping(value = "/confirmExcludedMVTodo/{todoId}", method = RequestMethod.POST)
    public ResponseEntity<?> confirmExcludedMVTodo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo,
                                                   @RequestBody List<ExcludedMVInfo> includedMVList) {
        try {
            if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
                throw new BusinessException("Already Confirmed before !");
            //set the to-do as completed
            payrollAuditTodo.setCompleted(true);
            payrollAuditTodo = payrollAuditTodoRepository.save(payrollAuditTodo);

            MonthlyPaymentRule monthlyPaymentRule = monthlyPaymentRuleRepository.findOne(payrollAuditTodo.getMonthlyPaymentRule().getId());


            // add the includedMVList to the monthlyPaymentRule to include them in the Accountant To-do
            if (includedMVList != null && includedMVList.size() > 0) {

                MonthlyPaymentRule monthlyPaymentRuleWPS = monthlyPaymentRuleRepository.findTopByPayrollMonthAndPayrollTypeAndPaymentMethodAndLockDate(monthlyPaymentRule.getPayrollMonth(), monthlyPaymentRule.getPayrollType(), PaymentRulePaymentMethod.WPS, monthlyPaymentRule.getLockDate());
                MonthlyPaymentRule monthlyPaymentRuleLocal = monthlyPaymentRuleRepository.findTopByPayrollMonthAndPayrollTypeAndPaymentMethodAndLockDate(monthlyPaymentRule.getPayrollMonth(), monthlyPaymentRule.getPayrollType(), PaymentRulePaymentMethod.LOCAL_TRANSFER, monthlyPaymentRule.getLockDate());
                List<ExcludedMVInfo> includedMVListWPS = new ArrayList<>();
                List<ExcludedMVInfo> includedMVListLocal = new ArrayList<>();
                ExcludedMVInfoRepository excludedMVInfoRepository = Setup.getRepository(ExcludedMVInfoRepository.class);
                for (ExcludedMVInfo excludedMVInfo : includedMVList) {
                    excludedMVInfo = excludedMVInfoRepository.findOne(excludedMVInfo.getId());
                    Housemaid housemaid = housemaidRepository.findOne(excludedMVInfo.getHousemaidId());
                    if (housemaid.getWithMolNumber() != null && housemaid.getWithMolNumber())
                        includedMVListWPS.add(excludedMVInfo);
                    else
                        includedMVListLocal.add(excludedMVInfo);
                }
                monthlyPaymentRuleLocal.setMustIncludedMVInfoList(new HashSet<>(includedMVListLocal));
                monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRuleLocal);

                monthlyPaymentRuleWPS.setMustIncludedMVInfoList(new HashSet<>(includedMVListWPS));
                monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRuleWPS);

            }

            // check if all monthlyPaymentRule's related todos are completed, then set the monthlyPaymentRule as finished auditing
            checkMonthlyRuleAuditTodos(monthlyPaymentRule);

            // send a report with included maids
            try {
                if (includedMVList.size() > 0)
                    sendIncludedMVByAuditorReport(payrollAuditTodo, includedMVList);
            } catch (Exception e) {
                DebugHelper.sendExceptionMail("<EMAIL>", e, "sendIncludedMVByAuditorReport ", false);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "confirmExcludedMVTodo for todo #" + payrollAuditTodo.getId(), false);
        }
        return ResponseEntity.ok("Done");
    }

    /**
     * check if all monthlyPaymentRule's related todos are completed, then set the monthlyPaymentRule as finished auditing
     * @param monthlyPaymentRule
     */
    public void checkMonthlyRuleAuditTodos (MonthlyPaymentRule monthlyPaymentRule){
        monthlyPaymentRule = monthlyPaymentRuleRepository.findOne(monthlyPaymentRule.getId());
        List<PayrollAuditTodo> auditTodoList = payrollAuditTodoRepository.findByMonthlyPaymentRule(monthlyPaymentRule);
        boolean allCompleted = true;
        List<String> wantedAuditTodos = new ArrayList() {
            {
                add(PayrollAuditTodoTaskNameType.HOUSEMAIDS.toString());
                add(PayrollAuditTodoTaskNameType.EXPATS.toString());
                add(PayrollAuditTodoTaskNameType.EMIRATI.toString());
                add(PayrollAuditTodoTaskNameType.OVERSEAS.toString());
                add(PayrollAuditTodoTaskNameType.EXCLUDED_MAID_VISA.toString());
            }
        };

        for (PayrollAuditTodo auditTodo : auditTodoList) {
            if (!auditTodo.getCompleted() && wantedAuditTodos.contains(auditTodo.getTaskName())) {
                allCompleted = false;
                break;
            }
        }

        if (allCompleted) {
            List<MonthlyPaymentRule> targetRules = new ArrayList<>();

            if (!monthlyPaymentRule.isTargetingHousemaid()) {
                targetRules.add(monthlyPaymentRule);
            } else {
                List<MonthlyPaymentRule> allRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findByPayrollMonthAndLockDateAndFinishedFalse(monthlyPaymentRule.getPayrollMonth(),
                        monthlyPaymentRule.getLockDate());

                for (MonthlyPaymentRule rule : allRules) {
                    if (rule.isTargetingHousemaid()) {
                        targetRules.add(rule);
                    }
                }
            }

            for (MonthlyPaymentRule rule : targetRules) {
                rule.setAuditingFinished(true);
                monthlyPaymentRuleRepository.save(rule);

            }

            accountantToDoService.createToDosAsync(targetRules.stream().filter(rule -> rule.getPaymentDate().compareTo(new java.sql.Date(System.currentTimeMillis())) <= 0)
                    .map(rule -> monthlyPaymentRuleRepository.findOne(rule.getId())).collect(Collectors.toList()));
        }
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getPayrollFiles')")
    @RequestMapping("/getPayrollFiles/{todoId}")
    public ResponseEntity<?> getPayrollFiles(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo,
                                             @RequestParam(required = false, name = "type") String type) {

        if (payrollAuditTodo.getFinishedGeneratingLogs() != null && !payrollAuditTodo.getFinishedGeneratingLogs()) {
            throw new BusinessException("Payroll logs are already being generated. Please wait until it's finished and try again.");
        }

        if (AsyncService.HOUSEMAID_REPORT_IS_BEING_GENERATED) {
            throw new BusinessException("Audit files are being generated. Please wait until it's finished and try again.");
        }

        if (type == null) {
            try {
                //add the files structure with its attachments
                List<PayrollAuditFile> fileList = Arrays.asList(
                        new PayrollAuditFile("Payroll Detailed File", "payroll_" + DateUtil.formatConnectedMonthYear(payrollAuditTodo.getPayrollMonth()) + "_detailed",
                                new ArrayList<>()),

                        new PayrollAuditFile("Payroll Pay Order File", "payroll_" + DateUtil.formatConnectedMonthYear(payrollAuditTodo.getPayrollMonth()) + "_pay_order",
                                new ArrayList<>()),

                        new PayrollAuditFile("Payroll Exceptions Report", "payroll_" + DateUtil.formatConnectedMonthYear(payrollAuditTodo.getPayrollMonth()) + "_exceptions_report",
                                 new ArrayList<>())
                );
                return ResponseEntity.ok(fileList);
            } catch (Exception e) {
                DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while payroll files!", false);
            }
        } else {

            AsyncService.HOUSEMAID_REPORT_IS_BEING_GENERATED = true;
            //call async service to send the files by email
            AsyncService asyncService = Setup.getApplicationContext().getBean(AsyncService.class);
            asyncService.getPayrollFiles(payrollAuditTodo, type);
        }
        return ResponseEntity.ok("Your request was registered, The files will be sent be email shortly.");
    }


    private void sendIncludedMVByAuditorReport(PayrollAuditTodo payrollAuditTodo, List<ExcludedMVInfo> includedMVList) throws IOException {

        List<Long> includedHousemaids = new ArrayList<>();
        if (includedMVList != null  && includedMVList.size() >0){
            for (ExcludedMVInfo excludedMVInfo : includedMVList) {
                excludedMVInfo = Setup.getRepository(ExcludedMVInfoRepository.class).findOne(excludedMVInfo.getId());
                includedHousemaids.add(excludedMVInfo.getHousemaidId());
            }
        }

        if (includedHousemaids.size() > 0) {
            List<ExcludedMVInfo> mustIncludedMVListInfo = Setup.getRepository(ExcludedMVInfoRepository.class)
                    .getByPayrollAuditTodoAndHousemaidIn(payrollAuditTodo.getExcludedMVInfos(), includedHousemaids);


            if (mustIncludedMVListInfo == null || mustIncludedMVListInfo.isEmpty())
                return;

            String title = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAIL_SUBJECT);
            String[] headers =
                    new String[]{"Housemaid Name", "Client Name", "Total Salary",
                            "Amount Of Payment"};

            String[] columns =
                    new String[]{"housemaidName", "clientName", "housemaidSalary",
                            "amount"};

            File file = CsvHelper.generateCsv(mustIncludedMVListInfo, ExcludedMVInfoProjection.class, headers, columns, title);

            // send the report to
            String emails = Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_MV_INCLUDED_BY_AUDITOR_EMAILS);
            List<EmailRecipient> recipients = Recipient.parseEmailsString(emails);
            TextEmail textEmail = new TextEmail(title, "please find attached files");
            textEmail.addAttachement(file);
            emailService.sendEmail(recipients, textEmail, EmailReceiverType.Office_Staff);
        }
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getMaidsSalariesOverNationalitiesTodo')")
    @RequestMapping("/getMaidsSalariesOverNationalitiesTodo/{todoId}")
    public ResponseEntity<?> getMaidsSalariesOverNationalitiesTodo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {
        if (payrollAuditTodo == null || payrollAuditTodo.getId() == null)
            throw new BusinessException("Payroll Audit Todo is not Existed, please go back to the Auditors page.");
        payrollAuditTodo = payrollAuditTodoRepository.findOne(payrollAuditTodo.getId());
        List<Housemaid> ccMaids = housemaidPayrollAuditService.getTargetListForHousemaidType(payrollAuditTodo.getMonthlyPaymentRule(), true);

        return ResponseEntity.ok(payrollExceptionsReportService.getHousemaidsOverNationalities(ccMaids));
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','sendQuestionToCCManager')")
    @RequestMapping("/sendQuestionToCCManager/{housemaidId}")
    public ResponseEntity<?> sendQuestionToCCManager(@PathVariable(name = "housemaidId") Housemaid housemaid, @RequestParam String notes) {

        QuestionAboutMaidSalary questionAboutMaidSalary = new QuestionAboutMaidSalary();
        questionAboutMaidSalary.setHousemaid(housemaid);
        questionAboutMaidSalary.setNotes(notes);
        questionAboutMaidSalary.setActionTaken(false);
        questionAboutMaidSalary = Setup.getRepository(QuestionAboutMaidSalaryRepository.class).save(questionAboutMaidSalary);

        //send email to cc manager.
        String ccManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CC_MANAGER_USER_ID);
        if (ccManagerId != null && !"".equals(ccManagerId)) {
            String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_QUESTION_ABOUT_SALARY_EMAIL_TITLE);
            subject = subject.replace("@maid_name@", housemaid.getName());
            String url = publicPageHelper.generatePublicURL(PublicPageHelper.QUESTION_ABOUT_MAID_SALARY, questionAboutMaidSalary.getId().toString(), ccManagerId);
            User ccManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(ccManagerId));
            List<EmailRecipient> recipients = Recipient.parseEmailsString(ccManager.getEmail());
            Map<String, String> params = new HashMap<>();
            params.put("maid_name", housemaid.getName());
            params.put("amount", housemaid.getSalaryWithCurrency());
            params.put("nationality", housemaid.getNationality() != null ? housemaid.getNationality().getName() : "");
            params.put("start_date", DateUtil.formatDateSlashed(housemaid.getStartDate()));
            params.put("client_name", housemaid.getCurrentClient() != null ? housemaid.getCurrentClient().getName() : "");
            params.put("auditor_notes", notes);
            params.put("url", url);
            Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(recipients, null, "Payroll_Question_About_Maid_Salary", subject, params, null, housemaid);
//            TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Question_About_Maid_Salary", params);
//            Setup.getMailService().sendEmail(recipients, templateEmail, null);
        }

        return ResponseEntity.ok("Sent Successfully.");
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','getCcSwitchedToMvMaidsInfo')")
    @RequestMapping("/getCcSwitchedToMvMaidsInfo/{todoId}")
    public ResponseEntity<?> getCcSwitchedToMvMaidsInfo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");
        Map<String, Object> resultMap = new HashMap<>();

        //this is a special case for the deployment month only to consider starting from the first of previous month not from the audit closing date
        String firstDeploymentDateSt = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH);
        java.sql.Date firstDeploymentDate = firstDeploymentDateSt != null && !firstDeploymentDateSt.isEmpty() ? DateUtil.parseDateDashedSql(firstDeploymentDateSt) : null;

        HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);
        java.util.Date from = firstDeploymentDate != null && firstDeploymentDate.equals(payrollAuditTodo.getPayrollMonth()) ? firstDeploymentDate : housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(DateUtil.addMonthsSql(payrollAuditTodo.getPayrollMonth(), -1));
        java.util.Date to = housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(payrollAuditTodo.getPayrollMonth());
        List<CcMaidSwitchedToMv> ccMaidSwitchedToMvList = housemaidPayrollAuditService.getCCSwitchedToMvMaidsTargetList(from, to);


        PayrollGenerationHelperService generationHelperService = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class);

        HousemaidPayrollPaymentServiceV2 payrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);
        java.sql.Date previousPayrollMonth = new java.sql.Date(DateUtil.addMonths(payrollAuditTodo.getPayrollMonth(), -1).getTime());
        LocalDate previousPayrollStart = new LocalDate(payrollPaymentServiceV2.getPayrollStartLockDateOfPayrollMonth(previousPayrollMonth));

        LocalDate startLockDate = new LocalDate(payrollPaymentServiceV2.getPayrollStartLockDateOfPayrollMonth(payrollAuditTodo.getPayrollMonth()).getTime());
        LocalDate endLockDate = new LocalDate(payrollPaymentServiceV2.getPayrollEndPaymentDateOfPayrollMonth(payrollAuditTodo.getPayrollMonth()).getTime());

        // Iterate over each CcMaidSwitchedToMv record. Perform calculations only if they haven't been done already.
        for (CcMaidSwitchedToMv maidSwitchedToMv : ccMaidSwitchedToMvList) {
            if(maidSwitchedToMv.getAlreadyCalculated().equals(false))
                payrollAuditTodoService.doMaidSwitchedToMvCalculations(maidSwitchedToMv, payrollAuditTodo.getPayrollMonth(), generationHelperService, startLockDate, endLockDate, previousPayrollStart);
        }

        Boolean confirmed = ccMaidSwitchedToMvRepository.countByConfirmedAndSwitchDateBetween(false, from, to) == 0;
        resultMap.put("confirmed", confirmed);
        resultMap.put("switchedMaids", ccMaidSwitchedToMvList);

        return ResponseEntity.ok(resultMap);
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','confirmCcSwitchedToMvMaidsRecord')")
    @RequestMapping("/confirmCcSwitchedToMvMaidsRecord/{id}")
    public ResponseEntity<?> confirmCcSwitchedToMvMaidRecord(@PathVariable(name = "id") CcMaidSwitchedToMv ccMaidSwitchedToMv) {

        String maxAdditionAsString = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV);
        Double maxAdditionAmount = 0.0;
        try {
            maxAdditionAmount = Double.parseDouble(maxAdditionAsString);
        }catch (Exception e){

        }
        if (maxAdditionAmount != null && (ccMaidSwitchedToMv.getFinalAmountToBeTransferred() > ccMaidSwitchedToMv.getLastCcSalary() + maxAdditionAmount)){
            throw new RuntimeException("Last CC salary to be transferred can't be more than (Previous cc salary" + (maxAdditionAmount != null && maxAdditionAmount != 0 ? " + " + NumberFormatter.formatNumber(maxAdditionAmount) : "") + ")");
        }
        ccMaidSwitchedToMv.setConfirmed(true);
        ccMaidSwitchedToMv = ccMaidSwitchedToMvRepository.save(ccMaidSwitchedToMv);
        return ResponseEntity.ok(ccMaidSwitchedToMv);
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','payCcSwitchedToMvMaidsTodo')")
    @RequestMapping(value = "/payCcSwitchedToMvMaidsTodo/{todoId}")
    public ResponseEntity<?> payCcSwitchedToMvMaidsTodo(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        // Set value of lastMonthlyPaymentRulePrimary
        MonthlyPaymentRule lastMonthlyPaymentRulePrimary;
        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("payrollMonth", "=", payrollAuditTodo.getPayrollMonth());
        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.sortBy("paymentDate", true);
        List<MonthlyPaymentRule> lastPayrollMonthlyRules = selectQuery.execute();

        if (lastPayrollMonthlyRules.size() > 0) {
            lastMonthlyPaymentRulePrimary = lastPayrollMonthlyRules.get(0);

            //The CcSwitchedToMvMaidsTodo can't be closed until the primary audit to-dos are closed
            if (lastMonthlyPaymentRulePrimary.getAuditingFinished() != null
                    && lastMonthlyPaymentRulePrimary.getAuditingFinished().equals(false)) {
                throw new BusinessException("This audit to-do cannot be closed until the primary audit to-dos are closed");
            }
        }

        if(payrollAuditTodo.getCompleted() != null && payrollAuditTodo.getCompleted())
            throw new BusinessException("Already Confirmed before !");

        //this is a special case for the deployment month only to consider starting from the first of previous month not from the audit closing date
        String firstDeploymentDateSt = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH);
        Date firstDeploymentDate = firstDeploymentDateSt != null && !firstDeploymentDateSt.isEmpty() ? DateUtil.parseDateDashedSql(firstDeploymentDateSt) : null;

        HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);
        java.util.Date from = firstDeploymentDate != null && firstDeploymentDate.equals(payrollAuditTodo.getPayrollMonth()) ? firstDeploymentDate : housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(DateUtil.addMonthsSql(payrollAuditTodo.getPayrollMonth(), -1));
        java.util.Date to = housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(payrollAuditTodo.getPayrollMonth());
        if(ccMaidSwitchedToMvRepository.countByConfirmedAndSwitchDateBetween(false, from, to) != 0)
            throw new BusinessException("You need to confirm all records before you can pay this todo");

        payrollAuditTodo.setCompleted(true);
        payrollAuditTodoRepository.save(payrollAuditTodo);

        // insert a new background task to generate files emails
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "createPaymentToDoForCcSwitchedToMvTodo", "accountantToDoService", "payroll",
                        "createPaymentToDoForCcSwitchedToMvTodo",
                        payrollAuditTodo.getEntityType(), payrollAuditTodo.getId(), false,
                        false, new Class[]{Long.class}, new Object[]{payrollAuditTodo.getId()});

        return ResponseEntity.ok("Accountant Todo will be generated now.");
    }

    @PreAuthorize("hasPermission('payrollAuditTodo','modifyAmountForCcSwitchedToMvMaidRecord')")
    @RequestMapping(value = "/modifyAmountForCcSwitchedToMvMaidRecord", method = RequestMethod.POST)
    public ResponseEntity<?> modifyAmountForCcSwitchedToMvMaidRecord(@RequestBody CcMaidSwitchedToMv ccMaidSwitchedToMv) {
        Double newAmount = ccMaidSwitchedToMv.getFinalAmountToBeTransferred();
        ccMaidSwitchedToMv = ccMaidSwitchedToMvRepository.findOne(ccMaidSwitchedToMv.getId());

        String maxAdditionAsString = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_MAX_ADDITION_VALUE_TO_MAID_AFTER_SWITCH_TO_MV);
        Double maxAdditionAmount = 0.0;
        try {
            maxAdditionAmount = Double.parseDouble(maxAdditionAsString);
        }catch (Exception e){

        }
        if (maxAdditionAmount != null && (newAmount > ccMaidSwitchedToMv.getLastCcSalary() + maxAdditionAmount)){
            throw new BusinessException("Last CC salary to be transferred can't be more than (Previous cc salary" + (maxAdditionAmount != null && maxAdditionAmount != 0 ? " + " + NumberFormatter.formatNumber(maxAdditionAmount) : "") + ")");
        }

        ccMaidSwitchedToMv.setFinalAmountToBeTransferred(newAmount);
        ccMaidSwitchedToMv = ccMaidSwitchedToMvRepository.save(ccMaidSwitchedToMv);
        return ResponseEntity.ok(ccMaidSwitchedToMv);
    }

    //##########################################################################################################//
    //TODO: remove it : test API only
    @NoPermission
    @RequestMapping("/runAuditTodoJob")
    public ResponseEntity<?> runAuditTodoJob() {

        PayrollAuditTodoJob job = new PayrollAuditTodoJob();
        job.run(null);
        return ResponseEntity.ok("Ok");
    }

    //TODO: remove it : test API only
    @NoPermission
    @RequestMapping("/countTargetList/{todoId}")
    public ResponseEntity<?> countTargetList(@PathVariable(name = "todoId") PayrollAuditTodo payrollAuditTodo) {

        try {
            if (payrollAuditTodo.getTaskName().equals(PaymentRuleEmployeeType.HOUSEMAIDS.toString())) {
                List<Housemaid> targetList = housemaidPayrollAuditService.getTargetList(payrollAuditTodo.getMonthlyPaymentRule());
                List<Long> ids = targetList.stream().map(BaseEntity::getId).collect(Collectors.toList());
                int countWithMol = housemaidRepository.countWithMolFile(targetList, true);
                DebugHelper.sendMail("<EMAIL>", payrollAuditTodo.getTaskName() + " --> countTargetList: " + targetList.size() + " , countWithMol: " + countWithMol + ", ids: " + ids);
            } else {
                List<OfficeStaff> targetList = officeStaffPayrollAuditService.getTargetList(payrollAuditTodo.getMonthlyPaymentRule(), PaymentRuleEmployeeType.valueOf(payrollAuditTodo.getTaskName()));
                int countWithMol = officeStaffRepository.countWithMolFile(targetList, true);
                DebugHelper.sendMail("<EMAIL>", payrollAuditTodo.getTaskName() + " --> countTargetList: " + targetList.size() + " , countWithMol: " + countWithMol);
            }
        }catch (Exception e){
            DebugHelper.sendExceptionMail("<EMAIL>", e, "error in countTargetList", false);
        }

        return ResponseEntity.ok("Ok");
    }

    @NoPermission
    @RequestMapping("/makeAuditFinishedGeneratingLogs/{id}")
    private ResponseEntity<?> makeAuditFinishedGeneratingLogs(@PathVariable("id") PayrollAuditTodo payrollAuditTodo) {
        payrollAuditTodo.setFinishedGeneratingLogs(true);
        getRepository().save(payrollAuditTodo);
        return new ResponseEntity<>(payrollAuditTodo, HttpStatus.OK);
    }


}
