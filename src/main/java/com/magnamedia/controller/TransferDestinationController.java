package com.magnamedia.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.ChangeBankInfoHistoryLog;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.TransferDestination;
import com.magnamedia.helper.PhoneNumberUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.BankInfoChangeStatus;
import com.magnamedia.module.type.OfficeStaffStatus;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.ReceiveMoneyMethod;
import com.magnamedia.repository.ChangeBankInfoHistoryLogRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.TransferDestinationRepository;
import com.magnamedia.service.OfficeStaffUpdateService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.Map;

@RequestMapping("/transferDestination")
@RestController
public class TransferDestinationController extends BaseRepositoryController<TransferDestination> {

    @Autowired
    private TransferDestinationRepository repository;

    @Autowired
    private OfficestaffPayrollController officestaffPayrollController;

    @Autowired
    private OfficeStaffUpdateService officeStaffUpdateService;

    @Autowired
    private OfficeStaffController officeStaffController;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Override
    public BaseRepository<TransferDestination> getRepository() {
        return repository;
    }


    public void update(TransferDestination origin, TransferDestination updated, JsonNode jsonNode) {
        super.update(origin, updated, jsonNode);
    }

    @Transactional
    @Override
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (checkPermission("update")) {
            TransferDestination updated = parse(objectNode);

            TransferDestination origin = getRepository().findOne(updated.getId());

            Map<String, Object> map = null;
            if(updated.getSelfReceiver()){
                // Convert ObjectNode to Map<String, Object>
                ObjectMapper mapper = new ObjectMapper();
                map = mapper.convertValue(objectNode, new TypeReference<Map<String, Object>>() {});
            }

            OfficeStaff officeStaff = origin.getOfficeStaff() != null ? officeStaffRepository.findOne(origin.getOfficeStaff().getId()) : null;

            checkDuplicateIBanDetection(officeStaff, updated);

            LocalDate dt = new LocalDate();
            String lockPayrollEnabled = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_ENABLED);
            Integer lockPayrollStart = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_START));
            Integer lockPayrollEnd = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_END));

            if ((!lockPayrollEnabled.equals("1")) || (dt.getDayOfMonth() < lockPayrollStart && dt.getDayOfMonth() > lockPayrollEnd)) {

                if(officeStaff != null) {
                    Boolean selfReceiver = updated.getSelfReceiver();

                    // handle country
                    if (objectNode.has("country"))
                        objectNode.remove("country");

                    if (selfReceiver && map != null && map.containsKey("country") && map.get("country") != null) {
                        Map<String, Object> countryMap = (Map<String, Object>) map.get("country");
                        if (countryMap.get("id") != null) {
                            Long countryId = Long.valueOf(countryMap.get("id").toString());
                            officeStaff.setCountry(
                                    Setup.getRepository(PicklistItemRepository.class).findOne(countryId)
                            );
                        }
                    } else if(!selfReceiver && updated.getCountry() != null){
                        origin.setCountry(Setup.getRepository(PicklistItemRepository.class).findOne(
                                updated.getCountry().getId()));
                    }

                    // handle city
                    if (objectNode.has("city"))
                        objectNode.remove("city");

                    if (selfReceiver && map != null && map.containsKey("city") && map.get("city") != null) {
                        Map<String, Object> cityMap = (Map<String, Object>) map.get("city");

                        if (cityMap.get("id") != null) {
                            Long cityId = Long.valueOf(cityMap.get("id").toString());
                            officeStaff.setCity(
                                    Setup.getRepository(PicklistItemRepository.class).findOne(cityId)
                            );
                        }
                    } else if(!selfReceiver && updated.getCity() != null){
                        origin.setCity(Setup.getRepository(PicklistItemRepository.class).findOne(
                                updated.getCity().getId()));
                    }


                    if(updated.getSelfReceiver()) {
                        officeStaff = updateOfficeStaffData(objectNode, officeStaff, map);
                    }

                    officeStaff = Setup.getRepository(OfficeStaffRepository.class)
                            .save(officeStaff);

                }

                normalizePhoneNumber(updated);

                // Create ChangeBankInfoHistoryLog record when iban OR receiveMoneyMethod is updated
                if(((updated.getIban() != null && !updated.getIban().equals(origin.getIban())) || (updated.getReceiveMoneyMethod() != null && !updated.getReceiveMoneyMethod().equals(origin.getReceiveMoneyMethod())))
                    && (officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI) || officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT))){

                        ChangeBankInfoHistoryLog bankHistoryLog = new ChangeBankInfoHistoryLog(
                                officeStaff,
                                updated.getReceiveMoneyMethod(),
                                officeStaff.getVisaNewRequest().getEmployeeAccountWithAgent(),
                                (updated.getReceiveMoneyMethod() != null && updated.getReceiveMoneyMethod().equals(ReceiveMoneyMethod.BANK_TRANSFER)) ? updated.getIban() : "DXB-LULU BARSHA",
                                updated.getBankName(),
                                BankInfoChangeStatus.PENDING
                        );

                        bankHistoryLog = Setup.getRepository(ChangeBankInfoHistoryLogRepository.class).save(bankHistoryLog);
                }

                if (objectNode.has("officeStaff")) objectNode.remove("officeStaff");

                update(origin, updated, objectNode);

                return super.updateEntity(origin);
            } else {
                return new ResponseEntity<>("Transfer destination Could not be updated because payroll is locked.", HttpStatus.BAD_REQUEST);
            }

        }else {
            return unauthorizedReponse();
        }
    }


    @Transactional
    @RequestMapping(value = "/create-new", method = RequestMethod.POST)
    public ResponseEntity<?> createNew(@RequestBody ObjectNode objectNode) throws IOException {
        if (this.checkPermission("create")) {
            TransferDestination transferDestination = parse(objectNode);

            Map<String, Object> map = null;
            if(transferDestination.getSelfReceiver()){
                // Convert ObjectNode to Map<String, Object>
                ObjectMapper mapper = new ObjectMapper();
                map = mapper.convertValue(objectNode, new TypeReference<Map<String, Object>>() {});
            }

            OfficeStaff officeStaff = transferDestination.getOfficeStaff() != null ? officeStaffRepository.findOne(transferDestination.getOfficeStaff().getId()) : null;

            checkDuplicateIBanDetection(officeStaff, transferDestination);

            LocalDate dt = new LocalDate();
            String lockPayrollEnabled = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_ENABLED);
            Integer lockPayrollStart = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_START));
            Integer lockPayrollEnd = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_LOCK_PAYROLL_END));


            if ((!lockPayrollEnabled.equals("1")) || (dt.getDayOfMonth() < lockPayrollStart && dt.getDayOfMonth() > lockPayrollEnd)) {

                if(officeStaff != null) {
                    Boolean selfReceiver = transferDestination.getSelfReceiver();

                    // handle country
                    if (objectNode.has("country"))
                        objectNode.remove("country");

                    if (selfReceiver && map != null && map.containsKey("country") && map.get("country") != null) {
                        Map<String, Object> countryMap = (Map<String, Object>) map.get("country");
                        if (countryMap.get("id") != null) {
                            Long countryId = Long.valueOf(countryMap.get("id").toString());
                            officeStaff.setCountry(
                                    Setup.getRepository(PicklistItemRepository.class).findOne(countryId)
                            );
                        }
                    }

                    // handle city
                    if (objectNode.has("city"))
                        objectNode.remove("city");

                    if (selfReceiver && map != null && map.containsKey("city") && map.get("city") != null) {
                        Map<String, Object> cityMap = (Map<String, Object>) map.get("city");

                        if (cityMap.get("id") != null) {
                            Long cityId = Long.valueOf(cityMap.get("id").toString());
                            officeStaff.setCity(
                                    Setup.getRepository(PicklistItemRepository.class).findOne(cityId)
                            );
                        }
                    }
                    if(transferDestination.getSelfReceiver()) {
                        officeStaff = updateOfficeStaffData(objectNode, officeStaff, map);

                    }

                    officeStaff = Setup.getRepository(OfficeStaffRepository.class)
                            .save(officeStaff);
                }

                normalizePhoneNumber(transferDestination);

                if (objectNode.has("officeStaff")) objectNode.remove("officeStaff");

                transferDestination = Setup.getRepository(TransferDestinationRepository.class).save(transferDestination);
                officeStaff.setSelectedTransferDestination(transferDestination);

                officeStaff = Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);

                // Create ChangeBankInfoHistoryLog record when new TransferDestination is selected
                if(officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI) || officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT)){

                    // Create history
                    ChangeBankInfoHistoryLog bankHistoryLog = new ChangeBankInfoHistoryLog(
                            officeStaff,
                            transferDestination.getReceiveMoneyMethod(),
                            officeStaff.getVisaNewRequest().getEmployeeAccountWithAgent(),
                            (transferDestination.getReceiveMoneyMethod() != null && transferDestination.getReceiveMoneyMethod().equals(ReceiveMoneyMethod.BANK_TRANSFER)) ? transferDestination.getIban() : "DXB-LULU BARSHA",
                            transferDestination.getBankName(),
                            BankInfoChangeStatus.PENDING
                    );

                    bankHistoryLog = Setup.getRepository(ChangeBankInfoHistoryLogRepository.class).save(bankHistoryLog);

                }
                return new ResponseEntity<>("Transfer Destination is created successfully.", HttpStatus.OK);

            } else {
                return new ResponseEntity<>("Transfer destination Could not be created because payroll is locked.", HttpStatus.BAD_REQUEST);
            }
        }else {
            return unauthorizedReponse();
        }
    }

    public void normalizePhoneNumber(TransferDestination destination) {
        if (!destination.getSelfReceiver()
                && destination.getPhoneNumber() != null
                && !destination.getPhoneNumber().isEmpty()) {

            String normalized = PhoneNumberUtil.normalizeInternationalPhoneNumber(destination.getPhoneNumber());
            if (!normalized.equals(destination.getPhoneNumber()))
                destination.setPhoneNumber(normalized);
        }
    }

    public OfficeStaff updateOfficeStaffData(ObjectNode objectNode, OfficeStaff officeStaff, Map<String, Object> map){
        // handle fullNameInArabic
        if (objectNode.has("fullNameInArabic"))
            objectNode.remove("fullNameInArabic");

        if (map != null && map.containsKey("fullNameInArabic") && map.get("fullNameInArabic") != null) {
            officeStaff.setFullNameInArabic((String) map.get("fullNameInArabic"));
        }

        // handle name
        if (objectNode.has("name"))
            objectNode.remove("name");

        if (map != null && map.containsKey("name") && map.get("name") != null) {
            officeStaff.setName((String) map.get("name"));
        }

        // handle phoneNumber
        if (objectNode.has("phoneNumber"))
            objectNode.remove("phoneNumber");

        if (map != null && map.containsKey("phoneNumber") && map.get("phoneNumber") != null) {
            officeStaff.setPhoneNumber((String) map.get("phoneNumber"));
        }

        // handle email
        if (objectNode.has("email"))
            objectNode.remove("email");

        if (map != null && map.containsKey("email") && map.get("email") != null) {
            officeStaff.setEmail((String) map.get("email"));
        }

        // handle cityName
        if (objectNode.has("cityName"))
            objectNode.remove("cityName");

        if (map != null && map.containsKey("cityName") && map.get("cityName") != null) {
            officeStaff.setCityName((String) map.get("cityName"));
        }

        // handle fullAddress
        if (objectNode.has("fullAddress"))
            objectNode.remove("fullAddress");


        if (map != null && map.containsKey("fullAddress") && map.get("fullAddress") != null) {
            officeStaff.setFullAddress((String) map.get("fullAddress"));
        }

        return officeStaff;
    }

    public void checkDuplicateIBanDetection(OfficeStaff officeStaff, TransferDestination transferDestination){
        if (officeStaff != null && OfficeStaffStatus.ACTIVE.equals(officeStaff.getStatus()) &&
                (OfficeStaffType.DUBAI_STAFF_EMARATI.equals(officeStaff.getEmployeeType()) ||
                        OfficeStaffType.DUBAI_STAFF_EXPAT.equals(officeStaff.getEmployeeType()))) {
            officestaffPayrollController.duplicateIBanDetection(officeStaff, transferDestination);
        }
    }
}
