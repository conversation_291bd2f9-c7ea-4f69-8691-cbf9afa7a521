package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.projection.IdLabel;
import com.magnamedia.core.repository.BaseAdditionalInfoRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.projection.HousemaidExcludeHistoryProjection;
import com.magnamedia.entity.projection.HousemaidList;
import com.magnamedia.entity.projection.HousemaidRepaymentProjectionClassV2;
import com.magnamedia.entity.projection.HousemaidStatusDateProjection;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import com.magnamedia.scheduledjobs.HousemaidStartDateCorrectSchedualedJob;
import com.magnamedia.scheduledjobs.NoKidsDeductionSchedualedJob;
import com.magnamedia.service.HousemaidUnpaidDayService;
import com.magnamedia.service.ManagerNoteService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import com.magnamedia.service.payroll.generation.newversion.ProRatedSalariesService;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@RequestMapping("/housemaid")
@RestController
public class HousemaidController extends BaseRepositoryController<Housemaid> {

    @Autowired
    private HousemaidRepository RepHousemaid;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private ProRatedSalariesService proRatedSalariesService;

    @Autowired
    private HousemaidUnpaidDayService housemaidUnpaidDayService;

    private HousemaidStartDateCorrectSchedualedJob housemaidStartDateCorrectJob;
    
    private NoKidsDeductionSchedualedJob noKidsDeductionSchedualedJob;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    RepaymentRepository repaymentRepository;

    @Autowired
    EmployeeLoanRepository employeeLoanRepository;

    @Autowired
    PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    RepaymentController repaymentController;

    @Autowired
    private UnpaidDeductionRepaymentRepository unpaidDeductionRepaymentRepository;

    @Autowired
    private HousemaidExcludeHistoryRepository housemaidExcludeHistoryRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private BaseAdditionalInfoRepository baseAdditionalInfoRepository;

    @Override
    public BaseRepository<Housemaid> getRepository() {
        return RepHousemaid;

    }

    //Jirra ACC-1087
    @PreAuthorize("hasPermission('housemaid','searchHousemaid')")
    @RequestMapping(value = "/searchHousemaid", method = RequestMethod.GET)
    protected ResponseEntity<?> searchHousemaid(
            @RequestParam(name = "search",required = false) String searchQuery,
            @RequestParam(name = "status",required = false) HousemaidStatus status,
            Pageable pageable){
        
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if(searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("name", "LIKE", "%"+searchQuery+"%");
        if(searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("status", "=", status);
        query.sortBy("name", true, false);
        
        return new ResponseEntity<>(
                query.execute(pageable)
                .map(maid -> projectionFactory.createProjection(HousemaidProjection.class,
                maid)), HttpStatus.OK);
        }

    //Jirra ACC-1087
    @PreAuthorize("hasPermission('housemaid','searchActiveHousemaid')")
    @RequestMapping(value = "/searchActiveHousemaid", method = RequestMethod.GET)
    protected ResponseEntity<?> searchActiveHousemaid(
            @RequestParam(name = "search",required = false) String searchQuery,
            Pageable pageable){
        
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        if(searchQuery != null && !searchQuery.isEmpty())
            query.filterBy("name", "LIKE", "%"+searchQuery+"%");
        query.filterBy("landedInDubaiDate", "IS NOT NULL", null);
        query.sortBy("name", true, false);
        
        return new ResponseEntity<>(
                query.execute(pageable)
                .map(maid -> projectionFactory.createProjection(HousemaidProjection.class,
                maid)), HttpStatus.OK);
        }
    
    public interface HousemaidProjection{
        Long getId();
        String getLabel();
    }
    
    @PreAuthorize("hasPermission('housemaid','findByStatus')")
    @RequestMapping(value = "/findbystatus/{status}",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> findByStatus(@PathVariable HousemaidStatus status) {

        List<Housemaid> hs = RepHousemaid.findByStatus(status);
       
        return new ResponseEntity<>(project(hs, IdLabel.class),
                HttpStatus.OK);
    }
    
    @NoPermission
    @RequestMapping(value = "/runhousemaidstartdatecorrect", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> runHousemaidStartDateCorrect() {
        housemaidStartDateCorrectJob = new HousemaidStartDateCorrectSchedualedJob();
        housemaidStartDateCorrectJob.run();
       
        return new ResponseEntity<>("", HttpStatus.OK);
    }
    
    @NoPermission
    @RequestMapping(value = "/runnokidsdeductionschedualedjob",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> runNoKidsDeductionSchedualedJob(
            @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(required = false, value="onlyemail") Boolean onlyEmail) {

        if(onlyEmail==null)
            onlyEmail = false;
        
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }
        
        LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(dt);
        LocalDate payrollEnd = PayrollGenerationLibrary.getPayrollEndDate(dt).plusDays(1);
        noKidsDeductionSchedualedJob = new NoKidsDeductionSchedualedJob();
        noKidsDeductionSchedualedJob.getNoKidsDeductionAmounts(
                payrollStart.toDate(), payrollEnd.toDate(), onlyEmail);
       
        return new ResponseEntity<>("", HttpStatus.OK);
    }
    
    
    @PreAuthorize("hasPermission('housemaid','getallmaidtypes')")
    @RequestMapping(value = "/getallmaidtypes")
    public ResponseEntity<?> getAllMaidTypes(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(HousemaidType.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x.toString()))
                        .filter(x -> search==null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }
    
    //Jirra ACC-684 ACC-1515
    @PreAuthorize("hasPermission('housemaid','advancesearch')")
    @RequestMapping(value = "/advancesearch/page",
            method = RequestMethod.GET)
    @Searchable(fieldName = "id",
                            label = "Housemaid ID",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "name",
                            label = "Housemaid Name",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "creationDate",
                            label = "Creation Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "basicSalary",
                            label = "Basic Salary",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "startDate",
                            label = "Start Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isAgency",
                            label = "Agency Maid",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "freedomMaid",
                            label = "Freedom Maid",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "housemaidType",
                            label = "Housemaid Type",
                            entity = Housemaid.class,
                            valuesApi = "/payroll/housemaid/getallmaidtypes",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "contracts.status",
                            label = "Contract status",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "contracts.contractProspectType",
                            label = "Contract Type",
                            entity = Housemaid.class,
                            valuesApi = "/public/picklist/items/ProspectType",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "nationality",
                            label = "Nationality",
                            entity = Housemaid.class,
                            valuesApi = "/public/picklist/items/nationalities",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "notArabicSpeaker",
                            label = "Not Arabic Speaker",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "freedomOperator",
                            label = "Freedom Operator",
                            entity = Housemaid.class,
                            valuesApi = "/payroll/freedomoperator/search/page",
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "freedomOperator.salaryCutOffDate",
                            label = "Freedom Operator Salary CutOff Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "landedInDubaiDate",
                            label = "Landed In Dubai Date",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isBeingPaid50PercentSalary",
                            label = "Is Being Paid 50 Percent Salary",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "oldRenewalBasicSalary",
                            label = "Old Renewal Basic Salary",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "payrollType",
                            label = "Payroll Type",
                            valuesApi = "/public/picklist/items/PAYROLL_TYPE",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isMaidsAt",
                            label = "Maid.at Maid",
                            entity = Housemaid.class,
                            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "isAfricanCandidate",
            label = "Is African Candidate",
            entity = Housemaid.class,
            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "liveOut",
            label = "Live Out",
            entity = Housemaid.class,
            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "location",
            label = "Location",
            entity = Housemaid.class,
            valuesApi = "/public/picklist/items/locations",
            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "landedInDubaiDate",
            label = "First attendance date",
            entity = Housemaid.class,
            apiKey = "housemaid_salaryrules_management")
    @Searchable(fieldName = "travelAssist",
            label = "Travel Assist",
            entity = Housemaid.class,
            apiKey = "housemaid_salaryrules_management")
    public ResponseEntity<?> advanceSearch(
            Pageable pageable) {
        
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy(CurrentRequest.getSearchFilter());
        
        return new ResponseEntity<>(
                query.execute(pageable).map(maid ->
                        projectionFactory.createProjection(HousemaidList.class, maid)),
                HttpStatus.OK);
    }


    private int calculateDays(LocalDate d1, LocalDate d2, LocalDate minDate) {

        int days = minDate.isAfter(d1) ?
                Days.daysBetween(minDate.toDateTimeAtStartOfDay(), d2.toDateTimeAtStartOfDay()).getDays() :
                Days.daysBetween(d1.toDateTimeAtStartOfDay(), d2.toDateTimeAtStartOfDay()).getDays();
        return days + 1;
    }


    //NOT USED ANYMORE
    @Deprecated
    public Map<Long, Integer> computeStatusHistory(List<Long> housemaids, java.sql.Date payrollMonth, Map<Long, Long> vacationDays,
                                                   Map<Long, Date> lastVacationDates) {
        int minDay = 1;
        LocalDate maximumDate = new LocalDate(payrollMonth).dayOfMonth().withMaximumValue();
        LocalDate minimumDate = new LocalDate(payrollMonth).withDayOfMonth(minDay);

        List<Long> firstRevisions = RepHousemaid.getFirstRevisions(housemaids,  maximumDate.plusDays(1).toDate());

//        DebugHelper.sendMail("<EMAIL>", "Start computing status history for " + housemaids.size() + " housemaids, first revisions of size: " + firstRevisions.size());


        Map<Long, LocalDate> currentLocalDates = new HashMap<>();
        Map<Long, Integer> days = new HashMap<>();
        Map<Long, Integer> lastContradiction = new HashMap<>();

        List<HousemaidStatus> acceptedStatuses =
                Arrays.asList(HousemaidStatus.WITH_CLIENT, HousemaidStatus.ASSIGNED_OFFICE_WORK,
                        HousemaidStatus.ON_VACATION);

        int page = 0;
        while (true) {
            Pageable pageable = PageRequest.of(page, 200);
            List<HousemaidStatusDateProjection> projections = RepHousemaid.getRevisions(housemaids, firstRevisions, maximumDate.plusDays(1).toDate(), pageable);
            if(projections.isEmpty()) break;

            for(HousemaidStatusDateProjection projection: projections) {

                HousemaidStatus currentStatus = projection.getStatus();

                LocalDate currentDate = currentLocalDates.getOrDefault(projection.getId(), maximumDate);

                LocalDate lastLocalDate = new LocalDate(projection.getLastModificationDate());


                // no need to calculate more
                if(minimumDate.isAfter(currentDate)) continue;

                if (currentStatus != null && acceptedStatuses.contains(currentStatus)) {

                    int toStore = days.getOrDefault(projection.getId(), 0);
                    long vacations = vacationDays.getOrDefault(projection.getId(), 0L);

                    int diffInDays = calculateDays(lastLocalDate, currentDate, minimumDate);
                    toStore += diffInDays;
                    vacations += diffInDays;

                    int lastDay = lastContradiction.getOrDefault(projection.getId(), -1);
                    if(lastDay == currentDate.getDayOfMonth()) {
                        toStore -= 1;
                        vacations -= 1;
                    }

                    if(currentStatus == HousemaidStatus.ON_VACATION) {
                        vacationDays.put(projection.getId(), vacations);
                        if(!proRatedSalariesService.isGroupOneVacation(projection.getId(), lastVacationDates)){
                            currentLocalDates.put(projection.getId(), lastLocalDate);
                            continue;
                        }
                    }

                    lastContradiction.put(projection.getId(), lastLocalDate.getDayOfMonth());
                    days.put(projection.getId(), toStore);


                }

                currentLocalDates.put(projection.getId(), lastLocalDate);
            }

            page++;
        }
        for (Long housemaidId : housemaids) {
            Housemaid housemaid = RepHousemaid.findOne(housemaidId);
            Integer group1ForgivenDays = housemaidUnpaidDayService.getForgivenDaysForHousemaidByType(housemaid, payrollMonth, true);
            days.put(housemaidId, days.getOrDefault(housemaidId, 0) + group1ForgivenDays) ;
        }
//        DebugHelper.sendMail("<EMAIL>", "Finish computing status history with size " + days.size());
        return days;
    }

    @Deprecated
    public List<WorkDays> computeStatusHistoryNew(Long housemaidId, java.sql.Date payrollMonth, java.sql.Date from , java.sql.Date to) {
        java.sql.Date payrollMonthStart = payrollMonth;
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
        if (housemaid != null) {
            if (housemaid.getStartDate() != null) {
                LocalDate startDate = new LocalDate(housemaid.getStartDate());
                if (housemaid.getReplacementSalaryStartDate() != null) {
                    startDate = new LocalDate(housemaid.getReplacementSalaryStartDate());
                }

                if (startDate.isAfter(new LocalDate(payrollMonth).withDayOfMonth(1))) {
                    payrollMonthStart = new java.sql.Date(startDate.toDate().getTime());
                }
            }
        }

        java.sql.Date payrollMonthEnd = new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        if (from != null && to != null) {
            payrollMonthStart = from;
            payrollMonthEnd = to;
        }
        List<HousemaidPayrollEvent> liveInOutEvents = Setup.getRepository(HousemaidPayrollEventRepository.class)
                .findByHousemaid_IdAndEndEffectiveDateBetween(housemaidId, payrollMonthStart, payrollMonthEnd);
        HousemaidPayrollEvent firstEventAfterEndOfMonth = Setup.getRepository(HousemaidPayrollEventRepository.class)
                .findTop1ByHousemaid_IdAndEndEffectiveDateGreaterThanEqual(housemaidId, payrollMonthEnd);
        List<WorkDays> allWorkDays = new ArrayList<>();
        List<WorkDays> subWorkDays;
        if (liveInOutEvents != null && !liveInOutEvents.isEmpty()) {
            java.sql.Date start = payrollMonthStart;
            for (HousemaidPayrollEvent event : liveInOutEvents) {
                java.sql.Date end = event.getEndEffectiveDate();
                subWorkDays = computeStatusHistoryNew(housemaidId, start, end);
                fillSalaryValues(subWorkDays, event);
                allWorkDays.addAll(subWorkDays);
                start = DateUtil.addDaysSql(end, 1);
            }
            if (payrollMonthEnd.after(start)) {
                subWorkDays = computeStatusHistoryNew(housemaidId, start, payrollMonthEnd);
                if (firstEventAfterEndOfMonth == null) {
                    fillSalaryValues(subWorkDays, housemaidId);
                } else {
                    fillSalaryValues(subWorkDays, firstEventAfterEndOfMonth);
                }

                allWorkDays.addAll(subWorkDays);
            }
        } else {
            subWorkDays = computeStatusHistoryNew(housemaidId, payrollMonthStart, payrollMonthEnd);
            if (firstEventAfterEndOfMonth == null) {
                fillSalaryValues(subWorkDays, housemaidId);
            } else {
                fillSalaryValues(subWorkDays, firstEventAfterEndOfMonth);
            }
            allWorkDays = subWorkDays;
        }
        allWorkDays.sort((o1, o2) -> o2.getTo() - o1.getTo());
        return allWorkDays;
    }

    @Deprecated
    private void fillSalaryValues(List<WorkDays> subWorkDays, HousemaidPayrollEvent event) {
        for (WorkDays workDays: subWorkDays){
            workDays.setBasicSalary(event.getBasicSalary());
            workDays.setPrimarySalary(event.getPrimarySalary());
            workDays.setAccommodationSalary(event.getAccommodationSalary());
            if (PayrollSwitchEventType.LIVE_OUT_TO_LIVE_IN.equals(event.getType())) {
                if (HousemaidSalaryGroup.GROUP_1.equals(workDays.getGroup())){
                    workDays.setGroup(HousemaidSalaryGroup.GROUP_5);
                }
                if (HousemaidSalaryGroup.GROUP_2.equals(workDays.getGroup())){
                    workDays.setGroup(HousemaidSalaryGroup.GROUP_6);
                }
            }
        }
    }
    @Deprecated
    private void fillSalaryValues(List<WorkDays> subWorkDays, Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);
        if (housemaid == null) {
            return;
        }
        for (WorkDays workDays: subWorkDays){
            workDays.setBasicSalary(housemaid.getBasicSalary());
            workDays.setPrimarySalary(housemaid.getPrimarySalary());
            workDays.setAccommodationSalary(housemaid.getAccommodationSalary());
            if (housemaid.getLiveOut()) {
                if (HousemaidSalaryGroup.GROUP_1.equals(workDays.getGroup())){
                    workDays.setGroup(HousemaidSalaryGroup.GROUP_5);
                }
                if (HousemaidSalaryGroup.GROUP_2.equals(workDays.getGroup())){
                    workDays.setGroup(HousemaidSalaryGroup.GROUP_6);
                }
            }
        }
    }

    @Deprecated
    private List<WorkDays> computeStatusHistoryNew(Long housemaidId, java.sql.Date from , java.sql.Date to) {
        Housemaid housemaid = RepHousemaid.findOne(housemaidId);
        LocalDate maximumDate = new LocalDate(to);
        LocalDate minimumDate = new LocalDate(from);

        Long firstRevision = RepHousemaid.getFirstRevisionsByHousemaid(housemaidId,  maximumDate.plusDays(1).toDate());

//        DebugHelper.sendMail("<EMAIL>", "Start computing status history new for housemaid: #" + housemaidId + ", first revisions: #" + firstRevision);


        Map<Long, LocalDate> currentLocalDates = new HashMap<>();
        Integer days = 0;
        Map<Long, Integer> lastContradiction = new HashMap<>();

        List<HousemaidStatus> acceptedStatuses =
                Arrays.asList(HousemaidStatus.WITH_CLIENT, HousemaidStatus.ASSIGNED_OFFICE_WORK,
                        HousemaidStatus.ON_VACATION);

        int page = 0;
        List<WorkDays> allGroupDays = new ArrayList<>();
        while (true) {
            Pageable pageable = PageRequest.of(page, 200);
            List<HousemaidStatusDateProjection> projections = RepHousemaid.getRevisionsByHousemaid(housemaidId, firstRevision, maximumDate.plusDays(1).toDate(), pageable);
            if(projections.isEmpty()) break;

            for(HousemaidStatusDateProjection projection: projections) {

                HousemaidStatus currentStatus = projection.getStatus();

                LocalDate currentDate = currentLocalDates.getOrDefault(projection.getId(), maximumDate);

                LocalDate lastLocalDate = new LocalDate(projection.getLastModificationDate());


                // no need to calculate more
                if(minimumDate.isAfter(currentDate)) continue;

                if (currentStatus != null && acceptedStatuses.contains(currentStatus)) {

//                    int toStore = days;
//                    long vacations = vacationDays.getOrDefault(projection.getId(), 0L);

                    int diffInDays = calculateDays(lastLocalDate, currentDate, minimumDate);
//                    toStore += diffInDays;
//                    vacations += diffInDays;

//                    int lastDay = lastContradiction.getOrDefault(projection.getId(), -1);
//                    if(lastDay == currentDate.getDayOfMonth()) {
//                        toStore -= 1;
//                        vacations -= 1;
//                    }

                    if(currentStatus == HousemaidStatus.ON_VACATION) {
//                        vacationDays.put(projection.getId(), vacations);
                        WorkDays workDays = new WorkDays(currentDate.getDayOfMonth(), minimumDate.isAfter(lastLocalDate) ? minimumDate.getDayOfMonth() : lastLocalDate.getDayOfMonth(), diffInDays, HousemaidSalaryGroup.GROUP_4);
                        allGroupDays.add(workDays);
                        currentLocalDates.put(projection.getId(), lastLocalDate);
                        continue;
                    }

                    WorkDays workDays = new WorkDays(currentDate.getDayOfMonth(), minimumDate.isAfter(lastLocalDate) ? minimumDate.getDayOfMonth() : lastLocalDate.getDayOfMonth(), diffInDays, HousemaidSalaryGroup.GROUP_1);
                    allGroupDays.add(workDays);
//                    lastContradiction.put(projection.getId(), lastLocalDate.getDayOfMonth());
//                    days =  toStore;


                } else {
                    // we will consider those days as group_2 then we will remove (group_3 and forgiven days) days from them
                    int diffInDays = calculateDays(lastLocalDate, currentDate, minimumDate);
                    WorkDays workDays = new WorkDays(currentDate.getDayOfMonth(), minimumDate.isAfter(lastLocalDate) ? minimumDate.getDayOfMonth() : lastLocalDate.getDayOfMonth(), diffInDays, HousemaidSalaryGroup.GROUP_2);
                    allGroupDays.add(workDays);
                }

                currentLocalDates.put(projection.getId(), lastLocalDate);
            }

            page++;
        }

        Setup.getApplicationContext().getBean(PayrollGroupService.class).removeDuplicateDaysFromWorkerDays(allGroupDays);
//        days = Math.toIntExact(getNumberOfDaysByGroup(allGroupDays, HousemaidSalaryGroup.GROUP_1, duplicateDays));
//        vacationDays.put(housemaidId, getNumberOfDaysByGroup(allGroupDays, HousemaidSalaryGroup.GROUP_4, duplicateDays));

//        Integer group1ForgivenDays =  housemaidUnpaidDayService.getForgivenDaysForHousemaidByType(housemaid, from, to, true);
//        DebugHelper.sendMail("<EMAIL>", "Finish computing status history for housemaid: #" + housemaidId + " with group one day: " + days);
//        return days + group1ForgivenDays;
        allGroupDays = allGroupDays.stream().filter(workDays -> workDays.getNumberOfDays() > 0).collect(Collectors.toList());
        return allGroupDays;
    }




    // This version computes housemaid days in group 1 and group 2 within two dates on the same payroll month
//    public Map<Long, Integer> computeStatusHistory(List<Long> housemaids, java.sql.Date fromDate, java.sql.Date toDate, Map<Long, Long> vacationDays,
//                                                   Map<Long, Date> lastVacationDates) {
//
//        LocalDate maximumDate = new LocalDate(toDate);
//        LocalDate minimumDate = new LocalDate(fromDate);
//
//        if(maximumDate.getMonthOfYear() != minimumDate.getMonthOfYear()) {
//            throw new RuntimeException("FromDate and ToDate should be on the same month!");
//        }
//        List<Long> firstRevisions = RepHousemaid.getFirstRevisions(housemaids,  maximumDate.plusDays(1).toDate());
//
////        DebugHelper.sendMail("<EMAIL>", "Start computing status history without on vacation for " + housemaids.size() + " housemaids, first revisions of size: " + firstRevisions.size());
//
//
//        Map<Long, LocalDate> currentLocalDates = new HashMap<>();
//        Map<Long, Integer> days = new HashMap<>();
//        Map<Long, Integer> lastContradiction = new HashMap<>();
//
//        List<HousemaidStatus> acceptedStatuses =
//                Arrays.asList(HousemaidStatus.WITH_CLIENT, HousemaidStatus.ASSIGNED_OFFICE_WORK,
//                        HousemaidStatus.ON_VACATION);
//
//        int page = 0;
//        while (true) {
//            Pageable pageable = PageRequest.of(page, 200);
//            List<HousemaidStatusDateProjection> projections = RepHousemaid.getRevisions(housemaids, firstRevisions, maximumDate.plusDays(1).toDate(), pageable);
//            if(projections.isEmpty()) break;
//
//            for(HousemaidStatusDateProjection projection: projections) {
//
//                HousemaidStatus currentStatus = projection.getStatus();
//
//                LocalDate currentDate = currentLocalDates.getOrDefault(projection.getId(), maximumDate);
//
//                LocalDate lastLocalDate = new LocalDate(projection.getLastModificationDate());
//
//                // no need to calculate more
//                if(minimumDate.isAfter(currentDate)) continue;
//
//                if (currentStatus != null && acceptedStatuses.contains(currentStatus)) {
//
//                    int toStore = days.getOrDefault(projection.getId(), 0);
//                    long vacations = vacationDays.getOrDefault(projection.getId(), 0L);
//
//                    int diffInDays = calculateDays(lastLocalDate, currentDate, minimumDate);
//                    toStore += diffInDays;
//                    vacations += diffInDays;
//
//                    int lastDay = lastContradiction.getOrDefault(projection.getId(), -1);
//                    if(lastDay == currentDate.getDayOfMonth()) {
//                        toStore -= 1;
//                        vacations -= 1;
//
//                    }
//
//                    if(currentStatus == HousemaidStatus.ON_VACATION) {
//                        vacationDays.put(projection.getId(), vacations);
//                        if(!proRatedSalariesService.isGroupOneVacation(projection.getId(), lastVacationDates)){
//                            currentLocalDates.put(projection.getId(), lastLocalDate);
//                            continue;
//                        }
//                    }
//
//                    lastContradiction.put(projection.getId(), lastLocalDate.getDayOfMonth());
//                    days.put(projection.getId(), toStore);
//
//                }
//
//                currentLocalDates.put(projection.getId(), lastLocalDate);
//            }
//
//            page++;
//        }
//        for (Long housemaidId : housemaids) {
//            Housemaid housemaid = RepHousemaid.findOne(housemaidId);
//            Integer group1ForgivenDays = housemaidUnpaidDayService.getForgivenDaysForHousemaidByType(housemaid, fromDate, toDate, true);
//            days.put(housemaidId, days.getOrDefault(housemaidId, 0) + group1ForgivenDays) ;
//        }
////        DebugHelper.sendMail("<EMAIL>", "Finish computing status history without on vacation with size " + days.size());
//        return days;
//    }

//    @NoPermission
//    @RequestMapping(value = "/statusHistory", method = RequestMethod.POST)
//    public ResponseEntity<?> statusHistory(@RequestBody List<Long> housemaids) {
//        return ResponseEntity.ok(computeStatusHistory(housemaids, new java.sql.Date(System.currentTimeMillis()), new HashMap<>(), new HashMap<>()));
//    }


//    @PreAuthorize("hasPermission('HousemaidPayroll','finalSettlement')")
//    @RequestMapping(value = "/finalSettlement/{id}", method = RequestMethod.GET)
//    public ResponseEntity<?> finalSettlement(@PathVariable(name = "id") Housemaid housemaid,
//                                             @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date){
//
//
//        Long previousVacationDays = Setup.getRepository(ExcludedMaidSalaryRepository.class)
//                .getPreviousVacationDays(housemaid.getId(), HousemaidStatus.ON_VACATION);
//
//        if(previousVacationDays == null) previousVacationDays = 0L;
//
//        Map<String, Object> map1 = new HashMap<>(), map2 = new HashMap<>(),
//                result = new HashMap<>();
//
//        Map<Long, Long> onVacation = new HashMap<>();
//
//        LocalDate dt = new LocalDate(date);
//
//        List<HousemaidDateProjection> projections = Setup.getRepository(ScheduledAnnualVacationRepository.class)
//                .findLastVacationDate(Arrays.asList(housemaid.getId()));
//
//        List<HousemaidDateProjection> vacationProjections = Setup.getRepository(HousemaidVacationRepository.class)
//                .findLastVacationDate(Arrays.asList(housemaid.getId()));
//
//        Map<Long, Date> lastVacationDates = new HashMap<>();
//        for(HousemaidDateProjection projection: projections) {
//            lastVacationDates.put(projection.getHousemaid(), projection.getDate());
//        }
//
//        for(HousemaidDateProjection projection: vacationProjections) {
//            Date vacationDate = lastVacationDates.get(projection.getHousemaid());
//            if(vacationDate == null || vacationDate.before(projection.getDate())) vacationDate = projection.getDate();
//
//            lastVacationDates.put(projection.getHousemaid(), vacationDate);
//        }
//
//        if(Setup.getRepository(HousemaidPayrollLogRepository.class)
//        .findTopByHousemaidAndPayrollMonthAndTransferredTrue(housemaid,
//                new java.sql.Date(dt.minusMonths(1).withDayOfMonth(1).toDate().getTime())) == null) {
//
//            java.sql.Date lastPayrollMonth = new java.sql.Date(dt.minusMonths(1)
//                    .withDayOfMonth(1).toDate().getTime());
//
//            int minDay = 1;
//
//            LocalDate maximumDate = new LocalDate(lastPayrollMonth).dayOfMonth().withMaximumValue();
//            LocalDate minimumDate = new LocalDate(lastPayrollMonth).withDayOfMonth(minDay);
//
//
//            Map<Long, Integer> group1Days = computeStatusHistory(Arrays.asList(housemaid.getId()),
//                    new java.sql.Date(minimumDate.toDate().getTime()),
//                    new java.sql.Date(maximumDate.toDate().getTime()),
//                    onVacation,
//                    lastVacationDates);
//
//            map1 = proRatedSalariesService.getSalaryBreakDownForFinalSettlement(housemaid, dt.minusMonths(1).withDayOfMonth(1),
//                    new java.sql.Date(dt.minusMonths(1)
//                            .withDayOfMonth(1).toDate().getTime()),
//                    new java.sql.Date(dt.minusMonths(1)
//                            .dayOfMonth().withMaximumValue().toDate().getTime()),
//                    group1Days.getOrDefault(housemaid.getId(), 0),
//                    previousVacationDays,
//                    onVacation.getOrDefault(housemaid.getId(), 0L),
//                    lastVacationDates);
//        }
//
//        java.sql.Date payrollMonth = new java.sql.Date(dt
//                .withDayOfMonth(1).toDate().getTime());
//
//        int minDay = 1;
//
//        LocalDate minimumDate = new LocalDate(payrollMonth).withDayOfMonth(minDay);
//
//
//        onVacation = new HashMap<>();
//        Map<Long, Integer> group1Days = computeStatusHistory(Arrays.asList(housemaid.getId()),
//                new java.sql.Date(minimumDate.toDate().getTime()),
//                new java.sql.Date(date.getTime()),
//                onVacation,
//                lastVacationDates);
//
//        map2 = proRatedSalariesService.getSalaryBreakDownForFinalSettlement(housemaid, dt.withDayOfMonth(1),
//                new java.sql.Date(dt.withDayOfMonth(1).toDate().getTime()),
//                new java.sql.Date(date.getTime()),
//                group1Days.getOrDefault(housemaid.getId(), 0),
//                previousVacationDays,
//                onVacation.getOrDefault(housemaid.getId(), 0L),
//                lastVacationDates);
//
//
//        if(map1.isEmpty()) {
//            result = map2;
//        } else {
//            result.put("group1Salary", (Double) map1.get("group1Salary") + (Double) map2.get("group1Salary"));
//            result.put("group2Salary", (Double) map1.get("group2Salary") + (Double) map2.get("group2Salary"));
//            result.put("group1Days", (Integer) map1.get("group1Days") + (Integer) map2.get("group1Days"));
//            result.put("group2Days", (Integer) map1.get("group2Days") + (Integer) map2.get("group2Days"));
//            result.put("group3Days", (Integer) map1.get("group3Days") + (Integer) map2.get("group3Days"));
//            result.put("total", (Double) map1.get("total") + (Double) map2.get("total"));
//        }
//        return ResponseEntity.ok(result);
//    }

    @Transactional
    public void updateAccommodationSalaryForHousemaid(Long housemaidId){
        Housemaid housemaid = RepHousemaid.findOne(housemaidId);
        String accommodationSalary = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_RENEWED_PHILIPPINES_HOUSEMAID_IN_ACCOMMODATION_SALARY);
        if(accommodationSalary != null && !accommodationSalary.isEmpty()){
            housemaid.setAccommodationSalary(Double.parseDouble(accommodationSalary));
        }
        RepHousemaid.save(housemaid);
    }

    @Transactional
    @PreAuthorize("hasPermission('HousemaidPayroll','modifyHousemaidSalaryOnNewMvContract')")
    @RequestMapping(value = "/modifyHousemaidSalaryOnNewMvContract", method = RequestMethod.POST)
    public ResponseEntity<?> modifyHousemaidSalaryOnNewMvContract(@RequestParam(value = "contract") Contract contract){

        Double workerSalary = contract.getWorkerSalary();
        Housemaid housemaid = contract.getHousemaid();

        if (housemaid != null && workerSalary != null && housemaid.getBasicSalary() != null) {
            housemaid = RepHousemaid.findOne(housemaid.getId());
            double totalSalary = housemaid.getBasicSalary();
            double primarySalary = housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0;
            double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0.0;
            double holiday = housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0;
            double overtime = housemaid.getOverTime() != null ? housemaid.getOverTime() : 0.0;

            if (workerSalary.compareTo(totalSalary) >= 0) { //add the difference to holiday
                overtime += workerSalary - totalSalary;
            } else if (workerSalary.compareTo(primarySalary + holiday) < 0) { //send an email to wesam to fix the contract
                String subject = "Issue in " + housemaid.getName() + " salary";
                List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAY_FIX_CONTRACT_RECIPIENT));
                Map<String,String > params = new HashMap<>();
                params.put("contract_id", contract.getId().toString());
                params.put("amount", NumberFormatter.formatNumber(primarySalary + holiday));
                params.put("worker_salary", NumberFormatter.formatNumber(workerSalary));
                Setup.getApplicationContext().getBean(MessagingService.class)
                        .send(recipients, null, "Payroll_Fix_MV_Contract_Email", subject
                                , null, null, params, housemaid, null, housemaid);
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Fix_MV_Contract_Email", params);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
            } else { //start reducing the difference from overtime then CA
                double difference = totalSalary - workerSalary;
                double nextDifference = totalSalary - workerSalary - overtime;
                overtime = Math.max(0, overtime - difference);
                if (nextDifference > 0)
                    monthlyLoan = Math.max(0, monthlyLoan - nextDifference);
            }

            housemaid.setOverTime(overtime);
            housemaid.setMonthlyLoan(monthlyLoan);
            // check if this maid has any RecalculateSalaryRequest and delete it
            Setup.getRepository(RecalculateSalaryRequestRepository.class).deleteByHousemaidAndDoneFalse(housemaid);

            RepHousemaid.save(housemaid);
        }

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','getIncludedMaidsForUpcomingPayroll')")
    @RequestMapping(value = "/getIncludedMaidsForUpcomingPayroll", method = RequestMethod.GET)
    public ResponseEntity<?> getIncludedMaidsForUpcomingPayroll(){
        List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(new java.sql.Date(System.currentTimeMillis()), PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        List<Long> includedMaids = new ArrayList<>();
        if (nextPayrollMonthlyRules.size() > 0) {
            List<Housemaid> housemaids = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getIncludedTargetList(nextPayrollMonthlyRules.get(0));
            includedMaids = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
        }

        return ResponseEntity.ok(includedMaids);
    }

    @PreAuthorize("hasPermission('HousemaidPayroll','getIncludedMaidsForNextPayroll')")
    @GetMapping(value = "get-included-maids-for-next-payroll")
    public ResponseEntity<?> getIncludedMaidsForNextPayroll() {
        List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(new java.sql.Date(System.currentTimeMillis()), PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        List<Long> includedMaids = new ArrayList<>();
        if (!nextPayrollMonthlyRules.isEmpty()) {
            List<Housemaid> housemaids = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getIncludedTargetListNew(nextPayrollMonthlyRules.get(0));
            includedMaids = housemaids.stream().map(BaseEntity::getId).collect(Collectors.toList());
        }

        return ResponseEntity.ok(includedMaids);
    }

    @PreAuthorize("hasPermission('housemaid','getSalaryAndLoanAmount')")
    @RequestMapping(value = "/getSalaryAndLoanAmount/{id}",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getSalaryAndLoanAmount(@PathVariable(name = "id") Housemaid housemaid) {

        Map<String, Double> result = new HashMap<>();
        result.put("salary", housemaid.getBasicSalary());
        result.put("loan", housemaid.getLoanBalance());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @Transactional
    public void zeroingHousemaidSalaryComponents(Long housemaidId) {
        Housemaid housemaid = RepHousemaid.findOne(housemaidId);
        if (housemaid != null) {
//            Setup.getApplicationContext().getBean(PayrollGroupService.class).createHousemaidPayrollEvent(housemaid, null);
            housemaid.setAirfareFee(0D);
            housemaid.setHoliday(0D);
            housemaid.setMonthlyLoan(0D);
            housemaid.setOverTime(0D);
            housemaid.setPrimarySalary(0D);
            housemaid.setAccommodationSalary(0D);
            // moved to updateSalaryComponent function
//            if (HousemaidType.MAID_VISA.equals(housemaid.getOldHousemaidType()) && !HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())) {
//                housemaid.setReplacementSalaryStartDate(null);
//            }
            RepHousemaid.save(housemaid);
        }
    }

    // used as BGT
    @Transactional
    public void updateSalaryComponent(Long housemaidId, String switchingCase) {
        Housemaid housemaid = RepHousemaid.findOne(housemaidId);
        if (housemaid.getBasicSalary() == null || housemaid.getBasicSalary() == 0.0) {
            return;
        }

        //Validate duplicated case
        SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);
        query.filterBy("name", "=", "updateSalaryComponent #" + housemaidId);
        query.filterBy("status", "=", BackgroundTaskStatus.Finished);
        query.filterBy("lastModificationDate", ">", DateUtil.addMinutes(new Date(), -5));
        List<BackgroundTask> activeTask = query.execute();
        if (activeTask != null && !activeTask .isEmpty()) {
            logger.info("the maid #" + housemaidId + " already updateSalaryComponent finished");
            return;
        }

        SalaryRule salaryRule = Setup.getApplicationContext().getBean(SalaryRuleController.class)
                .getRuleRelatedToHousemaid(housemaidId);
        if (salaryRule == null) {
            logger.info("the maid #" + housemaidId + " No Rule is found for her");
            return;
        } else
            logger.info("the maid #" + housemaidId + " Is related to the Rule " + salaryRule.getName() + " #" + salaryRule.getId());

        List<SalaryRuleDetails> ruleDetails = salaryRule.getSalaryRuleDetailses();
        Map<String, Double> components = new HashMap<>();
        for (SalaryRuleDetails details : ruleDetails) {
            if (details.getSalaryComponent() != null && details.getSalaryComponent().getCode() != null) {
                components.put(details.getSalaryComponent().getCode().toLowerCase(), details.getValue());
            }
        }

        PayrollSwitchEventType switchType = PayrollSwitchEventType.valueOf(switchingCase);
        PayrollGroupService payrollGroupService = Setup.getApplicationContext().getBean(PayrollGroupService.class);
        if (Arrays.asList(PayrollSwitchEventType.MV_TO_CC_LIVE_OUT, PayrollSwitchEventType.MV_TO_CC_LIVE_IN).contains(switchType)){
            // case maid switched from mv to cc from profile
            housemaid.setReplacementSalaryStartDate(null);
            housemaid = RepHousemaid.save(housemaid);
        }
        payrollGroupService.applyComponentRule(housemaid, components, switchType);

    }

    // used As BGT
    @Transactional
    public Boolean calculateSalaryBeforeStartDate(Long housemaidId, Date noteDate) {
        Housemaid housemaid = RepHousemaid.findOne(housemaidId);
        if (housemaid == null) {
            logger.info("there is no maid with id #" + housemaidId);
            return true;
        }

        Setup.getApplicationContext().getBean(PayrollGroupService.class).checkOfficeWorkDaysBeforeStartDate(housemaid, noteDate);
        return true;

    }

    @PreAuthorize("hasPermission('housemaid', 'searchHousemaidByName')")
    @RequestMapping(value = "/searchHousemaidByName", method = RequestMethod.GET)
    protected ResponseEntity<?> searchHousemaidByName(
            @RequestParam(name = "search", required = false) String searchQuery,
            @RequestParam(name = "housemaidId", required = false) Long housemaidId,
            Pageable pageable) {

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);

        if (searchQuery != null && !searchQuery.isEmpty()) {
            query.filterBy("name", "LIKE", "%" + searchQuery + "%");
        }

        if (housemaidId != null) {
            query.filterBy("id", "=", housemaidId);
        }

        query.sortBy("name", true, false);

        return new ResponseEntity<>(
                query.execute(pageable)
                        .map(maid -> projectionFactory.createProjection(HousemaidProjection.class, maid)),
                HttpStatus.OK);
    }


    @Transactional
    public void updateHousemaidCompanySalary(Long housemaidId, Double newSalary, Long userId) {
        newSalary = newSalary != null ? newSalary : 0.0;
        Housemaid housemaid = RepHousemaid.findOne(housemaidId);
        User user = userRepository.findOne(userId);
        if (housemaid != null) {
            Double primarySalary = housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0;
            Double holiday = housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0;
            Double overTime = housemaid.getOverTime() != null ? housemaid.getOverTime() : 0.0;
            Double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0.0;

            Double currentSalary = housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0.0;
            Double difference = newSalary - currentSalary;

            // if difference > 0 --> directly add it to the over time component
            if (difference >= 0) {
                housemaid.setOverTime(difference + overTime);
            } else {
                if (newSalary < (primarySalary + holiday)) {
                    String subject = "Worker salary is less than Basic salary and holiday for the maid - " + housemaid.getId();
                    List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AFTER_NEW_SALARY_EXCEPTION_RECIPIENT));
                    Map<String, String> params = new HashMap<>();
                    params.put("user_name", user != null ? user.getName() : "");
                    params.put("new_salary", NumberFormatter.formatNumber(newSalary));
                    Setup.getApplicationContext().getBean(MessagingService.class)
                            .send(recipients, null, "Payroll_After_New_Salary_Exception_Template", subject
                                    , params, null, housemaid);
                } else {
                    double temp = -difference;

                    //first delete the maximum value from Overtime
                    if (overTime - temp >= 0) {
                        housemaid.setOverTime(overTime - temp);
                        temp = 0;
                    } else {
                        temp -= overTime;
                        housemaid.setOverTime(0.0);
                    }

                    //second delete the rest value from monthly loan
                    if (monthlyLoan - temp >= 0) {
                        housemaid.setMonthlyLoan(monthlyLoan - temp);
                    } else {
                        housemaid.setMonthlyLoan(0.0);
                    }

                }
            }
            RepHousemaid.save(housemaid);
        }
    }

    @PreAuthorize("hasPermission('housemaid', 'getHousemaidSalary')")
    @RequestMapping(value = "/getHousemaidSalary", method = RequestMethod.GET)
    public ResponseEntity<?> getHousemaidSalary(
            @RequestParam(name = "contractId", required = false) Long contractId,
            @RequestParam(name = "mobileNumber", required = false) String mobileNumber)
    {
        Housemaid housemaid = null;
        Map<String, Object> result = new HashMap<>();

        // Check if contractId is provided and try to retrieve the contract
        if (contractId != null) {
            Contract contract = contractRepository.findById(contractId).orElse(null);

            // If contract exists, check if a housemaid is linked to it
            if (contract != null && contract.getHousemaid() != null) {
                housemaid = contract.getHousemaid();
            }
        }

        // If housemaid not found by contractId, check by mobile number
        if (housemaid == null && mobileNumber != null) {
            List<Housemaid> housemaids = RepHousemaid.findByNormalizedPhoneNumberOrPhoneNumberOrNormalizedWhatsAppPhoneNumberOrWhatsAppPhoneNumber(
                    mobileNumber, mobileNumber, mobileNumber, mobileNumber);
            if(housemaids != null && !housemaids.isEmpty()){
                housemaid = housemaids.get(0);
            }
        }

        // Check if still no housemaid found
        if (housemaid == null)
        {
            result.put("maidName", "No maid found");
            result.put("maidSalary", 0);

            return new ResponseEntity<>(result, HttpStatus.OK);
        }


        result.put("maidName", housemaid.getName());
        result.put("maidSalary", housemaid.getBasicSalary());

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('housemaid','getHousemaidsPayrollInfo')")
    @RequestMapping(value = "/getHousemaidsPayrollInfo", method = RequestMethod.GET)
    protected ResponseEntity<?> getHousemaidsPayrollInfo(
            @RequestParam(name = "mobileNumber",required = false) String mobileNumber,
            @RequestParam(name = "eidNumber",required = false) String eidNumber,
            @RequestParam(name = "firstName",required = false) String firstName,
            @RequestParam(name = "middleName",required = false) String middleName,
            @RequestParam(name = "lastName",required = false) String lastName,
            @RequestParam(name = "clientContractID",required = false) String contractId) {

        // result map to store payroll information
        Map<String, Object> result = new HashMap<>();

        // Retrieve the housemaid using provided details
        List<Housemaid> housemaids = getHousemaid(mobileNumber, eidNumber, firstName, middleName, lastName, contractId, null);

        // If not found, retry with formatted EID number
        if(housemaids.size() == 0 && eidNumber != null && !eidNumber.isEmpty()){
            eidNumber = eidNumber.replace("-", ""); // Removes dashes from the eidNumber
            housemaids = getHousemaid(mobileNumber, eidNumber, firstName, middleName, lastName, contractId, null);
        }

        // if housemaid not found
        if(housemaids.size() == 0){
            result.put("no_maids", true);
            return ResponseEntity.ok(result);
        }
        result.put("no_maids", false);

        result.put("multiple_maids",housemaids.size() > 1);
        Housemaid housemaid = housemaids.stream().filter(x -> !Housemaid.rejectedStatuses.contains(x.getStatus())).findFirst().orElse(housemaids.get(0));

        // Fetch the 2 latest Payroll History entries for the Maid
        List<HousemaidPayrollLog> last2HousemaidPayrollLogs = housemaidPayrollLogRepository.findTop2ByHousemaidOrderByCreationDateDesc(housemaid);
        String maidPayrollReport = "";
        Double additionAmount = 0.0;

        if (last2HousemaidPayrollLogs != null && !last2HousemaidPayrollLogs.isEmpty()) {

            HousemaidPayrollLog lastLog = last2HousemaidPayrollLogs.get(0);
            if(lastLog.getTotalAddition() != null)
                additionAmount = lastLog.getTotalAddition() ;
            if(lastLog.getTotalEarnings() != null)
                additionAmount -= lastLog.getTotalEarnings();

            StringBuilder reportBuilder = new StringBuilder();

            for (HousemaidPayrollLog log : last2HousemaidPayrollLogs) { // Process only the fetched entries
                try {
                    if (Boolean.TRUE.equals(log.getTransferred())) {
                        reportBuilder.append(String.format("%s salary was paid on %s",
                                        DateUtil.formatFullMonthFullYear(log.getPayrollMonth()),
                                        log.getPaidOnDate() != null && !log.getPaidOnDate().isEmpty() ? log.getPaidOnDate() : DateUtil.formatFullDate(log.getLastModificationDate()))
                                ).append(" / ");
                    } else {
                        reportBuilder.append(String.format("%s salary is pending payment because it was %s",
                                        DateUtil.formatFullMonthFullYear(log.getPayrollMonth()),
                                        (log.getPaidOnStatus() != null ? log.getPaidOnStatus() : "")))
                                .append(" / ");
                    }
                } catch (Exception ex) {
                    Logger.getLogger(HousemaidController.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            if(reportBuilder.length() > 0)
                maidPayrollReport = reportBuilder.toString().replaceAll(" / $", ""); // Remove / at the end of the String
        }

        result.put("additionAmount", NumberFormatter.formatNumberWithoutCommas(additionAmount));
        result.put("maidPayrollReport", maidPayrollReport);

        Date previousMonth = DateUtil.addMonths(new Date(), -1);
        Date prevMonth1stDay = new LocalDate(previousMonth).withDayOfMonth(1).toDateTimeAtStartOfDay().toDate();

        Boolean loansPaidCurrentMonth = false;
        String amountPaid = null;
        List<Repayment> repayments = repaymentRepository.findByHousemaidAndRepaymentDate(housemaid, prevMonth1stDay, DateUtil.getEndOfMonthDate(prevMonth1stDay));
        if(repayments.size() > 0) {
            double repaymentSum = 0.0;
            loansPaidCurrentMonth = true;
            for (Repayment repayment : repayments){
                repaymentSum += repayment.getAmount();
            }
            amountPaid = NumberFormatter.formatNumber(repaymentSum);
        }

        result.put("loansPaidCurrentMonth", loansPaidCurrentMonth);
        result.put("amountPaid", amountPaid);

        Double outstandingBalance = 0.0;
        if(housemaid.getLoanBalance() != null && housemaid.getLoanBalance() > 0) {
            outstandingBalance = housemaid.getLoanBalance();
        }
        result.put("outstandingBalance", NumberFormatter.formatNumberWithoutCommas(outstandingBalance));


        Double unpaidDeductions = 0.0;
        if(housemaid.getUnpaidDeductionBalance() != null) {
            unpaidDeductions = housemaid.getUnpaidDeductionBalance();
        }
        result.put("unpaidDeductions", NumberFormatter.formatNumberWithoutCommas(unpaidDeductions));

        Boolean eidReplacementDeduction = false;
        if(!unpaidDeductions.equals(0.0)){
            eidReplacementDeduction = true;
        }
        result.put("eidReplacementDeduction", eidReplacementDeduction);

        Double remainingBalance = outstandingBalance + unpaidDeductions;
        result.put("remainingBalance", NumberFormatter.formatNumberWithoutCommas(remainingBalance));

        //Loans related Info
        List<EmployeeLoan> loans = employeeLoanRepository.findByHousemaid(housemaid);

        // Set number of loans value
        int numberOfLoans = 0;
        if(!loans.isEmpty()) numberOfLoans = loans.size();

        result.put("numberOfLoans", String.valueOf(numberOfLoans));

        Boolean outsideCountrySalaryScheme = false;
        String loansReport = "";
        StringBuilder loansBuilder = new StringBuilder();

        Double outsideCountrySalarySchemeValue = 0.0;

        for(EmployeeLoan loan : loans){
            // add Exit_Loan amount to outsideCountrySalarySchemeValue & don't consider Exit_Loan in loansBuilder
            if (AbstractEmployeeLoan.LoanType.EXIT_LOAN.equals(loan.getLoanType())) {
                outsideCountrySalaryScheme = true;

                if(loan.getAmount() != null)
                    outsideCountrySalarySchemeValue += loan.getAmount();

            } else {
                loansBuilder.append(String.format("%sAED loan of type %s recorded on %s",
                                NumberFormatter.formatNumber(loan.getAmount()),
                                loan.getLoanType().toString(),
                                DateUtil.formatDateSlashed(loan.getLoanDate())))
                        .append(" , ");
            }
        }
        if(loansBuilder.length() > 0)
            loansReport = loansBuilder.toString().replaceAll(" , $", ""); // Remove comma at the end of the String
        result.put("loansReport", loansReport);


        result.put("outsideCountrySalaryScheme", outsideCountrySalaryScheme);
        result.put("outsideCountrySalarySchemeValue", NumberFormatter.formatNumberWithoutCommas(outsideCountrySalarySchemeValue));

        Double loansWithoutDeductions = 0.0;

        // Sum the `amount` of each loan in the `loans` list
        if (loans != null && !loans.isEmpty()) {
            loansWithoutDeductions = loans.stream().filter(x -> x.getLoanDate().compareTo(prevMonth1stDay) >= 0 && x.getLoanDate().compareTo(DateUtil.getEndOfMonthDate(prevMonth1stDay)) <= 0)
                    .mapToDouble(EmployeeLoan::getAmount)
                    .sum();
        }
        result.put("loansWithoutDeductions", NumberFormatter.formatNumber(loansWithoutDeductions));

        Double unpaidLastMonth = remainingBalance - loansWithoutDeductions;
        result.put("unpaidLastMonth", NumberFormatter.formatNumberWithoutCommas(unpaidLastMonth));

        if (housemaid.getExcludedFromPayroll() != null) {
            result.put("ExcludedFromPayroll", housemaid.getExcludedFromPayroll());
        } else {
            result.put("ExcludedFromPayroll", "");
        }

        List<HousemaidExcludeHistoryProjection> entities = housemaidExcludeHistoryRepository.getHousemaidExcludeHistoriesOrderByIdAsc(housemaid);

        String payrollExclusionHistory = entities.stream()
                .map(entity -> String.format(
                        "%sd on %s, note: %s",
                        entity.getAction() != null ? entity.getAction() : "",
                        entity.getActionDate() != null ? DateUtil.formatDateDashed(entity.getActionDate()) : "",
                        entity.getNotes() != null ? entity.getNotes() : ""
                ))
                .collect(Collectors.joining(" / ")); // Join entries with " / "

        result.put("PayrollExclusionHistory", payrollExclusionHistory);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    // This method retrieves additional payroll information for a housemaid based on optional search parameters.
    @PreAuthorize("hasPermission('housemaid','getAdditionalPayrollInfo')")
    @RequestMapping(value = "/getAdditionalPayrollInfo", method = RequestMethod.GET)
    protected ResponseEntity<?> getAdditionalPayrollInfo(
            @RequestParam(name = "mobileNumber",required = false) String mobileNumber,
            @RequestParam(name = "eidNumber",required = false) String eidNumber,
            @RequestParam(name = "firstName",required = false) String firstName,
            @RequestParam(name = "middleName",required = false) String middleName,
            @RequestParam(name = "lastName",required = false) String lastName,
            @RequestParam(name = "contractID",required = false) String contractID,
            @RequestParam(name = "clientNumber",required = false) String clientNumber)
    {
        // result map to store payroll information
        Map<String, Object> result = new HashMap<>();

        // Retrieve the housemaid using provided details
        List<Housemaid> housemaids = getHousemaid(mobileNumber, eidNumber, firstName, middleName, lastName, contractID, clientNumber);

        // If not found, retry with formatted EID number
        if(housemaids.size() == 0 && eidNumber != null && !eidNumber.isEmpty()){
            eidNumber = eidNumber.replace("-", ""); // Removes dashes from the eidNumber
            housemaids = getHousemaid(mobileNumber, eidNumber, firstName, middleName, lastName, contractID, clientNumber);
        }

        // if housemaid not found
        if(housemaids.size() == 0){
            result.put("no_maids", true);
            return ResponseEntity.ok(result);
        }
        result.put("no_maids", false);

        if(housemaids.size() > 1)
            result.put("multipleMaids", true);
        else
           result.put("multipleMaids", false);

        Housemaid housemaid = housemaids.stream().filter(x -> !Housemaid.rejectedStatuses.contains(x.getStatus())).findFirst().orElse(housemaids.get(0));

        //Add salary details (MOHRE and company salary) to the result map
        result.put("MOHREsalary", housemaid.getPrimarySalary() != null ? NumberFormatter.formatNumber(housemaid.getPrimarySalary()) : "");
        result.put("companySalary", housemaid.getBasicSalary() != null ? NumberFormatter.formatNumber(housemaid.getBasicSalary()) : "");

        //Add employeeAccountWithAgent to the result map
        result.put("accountWithAgentNumber", (housemaid.getVisaNewRequest() != null
                && housemaid.getVisaNewRequest().getEmployeeAccountWithAgent() != null)
                    ? housemaid.getVisaNewRequest().getEmployeeAccountWithAgent()
                    : "");

        //Retrieve the most recent payroll manager note with airfare tickets addition reason
        // & add its note date to the result map.
        PicklistItem additionReason =PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_AIRFARE_TICKET_ADDITION_CODE);
        String noteReasone = "Auto added by the system when the housemaid passes Upload The e-Residency step";
        PayrollManagerNote note = payrollManagerNoteRepository.findTopByHousemaidAndAdditionReasonAndNoteReasoneOrderByIdDesc(housemaid,
                additionReason,
                noteReasone
                );

        if(note != null)
            result.put("managerNoteTicketDate", note.getNoteDate() != null ? note.getNoteDate() : "");
        else
            result.put("managerNoteTicketDate", "");

        //Retrieve and format all manager notes for the housemaid. & add managerNotesDetails to the result map.
        List<PayrollManagerNote> managerNotes = payrollManagerNoteRepository.findAllByHousemaidWithoutZeroDeductions(housemaid, AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION);
        StringBuilder managerNotesDetails = new StringBuilder();

        if (managerNotes != null && !managerNotes.isEmpty()) {
            for (PayrollManagerNote managerNote : managerNotes) {

                PicklistItem reason = null;
                if (managerNote.getNoteType() != null && managerNote.getNoteType().equals(AbstractPayrollManagerNote.ManagerNoteType.ADDITION))
                    reason = managerNote.getAdditionReason();
                else if(managerNote.getNoteType() != null && managerNote.getNoteType().equals(AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION)){
                    reason = managerNote.getDeductionReason();
                }

                // Format manager note details.
                managerNotesDetails.append(String.format(
                        "Manager added a note of type %s, amount %s, reason %s on %s. Additional purpose: %s. Additional note reason: %s /// ",
                        managerNote.getNoteType(),
                        managerNote.getAmount() != null ? "AED " + NumberFormatter.formatNumber(managerNote.getAmount()) : "",
                        reason != null ? reason : "",
                        managerNote.getNoteDate() != null ? DateUtil.formatFullDate(managerNote.getNoteDate()) : "",
                        managerNote.getPurpose() != null ? managerNote.getPurpose().getName() : "",
                        managerNote.getNoteReasone() != null ? managerNote.getNoteReasone() : ""
                )); // Append a /// and space for separation
            }
            // Remove the trailing /// and space, if needed
            if (managerNotesDetails.length() > 0) {
                managerNotesDetails.setLength(managerNotesDetails.length() - 4);

                result.put("managerNotesDetails", managerNotesDetails.toString());
            }
        }else{
            result.put("managerNotesDetails", "");
        }

        // Retrieve repayment details, format them & add outstandingBalanceRepaymentsDetails to the result map.
        List<HousemaidRepaymentProjectionClassV2> repayments = repaymentController.mergeRepaymentWithUnpaidDeductionsRepayments(repaymentRepository.findRepaymentsByHousemaid(housemaid),
                unpaidDeductionRepaymentRepository.findUnpaidDeductionsByHousemaid(housemaid));
        if (repayments != null && !repayments.isEmpty()) {

            StringBuilder outstandingBalanceRepaymentsDetails = new StringBuilder();

            for (HousemaidRepaymentProjectionClassV2 repayment : repayments) {
                // Format repayment details.
                String formattedRepayment = String.format(
                        "In %s HM paid %s with outstanding balance repayments %s and unpaid deductions %s and the following notes: %s",
                        repayment.getFormattedRepaymentDate() != null ? repayment.getFormattedRepaymentDate() : "",
                        repayment.getAmount() != null ? "AED " + NumberFormatter.formatNumber(repayment.getAmount()) : "",
                        repayment.getLoanRepayment() != null ? "AED " + NumberFormatter.formatNumber(repayment.getLoanRepayment()) : "",
                        repayment.getUnpaidDeductionsRepayments() != null ? NumberFormatter.formatNumber(repayment.getUnpaidDeductionsRepayments()) : "",
                        repayment.getDescription() != null ? repayment.getDescription() : ""
                );
                outstandingBalanceRepaymentsDetails.append(formattedRepayment).append(", ");
            }

            // Remove trailing comma and space, if any.
            if (outstandingBalanceRepaymentsDetails.length() > 0) {
                outstandingBalanceRepaymentsDetails.setLength(outstandingBalanceRepaymentsDetails.length() - 2);
            }
            result.put("outstandingBalanceRepaymentsDetails", outstandingBalanceRepaymentsDetails.toString());
        } else{
            result.put("outstandingBalanceRepaymentsDetails", "");
        }

        //Retrieve the previous month's payroll log and add salary details to the result map.
        Date previousMonth = DateUtil.addMonths(new Date(), -1);
        java.sql.Date prevMonth1stDay = DateUtil.getFirstOfMonthDateSQL(previousMonth);
        java.sql.Date prevMonthLastDay = new java.sql.Date(DateUtil.getEndOfMonthDate(previousMonth).getTime());
        HousemaidPayrollLog log = housemaidPayrollLogRepository.findTopByHousemaidAndPayrollMonthBetweenOrderByCreationDateDesc(housemaid, prevMonth1stDay, prevMonthLastDay);
        if(log != null) {
            result.put("lastMonthNetSalary", log.getTotalSalary() != null ? NumberFormatter.formatNumber(log.getTotalSalary()) : "");
            result.put("recievedSalary", true);
        } else{
            result.put("lastMonthNetSalary", "");
            result.put("recievedSalary", false);
        }

        // Return the response entity containing the result map.
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    protected List<Housemaid> getHousemaid(String mobileNumber, String eidNumber, String firstName, String middleName, String lastName, String contractId, String clientNumber) {
        List<Housemaid> housemaids = new ArrayList<>();

        // Search by mobile number
        if (mobileNumber != null && !mobileNumber.isEmpty()) {
            List<Housemaid> housemaidsList = RepHousemaid.findByNormalizedPhoneNumberOrPhoneNumberOrNormalizedWhatsAppPhoneNumberOrWhatsAppPhoneNumber(
                    mobileNumber, mobileNumber, mobileNumber, mobileNumber);

            if(housemaidsList != null && !housemaidsList.isEmpty()){
                housemaids.add(housemaidsList.get(0));
                return housemaids;
            }
        }

        // Search by EID number
        if (eidNumber != null && !eidNumber.isEmpty()) {
            List<BigInteger> housemaidsIds = RepHousemaid.findByNewEidNumberOrderByCreationDateDesc(eidNumber);
            if(housemaidsIds != null && !housemaidsIds.isEmpty()) {
                housemaids.add(RepHousemaid.findOne(housemaidsIds.get(0).longValue()));
                return housemaids;
            }
        }

        // Search by name
        String name = "";
        if (firstName != null && !firstName.isEmpty())
            name = name.concat(firstName + "%");
        if (middleName != null && !middleName.isEmpty())
            name = name.concat(middleName + "%");
        if (lastName != null && !lastName.isEmpty())
            name = name.concat(lastName + "%");

        if (!name.equals("")) {
            housemaids = RepHousemaid.getHousemaidByNameLike(name);
            if(housemaids != null && !housemaids.isEmpty()){
                return housemaids;
            }
        }

        // Search by contract ID
        Long contractHousemaidId = null;
        if (contractId != null && !contractId.isEmpty()) {
            Contract contract = contractRepository.findOne(Long.valueOf(contractId));
            if (contract != null && contract.getHousemaid() != null) {
                contractHousemaidId = contract.getHousemaid().getId();

                Housemaid housemaid = RepHousemaid.getById(contractHousemaidId);
                if(housemaid != null) {
                    housemaids.add(housemaid);
                    return housemaids;
                }
            }
        }

        if(clientNumber != null && !clientNumber.isEmpty()){
            Client client = null;

            List<Client> clients = clientRepository.findByNormalizedMobileNumberOrMobileNumberOrNormalizedWhatsappNumberOrWhatsappNumber(clientNumber, clientNumber, clientNumber, clientNumber);
            if(clients.isEmpty())
                clients = clientRepository.findByNormalizedSpouseMobileNumberOrSpouseMobileNumberOrSpouseWhatsappNumberOrNormalizedSpouseWhatsappNumber(clientNumber, clientNumber, clientNumber, clientNumber);

            if(!clients.isEmpty())
                client = clients.get(0);

            if(client != null){
                Contract contract = contractRepository.findTop1ByClientOrderByCreationDateDesc(client);
                if (contract != null && contract.getHousemaid() != null) {
                    contractHousemaidId = contract.getHousemaid().getId();

                    Housemaid housemaid = RepHousemaid.getById(contractHousemaidId);
                    if(housemaid != null) {
                        housemaids.add(housemaid);
                        return housemaids;
                    }
                }
            }
        }

        // Return the result (empty list if no match)
        return housemaids;
    }


    @PreAuthorize("hasPermission('housemaid','reduceOverstayFines')")
    @GetMapping("/reduceOverstayFines/{housemaidId}")
    @Transactional
    public ResponseEntity<?> reduceOverstayFines(@PathVariable(name = "housemaidId") Housemaid housemaid,  @RequestParam(name = "reducedAmount") Double reducedAmount){

        if(!(reducedAmount > 0))
            throw new BusinessException("Reduced amount must be greater than zero! ");
        EmployeeLoan employeeLoan =  employeeLoanRepository.findTopByHousemaidAndLoanType(housemaid, AbstractEmployeeLoan.LoanType.OVERSTAY_FINES_FEES);
        if(employeeLoan == null)
            throw new BusinessException("There is no loan of type 'OVERSTAY_FINES_FEES' for this maid!");
        if(reducedAmount > employeeLoan.getAmount())
            throw new BusinessException("Reduced amount is greater than OVERSTAY_FINES_FEES loan amount!");

        //Get housemaid loan balance:
        Double loanBalance = housemaid.getLoanBalance();
        Double addition = reducedAmount - loanBalance;
        if(addition > 0){ // Create manager note of type Addition
           Setup.getApplicationContext().getBean(ManagerNoteService.class).addReducedOverstayFinesAddition(housemaid,addition);
        }
        //Update Overstay Fines Loan amount:
        if (reducedAmount < employeeLoan.getAmount()) { //Partly waived
            if (reducedAmount > loanBalance)
                employeeLoan.setAmount(employeeLoan.getAmount() - loanBalance);
            else
                employeeLoan.setAmount(employeeLoan.getAmount() - reducedAmount);
        } else { //Fully waived
            if (reducedAmount > loanBalance)
                employeeLoan.setAmount(employeeLoan.getAmount() - loanBalance);
            else
                employeeLoan.setAmount(0.0);
        }
        employeeLoanRepository.save(employeeLoan);

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('housemaid','getReplacementSalaryStartDate')")
    @RequestMapping(value = "/getReplacementSalaryStartDate", method = RequestMethod.GET)
    protected ResponseEntity<?> getReplacementSalaryStartDate(
            @RequestParam(name = "mobileNumber",required = false) String mobileNumber,
            @RequestParam(name = "eidNumber",required = false) String eidNumber,
            @RequestParam(name = "firstName",required = false) String firstName,
            @RequestParam(name = "middleName",required = false) String middleName,
            @RequestParam(name = "lastName",required = false) String lastName)
    {
        // result map to store payroll information
        Map<String, Object> result = new HashMap<>();

        // Retrieve the housemaid using provided details
        List<Housemaid> housemaids = getHousemaid(mobileNumber, eidNumber, firstName, middleName, lastName, null, null);

        // If not found, retry with formatted EID number
        if(housemaids.size() == 0 && eidNumber != null && !eidNumber.isEmpty()){
            eidNumber = eidNumber.replace("-", ""); // Removes dashes from the eidNumber
            housemaids = getHousemaid(mobileNumber, eidNumber, firstName, middleName, lastName, null, null);
        }

        // if housemaid not found
        if(housemaids.size() == 0){
            result.put("no_maids", true);
            return ResponseEntity.ok(result);
        }
        result.put("no_maids", false);

        if(housemaids.size() > 1) {
            result.put("multipleMaids", true);
            return ResponseEntity.ok(result);
        }
        result.put("multipleMaids", false);

        Housemaid housemaid = housemaids.get(0);

        result.put("replacementSalaryStartDate", housemaid.getReplacementSalaryStartDate() != null ? DateUtil.formatDateDashed(housemaid.getReplacementSalaryStartDate()) : "");

        // Return the response entity containing the result map.
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('housemaid','shiftMvMaidStartDate')")
    @RequestMapping(value = "/shiftMvMaidStartDate/{id}", method = RequestMethod.GET)
    protected ResponseEntity<?> shiftMvMaidStartDate(@PathVariable(name = "id") Housemaid housemaid,
                                                     @RequestParam(required = false, value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {

        if (housemaid.getReplacementSalaryStartDate() == null && housemaid.getStartDate() == null)
            return new ResponseEntity<>("", HttpStatus.OK);

        Integer monthsNumber = 1;

        JSONArray infoArray;
        BaseAdditionalInfo baseAdditionalInfo;
        if(housemaid.hasBaseAdditionalInfo("payrollShiftedMonths")){
            baseAdditionalInfo =  housemaid.getBaseAdditionalInfo("payrollShiftedMonths");
            infoArray = new JSONArray(baseAdditionalInfo.getInfoValue());
        } else{
            infoArray = new JSONArray();
        }

        if (housemaid.getReplacementSalaryStartDate() != null) {
            Date replacementSalaryStartDate = housemaid.getReplacementSalaryStartDate();

            if (date != null) {
                if (DateUtil.formatYearMonthDashed(replacementSalaryStartDate).equals(DateUtil.formatYearMonthDashed(date)))
                    return new ResponseEntity<>("Done!", HttpStatus.OK);
            }

            // Check if this month was already processed
            for (int i = 0; i < infoArray.length(); i++) {
                JSONObject item = infoArray.getJSONObject(i);
                if (item.getString("infoKey").equals(DateUtil.formatYearMonthDashed(replacementSalaryStartDate))) {
                    return new ResponseEntity<>("Done!", HttpStatus.OK);
                }
            }

            JSONObject dataItem = new JSONObject();
            dataItem.put("infoKey", DateUtil.formatYearMonthDashed(replacementSalaryStartDate));
            dataItem.put("infoValue", "The replacement salary start date is shifted due to the medical check");
            infoArray.put(dataItem);

            if (date != null) {
                Calendar replacementSalaryStartDateCal = Calendar.getInstance();
                replacementSalaryStartDateCal.setTime(replacementSalaryStartDate);
                int day = replacementSalaryStartDateCal.get(Calendar.DAY_OF_MONTH);

                Calendar newCal = Calendar.getInstance();
                newCal.setTime(date);
                newCal.set(Calendar.DAY_OF_MONTH, day);

                housemaid.setReplacementSalaryStartDate(newCal.getTime());
            } else {
                housemaid.setReplacementSalaryStartDate(DateUtil.addMonths(replacementSalaryStartDate, monthsNumber));
            }
        } else if (housemaid.getStartDate() != null) {
            Date startDate = housemaid.getStartDate();

            if (date != null) {
                if (DateUtil.formatYearMonthDashed(startDate).equals(DateUtil.formatYearMonthDashed(date)))
                    return new ResponseEntity<>("Done!", HttpStatus.OK);
            }

            // Check if this month was already processed
            for (int i = 0; i < infoArray.length(); i++) {
                JSONObject item = infoArray.getJSONObject(i);
                if (item.getString("infoKey").equals(DateUtil.formatYearMonthDashed(startDate))) {
                    return new ResponseEntity<>("Done!", HttpStatus.OK);
                }
            }

            JSONObject dataItem = new JSONObject();
            dataItem.put("infoKey", DateUtil.formatYearMonthDashed(startDate));
            dataItem.put("infoValue", "The salary start date is shifted due to the medical check");
            infoArray.put(dataItem);

            if (date != null) {

                Calendar startDateCal = Calendar.getInstance();
                startDateCal.setTime(startDate);
                int day = startDateCal.get(Calendar.DAY_OF_MONTH);

                Calendar newCal = Calendar.getInstance();
                newCal.setTime(date);
                newCal.set(Calendar.DAY_OF_MONTH, day);

                housemaid.setStartDate(newCal.getTime());
            } else {
                housemaid.setStartDate(DateUtil.addMonths(startDate, monthsNumber));
            }
        }

        housemaid.addBaseAdditionalInfo("payrollShiftedMonths", infoArray.toString());

        housemaid = RepHousemaid.save(housemaid);

        return new ResponseEntity<>("Done!", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('housemaid','getShiftedMonthsInfo')")
    @RequestMapping(value = "/getShiftedMonthsInfo/{id}", method = RequestMethod.GET)
    protected ResponseEntity<?> getShiftedMonthsInfo(@PathVariable(name = "id") Housemaid housemaid) {

        if(!housemaid.hasBaseAdditionalInfo("payrollShiftedMonths")){
            return new ResponseEntity<>(Collections.emptyList(), HttpStatus.OK);
        }

        BaseAdditionalInfo info = housemaid.getBaseAdditionalInfo("payrollShiftedMonths");

        return new ResponseEntity<>(info.getInfoValue(), HttpStatus.OK);
    }

}
