package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.HousemaidRepaymentProjectionClassV2;
import com.magnamedia.entity.projection.HousemaidRepaymentProjectionV2;
import com.magnamedia.entity.projection.RepaymentProjection;
import com.magnamedia.extra.RepaymentStatus;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.*;
import com.magnamedia.service.EmployeeLoanService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PendingManagerApprovalService;
import com.magnamedia.service.ScheduledMonthlyService;
import com.magnamedia.service.message.MessagingService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Abbas <<EMAIL>>
 */
@RequestMapping("/Repayment")
@RestController
public class RepaymentController extends BaseRepositoryController<Repayment> {

    @Autowired
    private RepaymentRepository repaymentRep;

    @Autowired
    private UnpaidDeductionRepaymentRepository deductionRepaymentRepo;

    @Autowired
    private ScheduledMonthlyService scheduledMonthlyService;

    @Autowired
    private HousemaidRepository housemaidRep;

    @Autowired
    private OfficeStaffRepository staffRep;

    @Autowired
    ProjectionFactory projectionFactory;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private EmployeeLoanRepository employeeLoanRepository;

    @Autowired
    private EmployeeLoanApproveRepository employeeLoanApproveRepository;

    @Autowired
    private EmployeeLoanService employeeLoanService;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    @Autowired
    private UserRepository userRepository;

    @Override
    public BaseRepository<Repayment> getRepository() {
        return repaymentRep;
    }

    @NoPermission
    @RequestMapping("/paidNotDeductFromSalaryRepayment/{id}")
    public ResponseEntity<?> paidNotDeductFromSalaryRepayment(@PathVariable Long id,
                                                              @RequestParam(required = true,value = "repaymentDate") @DateTimeFormat(pattern="yyyy-MM-dd") Date repaymentDate,
                                                              @RequestParam(required = true,value = "proofOfTransferId") Attachment proofOfTransfer,
                                                              @RequestParam(required = false,value = "notes") String notes) {

        String managerUserIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAY_LOAN_REPAYMENT_APPROVAL_USER_ID_RECIPIENTS);
        List<String> managerUserIdsList = Arrays.asList(managerUserIds.split(";"));

        RepaymentProjection repaymentProjection = repaymentRep.getRepaymentsByOfficestaffAndRepaymentDateBetween(id, DateUtil.getStartDayValue(repaymentDate), DateUtil.getEndDayValue(repaymentDate));
        OfficeStaff officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(id);

        Boolean emailSent = false;

        String month = repaymentProjection.getRepaymentDate() != null ? DateUtil.getMonthForInt(repaymentProjection.getRepaymentDate().getMonth()) : "";
        String subject = "Approve Loan repayment of " + month
                + " for " + officeStaff.getName();
        Map<String, String> params = new HashMap<>();
        params.put("month", month);
        params.put("currency", repaymentProjection.getSalaryCurrency());
        params.put("amount", repaymentProjection.getAmount() != null ? NumberFormatter.formatNumber(repaymentProjection.getAmount()) : "");
        params.put("user", CurrentRequest.getUser() != null ? CurrentRequest.getUser().getName() : "");
        params.put("request_date", DateUtil.formatDateDashedWithTime(new Date()));
        params.put("loan_balance_before_repayment", officeStaff.getLoanBalance() != null ? NumberFormatter.formatNumber(officeStaff.getLoanBalance()) : "");
        params.put("pay_repayment_notes", notes != null ? notes : "");


        for(String managerUserId : managerUserIdsList) {

            User manager = userRepository.findOne(Long.parseLong(managerUserId));
            if (manager != null && manager.getEmail() != null && !manager.getEmail().isEmpty() && repaymentProjection != null && officeStaff != null) {

                String url = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_MANAGER_APPROVE, id.toString() + ":" + DateUtil.formatDateDashed(repaymentDate) + "#Payroll_Pay_Loan_Repayment_Approval", managerUserId);
                List<EmailRecipient> recipients = Recipient.parseEmailsString(manager.getEmail());
                params.put("url", url);

                logger.info("url to " + subject + "= " + url);

                List<Attachment> attachments = null;
                if(proofOfTransfer != null) {
                    attachments = Collections.singletonList(proofOfTransfer);
                }

                Setup.getApplicationContext().getBean(MessagingService.class).send(recipients, null, "Payroll_Pay_Loan_Repayment_Approval", subject, params, attachments, officeStaff);

                emailSent = true;
            }
        }
        if(emailSent) {
            List<Repayment> repayments = repaymentRep.findByOfficestaffIdAndRepaymentDateBetweenAndStatus(id, DateUtil.getStartDayValue(repaymentDate), DateUtil.getEndDayValue(repaymentDate), RepaymentStatus.Normal);
            if (repayments != null && repayments.size() > 0) {
                repayments.forEach(r -> {
                    r.setStatus(RepaymentStatus.Pending_Approval);
                    Attachment repaymentProofOfTransfer = Storage.cloneTemporary(proofOfTransfer, "proofOfTransfer");
                    r.addAttachment(repaymentProofOfTransfer);
                    r.setNotes(notes != null && !notes.isEmpty() ? notes : "");
                    repaymentRep.save(r);
                });
            } else {
                return new ResponseEntity<>("No repayments to be paid", HttpStatus.BAD_REQUEST);
            }
        }

        return new ResponseEntity<>(HttpStatus.OK);
    }


    @NoPermission
    @RequestMapping("/getOfficeStaffRepayments/{id}")
    public ResponseEntity<?> getOfficeStaffRepayments(@PathVariable Long id) {
        OfficeStaff staff = staffRep.findOne(id);
        if (staff != null)
            return new ResponseEntity<>(repaymentRep.getRepaymentsByOfficestaff(staff.getId()), HttpStatus.OK);
        return new ResponseEntity<>("Please check the passed OfficeStaff's id", HttpStatus.BAD_REQUEST);

    }

    @NoPermission
    @RequestMapping("/getHousemaidRepayments/{id}")
    public ResponseEntity<?> getHousemaidRepayments(@PathVariable Long id) {
        Housemaid housemaid = housemaidRep.findOne(id);
        if (housemaid != null) {

            List<HousemaidRepaymentProjectionClassV2> repayments = mergeRepaymentWithUnpaidDeductionsRepayments(repaymentRep.findRepaymentsByHousemaid(housemaid),
                    deductionRepaymentRepo.findUnpaidDeductionsByHousemaid(housemaid));
            return new ResponseEntity<>(repayments,
                    HttpStatus.OK);
        }
        return new ResponseEntity<>("Please check the passed houseamid's id", HttpStatus.BAD_REQUEST);

    }

    public List<HousemaidRepaymentProjectionClassV2> mergeRepaymentWithUnpaidDeductionsRepayments(List<HousemaidRepaymentProjectionV2> repaymentsByHousemaidList, List<HousemaidRepaymentProjectionV2> unpaidDeductionsByHousemaidList) {

        List<HousemaidRepaymentProjectionClassV2> resultList = new ArrayList<>();
        if (repaymentsByHousemaidList != null && repaymentsByHousemaidList.size() > 0 ) {
            for(HousemaidRepaymentProjectionV2 repayment: repaymentsByHousemaidList){
                HousemaidRepaymentProjectionClassV2 newRepayment = new HousemaidRepaymentProjectionClassV2(repayment);
                if(unpaidDeductionsByHousemaidList != null && unpaidDeductionsByHousemaidList.size() > 0) {
                    List<HousemaidRepaymentProjectionV2> unpaidDeductionRepayments = unpaidDeductionsByHousemaidList.stream().filter(x -> x.getDescription() != null && x.getRepaymentDate().compareTo(repayment.getRepaymentDate()) == 0).collect(Collectors.toList());
                    if (unpaidDeductionRepayments != null && unpaidDeductionRepayments.size() > 0) {
                        //size always will be 1
                        HousemaidRepaymentProjectionV2 unpaidDeductionRepayment = unpaidDeductionRepayments.get(0);
                        newRepayment.addRepayment(unpaidDeductionRepayment);
                        unpaidDeductionsByHousemaidList.remove(unpaidDeductionRepayment);
                    }
                }
                resultList.add(newRepayment);
            }
        }

        //add the rest of unpaid deductions repayments
        if(unpaidDeductionsByHousemaidList != null && unpaidDeductionsByHousemaidList.size() > 0) {
            for(HousemaidRepaymentProjectionV2 repayment: unpaidDeductionsByHousemaidList) {
                HousemaidRepaymentProjectionClassV2 newRepayment = new HousemaidRepaymentProjectionClassV2(repayment);
                //Unpaid Deduction Repayment can't be edited
                newRepayment.setId(null);
                resultList.add(newRepayment);
            }
        }


        //sort the result by repayment date
        resultList.sort(Comparator.comparing(o -> o.getRepaymentDate()));
        return resultList;
    }


    @NoPermission
    @RequestMapping("changeDefaultMonthlyRepayment/{id}")
    public ResponseEntity<?> changeDefaultMonthlyRepayment(@PathVariable Long id, @RequestParam(value = "amount") Double amount) {
        Housemaid housemaid = housemaidRep.findOne(id);
        if (housemaid != null) {
            housemaid.setDefaulMonthlyRepayment(amount);
            housemaidRep.save(housemaid);
            return new ResponseEntity<>(HttpStatus.OK);

        }
        return new ResponseEntity<>("Please check the passed housemaid Id", HttpStatus.BAD_REQUEST);


    }

    @NoPermission
    @RequestMapping("changeStaffDefaultMonthlyRepayment/{id}")
    public ResponseEntity<?> changeStaffDefaultMonthlyRepayment(@PathVariable Long id, @RequestParam(value = "amount") Double amount) {
        OfficeStaff staff = staffRep.findOne(id);
        if (staff != null) {
            staff.setDefaulMonthlyRepayment(amount);
            staffRep.save(staff);
            return new ResponseEntity<>(HttpStatus.OK);

        }
        return new ResponseEntity<>("Please check the passed officeStaff's id", HttpStatus.BAD_REQUEST);


    }
    //PAY-854
    @NoPermission
    @RequestMapping(value = "/addWaverRepaymentForHousemaid", method = RequestMethod.GET)
    public ResponseEntity<?> addWaverRepaymentForHousemaid(
            @RequestParam(required = true, value = "housemaid") Housemaid housemaid
            ,@RequestParam(required = true, value ="approvalEmail") String approvalEmail
            ,@RequestParam(required = true, value ="amount") Double amount) {

        Date currentDateTime = new Date();

        Repayment newRepayment = new Repayment();

        newRepayment.setAmount(amount);
        newRepayment.setDescription("Waiver approved by " + approvalEmail);
        newRepayment.setHousemaid(housemaid);
        newRepayment.setRepaymentDate(currentDateTime);
        newRepayment.setType(Repayment.RepaymentType.CASH_ADVANCED);
        newRepayment.setExculdedFromPayroll(false);
        newRepayment.setPaidRepayment(true);
        newRepayment.setNotFinal(false);
        newRepayment.setDontConsiderAsDeduction(true);
        Setup.getRepository(RepaymentRepository.class).save(newRepayment);

        return new ResponseEntity<>(housemaid.getLoanBalance(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<?> createEntity(Repayment repayment) {

        if (repayment.getRepaymentDate() != null) {
            LocalDate td = new LocalDate();
            LocalDate repaymentDate = new LocalDate(repayment.getRepaymentDate());
            if (repaymentDate.isBefore(td))
                throw new BusinessException("Date of Repayment mustn't be before today date");

        }
        return super.createEntity(repayment);

    }

    @Override
    public ResponseEntity<?> updateEntity(Repayment repayment) {

        Repayment repaymentTemp = repaymentRep.findOne(repayment.getId());
        if (repaymentTemp.getRepaymentDate() != null) {
            LocalDate td = new LocalDate();
            LocalDate repaymentDate = new LocalDate(repaymentTemp.getRepaymentDate());
            if (repaymentDate.isBefore(td))
                throw new BusinessException("You can't update a repayment if its date is before today date");

        }
        return super.updateEntity(repayment);
    }

    @Override
    public ResponseEntity<?> deleteEntity(Repayment repayment) {
        Repayment repaymentTemp = repaymentRep.findOne(repayment.getId());
        if (repaymentTemp.getRepaymentDate() != null) {
            LocalDate td = new LocalDate();
            LocalDate repaymentDate = new LocalDate(repaymentTemp.getRepaymentDate());
            if (repaymentDate.isBefore(td))
                throw new BusinessException("You can't delete a repayment if its date is before today date");

        }
        return super.deleteEntity(repayment);
    }

    @NoPermission
    @RequestMapping(value = "/FixRepayments",
            method = RequestMethod.GET)
    public ResponseEntity<?> FixRepayments() {

        LocalDate dt = new LocalDate();
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        List<RepaymentFix> finalList = new ArrayList<>();
        List<Housemaid> housemaids = query.execute().stream().collect(Collectors.toList());
        List<Housemaid> templist = housemaids.stream().filter(x -> x.getLoanBalance() < 0).collect(Collectors.toList());
        for (Housemaid temp : templist) {
            SelectQuery<Repayment> RepaymentQuery = new SelectQuery<>(Repayment.class);
            RepaymentQuery.filterBy("housemaid.id", "=", temp.getId());
            RepaymentQuery.filterBy("repaymentDate", "=", dt.toDate());
            RepaymentQuery.filterBy("paidRepayment", "=", true);
            RepaymentQuery.filterBy("exculdedFromPayroll", "=", false);
            List<Repayment> repayments = RepaymentQuery.execute();
            RepaymentFix bean = new RepaymentFix();
            bean.housemaidId = temp.getId();
            bean.loanBalance = temp.getLoanBalance();
            bean.repaymentId = repayments.size() > 0 ? repayments.get(0).getId() : 0;
            finalList.add(bean);
        }

        return new ResponseEntity<>(finalList, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/deleteRepayments",
            method = RequestMethod.GET)
    public ResponseEntity<?> deleteRepayments(@RequestBody List<Repayment> repayments) {
        repaymentRep.delete(repayments);
        return new ResponseEntity<>(HttpStatus.OK);

    }

    @Transactional
    @NoPermission
    @RequestMapping(value = "/updateRepayments")
    public ResponseEntity<?> updateRepayments(@RequestBody Repayment repayment) {
        OfficeStaff staff = officeStaffRepository.getOne(repayment.getOfficestaff().getId());

        // 0- check if less than Zero
        if (repayment.getAmount() < 0)
            throw new BusinessException("Loan's repayment amount can't be negative!");

        // 1- calculate oldRepaymentAmount
        Double oldRepaymentAmount = 0.0;
        List<Repayment> repaymentList = repaymentRep.getCurrentMonthRepayments(staff, repayment.getRepaymentDate());
        for( Repayment loanRepayment : repaymentList){
            oldRepaymentAmount+= loanRepayment.getAmount() != null ? loanRepayment.getAmount() : 0.0;
        }

        // 2- create new Employee Loan Approve Entity with the type of EDIT_REPAYMENT
        EmployeeLoanApprove loanApprove = new EmployeeLoanApprove();
        loanApprove.setLoanApproveType(EmployeeLoanApprove.LoanApproveType.EDIT_REPAYMENT);
        loanApprove.setOfficeStaff(staff);
        loanApprove.setMonthlyRepaymentAmount(oldRepaymentAmount);
        loanApprove.setUpdatedRepaymentAmount(repayment.getAmount());
        loanApprove.setUpdatedMonth(repayment.getRepaymentDate());
        loanApprove= employeeLoanApproveRepository.save(loanApprove);


        // 3- generate public page url and send it to manager
        OfficeStaff finalManager = staff.getFinalManager();

        String url = "";
        url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, loanApprove.getId().toString() + "#Payroll_Edit_Loan_Repayment_Approval", String.valueOf(finalManager.getUser().getId()));

        pendingManagerApprovalService.insertNewPendingApprovalRequest(staff.getName(), DateUtil.formatFullDate(staff.getStartingDate()),staff.getSalaryWithCurrency(), staff.getJobTitle() != null ? staff.getJobTitle().getName() : "", staff.getEmployeeManager(), finalManager, staff.getSalaryCurrency() + " " + NumberFormatter.formatNumber(repayment.getAmount()), "Edit Monthly Repayment", null, url, loanApprove.getId().toString() + "#Payroll_Edit_Loan_Repayment_Approval");

//        Map<String, String> paramValues = new HashMap<>();
//        paramValues.put("employee_name", staff.getFirstLastName());
//        paramValues.put("old_value", NumberFormatter.formatNumber(oldRepaymentAmount) + " " + staff.getSalaryCurrency());
//        paramValues.put("new_value", NumberFormatter.formatNumber(repayment.getAmount()) + " " + staff.getSalaryCurrency());
//        paramValues.put("edited_month_date", DateUtil.formatMonth(repayment.getRepaymentDate()));
//        paramValues.put("url", url);
//
//        SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                "Edit Loan Repayment",
//                normalizePhoneNumber(finalManager.getPhoneNumber()),
//                finalManager.getEmail(),
//                SmsReceiverType.Office_Staff,
//                finalManager.getId(),
//                finalManager.getName(),
//                "Payroll_Edit_Loan_Repayment_Approval",
//                paramValues,
//                null,
//                finalManager.getPreferredCommunicationMethod());
//
//        if (smsResponse == null || !smsResponse.isSuccess())
//            throw new RuntimeException("Failed to send the Approval Message to the Final Manager");

        return ResponseEntity.ok("Your request was received, waiting the employee's final manager approval!");
    }

    public class RepaymentFix {
        public Long housemaidId;
        public Double loanBalance;
        public Long repaymentId;
    }
//Jirra ACC-1355
    @Transactional
    public Map executeHouseMaidSteps(
            Housemaid maid,
            LocalDate payrollStart,
            boolean finalFile) {
        Map result = new HashMap();
        maid = this.stepAHousemaid(maid);
        result.put("repaymentsUpdated", this.stepBHousemaid(maid, payrollStart));
        result.put("repaymentsInserted", this.stepCHousemaid(maid, payrollStart, finalFile));

        return result;
    }

    public Housemaid stepAHousemaid(Housemaid maid) {
        //boolean result = true;
        /*boolean drUpdated=true;
        if ((!maid.getRepaymentsList().stream()
                        .filter(x-> x.getExculdedFromPayroll() != null &&
                                !x.getExculdedFromPayroll() &&
                                x.getPaidRepayment() != null &&
                                x.getPaidRepayment() &&
                                x.getRepaymentDate() != null &&
                                includedInThisPayroll(x.getRepaymentDate()))
                        .collect(Collectors.toList()).isEmpty())
            ){

            //1 of b
            List<Repayment> repayment = maid.getRepaymentsList().stream()
                    .filter(x-> x.getExculdedFromPayroll() != null &&
                                !x.getExculdedFromPayroll() &&
                                x.getRepaymentDate() != null &&
                                includedInThisPayroll(x.getRepaymentDate()))
                    .collect(Collectors.toList());
            if (repayment.size()==1)
                maid.setDefaulMonthlyRepayment(
                        Math.min(repayment.get(0).getAmount(),maid.getLoanBalance()));

            //2 of b
            else if (maid.getLoanBalance() != null &&maid.getLoanBalance() == 0)
                maid.setDefaulMonthlyRepayment(0.0);

            //3 of b
            else if ((maid.getDefaulMonthlyRepayment() == null
                        && maid.getLoanBalance()<=getDefaultMonthlyRepayment(maid) && maid.getLoanBalance()>=1)
                    ||(maid.getDefaulMonthlyRepayment() != null
                        && maid.getLoanBalance()<maid.getDefaulMonthlyRepayment()&&maid.getLoanBalance()>=1))
                maid.setDefaulMonthlyRepayment(maid.getLoanBalance());

            else
                drUpdated=false;
        }
        else if (maid.getDefaulMonthlyRepayment() == null || maid.getDefaulMonthlyRepayment() == 0){
        maid.setDefaulMonthlyRepayment(getDefaultMonthlyRepayment(maid));
            result = true;
            drUpdated=true;
        }

        if(drUpdated)
            defaultRepaymentsUpdated++;*/
        double monthlyLoan = this.scheduledMonthlyService.getDefaultMonthlyRepayment(maid);
        if (maid.getDefaulMonthlyRepayment() == null || !maid.getDefaulMonthlyRepayment().equals(monthlyLoan)){
            maid = this.housemaidRepository.findOne(maid.getId());
            maid.setDefaulMonthlyRepayment(this.scheduledMonthlyService.getDefaultMonthlyRepayment(maid));
            this.housemaidRepository.save(maid);
        }
        return maid;
    }

    // Step 2: Update current repayments
    public Integer stepBHousemaid(Housemaid maid, LocalDate dt) {
        //Jirra ACC-1863
        if (maid.skipThisMonthRepayment()) return 0;

        Integer repaymentsUpdated = 0;
        List<Repayment> repayments = maid.getRepaymentsList().stream()
                .filter(x -> x.getExculdedFromPayroll() != null &&
                        !x.getExculdedFromPayroll() &&
                        x.getRepaymentDate() != null &&
                        x.getAmount() != null &&
                        this.scheduledMonthlyService.includedInThisPayroll(x.getRepaymentDate(), dt))
                .collect(Collectors.toList());

        for (Repayment rep : repayments) {
            rep.setPaidRepayment(true);
            repaymentsUpdated++;
        }
        repaymentRep.save(repayments);
        return repaymentsUpdated;
    }

    // Step 3: Insert Repayments
    public Integer stepCHousemaid(Housemaid maid, LocalDate dt, boolean finalFile) {
        Double loanBalance = maid.getLoanBalance();
        Integer repaymentsInserted = 0;
        if ((maid.getDefaulMonthlyRepayment() != null)
                //Jirra ACC-1863
                && !maid.skipThisMonthRepayment()
                && (maid.getRepaymentsList().stream()
                .filter(x -> x.getExculdedFromPayroll() != null &&
                        !x.getExculdedFromPayroll() &&
                        x.getRepaymentDate() != null &&
                        x.getOldRepayment() == null &&
                        this.scheduledMonthlyService.includedInThisPayroll(x.getRepaymentDate(), dt))
                .collect(Collectors.toList()).isEmpty() && loanBalance >= 1)
                ) {
            //1 of d
            Repayment newRepayment = new Repayment();
            newRepayment.setAmount(Math.min(maid.getDefaulMonthlyRepayment(), loanBalance));
            newRepayment.setDescription("Monthly Repayment of: "
                    + DateUtil.formatFullDateWithTime(dt.toDate()));
            newRepayment.setHousemaid(maid);
            newRepayment.setRepaymentDate(dt.toDate());
            newRepayment.setType(Repayment.RepaymentType.CASH_ADVANCED);
            newRepayment.setExculdedFromPayroll(false);
            newRepayment.setPaidRepayment(true);
            newRepayment.setNotFinal(!finalFile);
            repaymentRep.save(newRepayment);
            List<Repayment> repayments = maid.getRepaymentsList();
            repayments.add(newRepayment);
            maid.setRepaymentsList(repayments);
            repaymentsInserted++;
            //2 of d
            //maid.getRepayments();
        }


        return repaymentsInserted;
    }
}
