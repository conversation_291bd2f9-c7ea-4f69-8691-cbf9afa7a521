package com.magnamedia.controller;
import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.PayrollAccountantTodo;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.extra.HousemaidSalaryGroup;
import com.magnamedia.extra.WorkDays;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.message.TemplateService;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import org.hibernate.envers.NotAudited;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.util.*;

import org.joda.time.LocalDate;

@RequestMapping("/payrollTest")
@RestController
public class PayrollTestController {

    @NoPermission
    @RequestMapping(value = "/accountantToDo/{id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> accountantToDo(PayrollAccountantTodo payrollAccountantTodo) {


        return new ResponseEntity<>("", HttpStatus.OK);
    }


    @NoPermission
    @RequestMapping(value = "/loadTemplateForHousemaid/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> loadTemplateForHousemaid(@PathVariable(name = "id") Housemaid housemaid) {
        MessageTemplateService messageTemplateService = Setup.getApplicationContext().getBean(MessageTemplateService.class);
        Map<String, Object> details = (Map<String, Object>)Setup.getApplicationContext().getBean(YayaAppController.class).getHousemaidPayrollDetails(housemaid, "en").getBody();
        String con4Template = (String)details.get("whenYouWillReceiveSalaryTemplate_con4");
        String con2Template = (String)details.get("payslipTemplate_con2");

        Map<String, String> result = new HashMap<>();
        result.put("whenYouWillReceiveSalaryTemplate_con4", messageTemplateService.loadYayaContent(con4Template, housemaid));
        result.put("payslipTemplate_con2", messageTemplateService.loadYayaContent(con2Template, housemaid));

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @NoPermission
    @Transactional
    @RequestMapping(value = "/createAllowedParametersForAllTemplate", method = RequestMethod.GET)
    public ResponseEntity<?> createAllowedParametersForAllTemplate(){
        List<String> result = Setup.getApplicationContext().getBean(TemplateService.class)
                .addGenderAndWorkerTypeForTemplates();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @NoPermission
    @RequestMapping(value = "/getHousemaidSalaryDetails/{h_id}/{m_id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> getHousemaidSalaryDetails(@PathVariable(value = "h_id") Housemaid housemaid, @PathVariable(value = "m_id") MonthlyPaymentRule monthlyPaymentRule) {
        Date payrollMonth = monthlyPaymentRule.getPayrollMonth();
        LocalDate payrollMonthL = new LocalDate(payrollMonth);

        PayrollGenerationHelperService service = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class);
        Map<String, Object> res = new HashMap<>();
        res.put("1 getSalaryForHousemaid", service.getSalaryForHousemaid(housemaid, payrollMonthL));
        res.put("2 getSalaryBreakDownForHousemaid", service.getSalaryBreakDownForHousemaid(housemaid, payrollMonthL));
//        List<WorkDays> workDays = service.getWorkerDays(housemaid, payrollMonth);
//        res.put("3 workerDays", workDays);

        Long previousVacationDays = service.getPreviousVacationDays(housemaid, payrollMonthL);
//        Map<HousemaidSalaryGroup, Long> numberOfDaysForEachGroup = Setup.getApplicationContext().getBean(PayrollGroupService.class)
//                .getNumberOfDaysForEachGroup(housemaid, workDays, payrollMonthL);

        res.put("4 previousVacationDays", previousVacationDays);
//        res.put("5 numberOfDaysForEachGroup", numberOfDaysForEachGroup);

        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = "/createHousemaidAttendanceLog/{h_id}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> createAttendanceForList(@PathVariable(value = "h_id") Housemaid housemaid, @RequestParam(value = "date") @DateTimeFormat(pattern = "dd-MM-yyyy") java.util.Date date) {

        PayrollGroupService service = Setup.getApplicationContext().getBean(PayrollGroupService.class);
        service.createHousemaidPayrollAttendanceLogAndUnpaidDayForHousemaidList(Collections.singletonList(housemaid.getId().toString()), date);

        return new ResponseEntity<>("Done", HttpStatus.OK);
    }


}
