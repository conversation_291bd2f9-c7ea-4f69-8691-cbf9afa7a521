package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.PayrollAccountantTodo;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.AccountantToDoProjection;
import com.magnamedia.entity.projection.BankTransferProjection;
import com.magnamedia.entity.projection.PayrollFilesArchivingClass;
import com.magnamedia.entity.projection.PensionAuthorityProjection;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollAccountantTodoType;
import com.magnamedia.repository.HousemaidPayrollLogRepository;
import com.magnamedia.repository.OfficeStaffPayrollLogRepository;
import com.magnamedia.repository.PayrollAccountantTodoRepository;
import com.magnamedia.service.Auditor.AsyncService;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.AccountantToDoService;
import com.magnamedia.service.payroll.generation.newversion.SendingTransactionService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;

import static com.magnamedia.service.payroll.generation.newversion.TransferFilesService.createTextStyle;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 6/18/2020
 **/
@RestController
@RequestMapping("/accountantTodo")
public class PayrollAccountantTodoController extends BaseRepositoryController<PayrollAccountantTodo> {

    @Autowired
    private PayrollAccountantTodoRepository todoRepository;

    @Autowired
    private EmailTemplateService emailTemplateService;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private AccountantToDoService accountantToDoService;

    @Autowired
    private SendingTransactionService sendingTransactionService;

    @Autowired
    private OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private MailService mailService;

    @Override
    public BaseRepository<PayrollAccountantTodo> getRepository() {
        return todoRepository;
    }



    @PreAuthorize("hasPermission('accountantTodo', 'findOpenedTodos')")
    @RequestMapping(value = "/findOpenedTodos", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> findOpenedTodos(Pageable pageable) {

        return ResponseEntity.ok(this.project(todoRepository.findCompleteOpenToDos(pageable), AccountantToDoProjection.class));
    }

    @PreAuthorize("hasPermission('accountantTodo','completeTodo')")
    @RequestMapping(value = "/completeTodo")
    @Transactional
    public ResponseEntity<?> completeTodo(@RequestBody ObjectNode objectNode) throws IOException {
        this.update(objectNode);
        PayrollAccountantTodo updated = this.parse(objectNode);

        PayrollAccountantTodo payrollAccountantTodo = this.getRepository().findOne(updated.getId());

        AsyncService asyncService = Setup.getApplicationContext().getBean(AsyncService.class);
        String code = "";
        switch (PayrollAccountantTodoType.valueOf(payrollAccountantTodo.getTaskName())) {
            case WPS:
                //check attachment and save the To-Do as completed
                // PAY-1458
//                if (payrollAccountantTodo.getAttachment("proofOfTransfer") == null) {
//                    throw new BusinessException("Can't proceed, missing Proof of Transfer file!.");
//                }
                payrollAccountantTodo.setCompleted(true);
                todoRepository.save(payrollAccountantTodo);
                    // PAY-864
                if(payrollAccountantTodo.getMonthlyPaymentRule() != null && (payrollAccountantTodo.getMonthlyPaymentRule().isTargetingHousemaid()
                    || payrollAccountantTodo.getMonthlyPaymentRule().isTargetingEmiratis())) {

                    //send proof of transfer authorization
                    List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL));
                    List<EmailRecipient> CCRecipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL));

//                    TemplateEmail templateEmail = new TemplateEmail("Authorize Payroll Transfer of amount AED " + NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()), "Payroll_Authorize_Payroll_Transfer_Template", new HashMap<>());
//                    templateEmail.addAttachement(payrollAccountantTodo.getAttachment("proofOfTransfer"));

                    if (recipients.size() > 0) {
                        List<Attachment> attachments = null;
                        if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                            attachments = new ArrayList<>();
                            attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                        }
                        messagingService.send(recipients, CCRecipients, "Payroll_Authorize_Payroll_Transfer_Template", "Authorize Payroll Transfer of amount AED " + NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()),
                                new HashMap<>(), attachments,null);

//                        mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                                .recipients(recipients)
//                                .cc(CCRecipients)
//                                .html()
//                                .secure()
//                                .build());
                    }
                }

                //payrollAccountantTodo.allTransfersAreDone();
                asyncService.allTransfersAreDone(payrollAccountantTodo);

                //send email to Ansari
                Map<String, String> paramValues = new HashMap();
                if(payrollAccountantTodo.getSingleHousemaid()){
                    paramValues.put("payroll_month", DateUtil.formatSimpleMonthYear(payrollAccountantTodo.getPayrollMonth()));
                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(payrollAccountantTodo.getPaymentDate()));
                    paramValues.put("total", NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()));

                    List<Attachment> attachments = new ArrayList<>();
                    attachments.add(payrollAccountantTodo.getAttachment("WPSTransferFile"));
                    if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                        attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                    }

                    if (payrollAccountantTodo.getLabel().contains("switching to MV"))
                        emailTemplateService.sendEmail("WPS_CC_Switching_To_MV_Template", paramValues, attachments);
                    else {
                        paramValues.put("total_salaries", NumberFormatter.formatNumber(payrollAccountantTodo.getAmount()));
                        paramValues.put("ansari_charges", NumberFormatter.formatNumber(payrollAccountantTodo.getCharges() + payrollAccountantTodo.getVat()));

                        emailTemplateService.sendEmail("PAY_WPS_Single_Housemaid_Template", paramValues, attachments);
                    }
                }else{
                    paramValues.put("payroll_month", DateUtil.formatSimpleMonthYear(payrollAccountantTodo.getPayrollMonth()));
                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(payrollAccountantTodo.getPaymentDate()));
                    paramValues.put("total_salaries", NumberFormatter.formatNumber(payrollAccountantTodo.getAmount()));
                    paramValues.put("ansari_charges", NumberFormatter.formatNumber(payrollAccountantTodo.getCharges() + payrollAccountantTodo.getVat()));
                    paramValues.put("total", NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()));

                    List<Attachment> attachments = null;
                    if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                        attachments = new ArrayList<>();
                        attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                    }
                    emailTemplateService.sendEmail("PAY_WPS_Transfer_Email", paramValues, attachments);
                }

                return ResponseEntity.ok("Email has been sent to Ansari.");

            case CASH:
                //check attachment and save the To-Do as completed
//                PAY-1458
//                if (payrollAccountantTodo.getAttachment("proofOfTransfer") == null) {
//                    throw new BusinessException("Can't proceed, missing Proof of Transfer file!.");
//                }
                if (payrollAccountantTodo.getCompleted() != null && payrollAccountantTodo.getCompleted()) {
                    payrollAccountantTodo.setStopped(true);
                } else {
                    payrollAccountantTodo.setCompleted(true);
                }
                todoRepository.save(payrollAccountantTodo);
                return this.okResponse();
            case INTERNATIONAL_TRANSFER:
                code = "PAY_International_Exchange_Template";
                //check if total == amount + charges
                if (payrollAccountantTodo.getTotal() == null || payrollAccountantTodo.getAmount() == null || payrollAccountantTodo.getCharges() == null
                        || payrollAccountantTodo.getVat() == null
                        || Math.abs(payrollAccountantTodo.getTotal() -
                        (payrollAccountantTodo.getAmount() + payrollAccountantTodo.getCharges() + payrollAccountantTodo.getVat())) > 1e-4)
                    throw new BusinessException("Amount of Transfer is not equal to the sum of charges and salaries!");

                //check attachment and save the To-Do as completed
//                PAY-1458
//                if (payrollAccountantTodo.getAttachment("proofOfTransfer") == null) {
//                    throw new BusinessException("can't proceed, missing Proof of Transfer file!.");
//                }
                payrollAccountantTodo.setCompleted(true);
                todoRepository.save(payrollAccountantTodo);

                asyncService.allTransfersAreDone(payrollAccountantTodo);

                this.accountantToDoService.createSendingTransactionsToDo(payrollAccountantTodo);

                if(payrollAccountantTodo.getSingleOfficeStaff() == null || !payrollAccountantTodo.getSingleOfficeStaff()) {
                    //send proof of transfer authorization
                    List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL));
                    List<EmailRecipient> CCRecipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL));

//                    TemplateEmail templateEmail = new TemplateEmail("Authorize Payroll Transfer of amount AED " + NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()), "Payroll_Authorize_Payroll_Transfer_Template", new HashMap<>());
//                    templateEmail.addAttachement(payrollAccountantTodo.getAttachment("proofOfTransfer"));

                    if (recipients.size() > 0) {
                        List<Attachment> attachments = null;
                        if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                            attachments = new ArrayList<>();
                            attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                        }
                        messagingService.send(recipients, CCRecipients, "Payroll_Authorize_Payroll_Transfer_Template", "Authorize Payroll Transfer of amount AED " + NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()),
                                new HashMap<>(), attachments,null);

//                        mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                                .recipients(recipients)
//                                .cc(CCRecipients)
//                                .html()
//                                .secure()
//                                .build());
                    }
                }
                //send email to Ansari
                paramValues = new HashMap();
                paramValues.put("payment_date", DateUtil.formatMonthDayYear(payrollAccountantTodo.getCreationDate()));
                paramValues.put("unique_payment_code", payrollAccountantTodo.getUniquePaymentCode());

                List<Attachment> attachments = new ArrayList<>();
                attachments.add(payrollAccountantTodo.getAttachment("InternationalTransferFile"));
                if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                    attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                }

                emailTemplateService.sendEmail(code, paramValues, attachments);
                return ResponseEntity.ok("Email has been sent to Ansari.");
            case LOCAL_TRANSFER:
                code = "PAY_Local_Exchange_Template";
                //check if total == amount + charges
                if (payrollAccountantTodo.getTotal() == null || payrollAccountantTodo.getAmount() == null || payrollAccountantTodo.getCharges() == null
                        || payrollAccountantTodo.getVat() == null
                        || Math.abs(payrollAccountantTodo.getTotal() -
                        (payrollAccountantTodo.getAmount() + payrollAccountantTodo.getCharges() + payrollAccountantTodo.getVat())) > 1e-4)
                    throw new BusinessException("Amount of Transfer is not equal to the sum of charges and salaries!");

                //check attachment and save the To-Do as completed
//                if (payrollAccountantTodo.getAttachment("proofOfTransfer") == null) {
//                    throw new BusinessException("can't proceed, missing Proof of Transfer file!.");
//                }
                payrollAccountantTodo.setCompleted(true);
                todoRepository.save(payrollAccountantTodo);

                if(payrollAccountantTodo.getMonthlyPaymentRule() != null && payrollAccountantTodo.getMonthlyPaymentRule().isTargetingHousemaid()) {
                    //send proof of transfer authorization
                    List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_RECEIVERS_EMAIL));
                    List<EmailRecipient> CCRecipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                            PayrollManagementModule.PARAMETER_AUTHORIZE_PAYROLL_TRANSFER_CC_EMAIL));

//                    TemplateEmail templateEmail = new TemplateEmail("Authorize Payroll Transfer of amount AED " + NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()), "Payroll_Authorize_Payroll_Transfer_Template", new HashMap<>());
//                    templateEmail.addAttachement(payrollAccountantTodo.getAttachment("proofOfTransfer"));

                    if (recipients.size() > 0) {
                        attachments = null;
                        if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                            attachments = new ArrayList<>();
                            attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                        }
                        messagingService.send(recipients, CCRecipients, "Payroll_Authorize_Payroll_Transfer_Template", "Authorize Payroll Transfer of amount AED " + NumberFormatter.formatNumber(payrollAccountantTodo.getTotal()),
                                new HashMap<>(), attachments, null);
//                        mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                                .recipients(recipients)
//                                .cc(CCRecipients)
//                                .html()
//                                .secure()
//                                .build());
                    }
                }

              //  payrollAccountantTodo.allTransfersAreDone();
                asyncService.allTransfersAreDone(payrollAccountantTodo);


                //send email to Ansari
                paramValues = new HashMap();
                paramValues.put("payment_date", DateUtil.formatMonthDayYear(payrollAccountantTodo.getCreationDate()));

                attachments = null;
                if (payrollAccountantTodo.getAttachment("proofOfTransfer") != null) {
                    attachments = new ArrayList<>();
                    attachments.add(payrollAccountantTodo.getAttachment("proofOfTransfer"));
                }

                emailTemplateService.sendEmail(code, paramValues, attachments);

                return ResponseEntity.ok("Email has been sent to Ansari.");

            case SENDING_TRANSACTIONS:
                sendingTransactionService.extractTransactionNumbers(payrollAccountantTodo.getAttachments().get(0));
                payrollAccountantTodo.setCompleted(true);
                todoRepository.save(payrollAccountantTodo);
                return ResponseEntity.ok("Transactions are uploaded successfully!");

        }
        return new ResponseEntity("Failed", HttpStatus.BAD_REQUEST);
    }


    private int countRows(PayrollAccountantTodo todo) throws IOException {
        Attachment attachment = null;
        Workbook workbook = null;
        if (todo.getMonthlyPaymentRule().getPaymentMethod() == PaymentRulePaymentMethod.LOCAL_TRANSFER) {
            attachment = todo.getAttachment("LocalTransferFile");
        } else {
            attachment = todo.getAttachment("InternationalTransferFile");
        }

        if (attachment.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(attachment));
        else if (attachment.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(attachment));

        Sheet firstSheet = workbook.getSheetAt(0);
        Iterator<Row> iterator = firstSheet.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            Cell cell = row.getCell(0);
            if(isCellEmpty(cell)) continue;
            count++;
        }

        return count;
    }

    private int countRows(Attachment attachment) throws IOException {
        Workbook workbook = null;
        if (attachment.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(attachment));
        else if (attachment.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(attachment));

        Sheet firstSheet = workbook.getSheetAt(0);
        Iterator<Row> iterator = firstSheet.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            Cell cell = row.getCell(0);
            if(isCellEmpty(cell)) continue;
            count++;
        }

        return count;
    }

    private int countColumns(PayrollAccountantTodo todo) throws IOException {
        Attachment attachment = null;
        Workbook workbook = null;
        if (todo.getMonthlyPaymentRule().getPaymentMethod() == PaymentRulePaymentMethod.LOCAL_TRANSFER) {
            attachment = todo.getAttachment("LocalTransferFile");
        } else {
            attachment = todo.getAttachment("InternationalTransferFile");
        }

        if (attachment.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(attachment));
        else if (attachment.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(attachment));

        Sheet firstSheet = workbook.getSheetAt(0);
        Iterator<Row> iterator = firstSheet.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            Iterator<Cell> cols = row.cellIterator();
            while(cols.hasNext()) {
                cols.next();
                count++;
            }
            break;
        }
        return count;
    }

    private int countColumns(Attachment attachment) throws IOException {
        Workbook workbook = null;
        if (attachment.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(attachment));
        else if (attachment.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(attachment));

        Sheet firstSheet = workbook.getSheetAt(0);
        Iterator<Row> iterator = firstSheet.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            Iterator<Cell> cols = row.cellIterator();
            while(cols.hasNext()) {
                cols.next();
                count++;
            }
            break;
        }
        return count;
    }

    private InputStream getResponseStream(PayrollAccountantTodo todo) {
        InputStream originalStream = null;
        if (todo.getMonthlyPaymentRule().getPaymentMethod() == PaymentRulePaymentMethod.LOCAL_TRANSFER) {
            originalStream = Storage.getStream(todo.getAttachment("LocalTransferFile"));
        } else {
            originalStream = Storage.getStream(todo.getAttachment("InternationalTransferFile"));
        }
        return originalStream;
    }

    @PreAuthorize("hasPermission('accountantTodo','processMoneyExchangeFile')")
    @RequestMapping(value = "/processMoneyExchangeFile/{id}")
    public ResponseEntity<?> processMoneyExchangeFile(@RequestBody Attachment attachment,
                                                      @PathVariable(name = "id") PayrollAccountantTodo todo) throws IOException {
        attachment = Setup.getRepository(AttachementRepository.class)
                .findOne(attachment.getId());


        String localTag = "localExchangeResponse";
        String internationalTag = "internationalExchangeResponse";

        if (countRows(todo) != countRows(attachment) ||
                countColumns(todo) != countColumns(attachment)) {
            throw new BusinessException("Please make sure that you've uploaded the right file.");
        }

        double amount = 0d;
        double charges = 0d;
        double total = 0d;

        int amountIndex = 7, chargesIndex = 8, totalIndex = 9;
        if (attachment.getTag().equals(localTag)) {
            amountIndex = 3;
            chargesIndex = 4;
            totalIndex = 5;
        }

        Workbook workbook = null;
        if (attachment.getExtension().equals("xlsx"))
            workbook = new XSSFWorkbook(Storage.getStream(attachment));
        else if (attachment.getExtension().equals("xls"))
            workbook = new HSSFWorkbook(Storage.getStream(attachment));

        Sheet firstSheet = workbook.getSheetAt(0);
        Iterator<Row> iterator = firstSheet.iterator();
        int count = 0;
        while (iterator.hasNext()) {
            Row row = iterator.next();
            if (count++ == 0) continue;
            Cell amountCell = row.getCell(amountIndex);
            Cell chargesCell = row.getCell(chargesIndex);
            Cell totalCell = row.getCell(totalIndex);

            if (isCellEmpty(amountCell) || isCellEmpty(chargesCell) || isCellEmpty(totalCell)) {
                continue;
            }

            if (!isCellEmpty(amountCell)) {
                amount += amountCell.getCellType() == CellType.STRING ? Double.parseDouble(amountCell.getStringCellValue()) :
                        amountCell.getNumericCellValue();
            }
            if (!isCellEmpty(chargesCell)) {
                charges += chargesCell.getCellType() == CellType.STRING ? Double.parseDouble(chargesCell.getStringCellValue()) :
                        chargesCell.getNumericCellValue();
            }
            if (!isCellEmpty(totalCell)) {
                total += totalCell.getCellType() == CellType.STRING ? Double.parseDouble(totalCell.getStringCellValue()) :
                        totalCell.getNumericCellValue();
            }
        }

        Map<String, Double> response = new HashMap<>();
        charges = charges / 1.05d;
        double vat = charges * 0.05d;
        response.put("amount", NumberFormatter.twoDecimalPoints(amount));
        response.put("charges", NumberFormatter.twoDecimalPoints(charges));
        response.put("vat", NumberFormatter.twoDecimalPoints(vat));
        response.put("total", NumberFormatter.twoDecimalPoints(total));

        if (todo.getMonthlyPaymentRule().getPaymentMethod() == PaymentRulePaymentMethod.LOCAL_TRANSFER) {
            todo.addAttachment(Storage.cloneTemporary(attachment, "LocalTransferFile"));
        } else {
            todo.addAttachment(Storage.cloneTemporary(attachment, "InternationalTransferFile"));
        }
        this.getRepository().save(todo);
        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasPermission('accountantTodo', 'officeStaffs')")
    @RequestMapping(value = "/officeStaffs/{id}", method = RequestMethod.GET)
    public ResponseEntity<?> officeStaffs(@PathVariable(name = "id") PayrollAccountantTodo todo, Pageable pageable) {
        SelectQuery<OfficeStaffPayrollLog> query = new SelectQuery<>(OfficeStaffPayrollLog.class);
        query.filterBy("payrollAccountantTodo.id", "=", todo.getId())
                .and("transferred", "=", false)
                .and("willBeIncluded", "=", true);
        if (!todo.getSingleOfficeStaff()) {
            return ResponseEntity.ok(query.execute(pageable));
        } else {
            List<OfficeStaffPayrollLog> logs = query.execute();
            if(logs.isEmpty()) return ResponseEntity.ok(new PageImpl<>(Arrays.asList()));

            OfficeStaffPayrollLog accumulativeLog = logs.get(0);
            accumulativeLog.setTotalSalary(todo.getAmount());
            Page<OfficeStaffPayrollLog> page = new PageImpl<>(Arrays.asList(accumulativeLog));
            return ResponseEntity.ok(page);
        }
    }

    @PreAuthorize("hasPermission('accountantTodo', 'officeStaff')")
    @RequestMapping(value = "/officeStaff/{todoId}/{staffId}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> officeStaff(@PathVariable(name = "todoId") PayrollAccountantTodo todo, @PathVariable(name = "staffId") OfficeStaff staff) {

        OfficeStaffPayrollLog log = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                .findFirstByOfficeStaffAndPayrollAccountantTodo(staff, todo);

        if(log == null || log.getTransferred()) {
            throw new BusinessException("This transaction is already completed, you can't take any further action!");
        }

        if(todo.getSingleOfficeStaff()) {
            log.setTotalSalary(todo.getAmount());
            log.setTotalInAED(todo.getTotalInAED());
        }
        return ResponseEntity.ok(log);
    }


    @PreAuthorize("hasPermission('accountantTodo', 'officeStaff')")
    @RequestMapping(value = "/officeStaff/{todoId}/{staffId}/proofOfTransfer", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> officeStaff(@PathVariable(name = "todoId") PayrollAccountantTodo todo,
                                         @PathVariable(name = "staffId") OfficeStaff staff,
                                         @RequestParam(required = false, name = "attId") Long attachmentId) {

        Attachment attachment = null;
        if (attachmentId != null) {
            attachment = Setup.getRepository(AttachementRepository.class)
                    .findOne(attachmentId);
        }

        List<OfficeStaffPayrollLog> transferredLogs = new ArrayList<>();
        if(!todo.getSingleOfficeStaff()) {
            OfficeStaffPayrollLog log = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findFirstByOfficeStaffAndPayrollAccountantTodo(staff, todo);
            transferredLogs.add(log);
        } else {
            SelectQuery<OfficeStaffPayrollLog> query = new SelectQuery<>(OfficeStaffPayrollLog.class);
            query.filterBy("payrollAccountantTodo.id", "=", todo.getId())
                    .and("transferred", "=", false);
            transferredLogs.addAll(query.execute());
        }

        for(OfficeStaffPayrollLog log: transferredLogs) {
            log.setTransferred(true);
            log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
            if (attachment != null) {
                log.addAttachment(Storage.cloneTemporary(attachment, attachment.getTag()));
            }
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(log);
        }

        if(!todo.getSingleOfficeStaff()) {
            SelectQuery staffQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
            staffQuery.filterBy("officeStaff.id", "=", staff.getId())
                    .and("transferred", "=", false)
                    .and("willBeIncluded", "=", true)
                    .and("forEmployeeLoan", "=", false);
            List<OfficeStaffPayrollLog> staffLogs = staffQuery.execute();
            for (OfficeStaffPayrollLog log : staffLogs) {
                log.setTransferred(true);
                log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
            }
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(staffLogs);
        }
        PayrollAccountantTodoType type = PayrollAccountantTodoType.valueOf(todo.getTaskName());

        OfficeStaffPayrollLog log = transferredLogs.get(0);
        Double originalAmount = log.getTotalSalary();
        if(todo.getSingleOfficeStaff()) {
            log.setTotalSalary(todo.getAmount());
        }

        if(!todo.getSingleOfficeStaff()) {
            if (type == PayrollAccountantTodoType.BANK_TRANSFER) {
                todo.setLabel(String.format("Transfer Money to (%s) employees' bank accounts", todo.countUnCompletedStaffTransfer()));
            } else if (type == PayrollAccountantTodoType.PENSION_AUTHORITY) {
                todo.setAmount(todo.getAmount() - log.getContributionAmount());
                todo.setLabel(String.format("Transfer Money to Pension Authority for (%s) Employees", todo.countUnCompletedStaffTransfer()));
            }
        }

        if(todo.countUnCompletedHousemaidTransfer() == 0 && todo.countUnCompletedStaffTransfer() == 0) {
            todo.setCompleted(true);

            // Send bank transfer report to Adeeb
            if(type == PayrollAccountantTodoType.BANK_TRANSFER) {
                AsyncService asyncService = Setup.getApplicationContext().getBean(AsyncService.class);
                asyncService.allTransfersAreDone(todo);
            }
        }
        this.todoRepository.save(todo);

        if(type == PayrollAccountantTodoType.BANK_TRANSFER) {
            sendingTransactionService.bankTransferIsDone(log);
        }

        log.setTotalSalary(originalAmount);
        return this.okResponse();
    }

    @NoPermission
    @RequestMapping(value = "/allOfficeStaffs/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> officeStaffs(@PathVariable(name = "id") PayrollAccountantTodo todo) {
        SelectQuery<OfficeStaffPayrollLog> query = new SelectQuery<>(OfficeStaffPayrollLog.class);
        query.filterBy("payrollAccountantTodo.id", "=", todo.getId());

        List<Map<String, Object>> all = new ArrayList<>();
        for(OfficeStaffPayrollLog log: query.execute()) {
            Map<String, Object> map = new HashMap<>();
            map.put("log", log);
            map.put("staff", log.getOfficeStaff());
            all.add(map);
        }
        return ResponseEntity.ok(all);
    }

    @PreAuthorize("hasPermission('accountantTodo', 'housemaids')")
    @RequestMapping(value = "/housemaids/{id}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> housemaids(@PathVariable(name = "id") PayrollAccountantTodo todo, Pageable pageable) {
        SelectQuery<HousemaidPayrollLog> query = new SelectQuery<>(HousemaidPayrollLog.class);
        query.filterBy("payrollAccountantTodo.id", "=", todo.getId())
                .and("transferred", "=", false)
                .and("willBeIncluded", "=", true);
        return ResponseEntity.ok(query.execute(pageable));
    }

    @PreAuthorize("hasPermission('accountantTodo', 'housemaid')")
    @RequestMapping(value = "/housemaid/{todoId}/{housemaidId}", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> housemaid(@PathVariable(name = "todoId") PayrollAccountantTodo todo, @PathVariable(name = "housemaidId") Housemaid housemaid) {

        return ResponseEntity.ok(Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findFirstByHousemaidAndPayrollAccountantTodo(housemaid, todo));
    }

    @PreAuthorize("hasPermission('accountantTodo', 'housemaid')")
    @RequestMapping(value = "/housemaid/{todoId}/{staffId}/proofOfTransfer", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> housemaid(@PathVariable(name = "todoId") PayrollAccountantTodo todo,
                                         @PathVariable(name = "staffId") Housemaid housemaid,
                                         @RequestParam(required = false, name = "attId") Long attachmentId) {

        Attachment attachment = null;
        if (attachmentId != null) {
            attachment = Setup.getRepository(AttachementRepository.class)
                    .findOne(attachmentId);
        }
        HousemaidPayrollLog log = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findFirstByHousemaidAndPayrollAccountantTodo(housemaid, todo);
        log.setTransferred(true);
        log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
        if (attachment != null) {
            log.addAttachment(attachment);
        }
        Setup.getRepository(HousemaidPayrollLogRepository.class).save(log);

        if(todo.getTaskName().equals(PayrollAccountantTodoType.CASH.toString())) {
            todo.setRemainingAmount(todo.getRemainingAmount() - log.getTotalSalary());
        }

        if(todo.countUnCompletedHousemaidTransfer() == 0 && todo.countUnCompletedStaffTransfer() == 0) {
            todo.setCompleted(true);
        }
        this.todoRepository.save(todo);
        return this.okResponse();
    }

    @PreAuthorize("hasPermission('accountantTodo', 'cashAdvanceToDoId')")
    @RequestMapping(value = "/cashAdvanceToDoId", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> cashAdvanceToDoId() {

        Long todoId = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findLastAccountantToDoOfType(PayrollAccountantTodoType.CASH.toString());
        if (todoId == null) return this.okResponse();

        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class)
                .findOne(todoId);

        HashMap<String, Object> response = new HashMap<>();
        response.put("id", todo.getId());
        response.put("totalSalary", NumberFormatter.twoDecimalPoints(todo.getAmount()));
        response.put("remainingAmount", NumberFormatter.twoDecimalPoints(todo.getRemainingAmount()));

        return ResponseEntity.ok(response);
    }


//    @PreAuthorize("hasPermission('accountantTodo', 'testGenerateFile')")
//    @RequestMapping(value = "/testGenerateFile/{id}", method = RequestMethod.GET)
//    @ResponseBody
//    public ResponseEntity<?> testGenerateFile(@PathVariable(name = "id") PayrollAccountantTodo todo) throws IOException, URISyntaxException {
//        List<HousemaidPayrollLog> housemaidPayrollLogs = Setup.getRepository(HousemaidPayrollLogRepository.class)
//                .findByPayrollAccountantTodo(todo);
//
//        List<OfficeStaffPayrollLog> officeStaffPayrollLogs = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
//                .findByPayrollAccountantTodo(todo);
//        PayrollAccountantTodoType type = PayrollAccountantTodoType.valueOf(todo.getTaskName());
//        Attachment attachment = null;
//        if (type == PayrollAccountantTodoType.WPS) {
//            attachment = transferFilesService.generateWPSTransferFile(todo.getMonthlyPaymentRule(), housemaidPayrollLogs, officeStaffPayrollLogs);
//        } else if (type == PayrollAccountantTodoType.LOCAL_TRANSFER) {
//            attachment = transferFilesService.generateLocalTransferFile(todo.getMonthlyPaymentRule(), housemaidPayrollLogs, officeStaffPayrollLogs);
//        } else if (type == PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) {
//            attachment = transferFilesService.generateInternationalTransferFile(todo.getMonthlyPaymentRule(), officeStaffPayrollLogs);
//        }
//
//        return ResponseEntity.ok(attachment);
//    }

    public boolean isCellEmpty(final Cell cell) {
        if (cell == null) { // use row.getCell(x, Row.CREATE_NULL_AS_BLANK) to avoid null cells
            return true;
        }

        if (cell.getCellType() == CellType.BLANK) {
            return true;
        }

        if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().isEmpty()) {
            return true;
        }

        return false;
    }

    @NoPermission
    @RequestMapping(  value = "/exportToExcel/{id}" , method = RequestMethod.GET)
    public ResponseEntity<?> exportToExcel(HttpServletResponse response, @PathVariable(name = "id") PayrollAccountantTodo todo) throws IOException {
        String title = "";
        String[] headers;
        InputStream is = null;
        try {
            switch (PayrollAccountantTodoType.valueOf(todo.getTaskName())) {
                case BANK_TRANSFER:
                    List<OfficeStaffPayrollLog> bankTransferResult = officeStaffPayrollLogRepository.findBankTransferRecords(todo);
                    if (bankTransferResult == null || bankTransferResult.size() == 0)
                        throw new BusinessException("There are no results to show!");
                    title = String.format("Bank Transfer Todo " + DateUtil.formatFullDate(new Date()) + ".xls");
                    is = exportToExcelBankTransferFile(bankTransferResult);
                    break;
                case PENSION_AUTHORITY:
                    List<PensionAuthorityProjection> pensionAuthorityResult = project(officeStaffPayrollLogRepository.findPensionAuthorityRecords(todo), PensionAuthorityProjection.class);
                    if (pensionAuthorityResult == null || pensionAuthorityResult.size() == 0)
                        throw new BusinessException("There are no results to show!");
                    title = String.format("Pension Authority " + DateUtil.formatFullDate(new Date()) + ".csv");
                    headers = new String[]{"employeeName", "idNumber", "currency", "amount", "pensionAmount"};
                    PensionAuthorityProjection.Counter.resetCounter();
                    is = generateCsv(pensionAuthorityResult, PensionAuthorityProjection.class, headers);

                    break;
            }


            if(is == null)
                throw new BusinessException("No Report for this type");

            createDownloadResponse(response,title , is);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    @PreAuthorize("hasPermission('accountantTodo', 'allTransfersAreDoneAPI')")
    @RequestMapping(  value = "/allTransfersAreDoneAPI/{id}" , method = RequestMethod.GET)
    public ResponseEntity<?> allTransfersAreDoneAPI(@PathVariable(name = "id") PayrollAccountantTodo todo){
        if (todo != null)
            asyncService.allTransfersAreDone(todo);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('accountantTodo', 'tesSendingPayrollFilesEmail')")
    @RequestMapping(  value = "/tesSendingPayrollFilesEmail/{id}" , method = RequestMethod.GET)
    public ResponseEntity<?> tesSendingPayrollFilesEmail(@PathVariable(name = "id") PayrollAccountantTodo todo){
        if (todo != null)
            accountantToDoService.requestApprovalToProceed(todo.getId());
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('accountantTodo', 'fetchPayrollFile')")
    @RequestMapping(  value = "/fetchPayrollFile" , method = RequestMethod.POST)
    public ResponseEntity<?> fetchPayrollFile(@RequestBody PayrollFilesArchivingClass archivingClass){
        User currentUser = CurrentRequest.getUser();
        if (currentUser.getEmail() == null || currentUser.getEmail().isEmpty())
            throw new BusinessException("The current user doesn't have an email");

        String requestedFiles ="";
        SelectQuery<PayrollAccountantTodo> selectQuery = new SelectQuery<>(PayrollAccountantTodo.class);
        selectQuery.joinFetch("monthlyPaymentRule");

        //1- Employees Types
        SelectFilter employeesTypeFilter = new SelectFilter("monthlyPaymentRule.employeeTypeList", "member of", archivingClass.getEmployeesTypes().get(0));
        requestedFiles += archivingClass.getEmployeesTypes().get(0).getLabel();
        int i = 1;
        while (i < archivingClass.getEmployeesTypes().size()) {
            requestedFiles += " - " + archivingClass.getEmployeesTypes().get(i).getLabel();
            employeesTypeFilter.or("monthlyPaymentRule.employeeTypeList", "member of", archivingClass.getEmployeesTypes().get(i));
            i++;
        }
        selectQuery.filterBy(employeesTypeFilter);

        //2- payrollType
        selectQuery.filterBy("monthlyPaymentRule.payrollType", "=", archivingClass.getPayrollType());
        requestedFiles += " - " + archivingClass.getPayrollType().toString();

        requestedFiles += " - " + archivingClass.getFileType();

        //3- payment method
        selectQuery.filterBy("taskName", "=", archivingClass.getPaymentMethod().toString());
        requestedFiles += " - " + archivingClass.getPaymentMethod().toString();

        //4- payrollMonth
        selectQuery.filterBy("monthlyPaymentRule.payrollMonth", "=", archivingClass.getPayrollMonth());
        requestedFiles += " - " + DateUtil.formatFullMonthDashedFullYear(archivingClass.getPayrollMonth());

        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("completed", "=", true);


        List<PayrollAccountantTodo> payrollAccountantTodos = selectQuery.execute();
        if (payrollAccountantTodos.size() == 0)
            throw new BusinessException("Selected Payroll Month is not Paid yet");

        Attachment temp = null;
        List<Attachment> neededFiles = new ArrayList<>();

        for (PayrollAccountantTodo todo : payrollAccountantTodos) {
            if (archivingClass.getFileType().equalsIgnoreCase("detailed audit file")) {
                if (archivingClass.getEmployeesTypes().size() > 1) {
                    temp = todo.getAttachment("OfficeStaffDetailedPayrollFile");
                    if (temp != null)
                        neededFiles.add(temp);

                    temp = todo.getAttachment("HousemaidDetailedPayrollFile");
                } else {
                    temp = todo.getAttachment(archivingClass.getEmployeesTypes().get(0).equals(PaymentRuleEmployeeType.HOUSEMAIDS) ? "HousemaidDetailedPayrollFile" : "OfficeStaffDetailedPayrollFile");
                }
            } //payment files
            else {
                switch (archivingClass.getPaymentMethod()) {
                    case WPS:
                        temp = todo.getAttachment("WPSTransferFile");
                        break;
                    case LOCAL_TRANSFER:
                        neededFiles.add(todo.getAttachment("LocalTransferFile"));
                        break;
                    case INTERNATIONAL_TRANSFER:
                        neededFiles.add(todo.getAttachment("InternationalTransferFile"));
                        break;
                    case BANK_TRANSFER:
                        neededFiles.add(todo.getAttachment("BankTransferFile"));
                        break;
                }
            }
            if (temp != null)
                neededFiles.add(temp);
        }

        //send an email with all attachment to the current user
        if (neededFiles.size() > 0) {
            List<EmailRecipient> recipients = Recipient.parseEmailsString(currentUser.getEmail());
            TextEmail email = new TextEmail("Payroll Files of " + requestedFiles, "Please find attached files");
            for (Attachment att : neededFiles)
                email.addAttachement(att);
            mailService.sendEmail(new MailObject.builder(email, EmailReceiverType.Office_Staff)
                    .recipients(recipients)
                    .secure()
                    .build());
        }
        return ResponseEntity.ok("Your requested file is being generated and will be sent to your email once done.");
    }

    public FileInputStream exportToExcelBankTransferFile(List<OfficeStaffPayrollLog> officeStaffLogs) throws URISyntaxException, IOException {
        if(officeStaffLogs == null || officeStaffLogs.isEmpty())
            return null;
        URL resource = PayrollGenerationLibrary.class.getResource("/transferfiles/Overseas bank transfer file.xlsx");
        File file = new File(resource.toURI());
        try ( FileInputStream inputStream = new FileInputStream(file)) {
            Workbook workbook = new XSSFWorkbook(inputStream);

            Sheet spreadsheet = workbook.getSheetAt(0);

            CellStyle style = createTextStyle(workbook);

            int rowCount = 1;
            for (OfficeStaffPayrollLog log : officeStaffLogs) {
                Row row = spreadsheet.createRow(rowCount++);
                int cellCount = 0;
                row.createCell(cellCount++).setCellValue("SAL TRF");
                row.createCell(cellCount++).setCellValue(log.getPaymentType());
                row.createCell(cellCount++).setCellValue("***********************");
                row.createCell(cellCount++).setCellValue(NumberFormatter.formatNumber(log.getTotalSalaryInADCBReport()));
                row.createCell(cellCount++).setCellValue(log.getPaymentCurrency());
                row.createCell(cellCount++).setCellValue(DateUtil.formatDateDashedV2(DateUtil.addDays(new Date(), 1)));
                row.createCell(cellCount++).setCellValue(StringHelper.truncateToLimit(log.getAccountHolderName(), 35));
                row.createCell(cellCount++).setCellValue(StringHelper.truncateToLimit(log.getBeneficiaryAddress1(), 35));
                row.createCell(cellCount++).setCellValue(log.getBeneficiaryAddress2());
                row.createCell(cellCount++).setCellValue(log.getBeneficiaryAddress3());
                row.createCell(cellCount++).setCellValue(log.getBeneficiaryAccount());
                row.createCell(cellCount++).setCellValue(StringHelper.truncateToLimit(log.getBeneficiaryBankName(), 35));
                row.createCell(cellCount++).setCellValue(log.getBeneficiaryBankAddress1());
                row.createCell(cellCount++).setCellValue(log.getBeneficiaryBankAddress2());
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue(!log.getInsideUAE() ? (Objects.isNull(log.getSwiftCode()) ? "" : log.getSwiftCode()) : "");
                row.createCell(cellCount++).setCellValue("SAL List");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("Our");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("");
                row.createCell(cellCount++).setCellValue("SAL");
                row.createCell(cellCount++).setCellValue("REF");
                row.createCell(cellCount++).setCellValue("");

                for (int i = 0; i < cellCount; i++) {
                    row.getCell(i).setCellStyle(style);
                }
            }

            String fileName = "Bank Transfer File" + ".xls";

            File directory = Paths.get(System.getProperty("java.io.tmpdir"),
                    "payroll").toFile();

            directory.mkdir();

            File tempFile = Paths.get(System.getProperty("java.io.tmpdir"),
                            "payroll/" + fileName)
                    .toFile();

            try (FileOutputStream out = new FileOutputStream(tempFile)) {
                workbook.write(out);
            }

            return new FileInputStream(tempFile);
        }
    }


}
