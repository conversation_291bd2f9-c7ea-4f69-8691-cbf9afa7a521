package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.CardStatus;
import com.magnamedia.helper.CCAppContentHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.YayaAppContentHelper;
import com.magnamedia.module.type.HousemaidUnpaidStatus;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.util.*;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 4/19/2021
 **/
@RequestMapping("/yayaApp")
@RestController
public class YayaAppController extends BaseRepositoryController<Housemaid> {

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    private CCAppContentHelper ccAppContentHelper;

    @Autowired
    private YayaAppContentHelper yayaAppContentHelper;

    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;

    @Autowired
    private PaySlipsController paySlipsController;

    @Override
    public BaseRepository<Housemaid> getRepository() {
        return housemaidRepository;
    }

    @NoPermission
    @RequestMapping(path = "/getHousemaidPayrollDetails/{maid_id}", method = RequestMethod.GET)
    public ResponseEntity<?> getHousemaidPayrollDetails(@PathVariable(value = "maid_id") Housemaid housemaid,
                                                        @RequestParam(defaultValue = "en", required = false) String lang) {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        java.sql.Date nextPayrollMonth;
        java.sql.Date nextPrimaryPaymentDate;
        java.sql.Date nextPayrollMonthForCon2;
        java.sql.Date nextPrimaryPaymentDateForCon2;
        Boolean onPaymentDateAndTodoIsClosed = false;
        Boolean differentMonthsForCon2AndCon4 = false;

        List<MonthlyPaymentRule> nextPayrollMonthlyRulesForCon4 = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDate, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        List<MonthlyPaymentRule> nextPayrollMonthlyRulesForCon2 = nextPayrollMonthlyRulesForCon4;
        if(nextPayrollMonthlyRulesForCon4.size() != 0) {
            MonthlyPaymentRule nextPayrollMonthlyRule = nextPayrollMonthlyRulesForCon4.get(0);
            nextPayrollMonth = nextPayrollMonthlyRule.getPayrollMonth();

            //if the next monthly payment rule is finished then we are on Payment date
            // for Con-2 only if the payroll log is transferred then consider it as previous month (skip one month)
            // for Con-4 if the payroll log is transferred and payslip sent then consider it as previous month
            if (nextPayrollMonthlyRule.getFinished()) {
                HousemaidPayrollLog currentMonthLog = Setup.getApplicationContext().getBean(HousemaidPayrollLogRepository.class).findFirstByPayrollMonthAndHousemaid(nextPayrollMonth, housemaid);
                if(currentMonthLog != null && currentMonthLog.getTransferred()) {
                    onPaymentDateAndTodoIsClosed = true;
                    nextPayrollMonthlyRulesForCon2 = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(new java.sql.Date(DateUtil.addDays(currentDate, 2).getTime()), PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);

                    if (!currentMonthLog.getPayslipSent())
                        differentMonthsForCon2AndCon4 = true;
                }
            }
        }

        //normal case
        if (!onPaymentDateAndTodoIsClosed || (onPaymentDateAndTodoIsClosed && !differentMonthsForCon2AndCon4)) {
            nextPayrollMonthlyRulesForCon4 = nextPayrollMonthlyRulesForCon2;
            if(nextPayrollMonthlyRulesForCon4.size() != 0) {
                MonthlyPaymentRule nextPayrollMonthlyRule = nextPayrollMonthlyRulesForCon4.get(0);
                nextPayrollMonth = nextPayrollMonthlyRule.getPayrollMonth();
                nextPrimaryPaymentDate = nextPayrollMonthlyRule.getPaymentDate();
            }else {
                nextPayrollMonth = new Date(new LocalDate(new java.util.Date()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
                nextPrimaryPaymentDate = new Date(new LocalDate(nextPayrollMonth).withDayOfMonth(3).toDate().getTime());
            }

            return ResponseEntity.ok(yayaAppContentHelper.getCon4WithCon2ForMaidNormalCase(housemaid, nextPayrollMonth, nextPrimaryPaymentDate, lang));
        }else { //special case we are on payment date, the payroll log is transferred but the payslip is not sent

            Map<String, Object> details = new HashMap<>();
            //==========================================================
            String whenYouWillReceiveSalaryTemplate_con4 = "";
            Boolean showInHomePage = false;
            String payslipTemplate_con2 = "";

            //=========== get Con-4 template =============/
            if(nextPayrollMonthlyRulesForCon4.size() != 0) {
                MonthlyPaymentRule nextPayrollMonthlyRule = nextPayrollMonthlyRulesForCon4.get(0);
                nextPayrollMonth = nextPayrollMonthlyRule.getPayrollMonth();
                nextPrimaryPaymentDate = nextPayrollMonthlyRule.getPaymentDate();
            }else {
                nextPayrollMonth = new Date(new LocalDate(new java.util.Date()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
                nextPrimaryPaymentDate = new Date(new LocalDate(nextPayrollMonth).withDayOfMonth(3).toDate().getTime());
            }
            whenYouWillReceiveSalaryTemplate_con4 = yayaAppContentHelper.getCon2Or4ForMaid(housemaid, nextPayrollMonth, nextPrimaryPaymentDate, true);

            //=========== get Con-2 template =============/
            if(nextPayrollMonthlyRulesForCon2.size() != 0) {
                MonthlyPaymentRule nextPayrollMonthlyRule = nextPayrollMonthlyRulesForCon2.get(0);
                nextPayrollMonthForCon2 = nextPayrollMonthlyRule.getPayrollMonth();
                nextPrimaryPaymentDateForCon2 = nextPayrollMonthlyRule.getPaymentDate();
            }else {
                nextPayrollMonthForCon2 = new Date(new LocalDate(new java.util.Date()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
                nextPrimaryPaymentDateForCon2 = new Date(new LocalDate(nextPayrollMonthForCon2).withDayOfMonth(3).toDate().getTime());
            }
            payslipTemplate_con2 = yayaAppContentHelper.getCon2Or4ForMaid(housemaid, nextPayrollMonthForCon2, nextPrimaryPaymentDateForCon2, false);

            if (whenYouWillReceiveSalaryTemplate_con4.equalsIgnoreCase("@maid_special_condition@"))
                showInHomePage = true;

            String payslipsURL ="";
            java.sql.Date prevPayrollMonth = new java.sql.Date(DateUtil.getPreviousMonths(nextPayrollMonthForCon2,1).getTime());
            HousemaidPayrollLog log = Setup.getApplicationContext().getBean(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonthLessThanEqualAndTransferredTrueOrderByCreationDateDesc(housemaid, prevPayrollMonth);
            if(!"amh".equals(lang) && !"om".equals(lang) && !"tl".equals(lang) && !"hi".equals(lang))
                lang = "en";
            if(log != null)
                payslipsURL = Setup.getApplicationContext().getBean(PaySlipsController.class).generatePayslipPublicURLWithoutShorten(housemaid, log.getPayrollMonth(), lang);

            details.put("payslipURL",payslipsURL);
            details.put("whenYouWillReceiveSalaryTemplate_con4", whenYouWillReceiveSalaryTemplate_con4);
            details.put("payslipTemplate_con2", payslipTemplate_con2);
            details.put("showInHomePage", showInHomePage);

            return ResponseEntity.ok(details);
        }
    }

}