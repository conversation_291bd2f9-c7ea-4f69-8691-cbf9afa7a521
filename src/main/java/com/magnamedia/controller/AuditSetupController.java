package com.magnamedia.controller;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AuditSetup;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.repository.AuditSetupRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.service.Auditor.MOLFileHandlerService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/auditSetup")
@RestController
public class AuditSetupController extends BaseRepositoryController<AuditSetup> {

    @Autowired
    AuditSetupRepository auditSetupRepository;

    @Autowired
    MOLFileHandlerService molFileHandlerService;

    @Autowired
    OfficeStaffRepository officeStaffRepository;

    @Autowired
    HousemaidRepository housemaidRepository;

    @Override
    public BaseRepository<AuditSetup> getRepository() {
        return auditSetupRepository;
    }

    @NoPermission
    @RequestMapping("/getLastAuditSetup")
    public ResponseEntity<?> getLastAuditSetup() {
        AuditSetup lastAuditSetup = auditSetupRepository.findFirstByOrderByIdDesc();

        return new ResponseEntity<>(lastAuditSetup, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping("/getLastMOLFile")
    public ResponseEntity<?> getLastMOLFile() {
        AuditSetup lastAuditSetup = auditSetupRepository.findFirstByOrderByIdDesc();

        if (lastAuditSetup != null) {
            List<Attachment> molFiles = lastAuditSetup.getAttachments().stream().
                    filter(a -> a.getTag().equals("mol_list_file")).
                    sorted(Comparator.comparing(Attachment::getCreationDate).reversed()).
                    collect(Collectors.toList());

            if (molFiles != null && molFiles.size() > 0) {
                return new ResponseEntity<>(molFiles.get(0), HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Transactional
    @NoPermission
    @RequestMapping("/uploadNewMolFile/{id}")
    public ResponseEntity<?> uploadNewMolFile(@PathVariable(name = "id") Attachment attachment) {
        if (attachment == null || !attachment.getTag().equals("mol_list_file")) {

            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } else {

            try {
                AuditSetup lastAuditSetup = auditSetupRepository.findFirstByOrderByIdDesc();
                if (lastAuditSetup != null) {
                    lastAuditSetup.addAttachment(attachment);
                    auditSetupRepository.save(lastAuditSetup);

                    //update all staffs and housemaids
                    List<String> uniqueIdsList = molFileHandlerService.getUniqueIdListFromMolFile();

                    if(uniqueIdsList != null && uniqueIdsList.size() > 0) {
                        List<Long> officestaffsNotInMOLList = officeStaffRepository.findNotInMOLFile(uniqueIdsList);
                        List<Long> officestaffsInMOLList = officeStaffRepository.findInMOLFile(uniqueIdsList);
                        int updatedNotInFileOfficeStaff = 0 ;
                        int updatedInFileOfficeStaff = 0;
                        int updatedNotInFileHousemaids = 0;
                        int updatedInFileHousemaids = 0;
                        if(officestaffsNotInMOLList != null && officestaffsNotInMOLList.size() > 0)
                            updatedNotInFileOfficeStaff = officeStaffRepository.updateNotInMOLFile(officestaffsNotInMOLList);
                        if(officestaffsInMOLList != null && officestaffsInMOLList.size() > 0)
                            updatedInFileOfficeStaff = officeStaffRepository.updateInMOLFile(officestaffsInMOLList);

                        List<Long> housemaidNotInMolList = housemaidRepository.findNotInMOLFile(uniqueIdsList);
                        List<Long> housemaidInInMolList = housemaidRepository.findInMOLFile(uniqueIdsList);

                        if(housemaidNotInMolList != null && housemaidNotInMolList.size() > 0)
                            updatedNotInFileHousemaids = housemaidRepository.updateNotInMOLFile(housemaidNotInMolList);
                        if(housemaidInInMolList != null && housemaidInInMolList.size() > 0)
                            updatedInFileHousemaids = housemaidRepository.updateInMOLFile(housemaidInInMolList);
//                        DebugHelper.sendMail("<EMAIL>", "officestaffsNotInMOLList: " + updatedNotInFileOfficeStaff + " \nupdatedInFileOfficeStaff: " + updatedInFileOfficeStaff
//                                + "\nupdatedNotInFileHousemaids: " + updatedNotInFileHousemaids + "\nupdatedInFileHousemaids: " + updatedInFileHousemaids);
                    }
                    return ResponseEntity.ok("updated");
                } else {
                    return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
                }
            }catch (Exception ex){
                DebugHelper.sendExceptionMail(
                        "<EMAIL>", ex,
                        "Exception occurred while uploadNewMolFile", false);
            }
        }
        return ResponseEntity.ok("");
    }
    @NoPermission
    @RequestMapping("/getUniqueIdListFromMolFile")
    public List<String> getUniqueIdListFromMolFile() {
        List<String> uniqueIdsList = new ArrayList<>();
        Attachment MOLFile;

        AuditSetup lastAuditSetup = auditSetupRepository.findFirstByOrderByIdDesc();
        if (lastAuditSetup != null) {

            //get Last MOL File
            Attachment molFile = lastAuditSetup.getAttachment("mol_list_file");

            if (molFile == null)
                throw new BusinessException("couldn't find the MOL file!");

            try {

                DataFormatter formatter = new DataFormatter();
                Workbook workbook = null;
                if (molFile.getExtension().equals("xlsx"))
                    workbook = new XSSFWorkbook(Storage.getStream(molFile));
                else if (molFile.getExtension().equals("xls"))
                    workbook = new HSSFWorkbook(Storage.getStream(molFile));

                if (workbook != null) {
                    Sheet sheet = workbook.getSheetAt(0);
                    Iterator<Row> rowIterator = sheet.iterator();
                    Row row = null;

                    //skip first row (it's only have the table headers)
                    if (rowIterator.hasNext())
                        rowIterator.next();

                    while (rowIterator.hasNext()) {
                        try {
                            row = rowIterator.next();
                            String personalCode = formatter.formatCellValue(row.getCell(0));
                            if (!StringUtils.isNumeric(personalCode))
                                continue;

                            uniqueIdsList.add(personalCode);
                        } catch (Exception e) {
                            DebugHelper.sendExceptionMail(
                                    "<EMAIL>", e,
                                    "Exception occurred while getUniqueIdListFromMolFile in AuditSetupController in the #row: " + row.getRowNum(), false);
                        }
                    }
                }
            } catch (Exception ex) {
                DebugHelper.sendExceptionMail(
                        "<EMAIL>", ex,
                        "Exception occurred while getUniqueIdListFromMolFile in AuditSetupController", false);
            }
        }

        return uniqueIdsList;
    }

    @Override
    protected ResponseEntity<?> updateEntity(AuditSetup entity) {
        //check if values are positive
        if (entity.getLoanAmountLimit() <= 0 || entity.getRepeatedAdditionAndBonusLimit() <= 0 || entity.getTerminationCompensationLimit() <= 0)
            throw new BusinessException("all values must be positive numbers!");
        return super.updateEntity(entity);
    }

    public String getNewUniquePaymentCode() {
        AuditSetup lastAuditSetup = auditSetupRepository.findFirstByOrderByIdDesc();

        //Set lastUniqueCode value
        Integer lastUniqueCode = 0;
        if(lastAuditSetup.getUniquePaymentCode() != null) {
            lastUniqueCode = Integer.parseInt(lastAuditSetup.getUniquePaymentCode());
        }
        lastUniqueCode++;

        // Save the new code to the audit setup
        lastAuditSetup.setUniquePaymentCode(String.valueOf(lastUniqueCode));
        auditSetupRepository.save(lastAuditSetup);

        // Format the new code with leading zeros and return it
        return String.format("AP%08d", lastUniqueCode);

    }
    
}
