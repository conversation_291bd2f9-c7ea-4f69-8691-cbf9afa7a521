package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.CardStatus;
import com.magnamedia.helper.CCAppContentHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@RequestMapping("/ccApp")
@RestController
public class CCAppController extends BaseRepositoryController<Housemaid> {

    @Autowired
    HousemaidRepository housemaidRepository;

    @Autowired
    HousemaidPayrollLogRepository HousemaidPayrollLogRepository;

    @Autowired
    MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private CCAppContentHelper ccAppContentHelper;
    @Autowired
    private PayrollAuditTodoRepository payrollAuditTodoRepository;

    @Autowired
    private PaymentRepository paymentRepository ;

    @Override
    public BaseRepository<Housemaid> getRepository() {
        return housemaidRepository;
    }

    @NoPermission
    @RequestMapping("/getHousemaidSalaryDetails/{id}")
    public ResponseEntity<?> getHousemaidSalaryDetails(@PathVariable(name = "id") Housemaid housemaid){
        Date currentDate = new Date();
        java.sql.Date prevPayrollMonth =
                new java.sql.Date(DateUtil.getPreviousMonths(DateUtil.getFirstOfMonthDate(currentDate),1).getTime());

        HousemaidPayrollLog lastPayrollLog = HousemaidPayrollLogRepository.
                findFirstByPayrollMonthAndHousemaid(prevPayrollMonth, housemaid);

        int workingDays = lastPayrollLog != null ? lastPayrollLog.getGroupOneDays() + lastPayrollLog.getGroupTwoDays() + lastPayrollLog.getGroupFourDays() + lastPayrollLog.getGroupFiveDays() + lastPayrollLog.getGroupSixDays() : 0;
        int monthDays = new LocalDate(prevPayrollMonth).dayOfMonth().getMaximumValue();
        boolean fullSalary = monthDays == workingDays;

        CardStatus cardStatus = housemaid.getCardStatus();
        if(cardStatus == null) cardStatus = CardStatus.REQUEST_SENT_TO_ANSARI;

        Map<String, Object> details = new HashMap<>();
        details.put("ansariCardStatus", cardStatus);


        //==========================================================

        List<MonthlyPaymentRule> prevMonthRules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonthAndPaymentRuleEmployeeType(prevPayrollMonth, PaymentRuleEmployeeType.HOUSEMAIDS);

        int primaryPaymentDay = prevMonthRules.size() > 0 ? new LocalDate(prevMonthRules.get(0).getPaymentDate()).getDayOfMonth() : 3,
                firstSecondaryDay = prevMonthRules.size() > 1 ? new LocalDate(prevMonthRules.get(1).getPaymentDate()).getDayOfMonth() : 7,
                secondSecondaryDay = prevMonthRules.size() > 2 ? new LocalDate(prevMonthRules.get(2).getPaymentDate()).getDayOfMonth() : 14,
                thirdSecondaryDay = prevMonthRules.size() > 3 ? new LocalDate(prevMonthRules.get(3).getPaymentDate()).getDayOfMonth() : 21;

        //==========================================================


        boolean wasOnVacation = ccAppContentHelper.hasThisStatusOnPrimaryPaymentDate(housemaid.getId(), HousemaidStatus.ON_VACATION, new LocalDate(prevPayrollMonth).withDayOfMonth(primaryPaymentDay));

        boolean wasNoShow = ccAppContentHelper.hasThisStatusOnPrimaryPaymentDate(housemaid.getId(), HousemaidStatus.NO_SHOW, new LocalDate(prevPayrollMonth).withDayOfMonth(primaryPaymentDay));

        boolean onHold = housemaid.getExcludedFromPayroll() != null && housemaid.getExcludedFromPayroll();

        // When and how section

        java.sql.Date todayDate = new java.sql.Date(System.currentTimeMillis());

        LocalDate now = new LocalDate(todayDate);
        LocalDate startDate = new LocalDate(housemaid.getStartDate());

        boolean receiveSalaryCase1 = CardStatus.CARD_COLLECTED == cardStatus
                && (now.getDayOfMonth() >= 15 || now.getDayOfMonth() < primaryPaymentDay),

                receiveSalaryCase2 = CardStatus.CARD_READY_FOR_PICKUP == cardStatus
                        && (now.getDayOfMonth() >= 15 || now.getDayOfMonth() < primaryPaymentDay
                        || (now.getDayOfMonth() >= primaryPaymentDay && now.getDayOfMonth() <= 14 && !onHold)),


                receiveSalaryCase3 = CardStatus.REQUEST_SENT_TO_ANSARI == cardStatus
                        && (now.getDayOfMonth() < primaryPaymentDay || now.getDayOfMonth() >= 15
                        || (now.getDayOfMonth() >= primaryPaymentDay && now.getDayOfMonth() <= 14 && !onHold)),

                receiveSalaryCase4 = !(wasNoShow || wasOnVacation) &&
                        now.getDayOfMonth() >= primaryPaymentDay
                        && now.getDayOfMonth() <= 14
                        && CardStatus.CARD_COLLECTED == cardStatus;


        details.put("receiveSalaryCase1", receiveSalaryCase1);
        details.put("receiveSalaryCase2", receiveSalaryCase2);
        details.put("receiveSalaryCase3", receiveSalaryCase3);
        details.put("receiveSalaryCase4", receiveSalaryCase4);

        //==========================================================

        boolean onHoldCase = (wasOnVacation || wasNoShow)
                && housemaid.getStatus() != HousemaidStatus.ON_VACATION
                && housemaid.getStatus() != HousemaidStatus.NO_SHOW;

        details.put("onHoldCase", onHoldCase);


        // Question about salary

        List<PayrollManagerNote> deductions = payrollManagerNoteRepository
                .findByHousemaidAndNoteType(housemaid,
                        lockDateService.getLockDate(prevPayrollMonth, -1, PaymentRuleEmployeeType.HOUSEMAIDS),
                        lockDateService.getLockDate(prevPayrollMonth, 0, PaymentRuleEmployeeType.HOUSEMAIDS),
                        AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION);

        boolean questionAboutSalaryCase1 = lastPayrollLog != null &&
                (!deductions.isEmpty() || lastPayrollLog.getGroupTwoDays() > 0),

                questionAboutSalaryCase2 = lastPayrollLog != null
                        && deductions.isEmpty()
                        && lastPayrollLog.getLoanRepayment() != null
                        && lastPayrollLog.getLoanRepayment() > 0d,

                questionAboutSalaryCase3 = lastPayrollLog != null &&
                        (lastPayrollLog.getTotalDeduction() == null ||
                                lastPayrollLog.getTotalDeduction() == 0d)
                        && fullSalary && lastPayrollLog.getGroupTwoDays() == 0,

                questionAboutSalaryCase4 = lastPayrollLog != null &&
                        deductions.isEmpty() &&
                        (lastPayrollLog.getLoanRepayment() == null ||
                                lastPayrollLog.getLoanRepayment() == 0d)
                        && !fullSalary && lastPayrollLog.getGroupTwoDays() == 0,

                questionAboutSalaryCase6 = lastPayrollLog == null &&
                        !startDate.isBefore(new LocalDate(prevPayrollMonth).withDayOfMonth(27)) &&
                        !startDate.isAfter(new LocalDate(prevPayrollMonth).dayOfMonth().withMaximumValue()),

                questionAboutSalaryCase7 = lastPayrollLog == null &&
                        startDate.isAfter(new LocalDate(prevPayrollMonth).dayOfMonth().withMaximumValue());

        details.put("questionAboutSalaryCase1", questionAboutSalaryCase1);
        details.put("questionAboutSalaryCase2", questionAboutSalaryCase2);
        details.put("questionAboutSalaryCase3", questionAboutSalaryCase3);
        details.put("questionAboutSalaryCase4", questionAboutSalaryCase4);
        details.put("questionAboutSalaryCase5", onHoldCase);
        details.put("questionAboutSalaryCase6", questionAboutSalaryCase6);
        details.put("questionAboutSalaryCase7", questionAboutSalaryCase7);

        Predicate<Contract> byContractStatus = contract -> contract.getStatus() == ContractStatus.ACTIVE;
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(housemaid).stream()
                .filter(byContractStatus).collect(Collectors.<Contract>toList());

        if (activeContracts.size() > 0) {
            details.put("notifySalaryRelease", activeContracts.get(0).getNotifySalaryRelease());
        } else {
            details.put("notifySalaryRelease", false);
        }

        details.put("ansariLocation", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ANSARI_LOCATION));
        details.put("ansariAddress", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ANSARI_MAIN_BRANCH_ADDRESS));


        String molNumber = "";
        NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class)
                .findFirstByHousemaidOrderByCreationDateDesc(housemaid);

        if(newRequest != null) {
            molNumber = newRequest.getEmployeeUniqueId();
        }

        details.put("molNumber", molNumber);

        return new ResponseEntity<>(details, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping("/getHousemaidSalaryDetailsV2/{id}")
    public ResponseEntity<?> getHousemaidSalaryDetailsV2(@PathVariable(name = "id") Housemaid housemaid) {
        Date currentDate = new Date();
        java.sql.Date currentDateSql  = new java.sql.Date(System.currentTimeMillis());
        java.sql.Date prevPayrollMonth =
                new java.sql.Date(DateUtil.getPreviousMonths(DateUtil.getFirstOfMonthDate(currentDate), 1).getTime());

        HousemaidPayrollLog lastPayrollLog = HousemaidPayrollLogRepository.
                findFirstByPayrollMonthAndHousemaid(prevPayrollMonth, housemaid);

        int workingDays = lastPayrollLog != null ? lastPayrollLog.getGroupOneDays() + lastPayrollLog.getGroupTwoDays() + lastPayrollLog.getGroupFourDays() + lastPayrollLog.getGroupFiveDays() + lastPayrollLog.getGroupSixDays() : 0;
        int monthDays = new LocalDate(prevPayrollMonth).dayOfMonth().getMaximumValue();
        boolean fullSalary = monthDays == workingDays;

        CardStatus cardStatus = housemaid.getCardStatus();
        if (cardStatus == null) cardStatus = CardStatus.REQUEST_SENT_TO_ANSARI;

        Map<String, Object> details = new HashMap<>();
        details.put("ansariCardStatus", cardStatus);


        //==========================================================

        List<MonthlyPaymentRule> prevMonthRules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonthAndPaymentRuleEmployeeType(prevPayrollMonth, PaymentRuleEmployeeType.HOUSEMAIDS);

        int primaryPaymentDay = prevMonthRules.size() > 0 ? new LocalDate(prevMonthRules.get(0).getPaymentDate()).getDayOfMonth() : 3,
                firstSecondaryDay = prevMonthRules.size() > 1 ? new LocalDate(prevMonthRules.get(1).getPaymentDate()).getDayOfMonth() : 7,
                secondSecondaryDay = prevMonthRules.size() > 2 ? new LocalDate(prevMonthRules.get(2).getPaymentDate()).getDayOfMonth() : 14,
                thirdSecondaryDay = prevMonthRules.size() > 3 ? new LocalDate(prevMonthRules.get(3).getPaymentDate()).getDayOfMonth() : 21;

        //==========================================================


        boolean wasOnVacation = ccAppContentHelper.hasThisStatusOnPrimaryPaymentDate(housemaid.getId(), HousemaidStatus.ON_VACATION, new LocalDate(prevPayrollMonth).withDayOfMonth(primaryPaymentDay));

        boolean wasNoShow = ccAppContentHelper.hasThisStatusOnPrimaryPaymentDate(housemaid.getId(), HousemaidStatus.NO_SHOW, new LocalDate(prevPayrollMonth).withDayOfMonth(primaryPaymentDay));

        boolean onHold = housemaid.getExcludedFromPayroll() != null && housemaid.getExcludedFromPayroll();

        boolean hasEID = housemaid.getVisaNewRequest() != null && housemaid.getVisaNewRequest().getNewEidNumber() != null && !housemaid.getVisaNewRequest().getNewEidNumber().isEmpty();

        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);
        //first section
        String firstSectionTemplate = "";
        if (!HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType()))
            firstSectionTemplate = "@received_salary_date@";
        else {
            firstSectionTemplate = "@"+getFirstSectionTemplate(housemaid)+"@";
        }
        details.put("firstSectionTemplate", firstSectionTemplate);


        // When and how section
        LocalDate startDate = new LocalDate(housemaid.getStartDate());

        boolean receiveSalaryCase1 = CardStatus.CARD_COLLECTED == cardStatus,
                receiveSalaryCase2 = CardStatus.CARD_READY_FOR_PICKUP == cardStatus && hasEID,
                receiveSalaryCase3 = CardStatus.REQUEST_SENT_TO_ANSARI == cardStatus && hasEID,
                receiveSalaryCase4 = CardStatus.CARD_READY_FOR_PICKUP == cardStatus && !hasEID,
                receiveSalaryCase5 = CardStatus.REQUEST_SENT_TO_ANSARI == cardStatus && !hasEID;

        String receiveSalaryCaseTemplate = receiveSalaryCase1 ? "@has_atm_case1@" :
                receiveSalaryCase2 ? "@has_eid_atm_ready_case2@" :
                        receiveSalaryCase3 ? "@has_eid_atm_not_ready_case3@" :
                                receiveSalaryCase4 ? "@no_eid_atm_ready_case4@" :
                                        receiveSalaryCase5 ? "@no_eid_atm_not_ready_case5@" : "";

        details.put("receiveSalaryCaseTemplate", receiveSalaryCaseTemplate);

        //==========================================================

        boolean onHoldCase = (wasOnVacation || wasNoShow)
                && housemaid.getStatus() != HousemaidStatus.ON_VACATION
                && housemaid.getStatus() != HousemaidStatus.NO_SHOW;

        //in case maid visa, don't make onHoldCase true because we don't to view the template @salary_on_hold@
        details.put("onHoldCase", !HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType()) ? onHoldCase : false);


        // Question about salary

        List<PayrollManagerNote> deductions = payrollManagerNoteRepository
                .findByHousemaidAndNoteType(housemaid,
                        lockDateService.getLockDate(prevPayrollMonth, -1, PaymentRuleEmployeeType.HOUSEMAIDS),
                        lockDateService.getLockDate(prevPayrollMonth, 0, PaymentRuleEmployeeType.HOUSEMAIDS),
                        AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION);

        boolean questionAboutSalaryCase1 = lastPayrollLog != null &&
                (!deductions.isEmpty() || lastPayrollLog.getGroupTwoDays() > 0),

                questionAboutSalaryCase2 = lastPayrollLog != null
                        && deductions.isEmpty()
                        && lastPayrollLog.getLoanRepayment() != null
                        && lastPayrollLog.getLoanRepayment() > 0d,

                questionAboutSalaryCase3 = lastPayrollLog != null &&
                        (lastPayrollLog.getTotalDeduction() == null ||
                                lastPayrollLog.getTotalDeduction() == 0d)
                        && fullSalary && lastPayrollLog.getGroupTwoDays() == 0,

                questionAboutSalaryCase4 = lastPayrollLog != null &&
                        deductions.isEmpty() &&
                        (lastPayrollLog.getLoanRepayment() == null ||
                                lastPayrollLog.getLoanRepayment() == 0d)
                        && !fullSalary && lastPayrollLog.getGroupTwoDays() == 0,

                questionAboutSalaryCase6 = lastPayrollLog == null &&
                        !startDate.isBefore(new LocalDate(prevPayrollMonth).withDayOfMonth(27)) &&
                        !startDate.isAfter(new LocalDate(prevPayrollMonth).dayOfMonth().withMaximumValue()),

                questionAboutSalaryCase7 = lastPayrollLog == null &&
                        startDate.isAfter(new LocalDate(prevPayrollMonth).dayOfMonth().withMaximumValue());

        String questionAboutSalaryCaseTemplate = questionAboutSalaryCase1 ? "@salary_maid_has_deduction@" :
                questionAboutSalaryCase2 ? "@salary_maid_has_loan@" :
                        questionAboutSalaryCase3 ? "@salary_maid_received_full_salary@" :
                                questionAboutSalaryCase4 ? "@salary_maid_received_partial_salary@" :
                                        onHoldCase ? "@why_salary_was_on_hold@" :
                                                questionAboutSalaryCase6 ? "@salary_maid_joined_26@" :
                                                        questionAboutSalaryCase7 ? "@salary_maid_new_maid@" : "";

        details.put("questionAboutSalaryCaseTemplate", questionAboutSalaryCaseTemplate);

        Predicate<Contract> byContractStatus = contract -> contract.getStatus() == ContractStatus.ACTIVE;
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(housemaid).stream()
                .filter(byContractStatus).collect(Collectors.<Contract>toList());

        if (activeContracts.size() > 0) {
            details.put("notifySalaryRelease", activeContracts.get(0).getNotifySalaryRelease());
        } else {
            details.put("notifySalaryRelease", false);
        }

        details.put("ansariLocation", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ANSARI_LOCATION));
        details.put("ansariAddress", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ANSARI_MAIN_BRANCH_ADDRESS));


        String molNumber = "";
        NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class)
                .findFirstByHousemaidOrderByCreationDateDesc(housemaid);

        if(newRequest != null) {
            molNumber = newRequest.getEmployeeUniqueId();
        }

        details.put("molNumber", molNumber);

        return new ResponseEntity<>(details, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping("/notifySalaryRelease/{id}")
    public ResponseEntity<?> notifySalaryRelease(@PathVariable(name = "id") Housemaid housemaid,
                                                 @RequestParam Boolean notify){
        Predicate<Contract> byContractStatus = contract -> contract.getStatus() == ContractStatus.ACTIVE;
        List<Contract> activeContracts = Setup.getRepository(ContractRepository.class).findByHousemaid(housemaid).stream()
                .filter(byContractStatus).collect(Collectors.<Contract>toList());

        if (activeContracts.size() > 0) {
            activeContracts.get(0).setNotifySalaryRelease(notify);
            Setup.getRepository(ContractRepository.class).save(activeContracts.get(0));
        }
        return this.okResponse();
    }

    @NoPermission
    @RequestMapping("/atmCardVideos")
    public ResponseEntity<?> notifySalaryRelease(){

        Map<String, String> map = new HashMap<>();
        map.put("activateAtmCard", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ACTIVATE_ATM_CARD));
        map.put("checkAtmCardBalance", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CHECK_ATM_CARD_BALANCE));
        map.put("useAtmCardFromOutside", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_USE_ATM_CARD_FROM_OUTSIDE));
        map.put("sendMoneyToFamilyWithAtmCard", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_SEND_MONEY_TO_FAMILY_WITH_ATM_CARD));
        map.put("sendMoneyToFamilyWithoutAtmCard", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_SEND_MONEY_TO_FAMILY_WITHOUT_ATM_CARD));
        map.put("collectAtmCard", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_COLLECT_ATM_CARD));

        return ResponseEntity.ok(map);
    }

    private String getFirstSectionTemplate(Housemaid housemaid) {
        Contract contract = housemaid.getActiveContract();
        if (contract == null || contract.getStartOfContract() == null) {
            return ""; //cancelled contract
        }
        java.sql.Date contractMonthPaymentDate = lockDateService.getPaymentDateBasedDate(contract.getStartOfContract());
        java.sql.Date contractMonthPaymentDatePlusNDays = DateUtil.addDaysSql(contractMonthPaymentDate, 2);
        if (ccAppContentHelper.isContractStartThisMonth(contract, contractMonthPaymentDatePlusNDays)) {
            return "1FirstSectionTemplate";
        }
        if (ccAppContentHelper.isContractStartLastMonth(contract, contractMonthPaymentDatePlusNDays)) {
            return "2FirstSectionTemplate";
        }

        java.sql.Date currentDateSql = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        MonthlyPaymentRule monthlyPaymentRulePrimary = null;
        MonthlyPaymentRule lastMonthlyPaymentRulePrimary = null;
        if (nextPayrollMonthlyRules.size() > 0) {
            monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
            if (monthlyPaymentRulePrimary.getPaymentDate().getMonth() != currentDateSql.getMonth()) {
                //get the rule of the month before
                SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
                selectQuery.filterBy("payrollMonth", "=", DateUtil.addMonthsSql(monthlyPaymentRulePrimary.getPayrollMonth(), -1));
                selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
                selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
                selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
                selectQuery.filterBy("singleHousemaid", "=", false);
                selectQuery.filterBy("singleOfficeStaff", "=", false);
                selectQuery.sortBy("paymentDate", true);
                nextPayrollMonthlyRules = selectQuery.execute();
                if (nextPayrollMonthlyRules.size() > 0)
                    monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
            }
        }

        if (monthlyPaymentRulePrimary == null) {
            return "1FirstSectionTemplate";
        }

        //get the previous month payment rule
        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("payrollMonth", "=", DateUtil.addMonthsSql(monthlyPaymentRulePrimary.getPayrollMonth(), -1));
        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.sortBy("paymentDate", true);
        List<MonthlyPaymentRule> lastPayrollMonthlyRules = selectQuery.execute();
        if (lastPayrollMonthlyRules.size() > 0)
            lastMonthlyPaymentRulePrimary = lastPayrollMonthlyRules.get(0);

        java.sql.Date currentPayrollMonth = monthlyPaymentRulePrimary.getPayrollMonth();

        HousemaidPayrollLog housemaidLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonth(housemaid, monthlyPaymentRulePrimary.getPayrollMonth());
        HousemaidPayrollLog housemaidLogPrev = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonth(housemaid, DateUtil.addMonthsSql(currentPayrollMonth, -1));

        List<MonthlyPaymentRule> paymentRulesSecondary = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                .findNextHousemaidRuleByPayrollTypeAndPayrollMonthAfterPaymentDateOrderByPaymentDateAsc(currentDateSql, PayrollType.SECONDARY, PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRulePrimary.getPayrollMonth());

        List<Payment> paymentsList = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).
                getAllPaymentForHousemaid(monthlyPaymentRulePrimary, housemaid);
        List<Payment> LastMonthPaymentsList = null;
        if (lastMonthlyPaymentRulePrimary != null) {
            LastMonthPaymentsList = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).
                    getAllPaymentForHousemaid(lastMonthlyPaymentRulePrimary, housemaid);
        }

        Map<String, Object> paymentsStatus = ccAppContentHelper.getStatusAndTargetPaymentFromPayments(paymentsList);
        Map<String, Object> lastMonthPaymentsStatus = ccAppContentHelper.getStatusAndTargetPaymentFromPayments(LastMonthPaymentsList);
        if (paymentsStatus == null) {
            if (monthlyPaymentRulePrimary.getPaymentDateUpdatedManually() != null && monthlyPaymentRulePrimary.getPaymentDateUpdatedManually())
                return "17FirstSectionTemplate";
            if (ccAppContentHelper.isMaidFirstSalaryCCApp(contract)) {
                return "3FirstSectionTemplate";
            } else {
                //salary for last month
                if (housemaidLogPrev == null || housemaidLogPrev.getTransferred() || lastMonthPaymentsStatus == null ||
                        (PaymentStatus.BOUNCED.equals(lastMonthPaymentsStatus.get("status"))
                                && Boolean.FALSE.equals(lastMonthPaymentsStatus.get("isReplaced")))) {
                    return "4FirstSectionTemplate";
                } else if (PaymentStatus.BOUNCED.equals(lastMonthPaymentsStatus.get("status")) && Boolean.TRUE.equals(lastMonthPaymentsStatus.get("isReplaced"))) {
                    return "12FirstSectionTemplate";
                }
            }
        }

        if (housemaidLog != null && housemaidLog.getTransferred() != null && housemaidLog.getTransferred() && housemaidLog.getPayslipSent()) {
            return HousemaidPayrollLogRepository.countByMaidParentLog(housemaidLog) > 0 ? "16FirstSectionTemplate" : "8FirstSectionTemplate";
        }

        if (paymentsStatus != null && PaymentStatus.RECEIVED.equals(paymentsStatus.get("status"))) {
            if (housemaidLogPrev == null || housemaidLogPrev.getTransferred()) {
                return "5FirstSectionTemplate";
            } else {
                return "13FirstSectionTemplate";
            }
        }

        if (paymentsStatus != null && PaymentStatus.BOUNCED.equals(paymentsStatus.get("status")) && Boolean.FALSE.equals(paymentsStatus.get("isReplaced"))) {
            if (paymentRulesSecondary == null || paymentRulesSecondary.isEmpty())
                return "7FirstSectionTemplate";
            else if (housemaidLogPrev == null || (housemaidLogPrev.getTransferred() && housemaidLogPrev.getMaidParentLog() == null)
                    || lastMonthPaymentsStatus == null || (PaymentStatus.BOUNCED.equals(lastMonthPaymentsStatus.get("status"))
                    && Boolean.FALSE.equals(lastMonthPaymentsStatus.get("isReplaced"))))
                return "6FirstSectionTemplate";
            else if (housemaidLogPrev.getMaidParentLog() != null && housemaidLogPrev.getTransferred())
                return "15FirstSectionTemplate";
            else
                return "14FirstSectionTemplate";
        }

        if (paymentsStatus != null && PaymentStatus.BOUNCED.equals(paymentsStatus.get("status")) && Boolean.TRUE.equals(paymentsStatus.get("isReplaced"))) {
            if (paymentRulesSecondary != null && !paymentRulesSecondary.isEmpty()) {
                return "9FirstSectionTemplate";
            } else {
                return "11FirstSectionTemplate";
            }
        }

        return "8FirstSectionTemplate";
    }
}
