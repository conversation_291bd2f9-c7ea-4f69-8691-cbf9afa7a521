package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.RevokeAccessRequest;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.RevokeAccessRepository;
import com.magnamedia.service.payroll.generation.OfficeStaffFinalSettlementService;

import java.util.Map;

/**
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 * Created At 10/24/2022
 **/
@BusinessRule(moduleCode = "", entity = RevokeAccessRequest.class,
        events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "employeeGrantAccess.id", "approved"})
public class RevokeAccessRequestPayrollBusinessRule implements BusinessAction<RevokeAccessRequest> {

    @Override
    public boolean validate(RevokeAccessRequest entity, BusinessEvent event) {
        OfficeStaff staff = Setup.getRepository(OfficeStaffRepository.class).findOne(entity.getEmployeeGrantAccess().getId());
        RevokeAccessRequest oldEntity = Setup.getRepository(RevokeAccessRepository.class).findOne(entity.getId());

        return oldEntity != null && !oldEntity.getApproved() && entity.getApproved() != null && entity.getApproved() && staff.getTerminationDate() != null;
    }

    @Override
    public Map<String, Object> execute(RevokeAccessRequest entity, BusinessEvent even) {
        Setup.getApplicationContext().getBean(OfficeStaffFinalSettlementService.class).allAccessesRevoked(entity.getEmployeeGrantAccess());
        return null;
    }
}
