package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.AbstractEmployeeLoan;
import com.magnamedia.entity.AbstractPayrollManagerNote;
import com.magnamedia.entity.EmployeeLoan;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.entity.maidsatv2.MaidsAtCandidateWA;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.EmployeeLoanRepository;
import com.magnamedia.repository.MaidsAtCandidateWARepository;
import com.magnamedia.repository.PayrollManagerNoteRepository;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 1/14/2021
 **/
@BusinessRule(moduleCode = "recruitment", entity = MaidsAtCandidateWA.class,
        events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "housemaid.id", "applicationStatus", "exitLoan", "signingBonus"})
public class MaidsAtCandidateWABusinessRule implements BusinessAction<MaidsAtCandidateWA> {

    private static final Logger logger =
            Logger.getLogger(MaidsAtCandidateWABusinessRule.class.getName());

    @Override
    public boolean validate(MaidsAtCandidateWA entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "MaidsAtCandidateWABusinessRule Validation.");
        MaidsAtCandidateWARepository maidsAtCandidateWARepository = Setup.getRepository(MaidsAtCandidateWARepository.class);
        if (entity == null || entity.getId() == null)
            return false;
        MaidsAtCandidateWA old = maidsAtCandidateWARepository.findOne(entity.getId());

        return (old != null && !Objects.equals(old.getApplicationStatus(), MaidsAtCandidateWA.ApplicationStatus.SUCCESSFULL)
                && entity.getHousemaid() != null
                && entity.getHousemaid().getId() != null
                && Objects.equals(entity.getApplicationStatus(), MaidsAtCandidateWA.ApplicationStatus.SUCCESSFULL)
//                && ((entity.getExitLoan() != null && entity.getExitLoan() > 0) || (entity.getSigningBonus() != null && entity.getSigningBonus() > 0))
        ) ||
                (old != null && Objects.equals(old.getApplicationStatus(), MaidsAtCandidateWA.ApplicationStatus.SUCCESSFULL)
                        && entity.getHousemaid() != null
                        && entity.getHousemaid().getId() != null
                        && Objects.equals(entity.getApplicationStatus(), MaidsAtCandidateWA.ApplicationStatus.SUCCESSFULL)
//                        && ((old.getExitLoan() == null && entity.getExitLoan() != null && entity.getExitLoan() > 0) || (old.getSigningBonus() == null && entity.getSigningBonus() != null && entity.getSigningBonus() > 0))
                );
    }

    @Override
    public Map<String, Object> execute(MaidsAtCandidateWA entity, BusinessEvent even) {
        try {
            logger.log(Level.SEVERE, "MaidsAtCandidateWABusinessRule execute.");
            EmployeeLoanRepository employeeLoanRepository = Setup.getRepository(EmployeeLoanRepository.class);
            PayrollManagerNoteRepository payrollManagerNoteRepository = Setup.getRepository(PayrollManagerNoteRepository.class);

            EmployeeLoan oldLoan = employeeLoanRepository.findTopByHousemaidAndLoanType(entity.getHousemaid(), EmployeeLoan.LoanType.EXIT_LOAN);
            Double exitLoanAmount = entity.getExitLoan() == null ? 0.0 : Double.parseDouble(entity.getExitLoan().toString());
            //edit the old amount in case loan was already created
            if (oldLoan != null && !oldLoan.getAmount().equals(exitLoanAmount)) {
                // if null or zero then delete the loan
                if (exitLoanAmount.equals(0.0)){
                    employeeLoanRepository.delete(oldLoan);
                }else {
                    oldLoan.setAmount(exitLoanAmount);
                    employeeLoanRepository.save(oldLoan);
                }
            } else if (exitLoanAmount > 0.0 &&
                    oldLoan == null) {
                
                EmployeeLoan employeeLoan = new EmployeeLoan();
                employeeLoan.setHousemaid(entity.getHousemaid());
                employeeLoan.setLoanType(EmployeeLoan.LoanType.EXIT_LOAN);
                employeeLoan.setLoanDate(new Date());
                employeeLoan.setAmount(exitLoanAmount);
                employeeLoan.setCashLoanDescription("");
                employeeLoanRepository.save(employeeLoan);
            }

            PicklistItem item= PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, "bonus");

            PayrollManagerNote oldManagerNote = payrollManagerNoteRepository.findTopByHousemaidAndAdditionReason(entity.getHousemaid(),item);
            Double managerNoteAmount = entity.getSigningBonus() == null ? 0.0 : Double.parseDouble(entity.getSigningBonus().toString());
            //edit the old amount in case manager note was already created
            if (oldManagerNote != null && !oldManagerNote.getAmount().equals(managerNoteAmount)) {
                // if null or zero then delete the loan
                if (managerNoteAmount.equals(0.0)){
                    payrollManagerNoteRepository.delete(oldManagerNote);
                }else {
                    oldManagerNote.setAmount(managerNoteAmount);
                    payrollManagerNoteRepository.save(oldManagerNote);
                }
            } else if (managerNoteAmount > 0.0
                    && oldManagerNote== null) {

                PayrollManagerNote managerNote = new PayrollManagerNote();
                managerNote.setHousemaid(entity.getHousemaid());
                managerNote.setAmount(managerNoteAmount);
                managerNote.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
                managerNote.setNotFinal(false);
                managerNote.setNoteReasone("Singing Bonus");
                managerNote.setNoteDate(new Date());
                managerNote.setAdditionReason(item);
                payrollManagerNoteRepository.save(managerNote);
            }

        }catch (Exception e){
            logger.log(Level.SEVERE, "MaidsAtCandidateWABusinessRule in catch: " + e.getMessage());
            DebugHelper.sendExceptionMail("<EMAIL>", e, "MaidsAtCandidateWABusinessRule in catch: ", false);
            throw e;
        }

        logger.log(Level.SEVERE, "MaidsAtCandidateWABusinessRule execute end.");
        return null;
    }
}
