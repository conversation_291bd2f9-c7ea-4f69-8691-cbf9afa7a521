package com.magnamedia.service.payslip;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.projection.HousemaidPayslipProjection;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static java.util.logging.Level.SEVERE;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 8/30/2020
 **/
@Service
public class PayslipService {

    @Autowired
    private HousemaidRepository housemaidRep;

    @Autowired
    private HousemaidBalancesHistoryRepository housemaidBalancesHistoryRepo;

    @Autowired
    private HousemaidPsidRepository housemaidPsidRepository;

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    private InterModuleConnector interModuleConnector;

    public Page<HousemaidPayslipProjection> getHousemaidPayslips(
            Date date,
            String maidName,
            String linkedToYayaBot,
            PicklistItem nationality,
            Long housemaidId,
            String lang,
            Pageable pageable) {
        if (lang == null)
            lang = "EN";

        PayslipTranslateService translateService = PayslipTranslateService.getTranslateService(lang);
        LocalDate dt;
        if (date != null) {
            dt = new LocalDate(date);
        } else {
            dt = new LocalDate();
        }

        LocalDate payrollMonth = dt.withDayOfMonth(1);

        Parameter payslipsGenerationInProgress = Setup.getRepository(ParameterRepository.class).
                findByModuleAndCode(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_PAYSLIPS_ARE_BEING_GENRATED);

        if (payslipsGenerationInProgress.getValue().equals("1"))
            throw new RuntimeException("Payslips are already being generated, please wait until the job finishes");

        long countResult = 0;
        SelectQuery<HousemaidPayrollLog> query = new SelectQuery<>(HousemaidPayrollLog.class);

        //must be transferred
        query.filterBy("transferred", "=", true);

        // Old and new payroll systems
        query.filterBy(new SelectFilter("payrollMonth", "=", new java.sql.Date(payrollMonth.toDate().getTime())));

        List<HousemaidPayrollLog> payrollLogs;
        //filter by housemaid id
        if (housemaidId != null) {
            query.filterBy("housemaid.id", "=", housemaidId);
            payrollLogs = query.execute();
            countResult = payrollLogs.size();
        } else {

            //filter bu housemaid name
            if (maidName != null) {
                query.filterBy("housemaid.name", "LIKE", "%" + maidName + "%");
                //documents = documents.stream().filter(x -> x.getHousemaid().getName().toLowerCase().contains(maidName.toLowerCase())).collect(Collectors.toList());
            }

            //filter by Yayabot
            SelectQuery<HousemaidPsid> query2 = new SelectQuery<>(HousemaidPsid.class);
            List<HousemaidPsid> psids = query2.execute();
            if (linkedToYayaBot != null && linkedToYayaBot.equals("LINKED")) {
                query.filterBy("housemaid.id", "IN", psids.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList()));
            } else if (linkedToYayaBot != null && linkedToYayaBot.equals("NOT_LINKED")) {
                query.filterBy("housemaid.id", "NOT IN", psids.stream().map(x -> x.getHousemaid().getId()).collect(Collectors.toList()));
            }

            //filter by nationality
            if (nationality != null) {
                query.filterBy("housemaid.nationality.id", "=", nationality.getId());
                //documents = documents.stream().filter(x -> x.getHousemaid().getNationality() != null && x.getHousemaid().getNationality().getId().equals(nationality.getId())).collect(Collectors.toList());
            }
            Page<HousemaidPayrollLog> payrollLogsPage = query.execute(pageable);
            countResult = payrollLogsPage.getTotalElements();
            payrollLogs = payrollLogsPage.getContent();
        }

        List<HousemaidPayslipProjection> payslipsList = new ArrayList<>();
        //start creating the result
        if (payrollLogs != null) {
            for (HousemaidPayrollLog log : payrollLogs) {
                Housemaid housemaid = housemaidRep.findOne(log.getHousemaid().getId());
                HousemaidBalancesHistory housemaidBalancesHistory = housemaidBalancesHistoryRepo.findTopByHousemaidAndPayrollMonth(housemaid, new java.sql.Date(payrollMonth.toDate().getTime()));
                HousemaidPayslipProjection payslipProjection = new HousemaidPayslipProjection();
                payslipProjection.setLang(lang);
                payslipProjection.setHousemaidName(housemaid.getName());
                payslipProjection.setPayrollMonth(log.getPayrollMonth());
                java.sql.Date paymentDate = log.getPayrollAccountantTodo() != null ? log.getPayrollAccountantTodo().getPaymentDate() : new java.sql.Date(new LocalDate(log.getPayrollMonth()).plusMonths(1).withDayOfMonth(3).toDate().getTime());
                payslipProjection.setPayslipHeader(translateService.getPayslipHeader(paymentDate, housemaid.getName()));

                List<HousemaidPsid> psids = housemaidPsidRepository.findByHousemaid(housemaid);
                payslipProjection.setLinkedToYayaBot(psids != null && psids.size() > 0 ? true : false);

                payslipProjection.setPayslipIsSent(log.getPayslipSent());

                List<Map<String, Object>> proratedInfoList = new ArrayList<>();
                List<Map<String, Object>> additionsSection = new ArrayList<>();
                List<Map<String, Object>> deductionsSection = new ArrayList<>();
                List<Map<String, Object>> deductionConclusion = new ArrayList<>();
                Date endLockDate = lockDateService.getLockDate(log.getPayrollMonth(), 0, PaymentRuleEmployeeType.HOUSEMAIDS);
                Date startLockDate = lockDateService.getLockDate(log.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS);
                Date firstOfMonth = new LocalDate(log.getPayrollMonth()).withDayOfMonth(1).toDate();
                Date lastOfMonth = new LocalDate(log.getPayrollMonth()).dayOfMonth().withMaximumValue().plusDays(1).toDate();
                int daysOfMonth = DateUtil.getNumDaysOfMonth(log.getPayrollMonth());

                if (housemaid.getHousemaidType() != null && !housemaid.getHousemaidType().equals(HousemaidType.MAID_VISA)) {
                    Double oneDayInAccommodation = housemaid.getAccommodationSalary() != null ? housemaid.getAccommodationSalary() / daysOfMonth : log.getPrimarySalary() != null ? log.getPrimarySalary() / daysOfMonth : null;

                    Double oneDayWithClient = log.getBasicSalary() != null ? log.getBasicSalary() / daysOfMonth : null;

                    if (log.getGroupTwoDays() > 0 || log.getGroupSixDays() > 0)

                        if (log.getGroupSixDays() > 0) {
                            proratedInfoList.add(new HashMap<String, Object>() {
                                {
                                    put("label", translateService.getDaysInAccommodationWithGr6(log.getGroupTwoDays() + log.getGroupSixDays()));
                                    put("value", "+AED " + NumberFormatter.formatNumber(log.getMohreProRatedSalary() + log.getMohreLiveOutProRatedSalary()));
                                }
                            });
                        } else {
                            proratedInfoList.add(new HashMap<String, Object>() {
                                {
                                    put("label", translateService.getDaysInAccommodation(log.getGroupTwoDays(), "AED " + NumberFormatter.formatNumber(oneDayInAccommodation)));
                                    put("value", "+AED " + NumberFormatter.formatNumber(log.getMohreProRatedSalary()));
                                }
                            });
                        }
                    if (log.getGroupOneDays() > 0 || log.getGroupFiveDays() > 0)
                        if (log.getGroupFiveDays() > 0) {
                            proratedInfoList.add(new HashMap<String, Object>() {
                                {
                                    put("label", translateService.getDaysWithClientWithGr5(log.getGroupOneDays() + log.getGroupFiveDays()));
                                    put("value", "+AED " + NumberFormatter.formatNumber(log.getTotalProRatedSalary() + log.getTotalLiveOutProRatedSalary()));
                                }
                            });
                        } else {
                            proratedInfoList.add(new HashMap<String, Object>() {
                                {
                                    put("label", translateService.getDaysWithClient(log.getGroupOneDays(), "AED " + NumberFormatter.formatNumber(oneDayWithClient)));
                                    put("value", "+AED " + NumberFormatter.formatNumber(log.getTotalProRatedSalary()));
                                }
                            });
                        }
                    if (log.getGroupFourDays() > 0) {
                        proratedInfoList.add(new HashMap<String, Object>() {
                            {
                                put("label", translateService.getOnVacationDays(log.getGroupFourDays()));
                                put("value", "+AED " + NumberFormatter.formatNumber(log.getVacationSalary()));
                            }
                        });
                    }

                    if (proratedInfoList.size() > 0)
                        payslipProjection.setProratedInfo(proratedInfoList);
                }

                //get all additions here
                List<PicklistItem> excludedAdditions = new ArrayList<>();
                excludedAdditions.add(PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_COVER_DEDUCTION_LIMIT_ADDITION_CODE));
                excludedAdditions.add(PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_COVER_NEGATIVE_SALARY_ADDITION_CODE));
                List<PayrollManagerNote> additionManagerNotes = payrollManagerNoteRepository.getByHousemaidAndNoteTypeAndAdditionReasonNotIn(housemaid, startLockDate, endLockDate, new Date(), AbstractPayrollManagerNote.ManagerNoteType.ADDITION, excludedAdditions, new java.sql.Date(payrollMonth.toDate().getTime()));
                for (PayrollManagerNote managerNote : additionManagerNotes) {
                    if (managerNote.getAmount() != null && managerNote.getAmount() != 0.0)
                        additionsSection.add(new HashMap<String, Object>() {
                            {
                                java.sql.Date noteDate = "forgive_deduction".equals(managerNote.getAdditionReason().getCode()) && managerNote.getAdditionPayrollManagerNoteDeductionSource() != null ? new java.sql.Date(managerNote.getAdditionPayrollManagerNoteDeductionSource().getNoteDate().getTime())
                                        : ("salary_dispute".equals(managerNote.getAdditionReason().getCode()) ? computeLastPayroll(managerNote.getNoteDate()) : new java.sql.Date(managerNote.getNoteDate().getTime()));
                                put("label", translateService.translateAdditionReasonV2(managerNote.getAdditionReason().getCode(), noteDate));
                                put("value", "+AED " + NumberFormatter.formatNumber(managerNote.getAmount()));
                            }
                        });
                }
                if (additionsSection.size() > 0)
                    payslipProjection.setAdditionsSection(additionsSection);

                //Total Deduction
                Double loanRepayment = log.getLoanRepayment() != null ? log.getLoanRepayment() : 0.0;
                Double unpaidDeductionRepayment = log.getUnpaidDeductionRepayment() != null ? log.getUnpaidDeductionRepayment() : 0.0;
                Double deductionsThisMonth = loanRepayment + unpaidDeductionRepayment;

                Double previousUnpaidDeductionBalance = housemaidBalancesHistory != null ? housemaidBalancesHistory.getPreviousUnpaidDeductionBalance() : 0.0;
                Double deductionsSoFar = previousUnpaidDeductionBalance + log.getTotalDeduction() - loanRepayment;

                Double loanBalance = housemaid.getLoanBalance();
                Double unpaidDeductionBalance = housemaid.getUnpaidDeductionBalance();

                if (deductionsThisMonth > 0.0) {
                    payslipProjection.setYourDeductionsThisMonthSection(new HashMap<String, Object>() {
                        {
                            put("label", translateService.getYourDeductionsThisMonth());
                            put("value", "- AED " + NumberFormatter.formatNumber(deductionsThisMonth));
                        }
                    });

                    payslipProjection.setWhyDeductionSentence(translateService.getWhyDeductionSentence(NumberFormatter.formatNumber(deductionsThisMonth)));
                    payslipProjection.setClickHereToView(translateService.getClickToView());


                    //if the PreviousUnpaidDeductionBalance condition is true, then the second condition must be true also
                    // but the PreviousUnpaidDeductionBalance condition is false, then the second can be even true or false
                    if (previousUnpaidDeductionBalance > 0.0 && unpaidDeductionRepayment > 0.0) {
                        HashMap deductionMap = new HashMap();
                        deductionMap.put("label", translateService.getYourDeductionBefore(translateService.getTranslatedDateWithoutDayOfWeek(log.getPayrollMonth())));
                        deductionMap.put("value", "-AED " + NumberFormatter.formatNumber(previousUnpaidDeductionBalance));
                        deductionsSection.add(deductionMap);
                    }

                    List<PayrollManagerNote> deductionManagerNotes = payrollManagerNoteRepository.getByHousemaidAndNoteType(housemaid, firstOfMonth, lastOfMonth, AbstractPayrollManagerNote.ManagerNoteType.DEDUCTION);
                    if (log.getTotalDeduction() > 0.0) {
                        String label = "";
                        for (PayrollManagerNote managerNote : deductionManagerNotes) {
                            if (managerNote.getAmount() != null && managerNote.getAmount() > 0.0) {
                                boolean criminalized = false;
                                if (managerNote.getDeductionReason().getCode().equals("faulty_replacement") || managerNote.getDeductionReason().getCode().equals("self_replacement"))
                                    criminalized = checkCriminalDeduction(managerNote);
                                label = translateService.translateDeductionReason(managerNote.getDeductionReason().getCode(), new java.sql.Date(managerNote.getNoteDate().getTime()), criminalized);
                                HashMap deductionMap = new HashMap();
                                deductionMap.put("label", label);
                                deductionMap.put("value", "-AED " + NumberFormatter.formatNumber(managerNote.getAmount()));
                                deductionsSection.add(deductionMap);
                            }
                        }
                    }

                    if (deductionsSoFar > 0.0 && unpaidDeductionRepayment > 0.0) {
                        HashMap deductionsSoFarMap = new HashMap();
                        deductionsSoFarMap.put("label", translateService.getDeductionsSoFar());
                        deductionsSoFarMap.put("value", "-AED " + NumberFormatter.formatNumber(deductionsSoFar));
                        deductionsSection.add(deductionsSoFarMap);
                    }

                    if (deductionsSection.size() > 0)
                        payslipProjection.setAllDeductions(deductionsSection);


                    if (unpaidDeductionRepayment > 0.0 && loanRepayment > 0.0) {
                        Double previousLoan = loanBalance + loanRepayment;
                        payslipProjection.setLoanYouPreviouslyHad(translateService.getLoanYouPreviouslyHad("AED " + NumberFormatter.formatNumber(previousLoan)));

                        payslipProjection.setLoanRepayment(new HashMap() {
                            {
                                put("label", translateService.getLoanRepayment());
                                put("value", "-AED " + NumberFormatter.formatNumber(loanRepayment));
                            }
                        });

                        payslipProjection.setTotalDeductionsAndLoanRepayment(new HashMap() {
                            {
                                put("label", translateService.getTotalDeductionsAndLoans());
                                put("value", "-AED " + NumberFormatter.formatNumber(deductionsThisMonth));
                            }
                        });

                        deductionConclusion.add(translateService.getYourRemainingDeductions(NumberFormatter.formatNumber(unpaidDeductionBalance)));
                        if (loanBalance > 0.0)
                            deductionConclusion.add(translateService.getYourRemainingLoan("AED " + NumberFormatter.formatNumber(loanBalance)));
                        else
                            deductionConclusion.add(translateService.getYourRemainingLoan("ZERO"));
                    } else if (unpaidDeductionRepayment > 0.0) {
                        deductionConclusion.add(translateService.getYourRemainingDeductions(NumberFormatter.formatNumber(unpaidDeductionBalance)));

                        if (loanBalance > 0.0)
                            deductionConclusion.add(translateService.getWeWillNotDeduct("AED " + NumberFormatter.formatNumber(loanBalance)));
                    } else if (loanRepayment > 0.0) {
                        payslipProjection.setLoanRepayment(new HashMap() {
                            {
                                put("label", translateService.getLoanRepayment());
                                put("value", "-AED " + NumberFormatter.formatNumber(loanRepayment));
                            }
                        });

                        if (loanBalance > 0.0)
                            deductionConclusion.add(translateService.getYourRemainingLoan("AED " + NumberFormatter.formatNumber(loanBalance)));
                        else
                            deductionConclusion.add(translateService.getYourRemainingLoan("ZERO"));
                    }

                    if (deductionConclusion.size() > 0)
                        payslipProjection.setDeductionConclusion(deductionConclusion);

                    payslipProjection.setWeOnlyDeducted(translateService.getWeOnlyDeducted("AED " + NumberFormatter.formatNumber(deductionsThisMonth)));

                }

                Double totalSalary = log.getTotalSalary();
                if (HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())) {
                    List<HousemaidPayrollLog> oldPaidLogs = Setup.getRepository(HousemaidPayrollLogRepository.class).findByMaidParentLog(log);
                    for (HousemaidPayrollLog oldLog : oldPaidLogs)
                        totalSalary += oldLog.getTotalSalary();
                }

                HashMap youReceivedMap = new HashMap<String, Object>();
                youReceivedMap.put("label", translateService.getYouReceivedThisMonth());
                youReceivedMap.put("value", "AED " + NumberFormatter.formatNumber(totalSalary) + "");
                payslipProjection.setYouReceivedThisMonthSection(youReceivedMap);

                payslipsList.add(payslipProjection);
            }
        }
        if (pageable == null)
            return new PageImpl(payslipsList,
                    PageRequest.of(0, payslipsList.size()),
                    countResult);
        else
            return new PageImpl(payslipsList,
                    pageable,
                    countResult);
    }

    public boolean checkCriminalDeduction(PayrollManagerNote managerNote) {
        return interModuleConnector.get(
                PayrollManagementModule.URL_MODULE_STAFF_MGMT
                        + "/payrollmanagernote/checkcriminaldeduction/"
                        + managerNote.getId(),
                Boolean.class);
    }

    public java.sql.Date computeLastPayroll(Date date) {
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findLastPayroll(date, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        if (rules.size() > 0)
            return rules.get(0).getPaymentDate();
        else return new java.sql.Date(System.currentTimeMillis());
    }

    public boolean isTaskRunning(String taskName) {
        SelectQuery<BackgroundTask>  selectQuery =new SelectQuery<>(BackgroundTask.class);
        selectQuery.filterBy("name","=",taskName);
        selectQuery.filterBy("status","NOT IN",Arrays.asList(BackgroundTaskStatus.Finished , BackgroundTaskStatus.Failed));

        return !selectQuery.execute().isEmpty();
    }
}
