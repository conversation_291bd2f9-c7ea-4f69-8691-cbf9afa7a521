package com.magnamedia.service.payslip;

import com.magnamedia.helper.StringHelper;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.DayOfWeek;
import java.time.Month;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Creation Date 14/05/2020
 */
@Service
public class EnglishPayslipTranslateService extends PayslipTranslateService {

    @Override
    public String getLangCode() {
        return "en";
    }

    @Override
    public String noShowDeduction(String date) {
        return "Absent deduction on " + date;
    }

    @Override
    public String replacement(String type) {
        return "Replacement (" + type + ")";
    }

    @Override
    public String failedInterview(String date, String time) {
        return "Failed interview on "
                + date
                + " at " + time;
    }

    @Override
    public Map<String, Object> getPayslipHeader(Date paymentDate, String housemaidName) {
        HashMap paramMap = new HashMap();
        paramMap.put("name", housemaidName);
        paramMap.put("paymentDate", this.getTranslatedDate(paymentDate));

        return new HashMap(){
            {
                put("payslipForSentence", "Payslip for: @name@");
                put("dateSentence", "Date: @paymentDate@");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodation(int groupTwoDays, String oneDayInAccommodation) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayInAccommodation + " per day");

        return new HashMap(){
            {
                put("sentence", "You stayed " + groupTwoDays + " days in accommodation & earned @perDayAmount@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClient(int groupOneDays, String oneDayWithClient) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayWithClient + " per day");

        return new HashMap(){
            {
                put("sentence", "You lived " + groupOneDays + " days at client's home & earned @perDayAmount@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodationWithGr6(int groupTwoSixDays) {

        HashMap paramMap = new HashMap();
        return new HashMap() {
            {
                put("sentence", "You stayed " + groupTwoSixDays + " days without a client & earned");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClientWithGr5(int groupOneFiveDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "You stayed " + groupOneFiveDays + " days at the client’s home & earned");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getOnVacationDays(int groupFourDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "You stayed " + groupFourDays + " days in your vacation.");
                put("parameters", paramMap);
            }
        };
    }



    @Override
    public String getYourDeductionsThisMonth() {
        return "Your deductions this month:";
    }

    @Override
    public String getYouReceivedThisMonth() {
        return "Total amount sent to your account:";
    }

    @Override
    public String getWhyDeductionSentence(String totalDeduction) {
        return "If you want to know why we deducted AED " + totalDeduction + " from your salary, please click on the button below.";
    }

    @Override
    public String getClickToView() {
        return "Click here to view your deductions";
    }

    @Override
    public String getYourDeductionBefore(String firstOfPayrollMonth) {
        return "Your deductions before " + firstOfPayrollMonth + ":";
    }

    @Override
    public String getDeductionsSoFar() {
        return "Deductions so far:";
    }

    @Override
    public Map<String, Object> getLoanYouPreviouslyHad(String previousLoan) {
        HashMap paramMap = new HashMap();
        paramMap.put("previousLoan", previousLoan);

        return new HashMap(){
            {
                put("sentence", "Loan you previously had that you still need to pay is @previousLoan@.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String getLoanRepayment() {
        return "Loan repayment of this month:";
    }

    @Override
    public String getTotalDeductionsAndLoans() {
        return "Total deductions and loan repayments this month:";
    }

    @Override
    public Map<String, Object> getWeOnlyDeducted(String currentDeductions) {
        HashMap paramMap = new HashMap();
        paramMap.put("deductionsThisMonth", currentDeductions);

        return new HashMap(){
            {
                put("sentence", "We only deducted @deductionsThisMonth@ so you can have money to transfer for your family.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingDeductions(String remainingDeductionAmount) {
        if ("0".equals(remainingDeductionAmount))
            remainingDeductionAmount = "Zero";
        else
            remainingDeductionAmount = "AED " + remainingDeductionAmount;

        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingDeductionAmount);

        return new HashMap(){
            {
                put("sentence", "Your remaining deductions are now @amount@.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingLoan(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        String sentence = !"ZERO".equals(remainingLoanAmount) ? "And your remaining loan is now @amount@ that will be deducted from your future salaries."
                : "And your remaining loan is now @amount@.";

        return new HashMap(){
            {
                put("sentence", sentence);
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getWeWillNotDeduct(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        return new HashMap(){
            {
                put("sentence", "We will not deduct a loan repayment from you this month. Your remaining loan of amount @amount@ will be deducted from your future salaries.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String translateDeductionReason(String code, Date date, boolean criminalized) {
        String translatedDate = this.getTranslatedDate(date);
        if(criminalized){
            return "On " + translatedDate + ", your client replaced you because you hurt them or stole from them:";
        }else if(code.contains("faulty_replacement")){
            return "On " + translatedDate + ", your client replaced you:";
        }else if(code.contains("self_replacement")){
            return "On " + translatedDate + ", you decided not to work with your client:";
        }else if(code.contains("no_show") || code.contains("noshow")){
            return "On " + translatedDate + ", you were absent:";
        }else if(code.contains("Maid_is_not_wearing_the_mask")){
            return "On " + translatedDate + ", you were not wearing your mask:";
        } else if (code.contains("unemployment_insurance_plan")) {
            return "We paid on your behalf for your unemployment insurance plan.";
        } else if (code.contains("unemployment_insurance_fines")) {
            return "We paid on your behalf for MOHRE fines because you missed paying your unemployment insurance.";
        } else{
            return "You got other deductions on " + translatedDate + ":";
        }
    }

    @Override
    public String translateAdditionReasonV2(String code, Date date) {
        String translatedDate = this.getTranslatedDate(date);
        if(code.contains("forgive_deduction")){
            return "Addition because you got a wrong deduction on " + translatedDate + ":";
        }else if(code.contains("raffle_prize")){
            return "You won a raffle prize on " + translatedDate + ":";
        }else if(code.contains("recommendation_from_client")){
            return "Your client gave us a good review:";
        }else if(code.contains("salary_dispute")){
            return "Addition because the salary you received on " + translatedDate + " was wrong:";
        }else if(code.contains("previously_held_salary")){
            return "You were on vacation and you didn't receive your previous salary";
        }else if(code.contains("bonus")){
            return "You got a bonus on " + translatedDate + ":";
        }else if(code.contains("medical_assistant")){
            return "We helped you in your medical expenses on " + translatedDate + ":";
        }else if(code.contains("taxi_reimbursement")){
            return "We refunded your taxi ride on " + translatedDate + ":";
        }else{
            return "You got other additions on " + translatedDate + ":";
        }
    }

    @Override
    public String translateMonth(Month month) {
        return StringHelper.enumToCapitalizedFirstLetter(month.name());
    }

    @Override
    public String translateDay(DayOfWeek day) {
        return StringHelper.enumToCapitalizedFirstLetter(day.name());
    }

    @Override
    public String basicPayThisMonth(Integer days) {
        return "Basic Pay This Month (" + days + ")";
    }

    @Override
    public String managerAdditions() {
        return "Manager Addition";
    }
}
