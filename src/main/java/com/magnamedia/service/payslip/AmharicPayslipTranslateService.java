package com.magnamedia.service.payslip;

import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.DayOfWeek;
import java.time.Month;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Creation Date 14/05/2020
 */
@Service
public class AmharicPayslipTranslateService extends PayslipTranslateService {

    @Override
    public String getLangCode() {
        return "amh";
    }

    @Override
    public String noShowDeduction(String date) {
        return "የቅሩበት ቀናሽ " + date;
    }

    @Override
    public String replacement(String date) {
        return "በ ስህተት መተካት (ፎልቲ ሪፕለስመንት)";
    }

    @Override
    public String failedInterview(String date, String time) {
        return "ያልተሳካ ቃለ-መጠይቅ " + date + " " + time;
    }

    /********* starting new payslips*******
     * @return*/
    @Override
    public Map<String, Object> getPayslipHeader(Date paymentDate, String housemaidName) {
        HashMap paramMap = new HashMap();
        paramMap.put("name", housemaidName);
        paramMap.put("paymentDate", this.getTranslatedDate(paymentDate));

        return new HashMap(){
            {
                put("payslipForSentence", "የሚከፈል ለ @name@");
                put("dateSentence", "ቀን: @paymentDate@");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodation(int groupTwoDays, String oneDayInAccommodation) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", "በቀን " + oneDayInAccommodation + " ድርሃም");

        return new HashMap(){
            {
                put("sentence", "በአኮመዴሽን ውስጥ ለ " + groupTwoDays + " ቀን ለቆየሽው @perDayAmount@ ታገኛለሽ:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClient(int groupOneDays, String oneDayWithClient) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", "በቀን " + oneDayWithClient);

        return new HashMap(){
            {
                put("sentence", "በአሰሪሽ ቤት " + groupOneDays + " ቅናት ስለቆየሽ በዚያ ቆየሽበት ጊዜ @perDayAmount@ ይከፈልሻል:");
                put("parameters", paramMap);
            }
        };
    }


    @Override
    public Map<String, Object> getDaysInAccommodationWithGr6(int groupTwoSixDays) {

        HashMap paramMap = new HashMap();
        return new HashMap() {
            {
                put("sentence", "ያለ ደንበኛ ለ " + groupTwoSixDays + " ቀናት ቆይተዋል እና *amount* አግኝተዋል");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClientWithGr5(int groupOneFiveDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "በደንበኛው ቤት ለ " + groupOneFiveDays + " ቀናት ቆይተዋል እና *amount* አግኝተዋል");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getOnVacationDays(int groupFourDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "በእረፍትዎ ለ " + groupFourDays + " ቀናት ቆይተዋል እና *amount* አግኝተዋል");
                put("parameters", paramMap);
            }
        };
    }




    @Override
    public String getYourDeductionsThisMonth() {
        return "በዚህ ወር የተቀነሰው:";
    }

    @Override
    public String getYouReceivedThisMonth() {
        return "ወደ ሒሳብሽ የገባው ጠቅላላ ገንዘብ:";
    }

    @Override
    public String getWhyDeductionSentence(String totalDeduction) {
        return "ከክፍያሽ ላይ AED " + totalDeduction + " ድርሃም ፤ ይህንን ሊንክ ተጫኚ.";
    }

    @Override
    public String getClickToView() {
        return "ስለቅጣትሽ ዝርዝር መርጃ ለማግኘት ይህንን ይጫኑ፡፡";
    }

    @Override
    public String getYourDeductionBefore(String firstOfPayrollMonth) {
        return "በፊት የነበረውን ቅናሽ " + firstOfPayrollMonth + ":";
    }

    @Override
    public String getDeductionsSoFar() {
        return "እስካሁን ያለብሽ ቅጣት:";
    }

    @Override
    public Map<String, Object> getLoanYouPreviouslyHad(String previousLoan) {
        HashMap paramMap = new HashMap();
        paramMap.put("previousLoan", previousLoan);

        return new HashMap(){
            {
                put("sentence", "ክዚህ በፊት የተበደርሽው እና መክፈል የሚኖርብሽ  @previousLoan@ ድርሃም.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String getLoanRepayment() {
        return "በዚህ ወር የከፈልሽው ብድር:";
    }

    @Override
    public String getTotalDeductionsAndLoans() {
        return "ጠቅላላ  በዚህ ወር የተከፈለ ብድርና ቅጣት:";
    }

    @Override
    public Map<String, Object> getWeOnlyDeducted(String currentDeductions) {
        HashMap paramMap = new HashMap();
        paramMap.put("deductionsThisMonth", currentDeductions);

        return new HashMap(){
            {
                put("sentence", "ለቤተሰብ የሚላክ እንዲተርፍሽ የቀነስነው ቅጣት @deductionsThisMonth@ ድርሃም ብቻ ነው፡፡.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingDeductions(String remainingDeductionAmount) {
        if ("0".equals(remainingDeductionAmount))
            remainingDeductionAmount = "Zero";
        else
            remainingDeductionAmount = "AED " + remainingDeductionAmount;

        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingDeductionAmount);

        return new HashMap(){
            {
                put("sentence", "ያልቀነስነው የቅጣት ገንዘብ መጠን @amount@ ድርሃም ነው.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingLoan(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        String sentence = !"ZERO".equals(remainingLoanAmount) ? "የሚቀር ያልተከፈለ ብድር መጠን @amount@ እና ከሚቀጥለው ወር የሚታሰብ ይሆናል."
                : "የሚቀር ያልተከፈለ ብድር መጠን @amount@.";

        return new HashMap(){
            {
                put("sentence", sentence);
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getWeWillNotDeduct(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        return new HashMap(){
            {
                put("sentence", "ያለብሽን ብድር @amount@ በዚህ ወር አንቀንስም፡፡ በሚቀጥለው ወር እንቀንሳለን፡፡.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String translateDeductionReason(String code, Date date, boolean criminalized) {
        String translatedDate = this.getTranslatedDate(date);
        if(criminalized){
            return "በ " + translatedDate + " አሰሪሽኝ ስለጎዳሽ ወይም ከአሰሪሽ ስርቆት ስለፈጸምሽ ተክቶሻል፡፡:";
        }else if(code.contains("faulty_replacement")){
            return "በ " + translatedDate + " አሰሪሽ ተክቶሻል:";
        }else if(code.contains("self_replacement")){
            return "በ " + translatedDate + " አሰሪሽ ጋር ላለመስራት ወስነሻል፡፡:";
        }else if(code.contains("no_show") || code.contains("noshow")){
            return "በ " + translatedDate + " በስራ ቦታሽ አልተገኘሽም:";
        }else if(code.contains("Maid_is_not_wearing_the_mask")){
            return "በ " + translatedDate + " የአፍ መሸፈኛ ማስክ አላደረግሽም:";
        }else if (code.contains("unemployment_insurance_plan")) {
            return "እኛ በእርስዎ ስም ለእርስዎ የእረፍት ኢንሹራንስ ፕላን ክፍያ አከፋፈልን።";
        } else if (code.contains("unemployment_insurance_fines")) {
            return "እርስዎ የእረፍት ኢንሹራንስ እንደማትከፍሉ ስለተጣራችሁ MOHRE ቅጣቶችን በእርስዎ ስም እኛ ክፍያ አከፋፈልን።";
        }else{
            return "በ " + translatedDate + " የገንዘብ ቅናሽ ተደርጎብሻል፡፡:";
        }
    }

    @Override
    public String translateAdditionReasonV2(String code, Date date) {
        String translatedDate = this.getTranslatedDate(date);
        if(code.contains("forgive_deduction")){
            return "በ " + translatedDate + " በስህተት የተቀነሰብሽ ተጠምሮልሻል:";
        }else if(code.contains("raffle_prize")){
            return "የሽልማት እጣ አሸናፊ ሮነሻል " + translatedDate + ":";
        }else if(code.contains("recommendation_from_client")){
            return "አሰሪሽ ስላንቺ መልካም አስተያየት ሰጥቶናል:";
        }else if(code.contains("salary_dispute")){
            return "በባለፈው " + translatedDate + " በተፈጽመው ክፍያ ለተፈጠረው ስህተት የተደረገ ጭማሪ ክፍያ:";
        }else if(code.contains("previously_held_salary")){
            return "እረፍት ላይ ስለነበርሽ ደሞዝሽን አልተቀበልሽም፡፡";
        }else if(code.contains("bonus")){
            return "በ " + translatedDate + " የቦነስ ክፍያ አጊኝተሻል፡፡:";
        }else if(code.contains("medical_assistant")){
            return "በ " + translatedDate + " ቀን ያወጣሽውን የህክምና ወጪ አግዘንሻል፡፡:";
        }else if(code.contains("taxi_reimbursement")){
            return "ለታክሲ ያወጣሽውን ወጪ በ " + translatedDate + " ቀን ተክተንልሻል፡፡:";
        }else{
            return "በ " + translatedDate + " የገንዘብ ጭማሪ ተደርጎልሻል፡፡:";
        }
    }

    @Override
    public String translateMonth(Month month) {
        switch (month) {
            case JANUARY:
                return "ጥር";
            case FEBRUARY:
                return "የካቲት";
            case MARCH:
                return "መጋቢት";
            case APRIL:
                return "ሚያዚያ";
            case MAY:
                return "ግንቦት";
            case JUNE:
                return "ሰኔ";
            case JULY:
                return "ሀምሌ";
            case AUGUST:
                return "ነሐሴ";
            case SEPTEMBER:
                return "መስከረም";
            case OCTOBER:
                return "ጥቅምት";
            case NOVEMBER:
                return "ህዳር";
            case DECEMBER:
                return "ታህሳስ";
        }
        return "";
    }

    @Override
    public String translateDay(DayOfWeek day) {
        switch (day) {
            case SATURDAY:
                return "ቅዳሜ";
            case SUNDAY:
                return "እሁድ";
            case MONDAY:
                return "ሰኞ";
            case TUESDAY:
                return "ማክሰኞ";
            case WEDNESDAY:
                return "እሮብ";
            case THURSDAY:
                return "ሐሙስ";
            case FRIDAY:
                return "አርብ";
        }
        return "";
    }

    @Override
    public String basicPayThisMonth(Integer days) {
        return "በዚህ ወር መሰረታዊ የክፍያ (" + days + ")";
    }

    @Override
    public String managerAdditions() {
        return "በ ሥራ አስኪያጁ  የተጨምር";
    }
}
