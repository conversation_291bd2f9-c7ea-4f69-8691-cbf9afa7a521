package com.magnamedia.service.payslip;

import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.DayOfWeek;
import java.time.Month;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Creation Date 14/05/2020
 */
@Service
public class HindiPayslipTranslateService extends PayslipTranslateService {

    @Override
    public String getLangCode() {
        return "hi";
    }

    @Override
    public String noShowDeduction(String date) {
        return "अनुपस्थित रहने के लिए कटौती " + date;
    }

    @Override
    public String replacement(String type) {
        return "दोषपूर्ण प्रतिस्थापन";
    }

    @Override
    public String failedInterview(String date, String time) {
        return "साक्षात्कार में असफल रहा " + date + " " + time;
    }

    @Override
    public Map<String, Object> getPayslipHeader(Date paymentDate, String housemaidName) {
        HashMap paramMap = new HashMap();
        paramMap.put("name", housemaidName);
        paramMap.put("paymentDate", this.getTranslatedDate(paymentDate));

        return new HashMap(){
            {
                put("payslipForSentence", "@name@ के लिए पेयस्लि");
                put("dateSentence", "दिनांक: @paymentDate@");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodation(int groupTwoDays, String oneDayInAccommodation) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayInAccommodation + " प्रति दिन");

        return new HashMap(){
            {
                put("sentence", "आप आवास यानि एकोमोडेशन  में " + groupTwoDays + " दिन रहे और @perDayAmount@ कमाए:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClient(int groupOneDays, String oneDayWithClient) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayWithClient + " प्रति दिन");

        return new HashMap(){
            {
                put("sentence", "आप ग्राहक के घर पर " + groupOneDays + " दिन रहते थे और @perDayAmount@ कमाते थे:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodationWithGr6(int groupTwoSixDays) {
        return null;
    }

    @Override
    public Map<String, Object> getDaysWithClientWithGr5(int groupOneFiveDays) {
        return null;
    }

    @Override
    public Map<String, Object> getOnVacationDays(int groupFourDays) {
        return null;
    }

    @Override
    public String getYourDeductionsThisMonth() {
        return "इस महीने आपकी कटौती:";
    }

    @Override
    public String getYouReceivedThisMonth() {
        return "कुल राशि आपके खाते में भेजी गई:";
    }

    @Override
    public String getWhyDeductionSentence(String totalDeduction) {
        return "यदि आप जानना चाहते हैं कि हमने आपके वेतन से AED " + totalDeduction + " क्यों घटाया, तो कृपया नीचे दिए गए बटन पर क्लिक करें.";
    }

    @Override
    public String getClickToView() {
        return "अपनी कटौती देखने के लिए यहां क्लिक करें";
    }

    @Override
    public String getYourDeductionBefore(String firstOfPayrollMonth) {
        return firstOfPayrollMonth + " से पहले आपकी कटौती:";
    }

    @Override
    public String getDeductionsSoFar() {
        return "अब तक की कटौती:";
    }

    @Override
    public Map<String, Object> getLoanYouPreviouslyHad(String previousLoan) {
        HashMap paramMap = new HashMap();
        paramMap.put("previousLoan", previousLoan);

        return new HashMap(){
            {
                put("sentence", "आपके द्वारा पहले दिया गया ऋण, जो आपको अभी भी भुगतान करने की आवश्यकता है, @previousLoan@ है.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String getLoanRepayment() {
        return "इस महीने का कर्ज चुकाना:";
    }

    @Override
    public String getTotalDeductionsAndLoans() {
        return "इस महीने कुल कटौती और ऋण चुकौती:";
    }

    @Override
    public Map<String, Object> getWeOnlyDeducted(String currentDeductions) {
        HashMap paramMap = new HashMap();
        paramMap.put("deductionsThisMonth", currentDeductions);

        return new HashMap(){
            {
                put("sentence", "हमने केवल @deductionsThisMonth@ घटाया है, ताकि आपके पास अपने परिवार के लिए धन रखने के लिए धन हो.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingDeductions(String remainingDeductionAmount) {
        if ("0".equals(remainingDeductionAmount))
            remainingDeductionAmount = "Zero";
        else
            remainingDeductionAmount = "AED " + remainingDeductionAmount;

        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingDeductionAmount);

        return new HashMap(){
            {
                put("sentence", "आपकी शेष कटौती अब @amount@ है.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingLoan(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        String sentence = !"ZERO".equals(remainingLoanAmount) ? "और आपका शेष ऋण अब @amount@ है जो आपके भविष्य के वेतन से काट लिया जाएगा."
                : "और आपका शेष ऋण अब @amount@.";

        return new HashMap(){
            {
                put("sentence", sentence);
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getWeWillNotDeduct(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        return new HashMap(){
            {
                put("sentence", "हम इस महीने आपसे ऋण चुकौती नहीं लेंगे। आपकी शेष राशि @amount@ आपके भविष्य के वेतन से काट ली जाएगी.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String translateDeductionReason(String code, Date date, boolean criminalized) {
        String translatedDate = this.getTranslatedDate(date);
        if(criminalized){
            return "इस " + translatedDate + " पर, आपके क्लाइंट ने आपको बदल दिया क्योंकि आपने उन्हें चोट पहुंचाई या उनसे चोरी की:";
        }else if(code.contains("faulty_replacement")){
            return "यह " + translatedDate + " पर, आपके ग्राहक ने आपको बदल दिया:";
        }else if(code.contains("self_replacement")){
            return "इस " + translatedDate + " पर, आपने अपने ग्राहक के साथ काम नहीं करने का फैसला किया:";
        }else if(code.contains("no_show") || code.contains("noshow")){
            return "इस " + translatedDate + " पर, आप अनुपस्थित थे:";
        }else if(code.contains("Maid_is_not_wearing_the_mask")){
            return "इस " + translatedDate + " पर, आपने अपना मुखौटा नहीं पहना था:";
        }else if (code.contains("unemployment_insurance_plan")) {
            return "हमने आपकी ओर से आपकी बेरोजगारी बीमा योजना के लिए भुगतान किया है।";
        } else if (code.contains("unemployment_insurance_fines")) {
            return "आपने अपना बेरोजगारी बीमा नहीं भरा इसलिए MOHRE जुर्माने के लिए हमने आपकी ओर से भुगतान किया है।";
        }else{
            return "आपको " + translatedDate + " पर अन्य कटौती मिली:";
        }
    }

    @Override
    public String translateAdditionReasonV2(String code, Date date) {
        String translatedDate = this.getTranslatedDate(date);
        if(code.contains("forgive_deduction")){
            return "जोड़ क्योंकि आपको " + translatedDate + " पर गलत कटौती मिली इसलिए वो निकलडिया:";
        }else if(code.contains("raffle_prize")){
            return "आपने " + translatedDate + " पर राफ़ल पुरस्कार जीता:";
        }else if(code.contains("recommendation_from_client")){
            return "आपके ग्राहक ने हमें अच्छी समीक्षा दी:";
        }else if(code.contains("salary_dispute")){
            return "जोड़ क्योंकि आपको " + translatedDate + " पर मिलने वाला वेतन गलत था:";
        }else if(code.contains("previously_held_salary")){
            return "आप छुट्टी पर थे और आपको अपना पिछला वेतन यानि सैलरी नहीं मिला था";
        }else if(code.contains("bonus")){
            return "आपको " + translatedDate + " पर बोनस मिला:";
        }else if(code.contains("medical_assistant")){
            return "हमने आपके मेडिकल खर्चों में " + translatedDate + " पर मदद की:";
        }else if(code.contains("taxi_reimbursement")){
            return "हमने आपकी टैक्सी की सवारी को " + translatedDate + " पर वापस कर दिया:";
        }else{
            return "आपको " + translatedDate + " पर अन्य जोड़ भी मिले:";
        }
    }

    @Override
    public String basicPayThisMonth(Integer days) {
        return "मूल वेतन यह महीना (" + days + ")";
    }

    @Override
    public String managerAdditions() {
        return "प्रबंधक के अतिरिक्";
    }

    @Override
    public String translateMonth(Month month) {
        switch (month){
            case JANUARY:
                return "जनवरी";
            case FEBRUARY:
                return "फरवरी";
            case MARCH:
                return "मार्च";
            case APRIL:
                return "अप्रैल";
            case MAY:
                return "मई";
            case JUNE:
                return "जून";
            case JULY:
                return "जुलाई";
            case AUGUST:
                return "अगस्त";
            case SEPTEMBER:
                return "सितंबर";
            case OCTOBER:
                return "अक्टूबर";
            case NOVEMBER:
                return "नवंबर";
            case DECEMBER:
                return "दिसंबर";
        }
        return "";
    }

    @Override
    public String translateDay(DayOfWeek day) {
        switch (day){
            case SATURDAY:
                return "शनिवार";
            case SUNDAY:
                return "रविवार";
            case MONDAY:
                return "सोमवार";
            case TUESDAY:
                return "मंगलवार";
            case WEDNESDAY:
                return "बुधवार";
            case THURSDAY:
                return "गुरूवार";
            case FRIDAY:
                return "शुक्रवार";
        }
        return "";
    }
}
