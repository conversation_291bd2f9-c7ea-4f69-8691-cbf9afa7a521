package com.magnamedia.service.payslip;

import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.DayOfWeek;
import java.time.Month;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Creation Date 14/05/2020
 */
@Service
public class OromoPayslipTranslateService extends PayslipTranslateService {

    @Override
    public String getLangCode() {
        return "om";
    }

    @Override
    public String noShowDeduction(String date) {
        return "hafu ketin kan murame " + date;
    }

    @Override
    public String replacement(String type) {
        return "dogongoran hoji raa bahu ykn ballessa dhan";
    }

    @Override
    public String failedInterview(String date, String time) {
        return "gafif debi hinmilkofne " + date + " " + time;
    }

    @Override
    public Map<String, Object> getPayslipHeader(Date paymentDate, String housemaidName) {
        HashMap paramMap = new HashMap();
        paramMap.put("name", housemaidName);
        paramMap.put("paymentDate", this.getTranslatedDate(paymentDate));

        return new HashMap(){
            {
                put("payslipForSentence", " Kafalti  kan  @name@");
                put("dateSentence", "Guyyaa : @paymentDate@");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodation(int groupTwoDays, String oneDayInAccommodation) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayInAccommodation + " guyyaa dhan");

        return new HashMap(){
            {
                put("sentence", "akkommodashiini keessaa guyyaa " + groupTwoDays + " tese kanaf miindaan kee qarshii @perDayAmount@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClient(int groupOneDays, String oneDayWithClient) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayWithClient + " argata");

        return new HashMap(){
            {
                put("sentence", "Mana kaadiima keeti guyyaa " + groupOneDays + " kanaf guyyaa dhan @perDayAmount@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodationWithGr6(int groupTwoSixDays) {

        HashMap paramMap = new HashMap();
        return new HashMap() {
            {
                put("sentence", "Guyyoot " + groupTwoSixDays + " kaadimaa malee turtee 'amount' argateetta ");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClientWithGr5(int groupOneFiveDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "Guyyoota " + groupOneFiveDays + " mana kaadimaa kee turtee 'amaount' argateetta ");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getOnVacationDays(int groupFourDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "Guyyoota " + groupFourDays + " boqonnaa irra turtee 'amount' argateetta ");
                put("parameters", paramMap);
            }
        };
    }




    @Override
    public String getYourDeductionsThisMonth() {
        return "ji'a kana ti kan sira murame:";
    }

    @Override
    public String getYouReceivedThisMonth() {
        return "Walumagalati qaarshiin akkowuntii keetirati gale:";
    }

    @Override
    public String getWhyDeductionSentence(String totalDeduction) {
        return "Malif akka miindaa keetira AED " + totalDeduction + " siramurame beku yoo barbade liki kana xuqi.";
    }

    @Override
    public String getClickToView() {
        return "qaarshii sira murame laludhaf linki kana bani";
    }

    @Override
    public String getYourDeductionBefore(String firstOfPayrollMonth) {
        return "duran kan sira murame " + firstOfPayrollMonth + ":";
    }

    @Override
    public String getDeductionsSoFar() {
        return "amamati kan siramurame:";
    }

    @Override
    public Map<String, Object> getLoanYouPreviouslyHad(String previousLoan) {
        HashMap paramMap = new HashMap();
        paramMap.put("previousLoan", previousLoan);

        return new HashMap(){
            {
                put("sentence", "Idaan duran sira jiru kafalu qabdu @previousLoan@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String getLoanRepayment() {
        return "idaa ji'a kanati kafalu qabdu:";
    }

    @Override
    public String getTotalDeductionsAndLoans() {
        return "kafaliti waligala fii kan ji'a kanati kafalu qabdu:";
    }

    @Override
    public Map<String, Object> getWeOnlyDeducted(String currentDeductions) {
        HashMap paramMap = new HashMap();
        paramMap.put("deductionsThisMonth", currentDeductions);

        return new HashMap(){
            {
                put("sentence", "@deductionsThisMonth@  qofa sira muree kanaf qaarshii maatii keeti ergitu sihafeera.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingDeductions(String remainingDeductionAmount) {
        if ("0".equals(remainingDeductionAmount))
            remainingDeductionAmount = "Zero";
        else
            remainingDeductionAmount = "AED " + remainingDeductionAmount;

        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingDeductionAmount);

        return new HashMap(){
            {
                put("sentence", "ammaf idaan sirati hafee @amount@.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingLoan(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        String sentence = !"ZERO".equals(remainingLoanAmount) ? "idaan sirati hafee @amount@ kun miindaa kee fuldura irra kan muramudha."
                : " idaan sirati hafee @amount@.";

        return new HashMap(){
            {
                put("sentence", sentence);
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getWeWillNotDeduct(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        return new HashMap(){
            {
                put("sentence", "ji'a kanati idaa sira jiru hinmuru, idaan sira jiru @amount@ miindaa kee fuldura irraa murama.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String translateDeductionReason(String code, Date date, boolean criminalized) {
        String translatedDate = this.getTranslatedDate(date);
        if(criminalized){
            return "Adu guyyaa " + translatedDate + ", kaadiimni kee sidebiste, wan isan mitef ykn isan irraa hatef:";
        }else if(code.contains("faulty_replacement")){
            return "Adu guyyaa " + translatedDate + ", kaadinmi kee si debiste:";
        }else if(code.contains("self_replacement")){
            return "Adu guyyaa " + translatedDate + ", kaadiimaa kee hinhojadhu jete  murteessiterta:";
        }else if(code.contains("no_show") || code.contains("noshow")){
            return "Adu guyyaa " + translatedDate + ", hafterta ykn hindhufne:";
        }else if(code.contains("Maid_is_not_wearing_the_mask")){
            return "Adu guyyaa " + translatedDate + ", maskii ykn haguugaa fula hinufane:";
        }else if (code.contains("unemployment_insurance_plan")) {
            return "Nuuf keessanii kan keessaniif qorannoo dhabuumsaaf kaffallee jirra.";
        } else if (code.contains("unemployment_insurance_fines")) {
            return "Isin hin kaffalleef sababii kanaaf MOHRE adabbiiwwanif keessanii maqaa keessaniin nuti kaffalleerra.";
        }else{
            return "Adu guyyaa " + translatedDate + "kanbiraa siramurameraa:";
        }
    }

    @Override
    public String translateAdditionReasonV2(String code, Date date) {
        String translatedDate = this.getTranslatedDate(date);
        if(code.contains("forgive_deduction")){
            return "dabalata want dogoggoran adu guyyaa " + translatedDate + " sira muramef:";
        }else if(code.contains("raffle_prize")){
            return "Ixan si bahe " + translatedDate + ":";
        }else if(code.contains("recommendation_from_client")){
            return "Kaadimni kee wa'ee gaaruma keeti nuti himte:";
        }else if(code.contains("salary_dispute")){
            return "dabalata wan miindaan kee ji'a darbe " + translatedDate + " dogoggora ta'ef:";
        }else if(code.contains("previously_held_salary")){
            return "Boqqonna irra turte kanaf miindaa kee durani hinargane.";
        }else if(code.contains("bonus")){
            return "adu guyyaa " + translatedDate + " dabalata argate:";
        }else if(code.contains("medical_assistant")){
            return "Adu guyyaa " + translatedDate + " Baasii kee hospitaala tif  si gargare ture:";
        }else if(code.contains("taxi_reimbursement")){
            return "Adu guyyaa " + translatedDate + " baasii taaksii keeti si deebisneraa:";
        }else{
            return "Adu guyyaa " + translatedDate + " dabalataa argatertaa:";
        }
    }

    @Override
    public String basicPayThisMonth(Integer days) {
        return "kafalti yayyabaa kan ji'a kana (" + days + ")";
    }

    @Override
    public String managerAdditions() {
        return "dabalata hoogganaa raa";
    }

    @Override
    public String translateMonth(Month month) {
        switch (month){
            case JANUARY:
                return "Amajjii";
            case FEBRUARY:
                return "Guraandhala";
            case MARCH:
                return "Bitooteesa";
            case APRIL:
                return "Ebila";
            case MAY:
                return "Eebla";
            case JUNE:
                return "Waxabajjii";
            case JULY:
                return "Adoolessa";
            case AUGUST:
                return "Hagayya";
            case SEPTEMBER:
                return "Fulbaana";
            case OCTOBER:
                return "Onkololessa";
            case NOVEMBER:
                return "Sadaasa";
            case DECEMBER:
                return "Muddee";
        }
        return "";
    }

    @Override
    public String translateDay(DayOfWeek day) {
        switch (day){
            case SATURDAY:
                return "Sanbata";
            case SUNDAY:
                return "Dilbata";
            case MONDAY:
                return "Wiixata";
            case TUESDAY:
                return "Kibxata";
            case WEDNESDAY:
                return "Roobi";
            case THURSDAY:
                return "Kamila";
            case FRIDAY:
                return "Jimaata";
        }
        return "";
    }
}
