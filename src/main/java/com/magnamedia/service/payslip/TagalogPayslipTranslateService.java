package com.magnamedia.service.payslip;

import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.DayOfWeek;
import java.time.Month;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Creation Date 14/05/2020
 */
@Service
public class TagalogPayslipTranslateService extends PayslipTranslateService {

    @Override
    public String getLangCode() {
        return "tl";
    }

    @Override
    public String noShowDeduction(String date) {
        return "kaltas sa pagliban " + date;
    }

    @Override
    public String replacement(String type) {
        return "pagpalit sa nagawang mali";
    }

    @Override
    public String failedInterview(String date, String time) {
        return "pag bagsak sa panayam " + date + " " + time;
    }

    @Override
    public Map<String, Object> getPayslipHeader(Date paymentDate, String housemaidName) {
        HashMap paramMap = new HashMap();
        paramMap.put("name", housemaidName);
        paramMap.put("paymentDate", this.getTranslatedDate(paymentDate));

        return new HashMap(){
            {
                put("payslipForSentence", "Payslip para kay: @name@");
                put("dateSentence", "Petsa: @paymentDate@");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodation(int groupTwoDays, String oneDayInAccommodation) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayInAccommodation + " kada araw");

        return new HashMap(){
            {
                put("sentence", "Nanatili ka nang " + groupTwoDays + " araw sa accommodation at kumita ng @perDayAmount@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClient(int groupOneDays, String oneDayWithClient) {
        HashMap paramMap = new HashMap();
        paramMap.put("perDayAmount", oneDayWithClient + " kada araw");

        return new HashMap(){
            {
                put("sentence", "Tumira ka nang " + groupOneDays + " araw sa bahay ng kliyente at kumita ng @perDayAmount@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysInAccommodationWithGr6(int groupTwoSixDays) {

        HashMap paramMap = new HashMap();
        return new HashMap() {
            {
                put("sentence", "Ikaw ay nanatili ng " + groupTwoSixDays + " araw ng walang kliyente at kumita ng");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getDaysWithClientWithGr5(int groupOneFiveDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "Ikaw ay nanatili ng " + groupOneFiveDays + " araw sa bahay ng kliyente at kumita ng");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getOnVacationDays(int groupFourDays) {
        HashMap paramMap = new HashMap();

        return new HashMap() {
            {
                put("sentence", "Ikaw ay naka bakasyon ng " + groupFourDays + " araw at kumita ng");
                put("parameters", paramMap);
            }
        };
    }




    @Override
    public String getYourDeductionsThisMonth() {
        return "Ang mga kaltas mo ngayong buwan:";
    }

    @Override
    public String getYouReceivedThisMonth() {
        return "Kabuuang halaga na pinadala sa inyong account:";
    }

    @Override
    public String getWhyDeductionSentence(String totalDeduction) {
        return "Kung gusto mong malaman kung bakit ka namin binawasan ng AED " + totalDeduction + " mula sa iyong sweldo, pindutin itong button sa ibaba.";
    }

    @Override
    public String getClickToView() {
        return "Pindutin dito para makita ang iyong mga kaltas.";
    }

    @Override
    public String getYourDeductionBefore(String firstOfPayrollMonth) {
        return "Ang kaltas mo dati " + firstOfPayrollMonth + ":";
    }

    @Override
    public String getDeductionsSoFar() {
        return "Mga kaltas mo sa kasalukuyan:";
    }

    @Override
    public Map<String, Object> getLoanYouPreviouslyHad(String previousLoan) {
        HashMap paramMap = new HashMap();
        paramMap.put("previousLoan", previousLoan);

        return new HashMap(){
            {
                put("sentence", "Loan na kailangan mo pang bayaran ay @previousLoan@:");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String getLoanRepayment() {
        return "Pagbabayad sa loan ngayong buwan:";
    }

    @Override
    public String getTotalDeductionsAndLoans() {
        return "Kabuuang pagbabayad sa kaltas at loan ngayong buwan:";
    }

    @Override
    public Map<String, Object> getWeOnlyDeducted(String currentDeductions) {
        HashMap paramMap = new HashMap();
        paramMap.put("deductionsThisMonth", currentDeductions);

        return new HashMap(){
            {
                put("sentence", "Binawasan ka lamang namin ng @deductionsThisMonth@ para mayroon ka pang pera na maipapadala sa pamilya mo.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingDeductions(String remainingDeductionAmount) {
        if ("0".equals(remainingDeductionAmount))
            remainingDeductionAmount = "Zero";
        else
            remainingDeductionAmount = "AED " + remainingDeductionAmount;

        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingDeductionAmount);

        return new HashMap(){
            {
                put("sentence", "Ang natitira mong kaltas ngayon ay @amount@.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getYourRemainingLoan(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        String sentence = !"ZERO".equals(remainingLoanAmount) ? "At ang natitira mong loan ngayon ay @amount@ na ibabawas sa iyo sa mga susunod mong sweldo."
                : "At ang natitira mong loan ngayon ay @amount@";

        return new HashMap(){
            {
                put("sentence", sentence);
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public Map<String, Object> getWeWillNotDeduct(String remainingLoanAmount) {
        HashMap paramMap = new HashMap();
        paramMap.put("amount", remainingLoanAmount);

        return new HashMap(){
            {
                put("sentence", "Hindi ka namin babawasan para sa pagbabayad ng loan ngayong buwan. Ang natitira mong loan na @amount@ ay ibabawas sa iyong mga susunod na sweldo.");
                put("parameters", paramMap);
            }
        };
    }

    @Override
    public String translateDeductionReason(String code, Date date, boolean criminalized) {
        String translatedDate = this.getTranslatedDate(date);
        if(criminalized){
            return "Noong " + translatedDate + ", ni-replace ka ng client mo dahil sinaktan mo sila o may ninakaw ka sa kanila:";
        }else if(code.contains("faulty_replacement")){
            return "Noong " + translatedDate + ", ni-replace ka ng kliyente mo:";
        }else if(code.contains("self_replacement")){
            return "Noong " + translatedDate + ", nagdesisyon kang huwag nang magtrabaho sa kliyente mo:";
        }else if(code.contains("no_show") || code.contains("noshow")){
            return "Noong " + translatedDate + ", ikaw ay absent:";
        }else if(code.contains("Maid_is_not_wearing_the_mask")){
            return "Noong " + translatedDate + ", hindi ka nakasuot ng mask:";
        }else if (code.contains("unemployment_insurance_plan")) {
            return "Nagbayad kami para sa iyo para sa iyong plano ng seguro sa kawalan ng trabaho.";
        } else if (code.contains("unemployment_insurance_fines")) {
            return "Nagbayad kami para sa iyo para sa mga multa ng MOHRE dahil hindi mo nabayaran ang iyong seguro sa kawalan ng trabaho.";
        }else{
            return "Nagkaroon ka ng deduction noong " + translatedDate + ":";
        }
    }

    @Override
    public String translateAdditionReasonV2(String code, Date date) {
        String translatedDate = this.getTranslatedDate(date);
        if(code.contains("forgive_deduction")){
            return "Karagdagan dahil nagkaroon ka ng maling kaltas noong " + translatedDate + ":";
        }else if(code.contains("raffle_prize")){
            return "Nanalo ka sa raffle noong " + translatedDate + ":";
        }else if(code.contains("recommendation_from_client")){
            return "Nagbigay sa amin ang client mo ng magandang review:";
        }else if(code.contains("salary_dispute")){
            return "Karagdagan dahil ang swledo mo noong " + translatedDate + " ay mali:";
        }else if(code.contains("previously_held_salary")){
            return "Ikaw ay nasa bakasyon at hindi mo nakuha ang iyong nakaraan sahod";
        }else if(code.contains("bonus")){
            return "Nagkaroon ka ng bonus noong " + translatedDate + ":";
        }else if(code.contains("medical_assistant")){
            return "Tinulungan ka namin sa iyong medical expenses noong " + translatedDate + ":";
        }else if(code.contains("taxi_reimbursement")){
            return "Binalik namin ang binayad mo sa taxi noong " + translatedDate + ":";
        }else{
            return "Nagkaron ka ng karagdagan noong " + translatedDate + ":";
        }
    }

    @Override
    public String basicPayThisMonth(Integer days) {
        return "Pangunahing sahod sa buwan (" + days + ")";
    }

    @Override
    public String managerAdditions() {
        return "Dagdag sahod";
    }

    @Override
    public String translateMonth(Month month) {
        switch (month){
            case JANUARY:
                return "Enero";
            case FEBRUARY:
                return "Pebrero";
            case MARCH:
                return "Marso";
            case APRIL:
                return "Abril";
            case MAY:
                return "Mayo";
            case JUNE:
                return "Hunyo";
            case JULY:
                return "Hulyo";
            case AUGUST:
                return "Agosto";
            case SEPTEMBER:
                return "Setyembre";
            case OCTOBER:
                return "Oktubre";
            case NOVEMBER:
                return "Nobyembre";
            case DECEMBER:
                return "Disyembre";
        }
        return "";
    }

    @Override
    public String translateDay(DayOfWeek day) {
        switch (day){
            case SATURDAY:
                return "Sabado";
            case SUNDAY:
                return "Linggo";
            case MONDAY:
                return "Lunes";
            case TUESDAY:
                return "Martes";
            case WEDNESDAY:
                return "Miyerkules";
            case THURSDAY:
                return "Huwebes";
            case FRIDAY:
                return "Biyernes";
        }
        return "";
    }
}
