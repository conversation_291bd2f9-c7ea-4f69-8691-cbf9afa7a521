package com.magnamedia.service.payslip;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.extra.SalaryLibrary;

import java.sql.Date;
import java.time.DayOfWeek;
import java.time.Month;
import java.util.*;

/**
 * <AUTHOR>
 * Creation Date 14/05/2020
 */
public abstract class PayslipTranslateService {

    public abstract String getLangCode();

    public abstract String basicPayThisMonth(Integer days);

    public abstract String managerAdditions();

    public abstract String noShowDeduction(String date);

    public abstract String replacement(String type);

    public abstract String failedInterview(String date, String time);

     //----------------------PAY-262 * <PERSON><PERSON><PERSON>m Ha<PERSON> -----------------//
     //----------------------PAY-293 new updates * <PERSON><PERSON><PERSON><PERSON><PERSON> -----------------//
    public abstract Map<String, Object> getPayslipHeader(Date paymentDate, String housemaidName);

    public abstract Map<String, Object> getDaysInAccommodation(int groupTwoDays, String oneDayInAccommodation);

    public abstract Map<String, Object> getDaysWithClient(int groupOneDays, String oneDayWithClient);

    public abstract Map<String, Object> getDaysInAccommodationWithGr6(int groupTwoSixDays);

    public abstract Map<String, Object> getDaysWithClientWithGr5(int groupOneFiveDays);

    public abstract Map<String, Object> getOnVacationDays(int groupFourDays);

    public abstract String getYourDeductionsThisMonth();

    public abstract String getYouReceivedThisMonth();

    public abstract String getWhyDeductionSentence(String totalDeduction);
    public abstract String getClickToView();

    public abstract String getYourDeductionBefore(String firstOfPayrollMonth);
    public abstract String getDeductionsSoFar();

    public abstract Map<String, Object> getLoanYouPreviouslyHad(String previousLoan);
    public abstract String getLoanRepayment();

    public abstract String getTotalDeductionsAndLoans();

    public abstract Map<String, Object> getWeOnlyDeducted(String totalDeductions);

    public abstract Map<String, Object> getYourRemainingDeductions(String remainingDeductionAmount);
    public abstract Map<String, Object> getYourRemainingLoan(String remainingLoanAmount);
    public abstract Map<String, Object> getWeWillNotDeduct(String remainingLoanAmount);

    ///////////////////////////////////////
    public abstract String translateDeductionReason(String code, Date date, boolean criminalized);

    public abstract String translateAdditionReasonV2(String code, Date date);

    public String translateAdditionReason(PayrollManagerNote note) {
        PicklistItem additionReason = note.getAdditionReason();
        if(additionReason == null) return "";

        Tag translationTag = additionReason.getTagValue(this.getLangCode());
        if(translationTag != null) {
            return translationTag.getValue();
        }
        return additionReason.getName();
    }

    public static PayslipTranslateService getTranslateService(String lang) {
        switch (lang) {
            case "amh":
                return Setup.getApplicationContext().getBean(AmharicPayslipTranslateService.class);
            case "om":
                return Setup.getApplicationContext().getBean(OromoPayslipTranslateService.class);
            case "hi":
                return Setup.getApplicationContext().getBean(HindiPayslipTranslateService.class);
            case "tl":
                return Setup.getApplicationContext().getBean(TagalogPayslipTranslateService.class);
            default:
                return Setup.getApplicationContext().getBean(EnglishPayslipTranslateService.class);
        }
    }

    public static List<String> getHousemaidPayslipLanguages(Housemaid housemaid) {
        PicklistItem nationality = housemaid.getNationality();
        List<String> languages = new ArrayList<>();
        languages.add("en");
        if(nationality == null) {
            return languages;
        }

        if(Arrays.asList(SalaryLibrary.ETHIOPIAN_NATIONALITY_CODES).contains(nationality.getCode())) {
            languages.add("amh");
            languages.add("om");
        }

        if(Arrays.asList(SalaryLibrary.FILIPINO_NATIONALITY_CODES).contains(nationality.getCode())) {
            languages.add("tl");
        }

        if(Arrays.asList(SalaryLibrary.INDIAN_NATIONALITY_CODES).contains(nationality.getCode())) {
            languages.add("hi");
        }

        return languages;
    }

    public static String getPayslipSmsTemplate(Housemaid housemaid) {
        PicklistItem nationality = housemaid.getNationality();
        if(nationality == null) {
            return "Accounting_Send_URL";
        }
        if(Arrays.asList(SalaryLibrary.ETHIOPIAN_NATIONALITY_CODES).contains(nationality.getCode())) {
            return "Ethiopian_Housemaid_Payslips";
        }

        if(Arrays.asList(SalaryLibrary.FILIPINO_NATIONALITY_CODES).contains(nationality.getCode())) {
            return "Filipino_Housemaid_Payslips";
        }

        if(Arrays.asList(SalaryLibrary.INDIAN_NATIONALITY_CODES).contains(nationality.getCode())) {
            return "Indian_Housemaid_Payslips";
        }

        return "Accounting_Send_URL";
    }

    public static String getPayslipSms(String template, Map<String,String> paramValues) {
        String templateContent;
        switch (template) {
            case "Ethiopian_Housemaid_Payslips":
                templateContent = "Hello,\nYou can download this month’s payslip in English @payslip_en@, Amharic @payslip_amh@ or in Oromo @payslip_om@";
                break;
            case "Filipino_Housemaid_Payslips":
                templateContent = "Hello ate,\nYou can download this month’s payslip in English @payslip_en@ or in Tagalog @payslip_tl@";
                break;
            case "Indian_Housemaid_Payslips":
                templateContent = "Hello,\nYou can download this month’s payslip in English @payslip_en@, Hindi @payslip_hi@, or in Malayalm @payslip_ml@";
                break;
            default:
                templateContent = "Hello,\nYou can download this month’s payslip @url@";
        }

        for(String key: paramValues.keySet()) {
            templateContent = templateContent.replaceAll("@" + key + "@", paramValues.get(key));
        }
        return templateContent;
    }

    public String getTranslatedDate(java.sql.Date date) {
        return this.translateDay(date.toLocalDate().getDayOfWeek())
                + ", " + this.translateMonth(date.toLocalDate().getMonth())
                + " " + date.toLocalDate().getDayOfMonth();
    }

    public String getTranslatedDateWithoutDayOfWeek(java.sql.Date date) {
        return this.translateMonth(date.toLocalDate().getMonth())
                + " " + date.toLocalDate().getDayOfMonth();
    }
    public abstract String translateMonth(Month month);
    public abstract String translateDay(DayOfWeek day);
}
