package com.magnamedia.service.yayaapp;

import com.magnamedia.controller.PaySlipsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.TemplateTranslationRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.helper.YayaAppContentHelper;
import com.magnamedia.repository.MonthlyPaymentRuleRepository;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class YayaAppNotificationService {

    private final NotificationService notificationService;
    private final HousemaidPayrollAuditService housemaidPayrollAuditService;
    private final MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    public MessagingService messagingService;
    @Autowired
    public YayaAppContentHelper yayaAppContentHelper;

    public YayaAppNotificationService(NotificationService notificationService, HousemaidPayrollAuditService housemaidPayrollAuditService, MonthlyPaymentRuleRepository monthlyPaymentRuleRepository) {
        this.notificationService = notificationService;
        this.housemaidPayrollAuditService = housemaidPayrollAuditService;
        this.monthlyPaymentRuleRepository = monthlyPaymentRuleRepository;
    }


//    public PushNotification sendNotification(Housemaid housemaid, String templateName,
//                                             Map<String, String> params, Map<String, Object> context) {
//        try {
//            return notificationService.pushNotification(
//                    housemaid, templateName, null, null, templateName,
//                    housemaid.getYayaAppNotificationLang(), params, context, housemaid.getNormalizedPhoneNumber(),
//                    housemaid.getId(), housemaid.getEntityType());
//        } catch (Exception ex) {
//            Logger.getLogger(YayaAppNotificationService.class.getSimpleName()).log(Level.SEVERE, "Unable to send notification to " + housemaid.getName(), ex);
//        }
//
//        return null;
//    }

//    @Transactional
//    public boolean notifyAllEligibleMaidsBT(Long ruleId, Date paymentDate) {
//        MonthlyPaymentRule rule = monthlyPaymentRuleRepository.findOne(ruleId);
//        List<Housemaid> includedHousemaidCCList = housemaidPayrollAuditService.getTargetListForHousemaidType(rule, true);
//        Map<String, String> paramValue = new HashMap<>();
//        paramValue.put("month", DateUtil.formatMonth(rule.getPayrollMonth()));
//        paramValue.put("payment_date", DateUtil.formatDateDashed(paymentDate));
//
//        for (Housemaid housemaid : includedHousemaidCCList) {
//            paramValue.put("maid_name", housemaid.getName());
//            messagingService.send("Payroll_Late_Salary_Because_Of_Holiday_Notification", "Payroll_Late_Salary_Because_Of_Holiday_Notification",
//                    housemaid.getYayaAppNotificationLang(), housemaid, paramValue, housemaid, null, housemaid);
////            sendNotification(housemaid, "Payroll_Late_Salary_Because_Of_Holiday_Notification", paramValue, null);
//        }
//
//        List<Housemaid> eligibleListForMVMaids = housemaidPayrollAuditService.getEligibleListForMVMaids(rule);
//        paramValue.put("payment_date", DateUtil.formatDateDashed(DateUtil.addDays(paymentDate, 1)));
//        for (Housemaid housemaid : eligibleListForMVMaids) {
//
//            if (housemaid.getCurrentClient() != null) {
//                messagingService.notifyClientMaidSalaryIsDaley(housemaid.getCurrentClient(), DateUtil.formatClientFullDate(paymentDate), housemaid.getFormattedFirstName());
//            }
//
//            List<String> languages = yayaAppContentHelper.getLanguageForMaid(housemaid);
//            paramValue.put("maid_name", housemaid.getName());
//            //Template template = TemplateUtil.getTemplate("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS");
//            for (String lang : languages) {
//                //String text = TemplateUtil.compileTemplate(template, lang, null, paramValue);
////                smsService.send(
////                        "Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS",
////                        normalizePhoneNumber(housemaid.getPhoneNumber()),
////                        text,
////                        false,
////                        SmsReceiverType.Housemaid,
////                        housemaid.getId(),
////                        housemaid.getName()
////                );
//                messagingService.send("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS",
//                        lang, housemaid, paramValue, null, null, null);
//            }
//        }
//
//        return true;
//    }
}
