package com.magnamedia.service.mailprocessor;

import com.aspose.imaging.internal.cR.S;
import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.MailProcessor;
import com.magnamedia.entity.MaidRejectedSalary;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.HtmlUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.MaidRejectedSalaryRepository;
import com.magnamedia.repository.NewVisaRequestRepository;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Component(value = "processingMaidsRejectedSalaries")
public class ProcessingMaidsRejectedSalaries implements MailProcessor {
    @Override
    public void process(String senderEmail, String subject, String body, List<File> attachements) {
        Logger logger = Logger.getLogger(ProcessingMaidsRejectedSalaries.class.getName());
        logger.info("senderEmail = " + senderEmail);
        logger.info("subject = " + subject);
        logger.info("body " + body);
        try {
            String targetEmailSender = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ANSARI_MAIDS_REJECTED_SALARIES_EMAIL);

            if (subject == null || body == null || !targetEmailSender.equals(senderEmail)) {
                return;
            }
            List<List<String>> rows = HtmlUtil.parseHtmlTable(body, 0);
            if (!isValidateBody(rows)){
                rows = HtmlUtil.parseHtmlTable(body, 1);
                if (!isValidateBody(rows)){
                    return;
                }
            }
            rows = updateRows(rows);
            if (rows == null || rows.isEmpty()) {
                return;
            }
            String lastSif = rows.get(0).get(1);
            for (int i = 0 ; i < rows.size(); i++){
                List<String> row = rows.get(i);
                String personalNumber = row.get(4);
                if (personalNumber == null || personalNumber.isEmpty()){
                    continue;
                }
                String sif = row.get(1);
                if (sif == null || sif.isEmpty()){
                    sif = lastSif;
                }
                lastSif = sif;

                NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class)
                        .findFirstByEmployeeUniqueIdAndHousemaidIsNotNullOrderByCreationDateDesc(personalNumber);
                if (newRequest == null || newRequest.getHousemaid() == null){
                    continue;
                }
                MaidRejectedSalary maidRejectedSalary = new MaidRejectedSalary(newRequest.getHousemaid(), personalNumber, sif);
                maidRejectedSalary = Setup.getRepository(MaidRejectedSalaryRepository.class).save(maidRejectedSalary);

            }

        } catch (Exception ex){
            DebugHelper.sendExceptionMail("<EMAIL>;<EMAIL>", ex, "Error in ProcessingMaidsRejectedSalaries", false);

        }

    }

    private List<List<String>> updateRows(List<List<String>> rows) {
        List<List<String>> finalResAfterFill = rows.subList(1, rows.size());
        finalResAfterFill = finalResAfterFill.stream().filter(row -> (row.get(7).toUpperCase().contains("LOST") || row.get(7).toUpperCase().contains("CLOSED")))
                .collect(Collectors.toList());

        return finalResAfterFill;
    }

    private boolean isValidateBody(List<List<String>> rows) {
        if (rows == null || rows.isEmpty()) {
            return false;
        }
        List<String> header = rows.get(0);
        if (header == null || header.size() != 10) {
            return false;
        }
        List<String> validHeader = Arrays.asList("COMPANY NAME", "SIF NO", "RTC Date", "RTC AMOUNT", "PERSONAL NUMBER",
                "AMOUNT", "RTC CODE", "REASON", "COMPANY CODE", "MOL");

        for (int i = 0; i < 10; i++) {
            if (!validHeader.get(i).equalsIgnoreCase(header.get(i))) {
                return false;
            }
        }
        return true;
    }
}
