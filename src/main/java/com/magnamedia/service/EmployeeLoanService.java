package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.entity.EmployeeLoan;
import com.magnamedia.entity.EmployeeLoanApprove;
import com.magnamedia.entity.Repayment;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.EmployeeLoanRepository;
import com.magnamedia.repository.OfficeStaffPayrollLogRepository;
import com.magnamedia.repository.RepaymentRepository;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 5/18/2020
 **/

@Service
public class EmployeeLoanService {

    @Autowired
    private RepaymentRepository repaymentRepository;

    @Autowired
    private EmployeeLoanRepository employeeLoanRepository;

    @Transactional
    public void generateAllRepayments(Double monthlyRepaymentAmount, Date startingDate, EmployeeLoan employeeLoan) {
        Double remaining = 0.0;
        Repayment repayment;
        Calendar calendar = Calendar.getInstance();

        // 1- calculate remaining unpaid amount from startingDate and check if less than 0
        startingDate = new java.sql.Date(PayrollGenerationLibrary.getPayrollEndDate(new LocalDate(startingDate.getTime())).withDayOfMonth(1).toDate().getTime());
        remaining = getRemainingUnpaidAmount(startingDate, employeeLoan);

        if (remaining <= 0)
            return;

        // 2- calculate how many repayments we gonna need
        int repaymentsCount = (int) Math.ceil(remaining / monthlyRepaymentAmount);

        // 3- loop on the above number to create
        for (int i = 1; i <= repaymentsCount; i++) {
            repayment = new Repayment(monthlyRepaymentAmount, startingDate, employeeLoan);

            //if last repayment calculate the last repaymentAmount
            if (i == repaymentsCount) {
                repayment.setAmount(remaining - (monthlyRepaymentAmount * (repaymentsCount - 1)));
            }
            repayment = repaymentRepository.save(repayment);

            //add the new generated repayment to repaymentList
            employeeLoan.getRepaymentList().add(repayment);

            //get the next month
            calendar.setTime(startingDate);
            calendar.add(Calendar.MONTH, 1);
            startingDate = new Date(calendar.getTime().getTime());
        }

        // 4- save EmployeeLoan after adding the generated repayments
        employeeLoanRepository.save(employeeLoan);
    }

    @Transactional
    public void generateAllRepayments(Double monthlyRepaymentAmount, EmployeeLoan employeeLoan) {
        Double remaining = 0.0;
        Repayment repayment;
        Calendar calendar = Calendar.getInstance();

        // 1- calculate remaining unpaid amount from startingDate( first unpaid repayment date or from the same month) and check if less than 0
        Date startingDate = null;
        Repayment lastPaidRepayment = repaymentRepository.findTopByEmployeeLoanAndPaidRepaymentOrderByRepaymentDateDesc(employeeLoan, true);
        if (lastPaidRepayment != null)
            startingDate = DateUtil.addMonths(lastPaidRepayment.getRepaymentDate(), 1);
        else { //new loan
            if (employeeLoan.getDeductFromSameMonth())
                startingDate = employeeLoan.getLoanDate();
            else
                startingDate = DateUtil.addMonths(employeeLoan.getLoanDate(), 1);
        }

        startingDate = new java.sql.Date(new LocalDate(startingDate).withDayOfMonth(1).toDate().getTime());
        remaining = getRemainingUnpaidAmount(employeeLoan);

        if (remaining < 0)
            throw new BusinessException("Loan amount is less than paid amount");

        // 2- calculate how many repayments we gonna need
        int repaymentsCount = (int) Math.ceil(remaining / monthlyRepaymentAmount);

        // 3- loop on the above number to create
        for (int i = 1; i <= repaymentsCount; i++) {
            repayment = new Repayment(monthlyRepaymentAmount, startingDate, employeeLoan);

            //if last repayment calculate the last repaymentAmount
            if (i == repaymentsCount) {
                repayment.setAmount(remaining - (monthlyRepaymentAmount * (repaymentsCount - 1)));
            }
            repayment = repaymentRepository.save(repayment);

            //add the new generated repayment to repaymentList
            employeeLoan.getRepaymentList().add(repayment);

            //get the next month
            calendar.setTime(startingDate);
            calendar.add(Calendar.MONTH, 1);
            startingDate = new Date(calendar.getTime().getTime());
        }

        // 4- save EmployeeLoan after adding the generated repayments
        employeeLoanRepository.save(employeeLoan);
    }

    public Double getRemainingUnpaidAmount(Date startingDate, EmployeeLoan employeeLoan) {
        Double paidAmount = repaymentRepository.sumAmountByEmployeeLoanAndRepaymentDateLessThan(employeeLoan, startingDate);
        return employeeLoan.getAmount() - (paidAmount != null ? paidAmount : 0d);
    }

    public Double getRemainingUnpaidAmount(EmployeeLoan employeeLoan) {

        Double paidAmount = repaymentRepository.sumAmountByEmployeeLoanAndPaidRepayment(employeeLoan, true);
        return employeeLoan.getAmount() - (paidAmount != null ? paidAmount : 0d);
    }


    @Transactional
    public void deleteRepayments(Date startingDate, EmployeeLoan employeeLoan) {
        Date current = new java.sql.Date(PayrollGenerationLibrary.getPayrollEndDate(new LocalDate()).withDayOfMonth(1).toDate().getTime());
        //return if starting date in past
        if (startingDate.compareTo(current) < 0)
            throw new BusinessException("Can't delete paid repayments");

        repaymentRepository.deleteByEmployeeLoanAndRepaymentDateGreaterThanEqual(employeeLoan, startingDate);

    }

    @Transactional
    public void deleteRepayments(EmployeeLoan employeeLoan) {
        repaymentRepository.deleteByEmployeeLoanAndPaidRepayment(employeeLoan, false);

    }

    public boolean canDeductFromSameMonth(EmployeeLoanApprove entity){
        java.sql.Date payrollMonth = new java.sql.Date((new LocalDate(entity.getLoanDate()).withDayOfMonth(1)).toDate().getTime());
        OfficeStaffPayrollLog log = Setup.getRepository(OfficeStaffPayrollLogRepository.class).
                findTopByOfficeStaffAndPayrollMonthAndForEmployeeLoanFalse(entity.getOfficeStaff(), payrollMonth);
        return log == null || log.getPayrollAccountantTodo() == null;
    }
}
