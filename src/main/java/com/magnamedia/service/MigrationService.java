package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.OfficeStaffCandidate;
import com.magnamedia.entity.OfficeStaffDocument;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.OfficeStaffDocumentType;
import com.magnamedia.repository.OfficeStaffCandidateRepository;
import com.magnamedia.repository.OfficeStaffDocumentRepository;
import com.magnamedia.repository.OfficeStaffRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class MigrationService {

    @Autowired
    private OfficeStaffDocumentRepository officeStaffDocumentRepository;

    @Autowired
    private PicklistRepository picklistRepository;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;

    @Autowired
    private OfficeStaffCandidateRepository officeStaffCandidateRepository;

    @Transactional
    public void handleProfilePhoto(OfficeStaff staff, String photoUrl) throws BusinessException {
        try {

            // Extract Google Drive file ID and prepare everything before deleting old photos
            String fileId = extractGoogleDriveFileId(photoUrl);
            if (fileId == null) {
                throw new BusinessException("Invalid Google Drive URL format: " + photoUrl);
            }

            // Create direct download URL
            String directDownloadUrl = "https://drive.google.com/uc?export=download&id=" + fileId;
            URL url;
            try {
                url = new URL(directDownloadUrl);
            } catch (MalformedURLException e) {
                throw new BusinessException("Failed to create download URL: " + e.getMessage());
            }

            HttpURLConnection connection;
            try {
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000); // 10 seconds timeout
                connection.setReadTimeout(30000);    // 30 seconds timeout
                connection.connect();

                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    throw new BusinessException("Failed to download image. HTTP error code: " + responseCode);
                }
            } catch (IOException e) {
                throw new BusinessException("Connection error: " + e.getMessage());
            }

            // Determine file extension from content type
            String contentType = connection.getContentType();
            String fileExtension = ".jpg"; // default
            if (contentType != null && contentType.startsWith("image/")) {
                String subtype = contentType.substring("image/".length()).split(";")[0].trim().toLowerCase();
                if (subtype.equals("jpeg")) subtype = "jpg";
                fileExtension = "." + subtype;
            }

            // Create temporary file
            String fileName = staff.getEmail().replace("@", "_").replace(".", "_") + "_profile" + fileExtension;
            File tempFile = Paths.get(System.getProperty("java.io.tmpdir"), fileName).toFile();

            // Download file
            try (InputStream in = connection.getInputStream();
                 FileOutputStream out = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            } catch (IOException e) {
                throw new BusinessException("Failed to save image: " + e.getMessage());
            }

            // Verify file size
            if (tempFile.length() == 0) {
                tempFile.delete();
                throw new BusinessException("Downloaded file is empty");
            }

            // Create MultipartFile
            MultipartFile multipartFile;
            try {
                multipartFile = new MockMultipartFile(fileName, fileName, contentType, new FileInputStream(tempFile));
            } catch (IOException e) {
                tempFile.delete();
                throw new BusinessException("Failed to create MultipartFile: " + e.getMessage());
            }

            // Store attachment
            Attachment attachment;
            try {
                attachment = Storage.storeTemporary(multipartFile, "PROFILE_PHOTO", true);
            } catch (Exception e) {
                tempFile.delete();
                throw new BusinessException("Failed to store attachment: " + e.getMessage());
            }

            createProfilePhotoDocument(staff, null, attachment);

            // Clean up
            tempFile.delete();

        } catch (BusinessException e) {
            // Re-throw BusinessExceptions
            throw e;
        } catch (Exception e) {
            // Convert other exceptions to BusinessExceptions
            throw new BusinessException("Failed to process profile photo: " + e.getMessage());
        }
    }

    @Transactional
    public void createProfilePhotoDocument(OfficeStaff officeStaff, OfficeStaffCandidate officeStaffCandidate,Attachment attachment){

        // Skip processing if attachment is null
        if (attachment == null) {
            return;
        }

        // Skip processing if both entities are null or unsaved
        if ((officeStaff == null || officeStaff.getId() == null) &&
                (officeStaffCandidate == null || officeStaffCandidate.getId() == null)) {
            return;
        }

        // Check if staff already has a profile picture
        List<OfficeStaffDocument> existingPhotos;
        if(officeStaff != null) {
            existingPhotos = officeStaffDocumentRepository.findByOfficeStaffAndType(officeStaff, OfficeStaffDocumentType.PROFILE_PHOTO);
        } else {
            existingPhotos = officeStaffDocumentRepository.findByOfficeStaffCandidateAndType(officeStaffCandidate, OfficeStaffDocumentType.PROFILE_PHOTO);
        }

        if ((existingPhotos != null && !existingPhotos.isEmpty()) && existingPhotos.get(0).getAttachments() != null ){
            if(!existingPhotos.get(0).getAttachments().get(0).equals(attachment)){
                officeStaffDocumentRepository.delete(existingPhotos);
            } else {
                return;
            }
        }

        // Create new document
        OfficeStaffDocument document = new OfficeStaffDocument();

        if (officeStaff != null) {
            document.setOfficeStaff(officeStaff);

        } else if(officeStaffCandidate != null) {
            document.setOfficeStaffCandidate(officeStaffCandidate);
        }

        String documentName = "Profile Picture";
        document.setName(documentName);
        document.setType(OfficeStaffDocumentType.PROFILE_PHOTO);
        document.setUploadDate(new Date());
        document.setAttachments(Collections.singletonList(attachment));

        try {
            officeStaffDocumentRepository.save(document);
        } catch (Exception e) {
            throw new BusinessException("Failed to save document: " + e.getMessage());
        }
    }

    public void handleDepartments(OfficeStaff staff, String departmentsStr) {
        List<String> departmentNames = Arrays.stream(departmentsStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());

        Picklist departmentPicklist = picklistRepository.findByCode(PayrollManagementModule.PICKLIST_OFFICE_STAFF_DEPARTMENT);
        if (departmentPicklist == null) return;

        List<PicklistItem> departments = departmentNames.stream()
                .map(name -> Setup.getRepository(PicklistItemRepository.class)
                        .findByListAndCodeIgnoreCase(departmentPicklist, name))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        staff.setDepartments(departments);
    }


    // Helper method to extract Google Drive file ID from URL
    private String extractGoogleDriveFileId(String url) {
        // Pattern for Google Drive URLs
        Pattern pattern = Pattern.compile("https://drive\\.google\\.com/file/d/([^/]+)");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * Process staff profile data extracted from Excel
     * @param staffData Map of email to profile data (photo URL and departments)
     * Called as BGT
     */
    @Transactional
    public void processStaffProfileData(Map<String, Map<String, String>> staffData) {
        int processed = 0;
        int errors = 0;
        StringBuilder errorLog = new StringBuilder();

        try {
            for (Map.Entry<String, Map<String, String>> entry : staffData.entrySet()) {
                String email = entry.getKey();
                Map<String, String> data = entry.getValue();

                try {
                    // Find staff by email
                    List<OfficeStaff> staffs = officeStaffRepository.findByEmail(email);
                    if (staffs == null || staffs.isEmpty()) {
                        errorLog.append("Staff not found for email: ").append(email).append("\n");
                        errors++;
                        continue;
                    }

                    OfficeStaff staff = staffs.get(0);

                    // Process photo if URL is provided
                    String photoUrl = data.get("photoUrl");
                    if (photoUrl != null && !photoUrl.isEmpty()) {
                        try {
                            handleProfilePhoto(staff, photoUrl);
                        } catch (Exception e) {
                            errorLog.append("Error processing photo for ").append(email).append(": ").append(e.getMessage()).append("\n");
                            errors++;
                        }
                    }

                    // Process departments if provided
                    String departmentsStr = data.get("departments");
                    if (departmentsStr != null && !departmentsStr.isEmpty()) {
                        try {
                            handleDepartments(staff, departmentsStr);
                        } catch (Exception e) {
                            errorLog.append("Error processing departments for ").append(email).append(": ").append(e.getMessage()).append("\n");
                            errors++;
                        }
                    }

                    // Process zohoExactJobTitle if provided
                    String zohoExactJobTitle = data.get("zohoExactJobTitle");
                    if (zohoExactJobTitle != null && !zohoExactJobTitle.isEmpty()) {
                        staff.setZohoExactJobTitle(zohoExactJobTitle);
                    }

                    // Save updated staff record
                    officeStaffRepository.save(staff);
                    processed++;

                } catch (Exception e) {
                    errorLog.append("Error processing staff ").append(email).append(": ").append(e.getMessage()).append("\n");
                    errors++;
                }
            }
        } catch (Exception e) {
            errorLog.append("Fatal error: ").append(e.getMessage()).append("\n");
            e.printStackTrace();
        }

        // Send summary in an email
        String result = String.format("Processed %d staff records with %d errors.\nError log:\n%s", processed, errors, errorLog);
        DebugHelper.sendMail("<EMAIL>", result);
    }
}
