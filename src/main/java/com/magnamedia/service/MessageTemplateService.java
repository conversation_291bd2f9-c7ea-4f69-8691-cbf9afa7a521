package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.TemplateTranslationRepository;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.service.message.MessagingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR> <<EMAIL>>
 * Created at 5/10/2020
 */

@Service
public class MessageTemplateService {
    @Autowired
    private MessagingService messagingService;

    public static Properties getNoReplyMailProperties() {
        Properties properties = new Properties();
        properties.setProperty("host", "smtp.gmail.com");
        properties.setProperty("name", "Maids.cc");
        properties.setProperty("username", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_CORE_EMAIL_ADDRESS));
        properties.setProperty("password", Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_CORE_EMAIL_PASSWORD));
        properties.setProperty("port", "587");
        return properties;
    }

    public static String getMaidsCcSenderName() {
        return "Maids.cc";
    }

    public ArrayList<Template> getMessageTemplates() {

        Template template = createTemplate( "Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Sms", "", "Your salary of @salary_amount@ is waiting for you in your Al-Ansari account. If you want to collect it, ask the lady who does the attendance. Click on the following link to view your payslip @url@.", "" , true, true);
        Map<String , String> expressionParameters = new HashMap<>();
        Map<String , String> translations = new HashMap();
        translations.put("hi", "आपके अल-अंसारी खाते में @salary_amount@ का आपका वेतन आपका इंतजार कर रहा है। यदि आप इसे एकत्र करना चाहते हैं, तो उपस्थिति करने वाली महिला से पूछें। अपनी पेस्लिप @url@ देखने के लिए निम्न लिंक पर क्लिक करें।");
        translations.put("am", "በአል አንሳሪ ሂሳብ ቁጥርሽ ውስጥ ደምዝሽ @salary_amount@ እየጠበቀሽ ነው። ደሞዝሽ ን ማውጣት የምትፈልጊ ከሆነ አቴንዳንስ የምትወስደወን ልጅ እባክሽ ጠይቂያት። የደሞዝሽን ሪሲት ማየት የምትፈልጊ ከሆነ እባክሽ ይሄን ሊንክ እባክሽ ንኪው @url@");
        translations.put("az", "Miindaakee kan @salary_amount@ herreegakee is Al-Ansari kessattii sii eegaa jira.walittii qabachuu yoo barbaaddee giiftii ishee hirmaachistuu gaafadhuu liinkii armaan gadiittii bu'ii payslip kee ilaaluf @url@");
        translations.put("tl", "Ang iyong suweldo na @salary_amount@ ay naghihintay para sa iyo sa iyong Al-Ansari account. Kung nais mong kolektahin ito, tanungin ang babae na gumagawa ng pagdalo. Mag-click sa sumusunod na link upang tingnan ang iyong payslip @url@.");
        TemplateUtil.createTemplate(template, expressionParameters);
        template = TemplateUtil.getTemplate("Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Sms");

        if(template != null ) {
            TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            for (String lang : translations.keySet()) {
                PicklistItem item = Setup.getOrCreateItem(Picklist.TEMPLATE_LANGUAGES, lang);
                TemplateTranslation translation = translationRepository.findByLanguageAndTemplate(item, template);
                if (translation == null) {
                    translation = new TemplateTranslation();
                    translation.setTemplate(template);
                    translation.setLanguage(item);
                    translation.setTranslation(translations.get(lang));
                    translationRepository.save(translation);
                }
            }
        }


        template = createTemplate( "Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Sms", "", "Your salary of @salary_amount@ is waiting for you in your Al-Ansari account. Please click on the following link to know how to collect your salary and to view your payslip @url@", "" , true, true);
        expressionParameters = new HashMap<>();
        translations = new HashMap();
        translations.put("hi", "आपके अल-अंसारी खाते में @salary_amount@ का आपका वेतन आपका इंतजार कर रहा है। अपना वेतन कैसे प्राप्त करें और अपनी पेस्लिप @url@ देखने के लिए कृपया निम्नलिखित लिंक पर क्लिक करें");
        translations.put("am", "በአል አንሳሪ ሂሳብ ቁጥርሽ ውስጥ ደምዝሽ @salary_amount@ እየጠበቀሽ ነው። የደሞዝሽን ሪሲት ማየት የምትፈልጊ ከሆነ እባክሽ ይሄን ሊንክ እባክሽ ንኪው @url@");
        translations.put("az", "Miindaakee kan @salary_amount@ herregakee isa Al-Ansari kessattii sii eegaa jira.maaloo liinkii armaan gadii jiruttii bu'ii akka ittii miindaakee sassabatuu barruuf fi payslip kee ilaaluuf @url@");
        translations.put("tl", "Ang iyong suweldo na @salary_amount@ ay naghihintay para sa iyo sa iyong Al-Ansari account. Mangyaring i-click ang sumusunod na link upang malaman kung paano kokolektahin ang iyong suweldo at upang tingnan ang iyong payslip @url@");
        TemplateUtil.createTemplate(template, expressionParameters);
        template = TemplateUtil.getTemplate("Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Sms");

        if(template != null ) {
            TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            for (String lang : translations.keySet()) {
                PicklistItem item = Setup.getOrCreateItem(Picklist.TEMPLATE_LANGUAGES, lang);
                TemplateTranslation translation = translationRepository.findByLanguageAndTemplate(item, template);
                if (translation == null) {
                    translation = new TemplateTranslation();
                    translation.setTemplate(template);
                    translation.setLanguage(item);
                    translation.setTranslation(translations.get(lang));
                    translationRepository.save(translation);
                }
            }
        }

        // 16-  push Notification templates
        createSalaryTransferredNotificationTemplate();
        createSalaryOnHoldNotificationTemplate();
        createMvSalaryTransferredNotificationTemplate();
        createYayaInAccommodationMaidSalaryTransferredNotificationTemplate();
        createYayaWithClientMaidSalaryTransferredNotificationTemplate();
        createConfirmGoogleReviewNotification();
        createLateSalaryBecauseOfHolidayNotification();
        createMarkUnpaidSalaryApprovalTemplate();

        createNewSalaryExceptionTemplate();

        // PAY-1022
        createClientSalaryIsDelayedNotificationTemplate();

        createDuplicateIBanDetectionTemplate();

        createHousemaidVacationOverlapsNotificationTemplate();

        // PAY-PAY-1761
        createEditedPayrollRosterTemplate();

        //PAY-2094
        sendFinalPayrollExceptionFileToAuditorsTemplate();


        //PAY-1706
        createEmailTemplateWithSmsChannel("Salary_Rules_Report_Template", "Salary Rules checking Report",
                "Salary Rules checking Report attached kindly check it", null);

        // PAY-1597
        createZeroOrNegativeSalaryComponentTemplate();

        createMultipleProfilesEmailTemplate();

        createPaymentConfirmationTemplate();

        return new ArrayList() {
            {
                Template template = null;
                Map<String , String> expressionParameters = new HashMap<>();
                Map<String , String> translations = new HashMap<>();

                PicklistItem expertDelighters = Setup.getItem("template_skill", "Expert Delighters");
                PicklistItem maidsHelpLine = PicklistHelper.getItem("live_person_outbound_numbers", "971506715943");
                String salary_collection_month_ready_1 = "3007011859582647";
                String salary_collection_month_ready_2 = "549873936039138";
                String salary_collection_month_ready_1_am = "846395706280941";
                String salary_collection_month_ready_1_oro = "4976560825710552";
                String salary_collection_month_ready_2_am = "540599983783910";
                String salary_collection_month_ready_2_oro = "372337254223601";

                // PAY-47 Housemaid Payslips
                add(new Template("Filipino_Housemaid_Payslips", false, "Hello ate,\nYou can download this month's payslip in English @payslip_en@ or in Tagalog @payslip_tl@", "Sent once each month on payroll generation and sending"));
                add(new Template("Ethiopian_Housemaid_Payslips", false, "Hello,\nYou can download this month's payslip in English @payslip_en@, Amharic @payslip_amh@ or in Oromo @payslip_om@", "Sent once each month on payroll generation and sending"));
                add(new Template("Indian_Housemaid_Payslips", false, "Hello,\nYou can download this month's payslip in English @payslip_en@, Hindi @payslip_hi@, or in Malayalm @payslip_ml@", "Sent once each month on payroll generation and sending"));
                add(new Template("Payroll_New_Hire_Approval", false, "You hired @employee_name@ as @job_title@ for a salary of @salary@ starting @start_date@. Please click to approve or reject @url@", "ask for approval message that will be send to Final Manager when trustee saves information of new Hire in Hire Employee to-do"));
                add(new Template("Payroll_Update_OfficeStaff_Approval", false, "You hired @employee_name@ as @job_title@ for a salary of @salary@ starting @start_date@. Please click to approve or reject @url@", "ask for approval message that will be send to Final Manager when office staff's salary or starting date is updated"));
                add(new Template("Payroll_Salary_Raise_Approval", false, "@employee_name@ will receive a salary raise of @value@ @currency@ to monthly salary. Please click to approve or reject @url@", "Sent to final manager to approve an ADDITION manager note"));
                add(new Template("Payroll_Salary_Reduction_Approval", false, "@employee_name@ will receive a reduction of @value@ @currency@ to monthly salary. Please click to approve or reject @url@", "Sent to final manager to approve a REDUCTION manager note"));
                add(new Template("Payroll_Salary_Deduction_Approval", false, "@employee_name@ will receive a one-month deduction of @value@ @currency@. Please click to approve or reject @url@", "Sent to final manager to approve a DEDUCTION manager note"));
                add(new Template("Payroll_Bonus_Approval", false, "@employee_name@ will receives a one-month addition of @value@ @currency@. Please click to approve or reject @url@", "Sent to final manager to approve a BONUS manager note"));
                add(new Template("Payroll_Add_Loan_Approval", false, "A loan of amount @value@ is added to @employee_name@. Please click to approve or reject @url@", "ask for approval message that will be send to Final Manager when Trustee add new loan"));
                add(new Template("Payroll_Edit_Loan_Approval", false, "@employee_name@ loan edited from @old_value@ to @new_value@ with a repayment amount of @repayment_amount@. Please click to approve or reject @url@", "ask for approval message that will be send to Final Manager when trustee edit a loan"));
                add(new Template("Payroll_Change_Loan_repayments_Approval", false, "@employee_name@ loan repayment amount was edited from @old_value@ to @new_value@.\nPlease click to approve or reject @url@.", "ask for approval message that will be send to Final Manager when trustee edit a loan monthly repayment"));
                add(new Template("Payroll_Delete_Loan_Approval", false, "@employee_name@ loan of amount @loan_amount@ is deleted. Please click to approve or reject @url@", "ask for approval message that will be send to Final Manager when trustee delete a loan"));
                add(new Template("Payroll_OfficeStaff_Terminated", false, "Greetings, We want to notify you that @employee_name@ has left the company.", "Sent to all managers, final managers and colleagues of the terminated office staff"));
                add(new Template("Payroll_Overseas_OfficeStaff_Terminated", false, "Greetings, @employee_name@ is terminated. Please approve or edit their termination compensation. @url@", "Trustee terminates an overseas employee and determine their termination compensation"));
                add(new Template("Payroll_New_Hire_Notification", false, "@employee_name@ has just joined our company as @job_title@. Their manager is @direct_manager_name@. Email address: @employee_email_address@.", "After the final manager approves we will send a notification message to managers and colleagues"));
                add(new Template("Payroll_Accesses_Are_Revoked", false, "@employee_name@  accesses are revoked. You can now inform the employee that they are terminated. Please click here when done: @url@", "Sent to the manager who requested the termination after revoking the employees' accesses"));
                add(new Template("Payroll_Action_Taken_About_Compensation", false, "Please note that the termination compensation of amount @termination_compensation_amount@ that was requested for @employee_name@ was @action@ by @final_manager@.", "Sent to the managers of the employee requested for termination"));
                add(new Template("Payroll_Wait_Accesses_To_Be_Revoked", false, "Termination request is issued for @employee_name@. Do not inform them until accesses are revoked. Please wait until you receive revoke access confirmation.", "Sent to the managers who requested the termination to not tell their employees"));
                add(new Template("Payroll_Revoke_Access_Request_SMS", false, "Urgent! Revoke access now for @employee_name@. Please check your email for details.", "Revoke Access Request SMS"));
                add(new Template("Payroll_Edit_Loan_Repayment_Approval",false,"@employee_name@ loan repayment of @edited_month_date@ was edited from @old_value@ to @new_value@. Please click here to approve or reject: @url@.","ask for approval message that will be sent to Final Manager when trustee edit repayment of one month"));
                add(new Template("Payroll_Vacation_Added_Off_Days", false, "Please be informed that @employee_name@ will be on vacation from @vacation_start_date@ to @vacation_end_date@.", "Sent to manager and final manager when an employee take a vacation"));
                add(new Template("Payroll_Vacation_Added_With_Airfare_Ticket", false, "Please be informed that @employee_name@ will be on vacation from @vacation_start_date@ to @vacation_end_date@ and they got an airfare ticket allowance of amount @airfare_ticket_amount@.", "Sent to manager and final manager when an employee take a vacation with airfare ticket"));
                add(new Template("Payroll_Adding_Working_On_Weekly_Off_Day", false, "Please be informed that @employee_name@ was working on their weekly off date on @date_of_working_weekly_off_day@. They earned @number_of_earned_off_days@ off days and their new balance is @new_off_days_balance@.", "Sent to manager and final manager when an employee work on weekly day off"));
                add(new Template("Payroll_Adding_Working_On_Public_Holiday", false, "Please be informed that @employee_name@ was working on the public holiday of @holiday_name@ from @starting_date@ to @end_date@. They earned @number_of_earned_off_days@ off days and their new balance is @new_off_days_balance@.", "Sent to manager and final manager when an employee work on public holiday"));
                add(new Template("Payroll_Adding_Paid_Working_On_Public_Holiday", false, "Please be informed that @employee_name@ was working on the public holiday of @holiday_name@ from @starting_date@ to @end_date@. An amount of @paid_holiday_amount@ will be added to their Salary.", "Sent to manager and final manager when an employee work on public holiday and set as paid"));
                add(new Template("Payroll_Paid_Off_Days_For_Employee", false, "Please be informed that @employee_name@ will get paid @number_of_requested_days@ off days of amount @amount_of_paid_off_days@.", "Sent to final manager when an employee get paid off days"));
                add(new Template("Payroll_Vacation_Edit_Added_Off_Days", false, "Please be informed that the vacation of @employee_name@ was edited and the new dates are from @vacation_start_date@ to @vacation_end_date@.", "Sent to manager and final manager when an employee edit a vacation"));
                add(new Template("Payroll_Vacation_Edit_And_Removed_Airfare_Ticket", false, "Please be informed that the airfare ticket of @employee_name@ was deleted.", "Sent to manager and final manager when an employee edit a vacation and remove the airfare ticket"));
                add(new Template("Payroll_Vacation_Deleted", false, "Please be informed that the vacation of @employee_name@ that was scheduled from @vacation_start_date@ to @vacation_end_date@ was deleted.", "Sent to manager and final manager when an employee delete a vacation."));
                add(new Template("Payroll_Paid_Off_Days_Edited_For_Employee", false, "Please be informed that @employee_name@'s paid vacation was edited from @old_requested_days@ to @new_requested_paid_off@ days and the amount was updated from @previous_amount_of_paid_off_days@ to @new_amount_of_paid_off_days@.", "Sent to final manager when an employee edit paid off days"));
                add(new Template("Payroll_Paid_Off_Days_Deleted_For_Employee", false, "Please be informed that the paid off days that were added on @date_of_paid_off_days_addition@ for @employee_name@ are deleted.", "Sent to final manager when an employee delete paid off days"));
                add(new Template("Payroll_Sending_Transaction_Number",false,"Dear @receiver_name@, your salary was successfully transferred and is ready for collection. Your Transfer CE Number is: @ce_number@\n" +
                        "Thank you.", "Sending Transaction Number of the Transfers Done for Overseas Staff"));
                add(new Template("Payroll_Bank_Transfer_Completed", false, "Dear @receiver_name@, your salary @amount@ was successfully transferred to your bank account.\n" +
                        "Thank you.\n.", "Sent to the employee after completing his salary transfer (Bank transfer)"));
                add(new Template("Payroll_Confirm_Google_Review_By_Auditors",false,"Congratulations! Your clients are really happy with your work. We added AED @amount@ to your next salary as a thank you from maids.cc, Keep up the good work, we are proud of you!","Sent to housemaid when the auditor confirm the google review to tell her about the addition"));
                add(new Template("Payroll_Pending_Manager_Approval",false,"You have @count@ pending payroll changes to approve. Click @url@ to view the changes.","send to manager the amount of pending approval requests he has."));
                add(new Template("Payroll_OfficeStaffs_BirthDay_Reminder",false,"It's @employees_names@'s birthday today! We just wanted to remind you so you can send over a kind birthday wish.<br>Employee's contact info:<br>Email: @employee_email@<br>Phone number: @employee_phone_number@<br>Direct manager: @direct_manager@","send SMSs to managers on their employees' birthdays"));
                add(new Template("Payroll_OfficeStaffs_BirthDay_Reminder_sms",false,"It's @employees_names@'s birthday today! We just wanted to remind you so you can send over a kind birthday wish.\nEmployee's contact info:\nEmail: @employee_email@\nPhone number: @employee_phone_number@\nDirect manager: @direct_manager@","send SMSs to managers on their employees' birthdays"));

                // Templates for emails
                add(new Template("Payroll_Housemaids_With_No_EID_Report", false, "Please find attached file of @title@ ", "Sent Employees with no EID (Housemaids) Report"));

                //Final Settlement Templates
                add(new Template("Payroll_Final_Settlement_Of_OfficeStaff", false, "Hi, <br>@firstLastName@’s termination is requested. <br>Please click here to view the final settlement that is revised by the payroll manager: @url@ <br>", "Sent the final settlement for final manager in case of Emarati & Expat"));
                add(new Template("Payroll_Final_Settlement_Of_OfficeStaff_To_CFO", false, "Hi, <br> Please confirm the final settlement of @firstLastName@. <br> Note that this final settlement is revised by the payroll manager, and approved by @finalManagerName@ . <br> Click here to check the final settlement and the manager’s notes: @url@", "Sent the final settlement for CFO in case of Emarati & Expat"));
                add(new Template("Payroll_Final_Settlement_Of_OfficeStaff_To_CFO_Directly", false, "Hi,<br>@firstLastName@’s termination is requested. <br>Please click here to view their final settlement. <br>Note that this final settlement is reviewed by the payroll manager: @url@", "Sent the final settlement for CFO in case of Emarati & Expat without entering the final manager notes"));
                add(new Template("Payroll_Updated_Final_Settlement_Of_OfficeStaff", false, "The payroll manager updated the final settlement of @firstLastName@ based on your request. Please click here to check it @addInCaseFM@: @url@", "Sent the updated final settlement from the Payroll Manger to final manager or CFO in case of Emarati & Expat"));
                add(new Template("CFO_Rejected_Final_Settlement_Of_OfficeStaff",false,"Please note that @CFOName@ has rejected the final settlement for @firstLastName@ <br>CFO's Notes: @noteFromCFO@. <br>Thank you.","Sent rejection notes from the CFO to the Payroll Manager and Final Manager"));
                add(new Template("CFO_Ask_User_About_Final_Settlement_Of_OfficeStaff",false,"@CFOName@ is questioning about @OfficeStaffTermination@. <br>Please click @urlQuestion@ to view his question and further info.","Send when the CFO wants to ask a question about the Office Staff Termination"));
                add(new Template("User_Answer_About_Final_Settlement_Of_OfficeStaff",false,"The question : @question@ <br>The answer : @answer@. <br>Please click @url@ to view details about this final settlement request.","Sent The Answer Related To Question is Quested from CFO"));

                add(new Template("Payroll_Send_Change_Bank_Details_Email_To_Ansari", false, "Hello, please change the salary destination of Al Mustaqeem Domestic Workers Services employee @employeeName@ as per the attached request letter", "Send Change Bank Details Email To Ansari"));
                add(new Template("Payroll_Send_Grant_Access_Request", false, "Please grant access to @accessName@ .<br> Click here @url@ to provide the employee with information on how to access.<br> The manager noted the following:<br> @notes@", "Send Grant Access Request to Access Handler"));
                add(new Template("Payroll_Send_Revoke_Access_Request", false, "Please revoke access of @firstLastName@ to @accessName@ Click here @url@ to confirm that the access is revoked and the manager is informed.<br>Thank you,", "Send Revoke Access Request to Admins"));
                add(new Template("Payroll_Send_Inform_Add_Access_Email", false, "You now can access @accessName@.\n @notes@", "Inform the employee of newly Added Access"));
                add(new Template("Payroll_Send_Inform_Revoke_Access_Email", false, "Please note that the following accesses are revoked for the following employees:<br> @accessName@ <br>Thank you,", "Inform the employee of Revoke Access"));
                add(new Template("Payroll_Audit_TODO_Notification", false, "Hi,\n@todoLabel@ to-do is created.\nPlease check the auditors screen @url@ \n", "Inform the auditor when a new audit todo is created"));
                add(new Template("Payroll_Excluded_MV_Audit_TODO_Notification", false, "Please go to Auditor Screen and check the excluded MV maids with received payments to take actions ASAP. @url@.", "Inform the auditor when a Excluded MV audit todo is created"));
                add(new Template("Payroll_Payment_TODO_Notification", false, "Hi,\n@todoLabel@ to-do is created.\nPlease check the accountant screen @url@ \n", "Inform the accountants when a new accountant todo is created"));
                add(new Template("Payroll_Payroll_Manager_TODO_Notification", false, "Hi,\n@todoLabel@ to-do is created.\nPlease check the payroll manager screen @url@ \n", "Inform the trustees when a new payroll manager todo is created"));
                add(new Template("Payroll_Send_New_Hire_Questionnaire", false, "Greetings from Maids.cc, \n\nPlease fill the below questionnaire to help us transfer your salary.\n\nThanks in advance.\n @url@", "send a questionnaire url to new office staff"));
                add(new Template("Payroll_Send_Payroll_Roster_File", false, "Hi @firstLastName@,Please check the payroll roster of your employees. Please click on this link to approve or send it back for the payroll manager to apply any changes: @url@", "send payroll roster file to approve it"));
                add(new Template("Payroll_Send_Payroll_Roster_File_Trustee_Reminder", false, "@managerName@ didn't react to his roster, please check with him and let him take an action ASAP.", "send reminder email to payroll trustee to check with the manager"));
                add(new Template("Payroll_Send_Payroll_Roster_File_Payroll_Manager_Reminder", false, "Note that the following managers didn't approve their rosters yet: <br>@managers_names@", "send reminder email to payroll manager informing him about pending rosters"));
                add(new Template("Payroll_Salary_Transfer_Notification", false, "Dear @employeeName@, your salary @salary@ was successfully transferred to your bank account.", "send salary transfer notification to employee"));
                add(new Template("Payroll_Bank_Transfer_Report", false, " Kindly find the attached file containing all the bank transfers that were done to the overseas staff.", "Send a Report Once the Bank Transfer To-do is Closed"));
                add(new Template("Payroll_Request_Trustee_Approval_Notification",false,"Please note that @manager_name@ has approved the @request_type@ of @employee_name@ that you requested on @request_date@. <br><br>You can now check their profile @link_of_tab@.","Send notification to trustee when a manager approve any request"));
                add(new Template("Payroll_Request_Trustee_Rejection_Notification",false,"Please note that @manager_name@ has rejected the @request_type@ of @employee_name@ that you requested on @request_date@ .<br><br>If you want to add another one please click here: @link_of_tab@.<br><br>The manager noted the following: @notes@.","Send notification to trustee when a manager reject any request"));
                add(new Template("Payroll_Payment_Approve", false, "Good day,\nKindly find the attached payroll files for the month of @payroll_month@, and please confirm using this link @url@ to proceed with the payment process", "Request the approval of payroll files before proceeding with the payment process"));
                add(new Template("Payroll_Payment_Approve_For_Bank_Transfer", false, "Good day,\nKindly find the attached bank transfer payroll files for the month of @payroll_month@, and please confirm using this link @url@ to proceed with the payment process.", "Request the approval of Bank Transfer files before proceeding with the payment process"));
                add(new Template("Payroll_Send_Final_Payroll_Files_To_Payroll_Auditors", false, "Please check the final payroll files of @payroll_month_and_year@", "send the final payroll files to payroll auditors after CFO approval."));
                add(new Template("Payroll_Delete_Access_Email_Template", false, "Please note that @access_name@ is deleted. You can't use your access after now.", "when deleting External Access, inform the employee who has this access that it's now deleted"));
                add(new Template("Manager_Granting_Access_Template", false, "Note that @employee_name@ is granted access for @access_name@.", "inform the manager who requested the access that the access was granted to the employee"));
                add(new Template("Payroll_Audit_Todo_Reminder_Email_Template", false, "Please go to @open_audit_todo_link@ and close your to-dos. Note that you passed the cutoff time for closing your to-dos which was on 6:00 PM", "send audit todo reminder one day before the payment date if not closed yet"));
                add(new Template("Payroll_Accountant_Todo_Reminder_Email_Template", false, "The to-do of transferring money to Ansari for the payroll month of @payroll_month@ is still open. Please go to this page @link@ and attach the proof of transfer to close the to-do.", "send accountant todo reminder one day before the payment date if not closed yet"));
                add(new Template("Payroll_Authorize_Payroll_Transfer_Template", false, "Good day, <br>Please authorize the following transfer, and kindly reply to this email once it's done.", "send authorization email of proof of transfer file"));
                add(new Template("Payroll_Payroll_Generation_Started_Template", false, "Please note that the process of payroll generation of @payroll_month@ has just started.", "send email once the payroll generation is started"));
                add(new Template("Payroll_Payroll_Generation_Finished_Template", false, "Please note that the process of payroll generation of @payroll_month@ has just finished.", "send email once the payroll generation iis finished"));
                add(new Template("Syria_Transfers_Exchange_Rate", false, "Hello @employee_name@,<br>" +
                        "We'd just like to give you a heads-up that the exchange rate of your salary transfer processor (Ansari) as of today is @syrian_rate@ SYP (1 USD = @syrian_rate@ SYP)<br>" +
                        "<br>" +
                        "If you'd like to change your salary transfer method, please let us know using the bot, Just search Telegram for BAteamBot, chat with us there, select the option \"Payroll and Recruitment\", then the option \"Change Salary Destination\"<br>" +
                        "<br>" +
                        "If you wish to proceed with your salary transfer through Ansari, or if you have already informed us of an alternative payment method, then there's no action needed from your side here.<br>" +
                        "<br>" +
                        "Thank You!", "Email Campaign for Syrian Transfers Related to Exchange Rate"));

                add(new Template("Ansari_Syria_Exchange_Rate", false, "Hello," +
                        "<br>" +
                        "Please provide us with the updated rate for international transfers to Syria.<br>" +
                        "<br>" +
                        "The rate of USD TO AED<br>" +
                        "The rate of AED TO SYP<br>" +
                        "<br>" +
                        "Thank you", "Ask Ansari to send Syria transfers exchange rate"));

                add(new Template("Payroll_Approve_Money_File_By_CFO", false, "Dear CFO,<br><br>Please confirm the @file_type@ file sent to @money_transfer_center@ of amount @currency_with_total_amount@ for the accountant to transfer the money: @link@", "ask the CFO to approve the payment file before sending it to Ansari"));

                add(new Template("Payroll_Manager_Note_Taxi_Reimbursement_Reason_SMS_Template", false,
                        "Hello @maid_name@, we will give you back AED @amount@ because you paid for your taxi. " +
                                "We will add AED @amount@ to your account on @next_payroll_date@.",
                        "When payroll managet note created with an addition of type Taxi reimbursement"));
                add(new Template("Payroll_Question_About_Maid_Salary", false,
                        "Maid's name: @maid_name@<br>" +
                                "Maid's salary: @amount@<br>" +
                                "Maid's Nationality: @nationality@<br>" +
                                "Maid's Start date: @start_date@<br>" +
                                "Active client: @client_name@<br>" +
                                "Auditor's Question: @auditor_notes@<br>" +
                                "Click the link to enter your answer. @url@",
                        "Question About Maid Salary"));

                add(new Template("Payroll_Exclude_Maid_Manually_From_Profile", false,
                "Good day, <br><br>Please note that @maid_name@ was excluded from payroll by @user_name@, and they noted the following: @user_notes@.",
                        "send email to manager when excluding maid from her profile"));

                add(new Template("Payroll_CC_Maids_Excluded_Manually", false,
                "Please check the list of CC maids excluded from the payroll month of @payroll_month@, and enter your notes next to all of them. In case you want to include any maid, please unexclude her. Note that your actions are not valid after @lock_time_at_last_audit_day@. Click here to take actions: @url@",
                        "send email to cc manager with excluded cc maids on lock date"));

                add(new Template("Payroll_MV_Maids_Excluded_Manually", false,
                "Please check the list of MV maids excluded from the payroll month of @payroll_month@, and enter your notes next to all of them. In case you want to include any maid, please unexclude her. Note that your actions are not valid after @lock_time_at_last_audit_day@. Click here to take actions: @url@",
                        "send email to MV manager with excluded cc maids on lock date"));

                add(new Template("Payroll_Maids_Included_By_Manager_Todo_Email", false,
                        "Good day, <br><br>Please visit your audit to-do and check the maids that @manager_name@ decided to include in payroll. @url@",
                        "send email to auditor to inform him about this todo"));
                add(new Template("Payroll_Requested_For_Termination_Employees_Email", false,
                        "Please check the employees who were requested for termination today:@staffs_details@",
                        "send email to Adeeb to inform him about all terminated staffs"));

                add(new Template("Payroll_Fix_MV_Contract_Email", false,
                        "Maid's contract needs to be fixed due to an issue with her salary:<br>" +
                                "- Contract ID: @contract_id@<br>" +
                                "- Current MOHRE salary + Holiday: @amount@<br>" +
                                "- Newly entered salary: @worker_salary@<br><br>" +
                                "Thanks",
                        "send email to Wesam to fix the new created MV contract of a maid"));
                add(new Template("Payroll_Hire_New_Employee_Todo_Deleted_Template", false ,
                        "Hello <br>" +
                                "The payroll manager reversed the onboarding process for @employee_name@, <br>" +
                                "check the payroll manager note below.<br>" +
                                "@note@<br>" +
                                "Jazz HR employee profile: @link@.",
                        "it send to recruitment manager after deletion office staff todo with type Hire employee"));

                add(new Template("Payroll_After_Office_Staff_Starting_Date_Is_Updated", false,
                        "Please note that the joining date of @employee_name@ has changed from @old_date@ to @new_date@.",
                        "Send an email to the direct manager after office staff starting date is updated"));

                add(new Template("Payroll_Before_Primary_Payroll_Audit_Template", false,
                        "Please don’t deploy anything to Production tonight, and postpone any scheduled deployments to tomorrow",
                        "a reminder before primary payrolls to take care of our deployments"));
                add(new Template("Payroll_Before_Secondary_Payroll_Audit_Template", false,
                        "Please note that we have a Payroll Generation tonight at @audit_todo_time@, it’s not the primary one, so just make sure that the Payroll is generated before processing any deployment",
                        "a reminder before secondary payrolls to take care of our deployments"));


                template = createTemplate("Payroll_Pay_Loan_Repayment_Approval", "", "Please approve the loan repayment of @month@ of amount @currency@ @amount@ that is requested by @user@ on the date of @request_date@. The current loan balance is @loan_balance_before_repayment@.<br>@url@ ", "send email to approve pay loan payment",false, true);
                template.setMethod(Setup.getItem("template_methods", "e-mail"));
                TemplateUtil.createTemplate(template, new HashMap<>());

                template = createTemplate("Payroll_WhatsApp_Inform_InAccommodation_Maid_About_Salary_EN", "", "Hello! Your salary of @salary_amount@ is waiting for you in your Al-Ansari account. If you want to collect it, ask the lady who does the attendance. You can view your payslips in your Yayabot.", "" , true, true, salary_collection_month_ready_1, "Inform Maids about Salaries", maidsHelpLine, expertDelighters);
                TemplateUtil.createTemplate(template, new HashMap<>());
                template = createTemplate("Payroll_WhatsApp_Inform_InAccommodation_Maid_About_Salary_AMH", "", "ሰላም፤ ደሞዝሽ @salary_amount@ በአል አንሳሪ ሂሳብሽ ውስጥ ይገኛል፡፡ ለማውጣት ከፈለግሽ አቴንዳንስ የምትጠራው ልጅ አናግሪ፡፡ የደሞዝሽን ደረሰኝ ለማየት ያያቦት ውስጥ መመልከት ትችያለሽ፡፡", "" , true, true, salary_collection_month_ready_1_am, "Inform Maids about Salaries", maidsHelpLine, expertDelighters);
                TemplateUtil.createTemplate(template, new HashMap<>());
                template = createTemplate("Payroll_WhatsApp_Inform_InAccommodation_Maid_About_Salary_OM", "", "Hello! Miindaan ke @salary_amount@ herrega Al- Ansari kessati si eegati jira. Yoo fudhachu barbade, wardiyaa maqa wamtuti himi. Waraqaa kafalti keti yayaboti irrati lalu nidandesa.", "" , true, true, salary_collection_month_ready_1_oro, "Inform Maids about Salaries", maidsHelpLine, expertDelighters);
                TemplateUtil.createTemplate(template, new HashMap<>());

                template = createTemplate( "Payroll_WhatsApp_Inform_WithClient_Maid_About_Salary_EN", "", "Hello! Your salary of @salary_amount@ is waiting for you in your Al-Ansari account. If you want to collect it, use your ATM machine or physically visit Al-Ansari. You can view your payslips in your Yayabot.", "" , true, true, salary_collection_month_ready_2, "Inform Maids about Salaries", maidsHelpLine, expertDelighters);
                TemplateUtil.createTemplate(template, new HashMap<>());
                template = createTemplate( "Payroll_WhatsApp_Inform_WithClient_Maid_About_Salary_AMH", "", "ሰላም፤ ደሞዝሽ @salary_amount@ በአል አንሳሪ ሂሳብሽ ውስጥ ይገኛል፡፡ ለማውጣት ከፈለግሽ ኤቲኤም ማሽን ይለበት ቦታ ወይም ከባንኩ መውሰድ ትችያለሽ፡፡ የደሞዝሽን ደረሰኝ ለማየት ያያቦት ውስጥ መመልከት ትችያለሽ፡፡", "" , true, true, salary_collection_month_ready_2_am, "Inform Maids about Salaries", maidsHelpLine, expertDelighters);
                TemplateUtil.createTemplate(template, new HashMap<>());
                template = createTemplate( "Payroll_WhatsApp_Inform_WithClient_Maid_About_Salary_OM", "", "Hello! Miindaan ke @salary_amount@ herrega Al- Ansari kessati si eegati jira. Yoo fudhachu barbade, ATM ke faayadaami ykn Al- Ansari deemi. Waraqaa kafalti keti yayaboti irrati lalu nidandesa.", "" , true, true, salary_collection_month_ready_2_oro, "Inform Maids about Salaries", maidsHelpLine, expertDelighters);
                TemplateUtil.createTemplate(template, new HashMap<>());

                // CC App templates
                String housemaidFirstNameParameterName= "maid_first_name";
                String housemaidFirstNameParameterExpression= "housemaid!=null && !status.toString().equalsIgnoreCase(\"CANCELLED\") && !status.toString().equalsIgnoreCase(\"EXPIRED\") ?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getFirstName(housemaid.id):\"My maid\"";
                String housemaidSpecialFirstNameParameterExpression= "housemaid!=null && !status.toString().equalsIgnoreCase(\"CANCELLED\") && !status.toString().equalsIgnoreCase(\"EXPIRED\") ?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getSpecialFirstName(housemaid.id):\"My maid\"";
                String ansariLocationParameterName= "ansariLocation";
                String ansariOrMushrifMallAuhLocationParameterName= "ansariOrMushrifMallAuhLocation";
                String ansariOrMushrifMallAuhNameParamaterName = "ansariOrMushrifMallAuhName";
                String ansariLocationParameterExpression= "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getAnsariLocation()";
                String ansariOrMushrifMallAuhLocationParameterExpression= "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getAnsariOrMushrifMallAuhLocation(housemaid.id)";
                String ansariOrMushrifMallAuhNameExpression= "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getAnsariOrMushrifMallAuhName(housemaid.id)";
                String ansariBranchParameterName= "ansariBranch";
                String ansariBranchParameterExpression= "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getAnsariBranch()";


                // 1- Done
                template = createTemplate( "@received_salary_date@", "", "@maid_first_name@ usually receives her salary on the @release_primary_salary_day@ of every month. She'll receive her next salary on @release_primary_salary_date@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("release_primary_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDate()");
                TemplateUtil.createTemplate(template, expressionParameters);


                // 2- Done
                template = createTemplate( "@received_salary_notification@", "", "Notify me every time you send @maid_first_name@ her salary", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidSpecialFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 3- Done
                template = createTemplate( "@atm_card_ready@", "", "@maid_first_name@ needs her ATM card to withdraw her\n" +
                        "salary. Her ATM card is ready to be picked up.\n" +
                        "She'll just have to visit Al-Ansari's main branch to pick it up. Please read the instructions below for more information.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 4- Done
                template = createTemplate( "@received_salary_branch_instructions@", "", "<p>@maid_first_name@ needs to visit Al-Ansari's main branch to collect her salary. She'll need to print," +
                        " and show <span><a  href =\"http://maids.cc\">this document</a></span> at the branch. While she's at the branch, please let her ask Al-Ansari if her ATM card is ready. Once she gets her ATM card, she can collect her salary from any ATM machine without visiting Al-Ansari.</p>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

//                // 5- Done
                template = createTemplate( "@salary_on_hold@", "", "Usually, @maid_first_name@ receives her salary by the @release_primary_salary_day@ of the month. But because she was @on_hold_status@, she'll receive her salary by the @next_payment_day@ of this month.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("on_hold_status" , "housemaid!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").onHoldStatus(housemaid.id):\"\"");
                expressionParameters.put("next_payment_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").nextPaymentDay()");
                TemplateUtil.createTemplate(template, expressionParameters);

                // 6- Done
                template = createTemplate( "@send_money_has_ATM_intro@", "", "@maid_first_name@ can send money to her country through the Ansari WebApp. Please watch the video below for more information.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 7- Done
                template = createTemplate( "@send_money_NO_ATM_intro@", "", "If @maid_first_name@ already has her ATM card, she can send money to her country through the Ansari WebApp. Please watch the video below for step by step instructions on how she can send money through her Ansari WebApp.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 8- Done
                template = createTemplate( "@send_money_Collect_ATM_intro@", "", "@maid_first_name@'s ATM card is ready for pick up. She needs to visit Anasri's main branch with her Emirates ID to pick it up. She can then use the ATM card to withdraw her salary from any ATM machine. Please watch the video below or read the instructions for more info. ", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 9- Done
                template = createTemplate( "@salary_maid_has_deduction@", "", "@maid_first_name@ usually receives her salary on the @release_primary_salary_day@ of every month.\nPlease ask your maid to contact her team on her maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber() ");
                TemplateUtil.createTemplate(template, expressionParameters);

                // 10- Done
                template = createTemplate( "@salary_maid_has_loan@", "", "@maid_first_name@ usually receives her salary on the @release_primary_salary_day@ of every month.\n@maid_first_name@'s salary is AED @salary@. She received AED @partial_salary@ on her last salary because she borrowed money from us which she is paying back in monthly installments. We've deducted AED @loan@ this month, and the rest will be deducted from her future salaries." +
                        "If she thinks there's a mistake with her salary or has any questions, please ask her to contact her team on her maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("salary" , "housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHousemaidTotalSalaryPlusLoan(housemaid.id)):\"0\"");
                expressionParameters.put("partial_salary" , "housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHousemaidTotalSalary(housemaid.id)):\"0\"");
                expressionParameters.put("loan" , "housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHousemaidLoan(housemaid.id)):\"0\"");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber()");

                TemplateUtil.createTemplate(template, expressionParameters);

                // 11- Done
                template = createTemplate( "@salary_maid_received_full_salary@", "", "@maid_first_name@ usually receives her salary on the @release_primary_salary_day@ of every month.\nAccording to our records, @maid_first_name@'s salary is @salary@, and she should've received @salary@ this month as well.  \n" +
                        "If she thinks there's a mistake with her salary or has any questions, please ask her to contact her team on her maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("salary" , "housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHousemaidTotalSalary(housemaid.id)):\"0\"");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber()");
                TemplateUtil.createTemplate(template, expressionParameters);

                // 12- Done
                template = createTemplate( "@salary_maid_received_partial_salary@", "", "@maid_first_name@ usually receives her salary on the @release_primary_salary_day@ of every month.\n@maid_first_name@'s salary is AED @salary@. She only received AED @partial_salary@ on her last salary because she joined us on @maid_start_date@. So, we've paid her for this period only. \n" +
                        "If she thinks there's a mistake with her salary or has any questions, please ask her to contact her team on her maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("salary" , "housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHousemaidTotalSalaryPlusStartDateDeduction(housemaid.id)):\"0\"");
                expressionParameters.put("partial_salary" , "housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHousemaidTotalSalary(housemaid.id)):\"0\"");
                expressionParameters.put("maid_start_date" , " housemaid!=null?(T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").formatDate(housemaid.startDate)):\"\"");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber()");
                TemplateUtil.createTemplate(template, expressionParameters);

                //13- Done
                template = createTemplate( "@why_salary_was_on_hold@", "", "@maid_first_name@ has been @on_hold_status@ when salaries were out that's why she didn't receive her salary on time. Don't worry, she can collect her salary after the @next_payment_day@ of this month.\n" +
                        "If she thinks there's a mistake with her salary or has any questions, please ask her to contact her team on her maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("on_hold_status" , "housemaid!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").onHoldStatus(housemaid.id):\"\"");
                expressionParameters.put("next_payment_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").nextPaymentDay()");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber()");
                TemplateUtil.createTemplate(template, expressionParameters);

                // 14- Done
                template = createTemplate( "@salary_maid_joined_26@", "", "@maid_first_name@ joined our payroll after the 26th of @current_month_name@. So we will add these days to her @next_month_name@ salary. She'll receive it by the @release_primary_salary_day@ of @next_month@.\n" +
                        "If she thinks there's a mistake with her salary or has any questions, please ask her to contact her team on her maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber()");
                expressionParameters.put("next_month" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getNextMonth()");
                expressionParameters.put("current_month_name" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getCurrentMonthName()");
                expressionParameters.put("next_month_name" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getNextMonthName()");
                TemplateUtil.createTemplate(template, expressionParameters);
                
                // 15- Done
                template = createTemplate( "@salary_maid_new_maid@", "", "@maid_first_name@ usually receives her salary on the @release_primary_salary_day@ of every month.\nYour maid didn't receive a salary last month because she recently joined us. She'll receive her next salary on @next_release_primary_salary_date@. If your maid has any questions, please ask her to contact her team on the maids' app called \"yayabot\", they'd be happy to assist her. If she doesn't have the app or doesn't know how to use it, please ask her to WhatsApp her team at this number: @maid_team_number@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put("release_primary_salary_day" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getReleasePrimarySalaryDay()");
                expressionParameters.put("maid_team_number" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getHouseMaidTeamNumber()");
                expressionParameters.put("next_release_primary_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getNextReleasePrimarySalaryDate()");

                TemplateUtil.createTemplate(template, expressionParameters);

                // CMA-2637 new templates regarding ATM card status
                // 16 - receiveSalaryCase1
                template = createTemplate( "@has_atm_case1@", "", "@maid_first_name@ can withdraw her salary from any ATM machine using her ATM card. First Abu Dhabi (FAB) machines are free, all other machines cost AED 2 per transaction.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 17 - receiveSalaryCase2
                template = createTemplate( "@has_eid_atm_ready_case2@", "", "@maid_first_name@'s ATM card is ready for pickup and she needs it so she can collect her salary. @maid_first_name@ has to go to <a href =\"@ansariLocation@\" style=\"text-decoration: none;color:#4267b2;\">Al-Ansari Lulu branch</a> with her Emirates ID to get her ATM card.\n\n" +
                        "After she gets her ATM card, she can use any ATM machine to withdraw her salary. First Abu Dhabi (FAB) machines are free, all other machines cost AED 2 per transaction. @sentence_maid_in_abu_dhabi@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put(ansariLocationParameterName , ansariLocationParameterExpression);
                expressionParameters.put("sentence_maid_in_abu_dhabi","T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getSentenceMaidInAbuDhabi(id)");
                TemplateUtil.createTemplate(template, expressionParameters);

                // 18 - receiveSalaryCase3
                template = createTemplate( "@has_eid_atm_not_ready_case3@", "", "To collect her salary, @maid_first_name@ needs to visit any <a href =\"@ansariBranch@\" style=\"text-decoration: none;color:#4267b2;\">Al-Ansari Branch</a> with her Emirates ID.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put(ansariBranchParameterName , ansariBranchParameterExpression);
                TemplateUtil.createTemplate(template, expressionParameters);

                // 19 - receiveSalaryCase4
                template = createTemplate( "@no_eid_atm_ready_case4@", "", "@maid_first_name@ needs her ATM card so she can collect her salary. She has to go <a href =\"@ansariOrMushrifMallAuhLocation@\" style=\"text-decoration: none;color:#4267b2;\">to @ansariOrMushrifMallAuhName@</a> with a copy of her passport with the company stamp, which we gave her when she joined us. <strong>Her MOL number is @housemaid_mol_number@</strong>. If @maid_first_name@ doesn't have a copy of her passport, please let her use this <a href=\"document://maid_doc\" style=\"text-decoration: none;color:#4267b2;\">copy of the document</a> to get her ATM card." +
                        "After she gets her ATM card, she can use any ATM machine to withdraw her salary. First Abu Dhabi (FAB) machines are free, all other machines cost AED 2 per transaction. @sentence_maid_in_abu_dhabi@", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put(ansariOrMushrifMallAuhLocationParameterName , ansariOrMushrifMallAuhLocationParameterExpression);
                expressionParameters.put(ansariOrMushrifMallAuhNameParamaterName , ansariOrMushrifMallAuhNameExpression);
                expressionParameters.put("housemaid_mol_number" , "housemaid!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getMolNumber(housemaid.id):\"\"");
                expressionParameters.put("sentence_maid_in_abu_dhabi","T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getSentenceMaidInAbuDhabi(id)");
                TemplateUtil.createTemplate(template, expressionParameters);

                // 20 - receiveSalaryCase5
                template = createTemplate( "@no_eid_atm_not_ready_case5@", "", "To collect her salary, @maid_first_name@ needs to visit any <a href =\"@ansariBranch@\" style=\"text-decoration: none;color:#4267b2;\">Al-Ansari branch</a> with a copy of her passport with the company stamp, which we gave her when she first joined us. <strong>Her MOL number is @housemaid_mol_number@</strong>. If @maid_first_name@ doesn't have a copy of her passport, please let her use this <a href=\"document://maid_doc\" style=\"text-decoration: none;color:#4267b2;\">copy of the document</a>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put(housemaidFirstNameParameterName , housemaidFirstNameParameterExpression);
                expressionParameters.put(ansariBranchParameterName , ansariBranchParameterExpression);
                expressionParameters.put("housemaid_mol_number" , "housemaid!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getMolNumber(housemaid.id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                // CMA-3271
                // 1 -Client signs today
                String getGeneralParameterValue = "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"ccAppContentHelper\").getGeneralParameterValue(id,\"@param@\")";
                template = createTemplate( "@1FirstSectionTemplate@", "", "Please pay @maid_name@'s salary for @contract_month@ directly to her. Starting from her @next_contract_month@ salary onward, we'll transfer AED @worker_salary@ to her by the 5th of every month. She should receive the first salary from us by the 5th of @contract_month+2months@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name", replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("contract_month" , replaceParamWithName(getGeneralParameterValue,"contract_month"));
                expressionParameters.put("next_contract_month" , replaceParamWithName(getGeneralParameterValue,"next_contract_month"));
                expressionParameters.put("worker_salary" , replaceParamWithName(getGeneralParameterValue,"worker_salary"));
                expressionParameters.put("contract_month+2months", replaceParamWithName(getGeneralParameterValue,"contract_month+2months"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 2 -"Contract started last month
                //AND
                //Today is the 5th day of contract month + 1"
                template = createTemplate( "@2FirstSectionTemplate@", "", "@maid_name@ will receive her first salary from us by @upcoming_primary_payroll+2days@, We'll notify you when her first salary is ready for her to collect.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                expressionParameters.put("next_month" ,replaceParamWithName(getGeneralParameterValue,"next_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 3- "On the 1st of the month
                //IF
                //this month is contract month + 2 months (maid's first salary)
                //AND
                //Payment of the month is not updated yet"
                template = createTemplate( "@3FirstSectionTemplate@", "", "@maid_name@ will receive her first salary from us by @special_upcoming_primary_payroll+2days@. We'll notify you when her first salary is ready for her to collect.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("special_upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"special_upcoming_primary_payroll+2days"));
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 4- "On the 1st of the month
                //IF
                //Contract start date is not in the previous 2 months"
                template = createTemplate( "@4FirstSectionTemplate@", "", "@maid_name@ will receive her salary from us by @special_upcoming_primary_payroll+2days@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("special_upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"special_upcoming_primary_payroll+2days"));
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 5 -"Monthly payment status changes to RECEIVED before the primary payroll is paid
                //AND
                //Contract start date is not this month nor the month before "
                template = createTemplate( "@5FirstSectionTemplate@", "", "We've received your monthly payment for @this_month@. @maid_name@ will receive her salary by @upcoming_primary_payroll+2days@. We'll notify @her_you@ salary is ready for her to collect.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                expressionParameters.put("her_you" ,replaceParamWithName(getGeneralParameterValue,"her_you"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 6 -"""Monthly payment status changes to BOUNCED before the primary payroll is paid
                //AND
                //Contract start date is not this month nor the month before """
                template = createTemplate( "@6FirstSectionTemplate@", "", "We still didn't send @maid_name@ her salary because you haven't settled your @this_month@ payment yet. She'll receive her salary by @upcoming_secondary_payroll+1day@ if we receive your monthly payment before then.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("upcoming_secondary_payroll+1day" ,replaceParamWithName(getGeneralParameterValue,"upcoming_secondary_payroll+1day"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 7 -"Today greater than or equal last secondary day
                //AND
                //bounced monthly payment is still not replaced
                //AND
                //Contract start date is not this month nor the month before"
                template = createTemplate( "@7FirstSectionTemplate@", "", "We still didn't send @maid_name@ her salary because your @this_month@ payment is not settled yet. If we receive this month's payment before the end of the month, @maid_name@ will receive her salary for the last month along with her salary for this month by @upcoming_primary_payroll+2days@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 8 -"Send all payslips is clicked after primary payroll is paid
                //AND
                //Client's monthly payment of this month is RECEIVED (or you can depend on if the maid has her salary transferred)
                //AND
                //Contract start date is not this month nor the month before
                //"
                template = createTemplate( "@8FirstSectionTemplate@", "", "We've sent @maid_name@ her salary on @payroll_transfer_date@. She'll receive her next salary by @upcoming_primary_payroll+2days@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("payroll_transfer_date" ,replaceParamWithName(getGeneralParameterValue,"payroll_transfer_date"));
                expressionParameters.put("upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                expressionParameters.put("next_month" ,replaceParamWithName(getGeneralParameterValue,"next_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 9 -"Bounced monthly payment gets REPLACED
                //AND
                //The last secondary payroll of the previous month is still NOT paid (before the audit to-do related to last secondary payroll is closed)
                //AND
                //Contract start date is not this month nor the month before.
                //"
                template = createTemplate( "@9FirstSectionTemplate@", "", "We've received your @this_month@ payment. @maid_name@ will receive her salary by @upcoming_secondary_payroll+1day@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_secondary_payroll+1day" ,replaceParamWithName(getGeneralParameterValue,"upcoming_secondary_payroll+1day"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 11 -"Bounced payment gets REPLACED
                //AFTER the last secondary payroll is paid
                //AND today is less than the beginning of next month
                //"
                template = createTemplate( "@11FirstSectionTemplate@", "", "We've received your @this_month@ payment so @maid_name@ will receive her salary for last month along with her salary for this month by @upcoming_primary_payroll+2days@. If we don't receive your payment on the 1st of @next_month@, @maid_name@ will receive only one salary.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("this_month" ,replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                expressionParameters.put("next_month" ,replaceParamWithName(getGeneralParameterValue,"next_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                // 12 -"On the 1st of the month
                //IF
                //This month is greater than contract start date month + 2
                //AND
                //There is a previously unpaid salary (client replaced his bounced payment after last secondary payroll)"
                template = createTemplate( "@12FirstSectionTemplate@", "", "@maid_name@ will receive her salaries for the last two months by @special_upcoming_primary_payroll+2days@. If we don't receive your payment on the 1st of @this_month@, @maid_name@ will receive only one salary.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" , replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("special_upcoming_primary_payroll+2days" , replaceParamWithName(getGeneralParameterValue,"special_upcoming_primary_payroll+2days"));
                expressionParameters.put("this_month" , replaceParamWithName(getGeneralParameterValue,"this_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                //13 -"Once client's monthly payment for this month is RECEIVED before the primary payroll is paid
                //AND
                //The client's monthly payment for previous month was replaced after the last secondary payroll
                //AND
                //Contract start date is not this month nor in the 2 months before"
                template = createTemplate( "@13FirstSectionTemplate@", "", "We've received your @this_month@ payment. @maid_name@ will receive her salaries for the last two months by @upcoming_primary_payroll+2days@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("this_month" , replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("maid_name" , replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_primary_payroll+2days" , replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                TemplateUtil.createTemplate(template, expressionParameters);

                //14 -"Once the client's monthly payment for this month gets BOUNCED before the primary payroll is paid
                //AND
                //The client's monthly payment for previous month was replaced after the last secondary payroll
                //AND
                //Contract start date is not this month nor in the 2 months before"
                template = createTemplate( "@14FirstSectionTemplate@", "", "Since we didn't receive your @this_month@ payment on time, @maid_name@ will receive only one salary by @upcoming_primary_payroll+2days@. She'll receive her other salary by @upcoming_secondary_payroll+1day@ if we receive your monthly payment before then.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("this_month" , replaceParamWithName(getGeneralParameterValue,"this_month"));
                expressionParameters.put("maid_name" , replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_primary_payroll+2days" , replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                expressionParameters.put("upcoming_secondary_payroll+1day" , replaceParamWithName(getGeneralParameterValue,"upcoming_secondary_payroll+1day"));
                TemplateUtil.createTemplate(template, expressionParameters);

                //15-"Once the primary payroll is paid (send all payslips is clicked)
                //AND the client's monthly payment for this month is BOUNCED before the primary payroll is paid
                //AND
                //The client's monthly payment for previous month was replaced after the last secondary payroll
                //AND
                //Contract start date is not this month nor in the 2 months before"
                template = createTemplate( "@15FirstSectionTemplate@", "", "We've transferred @maid_name@'s salary for @this_month_2@ on @prev_payroll_transfer_date@. But we didn't transfer her @last_month@ salary because we didn't receive your monthly payment yet. She'll receive her @last_month@ salary by @upcoming_secondary_payroll+1day@, if we receive your monthly payment before then.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" , replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("this_month_2",replaceParamWithName(getGeneralParameterValue,"this_month_2"));
                expressionParameters.put("prev_payroll_transfer_date" , replaceParamWithName(getGeneralParameterValue,"prev_payroll_transfer_date"));
                expressionParameters.put("last_month" , replaceParamWithName(getGeneralParameterValue,"last_month"));
                expressionParameters.put("upcoming_secondary_payroll+1day" , replaceParamWithName(getGeneralParameterValue,"upcoming_secondary_payroll+1day"));
                TemplateUtil.createTemplate(template, expressionParameters);


                //16- "Primary payroll for this month is PAID,
                //AND
                //Send all payslips is clicked for the first time after primary payroll is paid
                //AND
                //Client's monthly payment for this month is RECEIVED
                //AND
                //The client's monthly payment for previous month was replaced after the last secondary payroll
                //AND
                //Contract start date is not this month nor in the 2 months before"
                template = createTemplate( "@16FirstSectionTemplate@", "", "We've sent @maid_name@ her salaries on @payroll_transfer_date@. She'll receive her next salary by @upcoming_primary_payroll+2days@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" ,replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("payroll_transfer_date" ,replaceParamWithName(getGeneralParameterValue,"payroll_transfer_date"));
                expressionParameters.put("upcoming_primary_payroll+2days" ,replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+2days"));
                expressionParameters.put("next_month" ,replaceParamWithName(getGeneralParameterValue,"next_month"));
                TemplateUtil.createTemplate(template, expressionParameters);

                //17-
                //"Late salary because of holidays (once the payment date is changed from the payroll date setup page
                //AND the Client payment of this month is still pending (not received and not bounced)"
                template = createTemplate( "@17FirstSectionTemplate@", "", "@maid_name@'s salary will be late because the bank will be closed. She'll receive her salary by @upcoming_primary_payroll+1day@, if we receive your monthly payment. We're sorry for the delay.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name", replaceParamWithName(getGeneralParameterValue,"maid_name"));
                expressionParameters.put("upcoming_primary_payroll+1day", replaceParamWithName(getGeneralParameterValue,"upcoming_primary_payroll+1day"));
                TemplateUtil.createTemplate(template, expressionParameters);

                /* ==============================================================*/

                //YayaApp Templates

                //------------- 0 --> 9 CON-4
                //0-maid_special_condition
                template = createTemplate( "@maid_special_condition@", "", "Please chat with us to know more about your salary", "" , true, true);
                expressionParameters = new HashMap<>();
                TemplateUtil.createTemplate(template, expressionParameters);

                //1-maid_enrolled_after_15th
                template = createTemplate( "@maid_enrolled_after_15th@", "", "Your salary will be waiting for you in your bank account on @next_salary_date@. We'll send you a message when it's ready for you to collect.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date" , "id!=null ? T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getUpcomingPrimaryPaymentDate(id,true):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //2-maid_enrolled_before_15th
                template = createTemplate( "@maid_enrolled_before_15th@", "", "We already sent you the salary for @last_salary_month@ on @last_paid_salary_date@. It should be waiting for you in your bank account.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("last_paid_salary_date" , "id!=null ? T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastPaidSalaryDate(id):\"\"");
                expressionParameters.put("last_salary_month" , "id!=null ? T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastSalaryMonth(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //3-maid_not_enrolled
                template = createTemplate( "@maid_not_enrolled@", "", "You need to join a client first to start earning your salary. \n" +
                        "Don't worry, I am sure you will get a client soon.", "" , true, true);
                expressionParameters = new HashMap<>();
                TemplateUtil.createTemplate(template, expressionParameters);

                //4-maid_enrolled_after_27th
                template = createTemplate( "@maid_enrolled_after_27th@", "", "@maid_name@, you won't get a salary on @next_salary_date@ because you recently joined us. Your first salary will be on @next_month_salary_start_date@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("maid_name" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getName(id):\"\"");
                expressionParameters.put("next_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDate()");
                expressionParameters.put("next_month_salary_start_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getFirstPrimaryPayrollAfterUpcomingPrimary()");
                TemplateUtil.createTemplate(template, expressionParameters);

                //5-maid_was_on_vacation
                template = createTemplate( "@maid_was_on_vacation@", "", "Since you were on vacation, you didn't receive your salary on @last_salary_date@. Don't worry, your salary will be waiting for you in your bank account on @next_salary_date_after_vacation@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("last_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastPrimarySalaryDate()");
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //6-maid_is_on_vacation
                template = createTemplate( "@maid_is_on_vacation@", "", "Since you are on vacation, you won't receive your salary on @missed_primary_salary_date@. Don’t worry, your salary will be waiting for you in your bank account on @next_salary_date_after_vacation@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("missed_primary_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastPrimarySalaryDate()");
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //7-maid_was_on_vacation_more_than_one_payroll
                template = createTemplate( "@maid_was_on_vacation_more_than_one_payroll@", "", "Since you are on vacation, you won't receive your salary on @first_missed_salary_date@ or on @second_missed_salary_date@. We'll send you your salaries on @next_salary_date_after_vacation@, if you return before then.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("first_missed_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getFirstMissSalaryDate()");
                expressionParameters.put("second_missed_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastPrimarySalaryDate()");
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //8-maid_is_on_vacation_more_than_one_payroll
                template = createTemplate( "@maid_is_on_vacation_more_than_one_payroll@", "", "Since you were on vacation, you didn't receive your salary on @first_missed_salary_date@ or on @second_missed_salary_date@. Don't worry, your salary will be waiting for you in your bank account on @next_salary_date_after_vacation@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("first_missed_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getFirstMissSalaryDate()");
                expressionParameters.put("second_missed_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastPrimarySalaryDate()");
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //9-maid_was_now_show
                template = createTemplate( "@maid_was_now_show@", "", "You were absent when we were sending the salaries so you didn't receive a salary yet. Don't worry, you will receive your salary on @next_salary_date@.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDate()");
                TemplateUtil.createTemplate(template, expressionParameters);

                //10-maid_is_on_vacation_and_will_return_after_primary
                template = createTemplate( "@maid_is_on_vacation_and_will_return_after_primary@", "", "Since you’re on vacation, we’ll hold your salary until you come back. We’ll send you your salary on @next_salary_date_after_vacation@ if you return before then.", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //------------- 11 --> 16 CON-2

                //11-maid_does_not_have_payslip
                template = createTemplate( "@maid_does_not_have_payslip@", "", "You still don't have a payslip. Your payslip will be available here after you get your first salary.", "" , true, true);
                expressionParameters = new HashMap<>();
                TemplateUtil.createTemplate(template, expressionParameters);

                //12-maid_enrolled_after_15th_payslip
                template = createTemplate( "@maid_enrolled_after_15th_payslip@", "", "Your payslip for the salary that you will receive on @next_salary_date@ is still not available.\n <b><a href=\"route://payslip_route\" style=\"text-decoration: none; color:#4267b2;\">Click here to view your previous payslip</a></b>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getUpcomingPrimaryPaymentDate(id,false):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //13-maid_enrolled_before_15th_payslip
                template = createTemplate( "@maid_enrolled_before_15th_payslip@", "", "Your payslip for the salary that you received on @last_salary_date@ is ready.\n <b><a href=\"route://payslip_route\" style=\"text-decoration: none; color:#4267b2;\">Click here to view your payslip</a></b>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("last_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getLastPaidSalaryDate(id)");
                TemplateUtil.createTemplate(template, expressionParameters);

                //14-maid_was_on_vacation_or_on_hold_payslip
                template = createTemplate( "@maid_was_on_vacation_or_on_hold_payslip@", "", "Your payslip for the salary that you will receive on @next_salary_date@ is still not available.\n <b><a href=\"route://payslip_route\" style=\"text-decoration: none; color:#4267b2;\">Click here to view your previous payslip</a></b>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date" , "T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDate()");
                TemplateUtil.createTemplate(template, expressionParameters);

                //15-maid_is_on_vacation_payslip
                template = createTemplate( "@maid_is_on_vacation_payslip@", "", "Your payslip for the salary that you will receive on @next_salary_date_after_vacation@ is still not available.\n <b><a href=\"route://payslip_route\" style=\"text-decoration: none; color:#4267b2;\">Click here to view your previous payslip</a></b>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);

                //16-maid_is_on_vacation_more_than_one_payroll_payslip
                template = createTemplate( "@maid_is_on_vacation_more_than_one_payroll_payslip@", "", "Your payslips for the salaries that you will receive on @next_salary_date_after_vacation@ are still not available.\n <b><a href=\"route://payslip_route\" style=\"text-decoration: none; color:#4267b2;\">Click here to view your previous payslip</a></b>", "" , true, true);
                expressionParameters = new HashMap<>();
                expressionParameters.put("next_salary_date_after_vacation" , "id!=null?T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"yaYaAppContentHelper\").getNextSalaryDateAfterVacation(id):\"\"");
                TemplateUtil.createTemplate(template, expressionParameters);
            }
        };
    }


    private void createZeroOrNegativeSalaryComponentTemplate() {
        createEmailTemplateWithSmsChannel("Payroll_Negative_Salary_Component_Email_Template", "",
                "Kindly note that @maid_full_name@’s @component_details@. <br>" +
                        "The system just tried to correct this mistake, please go to her payroll profile and check if her @component_check@, " +
                        "if not reach out to the person in charge to discuss the case further! ", null);
    }


    public void createSalaryTransferredNotificationTemplate(){
        Template template = new Template();
        template.setName("Payroll_Maid_Salary_Transferred_Notification");
        template.setDescription("");
        template.setText("We've just transferred @maid_name@'s salary to her account. She can withdraw her salary tomorrow.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(false);
        template.setNotificationHoursBeforeSendSms(1);
        template.setNotificationSmsTemplateName("Payroll_Maid_Salary_Transferred_Notification");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(24);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    public void createClientSalaryIsDelayedNotificationTemplate(){
        Template template = new Template();
        template.setName("Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date_SMS");
        template.setDescription("");
        template.setText("@greetings@ \n@maid_name@'s salary will be late because the bank will be closed. She'll receive her salary by @payment_date@, if we receive your monthly payment. We're sorry for the delay.");
        TemplateUtil.createTemplate(template, new HashMap<>());

        template = new Template();
        template.setName("Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date");
        template.setDescription("");
        template.setText("@maid_name@'s salary will be late because the bank will be closed. She'll receive her salary by @payment_date@, if we receive your monthly payment. We're sorry for the delay.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(19876);
        template.setNotificationSmsTemplateName("Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date_SMS");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(48);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    public void createSalaryOnHoldNotificationTemplate(){
        Template template = new Template();
        template.setName("Payroll_Maid_Salary_On_Hold_Notification");
        template.setDescription("");
        template.setText("Usually, your maid's salary is transferred on the 3rd of every month. This month because she @reason@, her salary will be released on @date@.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(false);
        template.setNotificationHoursBeforeSendSms(1);
        template.setNotificationSmsTemplateName("Payroll_Maid_Salary_On_Hold_Notification");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(24);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }
    public void createMvSalaryTransferredNotificationTemplate(){
        Template template = new Template();
        template.setName("Pay_MV_Sal_notify");
        template.setDescription("");
        template.setText("This is to notify you that we've sent @maid_name@ her salary. Please click on @link@ for more information on how @maid_name@ can collect her salary.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(false);
        template.setNotificationHoursBeforeSendSms(1);
        template.setNotificationExpirationHours(24);
        template.setNotificationSmsTemplateName("Pay_MV_Sal_notify");
        template.setNotificationCanClosedByUser(true);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    public void createConfirmGoogleReviewNotification(){
        Template template = new Template();
        template.setName("Payroll_Confirm_Google_Review_By_Auditors_Notification");
        template.setDescription("");
        template.setText("Congratulations! Your clients are really happy with your work. We added AED @amount@ to your next salary as a thank you from maids.cc, Keep up the good work, we are proud of you!.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(1);
        template.setNotificationSmsTemplateName("Payroll_Confirm_Google_Review_By_Auditors_Notification");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(24);
        TemplateUtil.createTemplate(template, new HashMap<>());
    }

    public void createLateSalaryBecauseOfHolidayNotification(){
        Template template = new Template();
        template.setName("Payroll_Late_Salary_Because_Of_Holiday_Notification");
        template.setDescription("");
        template.setText("Hello @maid_name@. Your salary for @month@ will be late because of the holiday. Your salary will be waiting for you in your Al-Ansari account on @payment_date@ at 6 pm. We're sorry for the delay.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(0);
        template.setNotificationSmsTemplateName("Payroll_Late_Salary_Because_Of_Holiday_Notification");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(24);
        TemplateUtil.createTemplate(template, new HashMap<>());

        Map<String, String> translations = new HashMap();
        translations.put("am", "ሰላም @maid_name@. በበዓል ምክንያት የ@month@ ደሞዝዎ ይዘገያል። ደሞዝዎ በአል-አንሷሪ አካውንትዎ @payment_date@ በ6 ሰአት ይጠብቆታል። በመዘግየቱ እናዝናለን።");
        translations.put("az", "Hello @maid_name@. mindaan ke @month@ ni barfataa wan ayyaan ta’eef. Mindaan ke herrega ke Al-Ansari kessati addu guyyaa @payment_date@ satati 6pm. Si eega. Barfachuu kenyaaf dhifama gafana.");
        translations.put("tl", "Hello @maid_name@. Mahuhuli ang sahod mo sa @month@ dahil sa holiday. Maghihintay sa iyo ang iyong suweldo sa iyong Al-Ansari account sa @payment_date@ sa ganap na 6 ng gabi. Ikinalulungkot namin ang pagkaantala.");

        template = TemplateUtil.getTemplate("Payroll_Late_Salary_Because_Of_Holiday_Notification");

        if(template != null ) {
            TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            for (String lang : translations.keySet()) {
                PicklistItem item = Setup.getOrCreateItem(Picklist.TEMPLATE_LANGUAGES, lang);
                TemplateTranslation translation = translationRepository.findByLanguageAndTemplate(item, template);
                if (translation == null) {
                    translation = new TemplateTranslation();
                    translation.setTemplate(template);
                    translation.setLanguage(item);
                    translation.setTranslation(translations.get(lang));
                    translationRepository.save(translation);
                }
            }
        }

        template = new Template();
        template.setName("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS");
        template.setDescription("");
        template.setText("Hello @maid_name@, your salary will be late because the bank will be closed. You'll receive your salary by @payment_date@. Please don't contact us before then. We're sorry for the delay.");
        TemplateUtil.createTemplate(template, new HashMap<>());

        translations = new HashMap();
        translations.put("am", "ሰላም @maid_name@ ባንኩ ስለሚዘጋ ደሞዝሽ ይዘገያል። ደሞዝሽን በ @payment_date@ ታገኚያለሽ። እባክሽ ከዚያ በፊት አታገኚን። በመዘግየቱ ይቅርታ እንጠይቃለን።");
        translations.put("az", "Akkam jirta @maid_name@, baankii waan cufamuuf mindaan keessan harkifata. Mindaa keessan @payment_date@ tti ni argattu. Sana dura nu hin qunnaminaa. Harkifannaa kanaaf dhiifam");
        translations.put("tl", "Kamusta@maid_name@,mahuhuli ang sahod mo dahil sarado ang bangko.Matatanggap mo ang iyong suweldo @payment_date@.Mangyaring huwag makipag-ugnayan sa amin bago iyon. paumanhin sa pagkaantala.");

        template = TemplateUtil.getTemplate("Payroll_Late_Salary_Because_Of_Holiday_Notification_MV_Maids_SMS");

        if(template != null ) {
            TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            for (String lang : translations.keySet()) {
                PicklistItem item = Setup.getOrCreateItem(Picklist.TEMPLATE_LANGUAGES, lang);
                TemplateTranslation translation = translationRepository.findByLanguageAndTemplate(item, template);
                if (translation == null) {
                    translation = new TemplateTranslation();
                    translation.setTemplate(template);
                    translation.setLanguage(item);
                    translation.setTranslation(translations.get(lang));
                    translationRepository.save(translation);
                }
            }
        }


    }

    public void createYayaInAccommodationMaidSalaryTransferredNotificationTemplate(){
        Template template = new Template();
        template.setName("Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Notification");
        template.setDescription("");
        template.setText("Your salary of @salary_amount@ is waiting for you in your Al-Ansari account. Please click the button below to know how to collect your salary and to view your payslip.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationSmsTemplateName("Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Sms");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(168);

        TemplateUtil.createTemplate(template, new HashMap<>());

        Map<String, String> translations = new HashMap();
        translations.put("hi", "आपके अल-अंसारी खाते में @salary_amount@ का आपका वेतन आपका इंतजार कर रहा है। यदि आप इसे एकत्र करना चाहते हैं, तो उपस्थिति करने वाली महिला से पूछें। अपनी पेस्लिप देखने के लिए नीचे दिए गए बटन पर क्लिक करें।");
        translations.put("am", "በአል አንሳሪ ሂሳብ ቁጥርሽ ውስጥ ደምዝሽ @salary_amount@ እየጠበቀሽ ነው።  ደሞዝሽን ማውጣት የምትፈልጊ ከሆነ አቴንዳንስ የምትወስደወን ልጅ እባክሽ ጠይቂያት።የደሞዝሽን ሪሲት ማየት የምትፈልጊ ከሆነ ከታች ያለውን እባክሽ ነኪው። ");
        translations.put("az", "Miindaakee kan @salary_amount@ herreegakee is Al-Ansari kessattii sii eegaa jira.walittii qabachuu yoo barbaaddee giiftii ishee hirmaachistuu gaafadhuu payslip kee ilaaluf qabduu gadittii bu'ii");
        translations.put("tl", "Ang iyong suweldo na @salary_amount@ ay naghihintay para sa iyo sa iyong Al-Ansari account. Kung nais mong kolektahin ito, tanungin ang babae na gumagawa ng pagdalo. I-click ang button sa ibaba para tingnan ang iyong payslip.");

        template = TemplateUtil.getTemplate("Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Notification");

        if(template != null ) {
            TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            for (String lang : translations.keySet()) {
                PicklistItem item = Setup.getOrCreateItem(Picklist.TEMPLATE_LANGUAGES, lang);
                TemplateTranslation translation = translationRepository.findByLanguageAndTemplate(item, template);
                if (translation == null) {
                    translation = new TemplateTranslation();
                    translation.setTemplate(template);
                    translation.setLanguage(item);
                    translation.setTranslation(translations.get(lang));
                    translationRepository.save(translation);
                }
            }
        }

    }

    public void createYayaWithClientMaidSalaryTransferredNotificationTemplate(){
        Template template = new Template();
        template.setName("Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Notification");
        template.setDescription("");
        template.setText("Your salary of @salary_amount@ is waiting for you in your Al-Ansari account. You can use your ATM machine or physically visit Al-Ansari to collect your salary. Click the button below to view your payslip.");
        template.setNotificationLocation(NotificationLocation.HOME);
        template.setSendSMSIfNotReceived(true);
        template.setNotificationHoursBeforeSendSms(2);
        template.setNotificationSmsTemplateName("Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Sms");
        template.setNotificationCanClosedByUser(true);
        template.setNotificationExpirationHours(168);

        TemplateUtil.createTemplate(template, new HashMap<>());

        Map<String, String> translations = new HashMap();
        translations.put("hi", "आपके अल-अंसारी खाते में @salary_amount@ का आपका वेतन आपका इंतजार कर रहा है। अपना वेतन कैसे प्राप्त करें और अपनी पेस्लिप कैसे देखें, यह जानने के लिए कृपया नीचे दिए गए बटन पर क्लिक करें।");
        translations.put("am", "በአል አንሳሪ ሂሳብ ቁጥርሽ ውስጥ ደምዝሽ @salary_amount@ እየጠበቀሽ ነው። ደሞዝሽን እንዴት ማውጣት እንደምትችይ ለማየት እንዲሁም የደሞዝሽን ሪሲት ለማየት ከታች ያለውን ነኪው እባክሽ");
        translations.put("az", "Miindaakee kan @salary_amount@ herregakee isa Al-Ansari kessattii sii eegaa jira. maaloo qabduu kan gadi jiruttii bu'ii akka ittii miindakee walittii qabduufi payslip kee ilaaluf");
        translations.put("tl", "Ang iyong suweldo na @salary_amount@ ay naghihintay para sa iyo sa iyong Al-Ansari account. Paki-click ang button sa ibaba para malaman kung paano kokolektahin ang iyong suweldo at upang tingnan ang iyong payslip.");

        template = TemplateUtil.getTemplate("Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Notification");

        if(template != null ) {
            TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            for (String lang : translations.keySet()) {
                PicklistItem item = Setup.getOrCreateItem(Picklist.TEMPLATE_LANGUAGES, lang);
                TemplateTranslation translation = translationRepository.findByLanguageAndTemplate(item, template);
                if (translation == null) {
                    translation = new TemplateTranslation();
                    translation.setTemplate(template);
                    translation.setLanguage(item);
                    translation.setTranslation(translations.get(lang));
                    translationRepository.save(translation);
                }
            }
        }

    }

    Template createTemplate(String name, String subject, String content, String specs, boolean unicode, boolean showInLog) {
        Template template = new Template(name, subject, content, specs);
        template.setUnicode(unicode);
        template.setStatus(Setup.getItem("template_status", "active"));
        template.setShowInReceiverLog(showInLog);
        return template;
    }

    Template createTemplate(String name, String subject, String content, String specs, boolean unicode, boolean showInLog, String livePersonTemplate, String livePersonCampaignName, PicklistItem livePersonOutboundNumber, PicklistItem skill) {
        Template template = new Template(name, subject, content, specs);
        template.setUnicode(unicode);
        template.setStatus(Setup.getItem("template_status", "active"));
        template.setShowInReceiverLog(showInLog);
        template.setLivePersonTemplate(livePersonTemplate);
        template.setLivePersonCampaignName(livePersonCampaignName);
        template.setLivePersonOutboundNumber(livePersonOutboundNumber);
        template.setSkill(skill);
        return template;
    }

    public void createDuplicateIBanDetectionTemplate() {
        createEmailTemplateWithSmsChannel("Payroll_Duplicate_IBan_Detection_Template",
                "an email if the Office Staff for Expat or Emirati Iban duplication",
                "Please note that a user tried to save an IBAN that already exists for another employee."
                        + "<br>Date and Time of Attempt: @date_and_time@ <br>User Initiating the Action:  @user_name@"
                        + "<br><br><br>Name of the Employee whose IBAN was being edited: @affected_employee@"
                        +"<br><br><br>Entered IBAN: @iban@"
                        +"<br><br><br>Duplicate Found For @duplicate_found_for@"
                        +"<br><br><br>Note that This action has been automatically blocked by the system.",
                "Please note that a user tried to save an IBAN that already exists for another employee."
                        + "<br>Date and Time of Attempt: @date_and_time@ <br>User Initiating the Action:  @user_name@"
                        + "<br><br><br>Name of the Employee whose IBAN was being edited: @affected_employee@"
                        +"<br><br><br>Entered IBAN: @iban@"
                        +"<br><br><br>Duplicate Found For @duplicate_found_for@"
                        +"<br><br><br>Note that This action has been automatically blocked by the system.");
    }

//    public SmsResponse sendMessageOrEmail(String type, String phoneNumber, String email, SmsReceiverType receiverType, Long id, String name, String relatedEntityType, String template, String smsTemplate, Map<String, String> paramValues, Object templateContext, CommunicationMethod preferredCommunicationMethod, boolean isSecure) {
//        if ((preferredCommunicationMethod == null || preferredCommunicationMethod == CommunicationMethod.SMS)
//                && !isSyrianNumber(phoneNumber))
//            return sendMessageOrEmail(type, phoneNumber, email, receiverType, id, name, relatedEntityType, smsTemplate, paramValues, templateContext, preferredCommunicationMethod, isSecure);
//        else
//        return sendMessageOrEmail(type, phoneNumber, email, receiverType, id, name, relatedEntityType, template, paramValues, templateContext, preferredCommunicationMethod, isSecure);
//
//    }

//    public void sendMessageOrEmail(String type, BaseEntity recipient, String template, Map<String, String> paramValues) {
//        messagingService.send(template, type, null, recipient, paramValues, null, null, recipient);
//        if ((preferredCommunicationMethod == null || preferredCommunicationMethod == CommunicationMethod.SMS)
//                && !isSyrianNumber(phoneNumber)) {
//            if (isSecure)
//                return smsService.send(new SmsObject.builder(type,
//                        phoneNumber,
//                        receiverType,
//                        id,
//                        name,
//                        template,
//                        paramValues,
//                        templateContext)
//                        .relatedEntity(id, relatedEntityType)
//                        .unicode()
//                        .secure()
//                        .build());
//            else
//                return smsService.send(
//                        type,
//                        phoneNumber,
//                        receiverType,
//                        id,
//                        name,
//                        template,
//                        paramValues,
//                        templateContext);
//        } else if (email != null && !email.isEmpty()) {
//            if (email != null && !email.isEmpty()) {
//                String testReceiver = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER);
//                List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.isProduction() ? email : testReceiver);
//
//                TemplateEmail templateEmail = new TemplateEmail(type, template, new HashMap<>(paramValues));
//                if (isSecure)
//                    Setup.getMailService().sendEmail(new MailObject.builder(templateEmail, receiverType != null && SmsReceiverType.Office_Staff.equals(receiverType) ? EmailReceiverType.Office_Staff : null)
//                            .recipients(recipients)
//                            .html()
//                            .senderName(MessageTemplateService.getMaidsCcSenderName())
//                            .secure()
//                            .build());
//                else
//                    Setup.getMailService().sendEmail(recipients, templateEmail, MessageTemplateService.getMaidsCcSenderName(), null);
//                return new SmsResponse(null, true, null, null);
//            }
//        }

        //return new SmsResponse(null, false, null, null);
//    }

    public boolean isManager(OfficeStaff officeStaff) {
        if (officeStaff == null || officeStaff.getJobTitle() == null) return false;

        return officeStaff.getJobTitle().hasTag("manager")
                || officeStaff.getJobTitle().hasTag("JOB_TITLE_MANAGER_TAG");
    }

    public String loadYayaContent(String templateName, Housemaid housemaid){
        try {
            return TemplateUtil.compileTemplate(templateName, housemaid, new HashMap());
        }catch(Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in loading template", false);
        }
        return  "";
    }

    private String replaceParamWithName(String exp, String param){
        return exp.replace("@param@",param);
    }

    public void createHousemaidVacationOverlapsNotificationTemplate() {
        createNotificationTemplateWithSmsChannel("housemaid_vacation_overlaps_primary_payroll_notification", "",
                "While you’re on vacation, your @payroll_month@ salary might be delayed and won’t be paid on @next_primary_payment_date@. Don’t worry, your salary will be ready for you when you come back from vacation. If you need to change your vacation dates, click this link to chat with us @chat_with_us@.",
                NotificationLocation.HOME, true, 2,
                "While you’re on vacation, your @payroll_month@ salary might be delayed and won’t be paid on @next_primary_payment_date@. Don’t worry, your salary will be ready for you when you come back from vacation. If you need to change your vacation dates, click this link to chat with us @chat_with_us@.",
                "priority_2", null, null, null);
    }

    public void createMarkUnpaidSalaryApprovalTemplate() {
        createEmailTemplateWithSmsChannel("Payroll_Mark_Unpaid_Approval",
                "an email if the salary marked as unpaid.",
                "The processed wire transfer salary of @payroll_date@ for @employee_name@, of the amount of @salary_amount@, was requested to be marked as unpaid.<br><br>Please review the notes below of the requestor, and take the necessary action:<br>@notes@<br><br>@url@",
                "The processed wire transfer salary of @payroll_date@ for @employee_name@, of the amount of @salary_amount@, was requested to be marked as unpaid.<br><br>Please review the notes below of the requestor, and take the necessary action:<br>@notes@<br><br>@url@");
    }

    public static void createEmailTemplateWithSmsChannel(String templateName, String description, String emailText, String smsText) {

        ChannelSpecificSetting smsChannelSpecificSetting = new ChannelSpecificSetting();
        smsChannelSpecificSetting.setType(ChannelSpecificSettingType.SMS);
        smsChannelSpecificSetting.setText(smsText != null ? smsText : emailText);

        ChannelSpecificSetting emailChannelSpecificSetting = new ChannelSpecificSetting();
        emailChannelSpecificSetting.setType(ChannelSpecificSettingType.Email);
        emailChannelSpecificSetting.setText(emailText != null ? emailText : smsText);
        emailChannelSpecificSetting.setOnFailAction(ChannelSpecificSettingType.SMS);

        TemplateUtil.createTemplate(new Template.TemplateBuilder()
                .Template(templateName,"", description)
                .newModel()
                .method("e-mail")
                .channelSetting(emailChannelSpecificSetting)
                .channelSetting(smsChannelSpecificSetting)
                .build()
        );
    }

    public static void createNotificationTemplateWithSmsChannel(String templateName, String description, String notificationText, NotificationLocation notificationLocation, boolean notificationCanBeCloseByUser, int notificationExpirationHours,
                                                                String smsText, String priority, Map<String, String> notificationTranslation, Map<String, String> smsTranslation, String itemCodeExpiration) {

        Template template = null;
        ChannelSpecificSetting smsChannelSpecificSetting = new ChannelSpecificSetting();
        smsChannelSpecificSetting.setType(ChannelSpecificSettingType.SMS);
        smsChannelSpecificSetting.setText(smsText != null ? smsText : notificationText);

        ChannelSpecificSetting notificationChannelSpecificSetting = new ChannelSpecificSetting();
        notificationChannelSpecificSetting.setShowOnHomePage(true);
        notificationChannelSpecificSetting.setType(ChannelSpecificSettingType.Notification);
        notificationChannelSpecificSetting.setText(notificationText);
        notificationChannelSpecificSetting.setNotificationExpirationHours(notificationExpirationHours);
        notificationChannelSpecificSetting.setNotificationCanClosedByUser(notificationCanBeCloseByUser);
        notificationChannelSpecificSetting.setOnFailAction(ChannelSpecificSettingType.SMS);

        if (itemCodeExpiration != null && !itemCodeExpiration.isEmpty()){
            PicklistItem relativeExpirationItem = Setup.getItem(Picklist.IMS_NOTIFICATION_RELATIVE_EXPIRATION, itemCodeExpiration);
            notificationChannelSpecificSetting.setNotificationRelativeExpirationType(relativeExpirationItem);
            notificationChannelSpecificSetting.setNotificationRelativeExpirationHours(0);
        }


        PicklistItem messageDelayItem = null;
        switch (priority) {
            case "priority_1":
                messageDelayItem = Setup.getItem("template_message_delay", "with_notification");
                break;
            case "priority_2":
                notificationChannelSpecificSetting.setMessageDelayHours(2);
                messageDelayItem = Setup.getItem("template_message_delay", "x_hours_after_notification");
                break;
            case "priority_3":
                notificationChannelSpecificSetting.setMessageDelaySpecificDate(
                        Setup.getItem("template_message_delay_specific_date", "@nextMorning@"));
                notificationChannelSpecificSetting.setMessageDelaySpecificTime(DateUtil.todayAt(9, 0));
                messageDelayItem = Setup.getItem("template_message_delay", "specific_time");
                break;
            case "priority_4":
                messageDelayItem = Setup.getItem("template_message_delay", "no_sms");
                break;
        }
        notificationChannelSpecificSetting.setMessageDelayType(messageDelayItem);

        template = TemplateUtil.createTemplate(new Template.TemplateBuilder()
                .Template(templateName,"", description)
                .newModel()
                .method("Push Notification")
                .priority(priority)
                .notificationLocation(notificationLocation)
                .channelSetting(notificationChannelSpecificSetting)
                .channelSetting(smsChannelSpecificSetting)
                .build()
        );
        if (template != null){
            Template finalTemplate = template;
            if (notificationTranslation != null){
                notificationTranslation.keySet().forEach(lang -> {
                    createTranslation(finalTemplate, ChannelSpecificSettingType.Notification, lang, notificationTranslation.get(lang));
                    if (smsText == null){
                        createTranslation(finalTemplate, ChannelSpecificSettingType.SMS, lang, notificationTranslation.get(lang));
                    }
                });
            }
            if (smsTranslation != null){
                smsTranslation.keySet().forEach(lang -> {
                    createTranslation(finalTemplate, ChannelSpecificSettingType.SMS, lang, smsTranslation.get(lang));
                });
            }
        }
    }

    public static void createWhatsappTemplate(String templateName, String description, String whatsappText) {

        ChannelSpecificSetting whatsappChannelSpecificSetting = new ChannelSpecificSetting();
        whatsappChannelSpecificSetting.setType(ChannelSpecificSettingType.Whatsapp);
        whatsappChannelSpecificSetting.setText(whatsappText != null ? whatsappText : "");

        TemplateUtil.createTemplate(new Template.TemplateBuilder()
                .Template(templateName,"", description)
                .newModel()
                .method("whatsapp")
                .channelSetting(whatsappChannelSpecificSetting)
                .build()
        );
    }

    public static void createTranslation(Template englishTemplate, ChannelSpecificSettingType channelSpecificSettingType, String language, String text) {
        TemplateTranslationRepository translationRepository = Setup.getRepository(TemplateTranslationRepository.class);

        PicklistItem languageItem = PicklistHelper.getItem(Picklist.TEMPLATE_LANGUAGES, language);

        if (translationRepository.findByLanguageAndTemplate(languageItem, englishTemplate) != null) {
            return;
        }
        TemplateUtil.addTranslation(englishTemplate.getName(),
                channelSpecificSettingType,
                language,
                text,
                null);
    }

    public void createNewSalaryExceptionTemplate() {
        createEmailTemplateWithSmsChannel("Payroll_After_New_Salary_Exception_Template",
                "an exception email if the new salary is less than the basic salary.",
                "@user_name@ tried to set the worker`s salary to @new_salary@ which is lower than the housemaid’s basic salary and holiday and that’s not allowed. Please check and resolve the issue.",
                "@user_name@ tried to set the worker`s salary to @new_salary@ which is lower than the housemaid’s basic salary and holiday and that’s not allowed. Please check and resolve the issue.");
    }

    public void createEditedPayrollRosterTemplate() {
        createEmailTemplateWithSmsChannel("Payroll_Send_Payroll_Roster_File_Edited",
                "send payroll roster file edited to approve it",
                "Hi @firstLastName@, Please check the revised payroll roster of your employees. The changes from the previous roster version are highlighted in Yellow. " +
                        "Please click on this link to approve or send it back for the payroll manager to apply any changes: @url@",
                null);
    }

    public void sendFinalPayrollExceptionFileToAuditorsTemplate() {
        createEmailTemplateWithSmsChannel("Payroll_Send_Final_Payroll_Exception_File_To_Payroll_Auditors",
                "send payroll exception file to payroll auditors",
                "Please check the payroll Exception file of @payroll_month_and_year@",
                "Please check the payroll Exception file of @payroll_month_and_year@");
    }

    public void createMultipleProfilesEmailTemplate() {
        createEmailTemplateWithSmsChannel(
                "Payroll_Multiple_Profiles_Summary_Template",
                "Multiple Profiles Linked to The Same Money Receiver Name",
                "The following money receiver names are actively linked to more than one overseas office staff profile. " +
                        "Please verify and reach out to the respective employees below with their corresponding CE numbers.<br><br>" +
                        "@info@"+
                        "Kindly add the <b>Money Receiver Email Address</b> for the money receiver names listed above. " +
                        "Please <b>do not add any office staff emails</b> in the <b>Money Receiver Email Address</b> field to avoid mixed notifications.",
                "The following money receiver names are actively linked to more than one overseas office staff profile. " +
                        "Please verify and reach out to the respective employees below with their corresponding CE numbers.<br><br>" +
                        "@info@"+
                        "Kindly add the <b>Money Receiver Email Address</b> for the money receiver names listed above. " +
                        "Please <b>do not add any office staff emails</b> in the <b>Money Receiver Email Address</b> field to avoid mixed notifications."
        );
    }

    public void createPaymentConfirmationTemplate() {
        String messageText = "We've successfully received your payment of AED @total_amount@. This amount includes your maid's salary of AED @maid_salary@ for @month@, which will be transferred to her on @5th_day_of_month@, and the monthly fee of AED @monthly_fee@ for family and maid support.\n\n@parameter1@\n\n@parameter2@";

        new Template.TemplateBuilder().Template("MV_PAYMENT_RECEIVED_ON_X_MONTH_NOTIFICATION", "", "")
                .newModel()
                .method("whatsapp")
                .channelSetting(new ChannelSpecificSetting.WhatsappChannelSettingBuilder()
                        .text(messageText)
                        .livePersonCampaignName("Payment Confirmation")
                        .livePersonTemplate("HX63734783ffbef429b1c6ba0046fe300e")
                        .outboundNumber(Setup.getOrCreateItem(Picklist.LIVE_PERSON_OUTBOUND_NUMBERS, "************"))
                        .metaBusinessAccount(Setup.getOrCreateItem("meta_business_accounts", "***************"))
                        .skill(Setup.getOrCreateItem(Picklist.TEMPLATE_SKILL, "GPT_MV_RESOLVERS"))
                        .build())
                .channelSetting(new ChannelSpecificSetting.SmsChannelSettingBuilder()
                        .text(messageText)
                        .build())
                .build();
    }
}
