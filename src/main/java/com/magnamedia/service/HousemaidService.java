package com.magnamedia.service;

import com.magnamedia.entity.*;
import com.magnamedia.extra.CardStatus;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.extra.ZajelTrackingStatus;
import com.magnamedia.repository.RenewVisaRequestRepository;
import com.magnamedia.repository.RepeatEIDRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.magnamedia.extra.AnsariPaymentMethod.BANK_TRANSFER;
import static com.magnamedia.extra.AnsariPaymentMethod.DU_PAY_CARD;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 5/24/2025
 **/

@Service
public class HousemaidService {

    @Autowired
    RepeatEIDRequestRepository repeatEIDRequestRepository;

    @Autowired
    RenewVisaRequestRepository renewVisaRequestRepository;

    private boolean isCardInProcess(CardStatus status) {
        return EnumSet.of(
                CardStatus.REQUEST_SENT_TO_ANSARI,
                CardStatus.REQUEST_RECEIVED,
                CardStatus.PROCESSING_REQUEST,
                CardStatus.CARD_PREPARED_FOR_DISPATCH,
                CardStatus.CARD_IN_TRANSIT
        ).contains(status);
    }

    private boolean isCardReadyForPickup(CardStatus status) {
        return EnumSet.of(
                CardStatus.CARD_READY_FOR_PICKUP,
                CardStatus.REPLACED_CARD_READY_FOR_PICKUP
        ).contains(status);
    }
    private boolean isCardCollected(CardStatus status) {
        return EnumSet.of(
                CardStatus.CARD_COLLECTED,
                CardStatus.REPLACED_CARD_COLLECTED
        ).contains(status);
    }
    private boolean isCardRelocating(CardStatus status) {
        return EnumSet.of(
                CardStatus.REPLACED_REQUEST_SENT_TO_ANSARI,
                CardStatus.REPLACED_REQUEST_RECEIVED,
                CardStatus.REPLACED_CARD_PROCESSING_REQUEST,
                CardStatus.REPLACED_CARD_PREPARED_FOR_DISPATCH,
                CardStatus.REPLACED_CARD_IN_TRANSIT
        ).contains(status);
    }

    private boolean hasDeliveredEID(Housemaid housemaid) {
        // Determine if housemaid has EID status= EID Delivered (Family)
        String ZajelTrackingStatus = getZajelTrackingStatus(housemaid);
        return ZajelTrackingStatus.equals("Delivered (Family)");
    }

    public Map<String, String> determineTemplateParameters(Housemaid housemaid, String maidFirstName, String documentLink, String atmCardLocation) {
        String parameter1 = null;
        String parameter2 = null;

        if (documentLink == null || documentLink.isEmpty()){
            documentLink = "";
        }

        if (housemaid.getAnsariPaymentMethod() != null &&
                housemaid.getAnsariPaymentMethod().equals(BANK_TRANSFER.getLabel())) {
            // First payment with bank transfer
            parameter1 = maidFirstName + "'s salary will be deposited directly into her bank account.";
            parameter2 = "Feel free to contact us if you have any questions.";

            Map<String, String> parameters = new HashMap<>();
            parameters.put("parameter1", parameter1);
            parameters.put("parameter2", parameter2);
            return parameters;
        }

        CardStatus status = housemaid.getCardStatus();
        if (status == null) {
            return null;
        }

        if (isCardInProcess(status)) {
            if( hasDeliveredEID(housemaid)) {
                // First payment with EID but ATM card not ready
                parameter1 = "Since " + maidFirstName + "'s ATM card is not ready yet, she can visit her nearest Al-Ansari branch and show her EID to collect her salary over the counter.";
                parameter2 = "Let us know if you need any assistance.";
            } else {
                // First payment without EID and ATM card not ready
                parameter1 = "As " + maidFirstName + "'s ATM card is not ready yet, she can visit her nearest Al-Ansari branch to collect her salary over the counter. At the counter, she must click on the following link: " + documentLink + " and show these documents on her phone." ;
                parameter2 = "Let us know if you need further assistance.";
            }
        }

        if (isCardReadyForPickup(status)) {
            if(hasDeliveredEID(housemaid)) {
                // First payment with EID and ATM card ready for pickup
                parameter1 = maidFirstName + "'s ATM card is now ready for collection at " + atmCardLocation + ". She must show her EID at the counter to collect her ATM card and salary." ;
                parameter2 = "Let us know if you need any assistance." ;
            } else {
                // First payment without EID and ATM card ready for pickup
                parameter1 =  maidFirstName + "'s ATM card is ready for collection at " + atmCardLocation + ". When she arrives at the Al-Ansari counter, she must click on the following link: " + documentLink + " and show these documents on her phone.";
                parameter2 = "Let us know if you need any assistance.";
            }
        }

        if (isCardCollected(status)) {
            // First payment with ATM card already collected
            parameter1 = "Since " + maidFirstName + "'s ATM card has been picked up, she can use it at any ATM to withdraw her salary.";
            parameter2 = "Let us know if you need any assistance.";
        }

        if (isCardRelocating(status)) {
            if (hasDeliveredEID(housemaid)) {
                // First payment with EID and card being relocated
                parameter1 = "As " + maidFirstName + "'s ATM card is being relocated per your request, she can visit her nearest Al-Ansari branch and show her EID to collect her salary over the counter.";
                parameter2 = "Let us know if you need any assistance.";
            } else {
                // First payment without EID and card being relocated
                parameter1 = "As " + maidFirstName + "'s ATM card is being relocated as per your request, she can visit her nearest Al-Ansari branch to collect her salary over the counter. At the counter, she must click on the following link: " + documentLink + " and show these documents on her phone.";
                parameter2 = "Let us know if you need any assistance.";
            }
        }

        Map<String, String> parameters = new HashMap<>();
        parameters.put("parameter1", parameter1);
        parameters.put("parameter2", parameter2);

        return parameters;
    }

    public String getZajelTrackingStatus(Housemaid housemaid) {

        RepeatEIDRequest repeatRequest = repeatEIDRequestRepository.findFirst1ByHousemaidAndStoppedFalseOrderByCreationDateDesc(housemaid);
        RenewRequest renewRequest = renewVisaRequestRepository.findFirstByHousemaidAndStoppedFalseOrderByCreationDateDesc(housemaid);
        NewRequest newRequest = housemaid.getVisaNewRequest();

        List<EidRequest> maidRequests = Stream.of(newRequest, renewRequest, repeatRequest)
                .filter(Objects::nonNull)
                .sorted((r1, r2) -> r2.getCreationDate().compareTo(r1.getCreationDate()))
                .collect(Collectors.toList());

        for (EidRequest request : maidRequests) {
            if (!request.isCompleted()) { // Active request
                ZajelTrackingStatus zajelTrackingStatus = request.getZajelTrackingStatus();
                if (zajelTrackingStatus != null) {
                    return formatZajelStatus(zajelTrackingStatus, request);
                } else if (request instanceof RepeatEIDRequest) {
                    String eidCardStatus = formatEidStatus(request.getEidStatus());
                    if (StringUtils.isNotBlank(eidCardStatus)) {
                        return eidCardStatus;
                    }
                }
            } else {
                return formatZajelStatus(ZajelTrackingStatus.DELIVERED, null);
            }
        }

        if (!maidRequests.isEmpty()) {
            return formatEidStatus(maidRequests.get(0).getEidStatus());
        }

        return "";
    }

    private String formatEidStatus(String eidStatus) {
        if (StringUtils.isBlank(eidStatus) ||
                Stream.of("Rejected to Receive", "Returned for Modification", "Waiting for residency issue").anyMatch(it -> it.equalsIgnoreCase(eidStatus.trim()))) {
            return "EID Under Processing";
        }
        return "";
    }

    private String formatZajelStatus(ZajelTrackingStatus status, EidRequest request) {
        if (status != null) {
            switch (status) {
                case PICKED_UP:
                    return "Picked up by Zajel";
                case RECEIVED_AT_ZAJEL:
                    return "Arrived at Zajel";
                case OUT_FOR_DELIVERY:
                    return "Out for delivery by Zajel";
                case DELIVERED:
                    return request == null || StringUtils.isNotBlank(request.getClientDeliveryAddress()) ?
                            "Delivered (Family)" : "Delivered (maids.cc)";
            }
        }

        return "";
    }
}
