package com.magnamedia.service.payroll.revamp;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.ExclusionRule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.NationalityCategory;
import com.magnamedia.module.type.VisaType;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PayrollHousemaidExclusionService {

    @Autowired
    TotalSalaryLimitRepository totalSalaryLimitRepository;

    @Autowired
    RenewVisaRequestRepository renewVisaRequestRepository;

    @Autowired
    HousemaidRepository housemaidRepository;

    @Autowired
    NewVisaRequestRepository newVisaRequestRepository;

    @Autowired
    PayrollManagerNoteRepository payrollManagerNoteRepository;

    @Autowired
    HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    PayrollGenerationHelperService payrollGenerationHelperService;

    //1 CC_MAIDS_SALARY_CHECK
    /** Checks if housemaids with new or renewed visa requests exceed salary limits defined in the salary limit configuration. */
    public Set<Housemaid> checkCcMaidsSalary(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd) {
        Set<Housemaid> excludedHousemaids = new HashSet<>(); // List of excluded maids

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.CC_MAIDS_SALARY_CHECK);
        if(housemaidsByRule.isEmpty())
            return null;

        //salaryLimitConfig contains nationality_code, liveOut, VisaType, salary amount
        Map<String, Map<Boolean, Map<VisaType, Double>>> salaryLimitConfig = new HashMap<>();

        // List of maids who have Re-new visa request
        List<Housemaid> renewedVisaMaids =
                renewVisaRequestRepository.findHousemaidsWithCompletedOrActiveRenewRequest(housemaidsByRule);
        // List of maids who have New visa request
        List<Housemaid> newVisaMaids =
                newVisaRequestRepository.findByHousemaidIn(housemaidsByRule);

        // Remove duplicates: newVisaMaids = maids that has new request & not have re-new request.
        newVisaMaids = newVisaMaids.stream()
                .filter(housemaid -> !renewedVisaMaids.contains(housemaid))
                .collect(Collectors.toList());

        //For maids with renew visa request
        for (Housemaid housemaid : renewedVisaMaids) {
            //get the salary from the salaryLimitConfig map, or salary = null
            Double salaryLimit = getSalaryAmountFromSalaryLimitConfig(salaryLimitConfig, housemaid, VisaType.RENEW);

            //If salary not found in the salaryLimitConfig map, then retrieve it from the db & save it in salaryLimitConfig
            if (salaryLimit == null) {
                TotalSalaryLimit salaryLimitRecord = getSalaryLimitRecord(housemaid, VisaType.RENEW);

                // just in case salaryLimitRecord == null
                if (salaryLimitRecord == null)
                    continue;

                String nationality;
                if (salaryLimitRecord.getNationalityCategory().equals(NationalityCategory.SPECIFIC_NATIONALITY)) {
                    nationality = salaryLimitRecord.getNationality().getCode();
                } else if (salaryLimitRecord.getNationalityCategory().equals(NationalityCategory.AFRICAN_NATIONALITIES)) {
                    nationality = "african_nationalities";
                } else {
                    nationality = "other_nationalities";
                }

                Boolean liveOut = salaryLimitRecord.getLiveOut();
                salaryLimit = salaryLimitRecord.getAmount();

                salaryLimitConfig
                        .computeIfAbsent(nationality, n -> new HashMap<>())
                        .computeIfAbsent(liveOut, l -> new HashMap<>())
                        .put(VisaType.RENEW, salaryLimit);

            }

            if (housemaid.getBasicSalary() != null && housemaid.getBasicSalary() > salaryLimit)
                excludedHousemaids.add(housemaid);
        }

        for (Housemaid housemaidWithNewVisaRequest : newVisaMaids) {
            //get the salary from the salaryLimitConfig map, or salary = null
            Double salaryLimit = getSalaryAmountFromSalaryLimitConfig(salaryLimitConfig, housemaidWithNewVisaRequest, VisaType.NEW);

            //If salary not found in the salaryLimitConfig map, then retrieve it from the db & save it in salaryLimitConfig
            if (salaryLimit == null) {
                TotalSalaryLimit salaryLimitRecord = getSalaryLimitRecord(housemaidWithNewVisaRequest, VisaType.NEW);

                // just in case salaryLimitRecord == null
                if (salaryLimitRecord == null)
                    continue;

                String nationality;
                if (salaryLimitRecord.getNationalityCategory().equals(NationalityCategory.SPECIFIC_NATIONALITY)) {
                    nationality = salaryLimitRecord.getNationality().getCode();
                } else if (salaryLimitRecord.getNationalityCategory().equals(NationalityCategory.AFRICAN_NATIONALITIES)) {
                    nationality = "african_nationalities";
                } else {
                    nationality = "other_nationalities";
                }

                Boolean liveOut = salaryLimitRecord.getLiveOut();
                salaryLimit = salaryLimitRecord.getAmount();

                salaryLimitConfig
                        .computeIfAbsent(nationality, n -> new HashMap<>())
                        .computeIfAbsent(liveOut, l -> new HashMap<>())
                        .put(VisaType.NEW, salaryLimit);

            }

            if (housemaidWithNewVisaRequest.getBasicSalary() != null && housemaidWithNewVisaRequest.getBasicSalary() > salaryLimit)
                excludedHousemaids.add(housemaidWithNewVisaRequest);
        }

        return excludedHousemaids;
    }

    //2 INVALID_AIRFARE_TICKET
    //TODO:REVAMP
    public Map<String, Set<?>> checkInvalidAirfareTicket(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.INVALID_AIRFARE_TICKET);
        if(housemaidsByRule.isEmpty())
            return null;

        // Get manager notes With Air Fair Ticket addition reason
        PicklistItem airFairTicketItem =
                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PayrollManagementModule.PICKLIST_ITEM_MANAGER_NOTE_AIRFARE_TICKET_ADDITION_CODE);

        // Get parameters values:
        // Get default ticket allowance amount parameter value
        Double defaultTicketAllowanceAmount = Double.parseDouble(
                Setup.getParameter(Setup.getModule("visa"),
                        "default_ticket_allowance_amount"));

        Integer timeFrameMonthsCount = Integer.parseInt(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.TIME_FRAME_MONTHS_PARAM));

        // List of manager notes with air fair ticket addition reason
        List<PayrollManagerNote> notesWithAirFairTicket =
                payrollManagerNoteRepository.getByNoteTypeAndAdditionReasonAndNoteDateInHousemaidList(
                        housemaidsByRule, AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                        airFairTicketItem,payrollStart, payrollEnd);

        //List of maids with not completed renew requests
        Set<Housemaid> maidsWithRenewRequest = renewVisaRequestRepository.findMaidsWithRenewRequest(housemaidsByRule);

        for(PayrollManagerNote note : notesWithAirFairTicket){
            if(!note.getHousemaid().getHousemaidType().equals(HousemaidType.MAID_VISA)) { //For MAID_CC

                //Get scheduled Annual Vacation Amount for the housemaid's nationality
                Tag tagValue = note.getHousemaid().getNationality().getTagValue("ScheduledAnnualVacationAmount");
                Double scheduledAnnualVacationAmount = null;
                if (tagValue != null)
                    scheduledAnnualVacationAmount = Double.parseDouble(tagValue.getValue());

                //1- Check if housemaid has "air fair ticket" manager note with amount greater than what she is supposed to take.
                if ((scheduledAnnualVacationAmount != null && note.getAmount() > scheduledAnnualVacationAmount)
                        || (scheduledAnnualVacationAmount == null && note.getAmount() > defaultTicketAllowanceAmount)) {
                    excludedHousemaids.add(note.getHousemaid());
                    notesOfExcludedMaids.add(note);
                    continue;
                }

                //2- Check if housemaid has "air fair ticket" manager note and has no active renewal visa request
                if (!maidsWithRenewRequest.contains(note.getHousemaid())) {
                    excludedHousemaids.add(note.getHousemaid());
                    notesOfExcludedMaids.add(note);
                    continue;
                }

                //3- Check if the maid has "air fair ticket" manager note, and she had another one in the past timeFrameMonthsCount
                //TODO:REVAMP Shahd modify the query to get the manager notes only for the previous 20 months
                List<PayrollManagerNote> maidsHaveAirFareTicketNoteInPreviousMonths =
                        payrollManagerNoteRepository.findNotesByHousemaidAndNoteTypeAndAdditionReasonAndNoteDate(
                                note.getHousemaid(), AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                                airFairTicketItem, DateUtil.addMonths(payrollStart, (-1 * timeFrameMonthsCount)), payrollEnd);
                /* todo: recheck when working on payroll revamp part 3
                toDate = payrollEnd, if i want to combine point 3 & 4 in one (means if the reason is not needed)
                But if we need reason -> then toDate = payrollStart as it covers only point3
                 */

                if (maidsHaveAirFareTicketNoteInPreviousMonths != null && !maidsHaveAirFareTicketNoteInPreviousMonths.isEmpty()) {
                    excludedHousemaids.add(note.getHousemaid());
                    notesOfExcludedMaids.add(note);
                    notesOfExcludedMaids.addAll(maidsHaveAirFareTicketNoteInPreviousMonths);
                    continue;
                }

                //TODO:REVAMP should be fixed (query and duplication)
                //4- Check if maid has 2 manager notes with “airfare tickets” reason within the same payroll month
                List<PayrollManagerNote> housemaidsHaveAirFairNote =
                        payrollManagerNoteRepository.findNotesByHousemaidAndNoteTypeAndAdditionReasonAndNoteDate(
                                note.getHousemaid(), AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                                airFairTicketItem, payrollStart, payrollEnd);

                if(housemaidsHaveAirFairNote != null && !housemaidsHaveAirFairNote.isEmpty()){
                    excludedHousemaids.add(note.getHousemaid());
                    notesOfExcludedMaids.addAll(housemaidsHaveAirFairNote);
                }
            }else{ //if maid type = MAID_VISA, then the maid is excluded as maid visa must not have air fair manager note
                excludedHousemaids.add(note.getHousemaid());
            }
        }

        result.put("excludedHousemaids", excludedHousemaids);
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);
        return result;
    }

    //3 NET_PAYOUT_VIOLATION
    public Set<Housemaid> checkNetPayoutViolation(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.NET_PAYOUT_VIOLATION);
        if(housemaidsByRule.isEmpty())
            return null;

        Double ccNewMultiplierValue = Double.parseDouble(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.CC_NEW_MULTIPLIER_PARAM));

        Double ccRenewalMultiplierValue = Double.parseDouble(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.CC_RENEWAL_MULTIPLIER_PARAM));

        Double mvMultiplierValue = Double.parseDouble(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.MV_MULTIPLIER_PARAM));

        //For CC_MAIDS And MV_MAIDS //TODO:REVAMP
        List<Housemaid> maidsWithNetPayoutIssue = housemaidRepository.findMaidsWithExcessNetPayout(housemaidsByRule,
                rule.getPayrollMonth(),
                ccNewMultiplierValue,
                ccRenewalMultiplierValue,
                mvMultiplierValue);
        excludedHousemaids.addAll(maidsWithNetPayoutIssue);

        return excludedHousemaids;
    }

    //4 MAIDS_WITH_DUPLICATE_ADDITIONS
    public Map<String, Set<?>> checkMaidsWithDuplicateAdditions(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.MAIDS_WITH_DUPLICATE_ADDITIONS);
        if(housemaidsByRule.isEmpty())
            return null;

        //timeIntervalInMinutes parameter value
        Integer timeIntervalInMinutes = Integer.parseInt(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.TIME_INTERVAL_PARAM));

        //TODO:REVAMP
        List<PayrollManagerNote> notesOfMaidsWithDuplicateNotesInTimeRange = payrollManagerNoteRepository.findDuplicateNotesForMaidsInTimeInterval(
                housemaidsByRule,
                AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                payrollStart,
                payrollEnd,
                timeIntervalInMinutes
        );

        if (!notesOfMaidsWithDuplicateNotesInTimeRange.isEmpty()) {
            for(PayrollManagerNote note : notesOfMaidsWithDuplicateNotesInTimeRange) {
                excludedHousemaids.add(note.getHousemaid());
                notesOfExcludedMaids.add(note);
            }
        }
        result.put("excludedHousemaids", excludedHousemaids);
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);
        return result;
    }

    //5 CC_MAIDS_WITH_NO_LOAN_REPAYMENT
    public Set<Housemaid> checkCCMaidsWithNoLoanRepayment(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.CC_MAIDS_WITH_NO_LOAN_REPAYMENT);
        if(housemaidsByRule.isEmpty())
            return null;

        // List of Housemaid Payroll Logs that has remainingLoan > 0, with no repayment this month
        List<HousemaidPayrollLog> logsWithLoanBalanceAndNoRepayment =
                housemaidPayrollLogRepository.findLogsWithLoanBalanceAndNoRepayment(rule.getPayrollMonth(), housemaidsByRule);

        Housemaid housemaid;

        for(HousemaidPayrollLog log : logsWithLoanBalanceAndNoRepayment){
            housemaid = log.getHousemaid();

            // Check if she has net payout (total salary) > her accommodation salary
            // then exclude her
            if(housemaid.getAccommodationSalary() != null && (log.getTotalSalary() > housemaid.getAccommodationSalary())){
                excludedHousemaids.add(housemaid);
                continue;
            }

            // Set values of groupOneDays & groupFiveDays By calling getSalaryBreakDownForHousemaid() method
            LocalDate payrollMonthL = new LocalDate(rule.getPayrollMonth());
            Map<String, Object> breakDown = payrollGenerationHelperService.getSalaryBreakDownForHousemaid(housemaid, payrollMonthL);
            Integer groupOneDays = (Integer) breakDown.get("group1Days");
            Integer groupFiveDays = (Integer) breakDown.get("group5Days");

            // Check if she has spent more than 21 days with a client OR this is not her first working month
            // then exclude her
            //todo: recheck it with Hakiim
            if(groupOneDays + groupFiveDays > 21 ||
                    (housemaid.getStartDate() != null && housemaid.getStartDate().before(payrollMonthL.withDayOfMonth(1).toDate()) ||
                            (housemaid.getReplacementSalaryStartDate() != null &&
                                    housemaid.getReplacementSalaryStartDate().before(payrollMonthL.withDayOfMonth(1).toDate())))){

                excludedHousemaids.add(housemaid);
            }
        }

        return excludedHousemaids;
    }

    //6 ACCOUNT_WITH_AGENT_AND_ANSARI_UNIQUE_AUDIT
    public Set<Housemaid> checkAccountWithAgentAndAnsariUniqueAudit(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.ACCOUNT_WITH_AGENT_AND_ANSARI_UNIQUE_AUDIT);
        if(housemaidsByRule.isEmpty())
            return null;

        List<Housemaid> housemaidsWithNotUniqueEmployeeAccountWithAgent = housemaidRepository.findHousemaidsWithNotUniqueEmployeeAccountWithAgent(housemaidsByRule);
        List<Housemaid> housemaidsWithNotUniqueAnsariUniqueId = housemaidRepository.findHousemaidsWithNotUniqueAnsariUniqueId(housemaidsByRule);

        if(!housemaidsWithNotUniqueEmployeeAccountWithAgent.isEmpty())
            excludedHousemaids.addAll(housemaidsWithNotUniqueEmployeeAccountWithAgent);
        if(!housemaidsWithNotUniqueAnsariUniqueId.isEmpty())
            excludedHousemaids.addAll(housemaidsWithNotUniqueAnsariUniqueId);

        return excludedHousemaids;
    }

    //7 INVALID_PRORATED_SALARY_ADDITION
    public Map<String, Set<?>> checkInvalidProratedSalaryAddition(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.INVALID_PRORATED_SALARY_ADDITION);
        if(housemaidsByRule.isEmpty())
            return null;

        // Set the value to 26th of the previous month
        Date previousMonth = DateUtil.addMonths(rule.getPayrollMonth(), -1);
        Date dayOf26th =  new LocalDate(previousMonth).withDayOfMonth(26).toDate();

        PicklistItem proratedSalary =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        "prorated_salary");

        // List of housemaids whose start date is before the 26th of the previous payroll month
        // and who have an "addition" payroll manager note with the "prorated salary" reason added to the current payroll month
        List<PayrollManagerNote> notesOfMaidsWithProratedSalary =
                payrollManagerNoteRepository.getManagerNoteByHousemaidInAndStartDateBeforeDate(housemaidsByRule,
                        dayOf26th, payrollStart, payrollEnd,
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, proratedSalary);

        if(notesOfMaidsWithProratedSalary != null && !notesOfMaidsWithProratedSalary.isEmpty()) {
            for(PayrollManagerNote note : notesOfMaidsWithProratedSalary) {
                excludedHousemaids.add(note.getHousemaid());
                notesOfExcludedMaids.add(note);
            }
        }

        result.put("excludedHousemaids", excludedHousemaids); //All maidsWithRafflePrize will be excluded
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);

        return result;
    }

    //8 REFERRAL_BONUS_AUDIT
    public Map<String, Set<?>> checkReferralBonusAudit(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.REFERRAL_BONUS_AUDIT);
        if(housemaidsByRule.isEmpty())
            return null;

        PicklistItem referralBonusItem = PicklistHelper.getItem("HousemaidPurposesForBonusAdditionalDescription", "referral_bonus");

        // Check#1 (no.3 in PAY-1734 specs) :
        //Check if maid has 2 or more manager notes with “referral_bonus” addition reason within the same payroll month
        List<Housemaid> housemaidsWithMultipleReferralBonusNotes =
                payrollManagerNoteRepository.findHousemaidsWithMultiplePayrollManagerNotes(
                        housemaidsByRule, AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                        referralBonusItem, payrollStart, payrollEnd);

        excludedHousemaids.addAll(housemaidsWithMultipleReferralBonusNotes);

        // Remove the excluded housemaids from the included maids list
        housemaidsByRule.removeAll(excludedHousemaids);

        //Map to have nationalities referral bonus amounts
        Map<String, Double> nationalityReferralBonusAmountMap = new HashMap<>(); // <Nationality Code, Referral Bonus Amount>

        //Get referral default bonus amount parameter value
        Double referralDefaultBonusAmount = Double.parseDouble(
                Setup.getParameter(Setup.getModule("recruitment"),
                        "referral_default_bonus"));

        //List of "addition" payroll manager notes with "referral bonus" reason
        List<PayrollManagerNote> notesWithReferralBonus =
                payrollManagerNoteRepository.getByNoteTypeAndAdditionReasonAndNoteDateInHousemaidList(
                        housemaidsByRule, AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                        referralBonusItem,payrollStart, payrollEnd);

        //Get allowed referral nationalities codes from the parameter
        String allowedReferralNationalitiesString = Setup.getParameter(Setup.getModule("recruitment"),
                "ALLOWED_REFERRAL_NATIONALITIES");

        if(allowedReferralNationalitiesString != null & !allowedReferralNationalitiesString.isEmpty()) {
            // Convert allowed referral nationalities to a Set for efficient lookup
            Set<String> allowedReferralNationalities = Arrays.stream(allowedReferralNationalitiesString.split(";"))
                    .map(String::trim)
                    .collect(Collectors.toSet());

            for (PayrollManagerNote note : notesWithReferralBonus) {
                Housemaid housemaid = note.getHousemaid();
                if (housemaid != null && housemaid.getNationality() != null) {
                    String nationalityCode = housemaid.getNationality().getCode();

                    // Check#2 (no.2 in PAY-1734 specs) :
                    if (!allowedReferralNationalities.contains(nationalityCode)) {
                        // If nationality is not in the allowed list, exclude this housemaid
                        excludedHousemaids.add(housemaid);
                        continue;
                    }

                    // Check#3 (no.1 in PAY-1734 specs) :
                    // Set maid's max referral bonus amount
                    Double maxBonusAmount;
                    if (nationalityReferralBonusAmountMap.containsKey(nationalityCode)) {
                        maxBonusAmount = nationalityReferralBonusAmountMap.get(nationalityCode);
                    } else {
                        nationalityReferralBonusAmountMap.put(nationalityCode, referralDefaultBonusAmount);
                        maxBonusAmount = referralDefaultBonusAmount;
                    }

                    // Exclude housemaid if note amount exceeds max allowable bonus
                    if (note.getAmount() > maxBonusAmount) {
                        excludedHousemaids.add(housemaid);
                    }
                }
            }
        }

        result.put("excludedHousemaids", excludedHousemaids); //All maidsWithRafflePrize will be excluded
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);

        return result;
    }

    //9 RAFFLE_PRIZE_LIMIT_VIOLATIONS
    public Map<String, Set<?>> checkRafflePrizeLimitViolations(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        //Excluded maids Set
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        //Included maids List
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.RAFFLE_PRIZE_LIMIT_VIOLATIONS);
        if(housemaidsByRule.isEmpty())
            return null;

        //Raffle Reason PicklistItem
        PicklistItem raffleReason = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE, "raffle_prize");

        Integer raffle1stPrizeWinnersCount = Integer.parseInt(
                Setup.getParameter(Setup.getModule("staffmgmt"),
                        "raffle_first_prize_winners"));

        Integer raffle2ndPrizeWinnersCount = Integer.parseInt(
                Setup.getParameter(Setup.getModule("staffmgmt"),
                        "raffle_second_prize_winners"));

        int totalAllowedWinners = raffle1stPrizeWinnersCount + raffle2ndPrizeWinnersCount;

        Double totalRafflePrizeLimitAmount = Double.parseDouble(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.TOTAL_RAFFLE_PRIZE_LIMIT_PARAM));

        Double raffle1stPrizeAmount = Double.parseDouble(
                Setup.getParameter(Setup.getModule("staffmgmt"),
                        "raffle_first_prize"));

        // Fetch the list of notes with "Raffle Prize" reason within this payroll month for included housemaids
        //todo: check the used query with Hakiim.
        List<PayrollManagerNote> rafflePrizeNotes =
                payrollManagerNoteRepository.getByNoteTypeAndAdditionReasonAndNoteDateInHousemaidList(
                        housemaidsByRule, AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                        raffleReason,payrollStart, payrollEnd);

        // Collect housemaids with "Raffle Prize" reason into a set to remove duplicates
        Set<Housemaid> maidsWithRafflePrize = rafflePrizeNotes.stream()
                .map(PayrollManagerNote::getHousemaid)
                .collect(Collectors.toSet());

        // Check#1 (no.1 in PAY-1734 specs) :
        // This check is done on All maids With Raffle Prize in the current payroll month
        // Check if the count of unique housemaids with raffle prizes exceeds the allowed winners count
        if (maidsWithRafflePrize.size() > totalAllowedWinners) {
            excludedHousemaids.addAll(maidsWithRafflePrize);
            notesOfExcludedMaids.addAll(rafflePrizeNotes);

            result.put("excludedHousemaids", excludedHousemaids); //All maidsWithRafflePrize will be excluded
            result.put("notesOfExcludedMaids", notesOfExcludedMaids);

            return result;
        }

        // Check#2 (no.3 in PAY-1734 specs) :
        // Calculate the total raffle prize addition for the notes in this payroll period
        double totalRafflePrizeAddition = rafflePrizeNotes.stream()
                .mapToDouble(PayrollManagerNote::getAmount)
                .sum();

        //This check is done on all raffle prize manager notes in the current payroll month)
        // Check if the total raffle prize addition exceeds the set limit
        if (totalRafflePrizeAddition > totalRafflePrizeLimitAmount) {
            excludedHousemaids.addAll(maidsWithRafflePrize); //All maidsWithRafflePrize will be excluded
            notesOfExcludedMaids.addAll(rafflePrizeNotes);
        }

        // Check#3 (no.2 in PAY-1734 specs) :
        // Set of first-prize winners housemaids
        Set<Housemaid> firstPrizeWinners = rafflePrizeNotes.stream()
                .filter(note -> raffle1stPrizeAmount.equals(note.getAmount())) // Only include notes with amount = the first prize amount
                .map(PayrollManagerNote::getHousemaid)
                .collect(Collectors.toSet());

        // Check if the first prize winners count exceeds the allowed limit
        if (firstPrizeWinners.size() > raffle1stPrizeWinnersCount) {
            excludedHousemaids.addAll(firstPrizeWinners);
        }

        result.put("excludedHousemaids", excludedHousemaids);
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);

        return result;
    }

    //10 MAIDS_WITH_MORE_THAN_THREE_ADDITIONS_AUDIT
    public Map<String, Set<?>> checkMaidsWithMoreThanThreeAdditionsAudit(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.MAIDS_WITH_MORE_THAN_THREE_ADDITIONS_AUDIT);
        if(housemaidsByRule.isEmpty())
            return null;

        // List of ccMaids with three or more manager notes of type “Addition”
        // that are not added automatically through the system (added by a user)
        List<PayrollManagerNote> notesOfCcMaids = payrollManagerNoteRepository.findNotesWith3orMorePayrollManagerNotesNotAddedAutomatically(housemaidsByRule,
                AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                payrollStart, payrollEnd);

        if(notesOfCcMaids != null && !notesOfCcMaids.isEmpty()) {
            for(PayrollManagerNote note : notesOfCcMaids) {
                excludedHousemaids.add(note.getHousemaid());
                notesOfExcludedMaids.add(note);
            }
        }

        result.put("excludedHousemaids", excludedHousemaids);
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);

        return result;
    }

    //11 CC_HOUSEMAIDS_WITH_NOT_ELIGIBLE_FOR_PAYROLL_STATUS
    public Set<Housemaid> checkCcHousemaidsWithNotEligibleForPayrollStatus(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.CC_HOUSEMAIDS_WITH_NOT_ELIGIBLE_FOR_PAYROLL_STATUS);
        if(housemaidsByRule.isEmpty())
            return null;

        //Get statuses eligible for payroll from the parameter
        String statusesEligibleForPayrollString = Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.STATUSES_ELIGIBLE_FOR_PAYROLL_PARAM); // TODO: Recheck the values in param again as they are not clear (the yellow part in specs)

        // Convert allowed referral statuses to a Set for efficient lookup
        Set<String> statusesEligibleForPayroll = Arrays.stream(statusesEligibleForPayrollString.split(";"))
                .map(String::trim)
                .collect(Collectors.toSet());

        for (Housemaid housemaid: housemaidsByRule) {
            HousemaidStatus maidStatus = housemaid.getStatus();
            if (maidStatus != null && !statusesEligibleForPayroll.contains(maidStatus.toString())) {
                // If status is not in the eligible statuses for payroll list, exclude this housemaid
                excludedHousemaids.add(housemaid);
            }
        }

        return excludedHousemaids;
    }

    //12 MV_MAID_WITH_NO_ELIGIBLE_ADDITION
    public Map<String, Set<?>> checkMvMaidWithNoEligibleAddition(List<Housemaid> housemaids, MonthlyPaymentRule rule, Boolean shouldCreate, Date payrollStart, Date payrollEnd){
        Set<Housemaid> excludedHousemaids = new HashSet<>();
        Set<PayrollManagerNote> notesOfExcludedMaids = new HashSet<>();
        Map<String, Set<?>> result = new HashMap<>();

        // List of included maids
        List<Housemaid> housemaidsByRule = getMaidsListByRule(housemaids, ExclusionRule.MV_MAID_WITH_NO_ELIGIBLE_ADDITION);
        if(housemaidsByRule.isEmpty())
            return null;

        PicklistItem referralBonusItem = PicklistHelper.getItem("HousemaidPurposesForBonusAdditionalDescription", "referral_bonus");

        // Get housemaids who have payroll manager notes with addition reason != "referral bonus"
        List<PayrollManagerNote> notesOfMvMaids =
                payrollManagerNoteRepository.getByNoteTypeAndAdditionReasonNotEqualAndNoteDateInHousemaidList(
                        housemaidsByRule, AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                        referralBonusItem,payrollStart, payrollEnd);

        if(notesOfMvMaids != null && !notesOfMvMaids.isEmpty()) {
            for(PayrollManagerNote note : notesOfMvMaids) {
                excludedHousemaids.add(note.getHousemaid());
                notesOfExcludedMaids.add(note);
            }
        }

        result.put("excludedHousemaids", excludedHousemaids);
        result.put("notesOfExcludedMaids", notesOfExcludedMaids);

        return result;

    }

    /*Helper method for the 1st Housemaid Exclusion Rule CC_MAIDS_SALARY_CHECK,
    to get salary from salaryLimitConfig if found in it, else salary = null */
    private Double getSalaryAmountFromSalaryLimitConfig(Map<String, Map<Boolean, Map<VisaType, Double>>> salaryLimitConfig, Housemaid housemaid, VisaType visaType){
        Double salary = null;

        if(housemaid.getNationality() != null && housemaid.getLiveOut() != null)
            salary = Optional.ofNullable(salaryLimitConfig.get(housemaid.getNationality().getCode()))
                .map(liveOutMap -> liveOutMap.get(housemaid.getLiveOut()))
                .map(visaMap -> visaMap.get(visaType))
                .orElse(null);

        if (salary == null) {
            if (housemaid.getNationality() != null && housemaid.getNationality().hasTag("african_nationality")) {
                salary = Optional.ofNullable(salaryLimitConfig.get("african_nationalities"))
                        .map(liveOutMap -> liveOutMap.get(housemaid.getLiveOut()))
                        .map(visaMap -> visaMap.get(visaType))
                        .orElse(null);
            } else {
                salary = Optional.ofNullable(salaryLimitConfig.get("other_nationalities"))
                        .map(liveOutMap -> liveOutMap.get(housemaid.getLiveOut()))
                        .map(visaMap -> visaMap.get(visaType))
                        .orElse(null);
            }
        }
        return salary;
    }

    private TotalSalaryLimit getSalaryLimitRecord(Housemaid housemaid, VisaType visaType){

        List<TotalSalaryLimit> salaryLimitRecord = null;

        if(housemaid.getNationality() == null)
            salaryLimitRecord = totalSalaryLimitRepository.findByNationalityCategoryAndLiveOutAndVisaType(NationalityCategory.OTHER_NATIONALITIES, housemaid.getLiveOut(), visaType);
        else // get SPECIFIC_NATIONALITY record
            salaryLimitRecord = totalSalaryLimitRepository.findByNationalityCategoryAndNationalityAndLiveOutAndVisaType(NationalityCategory.SPECIFIC_NATIONALITY, housemaid.getNationality(), housemaid.getLiveOut(), visaType);

        if(salaryLimitRecord == null || salaryLimitRecord.isEmpty()) {
            if (housemaid.getNationality().hasTag("african_nationality")) {
                // AFRICAN_NATIONALITIES record
                salaryLimitRecord = totalSalaryLimitRepository.findByNationalityCategoryAndLiveOutAndVisaType(NationalityCategory.AFRICAN_NATIONALITIES, housemaid.getLiveOut(), visaType);
            } else {
                // OTHER_NATIONALITIES record
                salaryLimitRecord = totalSalaryLimitRepository.findByNationalityCategoryAndLiveOutAndVisaType(NationalityCategory.OTHER_NATIONALITIES, housemaid.getLiveOut(), visaType);
            }
        }

        if(salaryLimitRecord != null && !salaryLimitRecord.isEmpty()) {
            return salaryLimitRecord.get(0);
        }

        return null;
    }

    /* Helper method, to get the housemaids list for the Housemaid Exclusion Rule
    * It filters based on forMaidVisa condition and collect matching housemaids into a List
    * -- forMaidVisa Logic: true -> only maid_visa, false -> only cc_maids, null -> all maids in the list */
    private List<Housemaid> getMaidsListByRule(List<Housemaid> housemaids, ExclusionRule exclusionRule){

        Boolean forMaidVisa = exclusionRule.getForMaidVisa();
        List<Housemaid> housemaidsByRule = housemaids.stream()
                .filter(housemaid ->
                        forMaidVisa == null ||  // If forMaidVisa is null, include all housemaids (MAID_ISA & MAID_CC)
                                (forMaidVisa.equals(true) && housemaid.getHousemaidType().equals(HousemaidType.MAID_VISA)) ||
                                (forMaidVisa.equals(false) && !housemaid.getHousemaidType().equals(HousemaidType.MAID_VISA))
                )
                .collect(Collectors.toList());
        return housemaidsByRule;
    }
}
