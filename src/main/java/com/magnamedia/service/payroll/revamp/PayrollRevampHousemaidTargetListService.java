package com.magnamedia.service.payroll.revamp;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class PayrollRevampHousemaidTargetListService {
    private static final Logger logger =
            Logger.getLogger(PayrollRevampHousemaidTargetListService.class.getName());

    @Autowired
    private LockDateService lockDateService;

    public List<Housemaid> getFinalHousemaidTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter = !PayrollType.SECONDARY.equals(monthlyPaymentRule.getPayrollType())
                ? getFinalHousemaidTargetListFilter(monthlyPaymentRule)
                : getFinalHousemaidIncludedTargetListFilter(monthlyPaymentRule);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();
    }

    /**
     * return the list of housemaid that will be included
     *
     * @param monthlyPaymentRule
     * @return
     */
    public List<Housemaid> getFinalHousemaidIncludedTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        // if not targeting housemaid
        if (!monthlyPaymentRule.isTargetingHousemaid())
            return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy(getFinalHousemaidIncludedTargetListFilter(monthlyPaymentRule));

        query.sortBy("name", true);
        return query.execute();
    }

    public List<Housemaid> getAuditingHousemaidTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter;
        if(monthlyPaymentRule.isSecondaryMonthlyRule())
            selectFilter = getAuditingHousemaidIncludedTargetListFilter(monthlyPaymentRule);
        else
            selectFilter = getAuditingHousemaidTargetListFilter(monthlyPaymentRule);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();

    }

    public List<Housemaid> getAuditingHousemaidIncludedTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter = getAuditingHousemaidIncludedTargetListFilter(monthlyPaymentRule);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();

    }

    /////////helper methods//////////

    public SelectFilter getAuditingHousemaidTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {

        SelectFilter maidCCFilter = new SelectFilter();
        SelectFilter maidVisaFilter = new SelectFilter();

        // Maid CC Filter
        if(monthlyPaymentRule.isTargetingMaidCC()) {
            //get all Housemaid Statuses that we need to generate payroll log for it
            maidCCFilter = new SelectFilter("status", "IN", getFinalHousemaidsStatusesMustBeGenerated(monthlyPaymentRule));
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);
        }

        // Maid Visa Filter
        if(monthlyPaymentRule.isTargetingMaidVisa()){

            maidVisaFilter = new SelectFilter("housemaidType", "=", HousemaidType.MAID_VISA);
        }

        if (monthlyPaymentRule.isTargetingMaidCCOnly()) {
            maidVisaFilter = null;
        } else if (monthlyPaymentRule.isTargetingMaidVisaOnly()) {
            maidCCFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;


        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude pre-paid vacation housemaids
        PicklistItem vacationType =
                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        "pre-paid_vacation");
        List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                        lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
        if (!prePaidVacationMaids.isEmpty()) excludedHousemaids.addAll(prePaidVacationMaids);

        if (!excludedHousemaids.isEmpty()) baseFilter.and("id", "NOT IN", excludedHousemaids);

        //startDate && replacementSalaryStartDate must be before 27 of the month
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public SelectFilter getAuditingHousemaidIncludedTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();


        // MAID CC Filter
        if (monthlyPaymentRule.isTargetingMaidCC()) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            //delayed items
            List<PicklistItem> delayItems = HousemaidPayrollPaymentServiceV2.getDelayedItems();

            SelectFilter wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            SelectFilter payCashHousemaidsFilter = new SelectFilter(
                    new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                            .or("status", "=", HousemaidStatus.AVAILABLE)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                            .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                            //Jirra ACC-1005
                            //.or("status", "=", HousemaidStatus.ON_VACATION)
                            .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                            .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                            .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                            //Jirra ACC-1223
                            .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                            .or(
                                    new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                            //Jirra ACC-1005
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                            .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                            .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                    .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                    .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                            .and("pendingSince", ">=", new LocalDate().toDate())
                                                            .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                    .and("pendingUntil", ">=", payrollStart))))
                            ));

            //filter all housemaids
            maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));



            // exclude pre-paid vacation housemaids
            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);

        }

        Set<Long> excludedHousemaids = new HashSet<>();


        // exclude transferred before housemaids But not those who have important manager notes
        List<PicklistItem> mustBePaidManagerNotes = HousemaidPayrollPaymentServiceV2.getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        // MAID VISA Filter
        if (monthlyPaymentRule.isTargetingMaidVisa()) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);

            List<Long> paidHousemaidsIds = HousemaidPayrollPaymentServiceV2.getPaidMVMaidsIdsNewV2(monthlyPaymentRule, transferredIds, true);

            // add the MV maids who were included by the auditor
            if (monthlyPaymentRule.getMustIncludedMVInfoList().size() > 0)
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream().map(ExcludedMVInfo::getHousemaidId).collect(Collectors.toList()));

            if(paidHousemaidsIds.size() > 0)
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else
                maidVisaFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        //excluded
        excludedHousemaids.addAll(excludedCCHousemaids);

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        //withMolNumber filter
        baseFilter.and("withMolNumber", "=", true);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    /**
     * return the filter Of PRIMARY Salary of maids that we will generate a Payroll Log for them
     *
     * @param monthlyPaymentRule
     * @return
     */
    public SelectFilter getFinalHousemaidTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {

        SelectFilter maidCCFilter = new SelectFilter();
        SelectFilter maidVisaFilter = new SelectFilter();

        // Maid CC Filter
        if(monthlyPaymentRule.isTargetingMaidCC()) {
            //get all Housemaid Statuses that we need to generate payroll log for it
            maidCCFilter = new SelectFilter("status", "IN", getFinalHousemaidsStatusesMustBeGenerated(monthlyPaymentRule));
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);


        }

        // Maid Visa Filter
        if(monthlyPaymentRule.isTargetingMaidVisa()){

            maidVisaFilter = new SelectFilter("housemaidType", "=", HousemaidType.MAID_VISA);
        }

        if (monthlyPaymentRule.isTargetingMaidCCOnly()) {
            maidVisaFilter = null;
        } else if (monthlyPaymentRule.isTargetingMaidVisaOnly()) {
            maidCCFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;

        //MOL Filter
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            baseFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }


        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude pre-paid vacation housemaids
        PicklistItem vacationType =
                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        "pre-paid_vacation");
        List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                        lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
        if (!prePaidVacationMaids.isEmpty()) excludedHousemaids.addAll(prePaidVacationMaids);

        if (!excludedHousemaids.isEmpty()) baseFilter.and("id", "NOT IN", excludedHousemaids);

        //startDate && replacementSalaryStartDate must be before 27 of the month
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    /**
     * return the filter of Included maids that will get their salaries
     *
     * @param monthlyPaymentRule
     * @return
     */
    public SelectFilter getFinalHousemaidIncludedTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();


        // MAID CC Filter
        if (monthlyPaymentRule.isTargetingMaidCC()) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            //delayed items
            List<PicklistItem> delayItems = getDelayedItems();

            SelectFilter wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            SelectFilter payCashHousemaidsFilter = new SelectFilter(
                    new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                            .or("status", "=", HousemaidStatus.AVAILABLE)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                            .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                            //Jirra ACC-1005
                            //.or("status", "=", HousemaidStatus.ON_VACATION)
                            .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                            .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                            .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                            //Jirra ACC-1223
                            .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                            .or(
                                    new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                            //Jirra ACC-1005
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                            .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                            .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                    .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                    .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                            .and("pendingSince", ">=", new LocalDate().toDate())
                                                            .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                    .and("pendingUntil", ">=", payrollStart))))
                            ));


            if (monthlyPaymentRule.isTargetingInAccommodationOnly()) {
                maidCCFilter.and(payCashHousemaidsFilter);
            } else if (monthlyPaymentRule.isTargetingWithClientOnly()) {
                maidCCFilter.and(wpsHousemaidsFilter);
            } else {
                maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));
            }


            // exclude pre-paid vacation housemaids
            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);

        }

        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude transferred before housemaids But not those who have important manager notes
        List<PicklistItem> mustBePaidManagerNotes = getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        // MAID VISA Filter
        if (monthlyPaymentRule.isTargetingMaidVisa()) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);

            List<Long> paidHousemaidsIds = getPaidMVMaidsIdsNew(monthlyPaymentRule, transferredIds, false);

            // add the MV maids who were included by the auditor
            if (monthlyPaymentRule.getMustIncludedMVInfoList().size() > 0)
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream().filter(x-> x.getPayrollMonth().equals(monthlyPaymentRule.getPayrollMonth())).map(ExcludedMVInfo::getHousemaidId).collect(Collectors.toList()));

            if(paidHousemaidsIds.size() > 0)
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else if (maidVisaFilter != null)
                maidVisaFilter = null;
            else
                return null;
        }


        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else
            baseFilter = new SelectFilter(maidVisaFilter);

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        //MOL Filter
        if (monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL || monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITHOUT_MOL) {
            baseFilter.and("withMolNumber", "=", monthlyPaymentRule.getMolType() == AbstractPaymentRule.MolType.WITH_MOL);
        }

        //excluded
        excludedHousemaids.addAll(excludedCCHousemaids);
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidsDueToMissingFields().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidsDueToOverLap().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
//        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidDueToEVisaIssue().stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedDueToMedicalTest().stream().map(BaseEntity::getId).collect(Collectors.toList()));
        excludedHousemaids.addAll(monthlyPaymentRule.getExcludedMaidsDueToMissingMolNumber().stream().map(BaseEntity::getId).collect(Collectors.toList()));

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public static List<PicklistItem> getDelayedItems() {
        PicklistRepository picklistRepository =
                Setup.getRepository(PicklistRepository.class);
        Picklist reasonOfPendingPicklist =
                picklistRepository.findByCode("reasonOfPending");

        PicklistItemRepository picklistItemRepository =
                Setup.getRepository(PicklistItemRepository.class);
        PicklistItem delayedOnSickWithoutClientPicklistItem
                = picklistItemRepository
                .findByListAndCodeIgnoreCase(reasonOfPendingPicklist,
                        "absent-sick_without_client");
        PicklistItem delayedOnReturningBackFromVacationPicklistItem
                = picklistItemRepository
                .findByListAndCodeIgnoreCase(reasonOfPendingPicklist,
                        "did_not_return_from_her_vacation");

        List<PicklistItem> delayItems = new ArrayList();
        if (delayedOnSickWithoutClientPicklistItem != null)
            delayItems.add(delayedOnSickWithoutClientPicklistItem);
        if (delayedOnReturningBackFromVacationPicklistItem != null)
            delayItems.add(delayedOnReturningBackFromVacationPicklistItem);

        return delayItems;
    }

    /**
     * return all the manager notes picklistitem types that must be included even if we are in Secondary Payroll
     *
     * @return
     */
    public static List<PicklistItem> getMustBePaidManagerNotes() {
        // **who have salary dispute PAY-282
        // **who have Taxi Reimbursement PAY-344
        // **who have Forgive Deduction PAY-376
        List<PicklistItem> result = new ArrayList<>();
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "salary_dispute"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "taxi_reimbursement"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "forgive_deduction"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "airfare_ticket"));
        result.add(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "AR-1"));

        return result;
    }

    /**
     * get all MV maid that can receive a salary new
     * @param monthlyPaymentRule
     * @return
     */
    public static List<Long> getPaidMVMaidsIdsNew (MonthlyPaymentRule monthlyPaymentRule, List<Long> transferredMaidsIds, Boolean forAudit) {
        java.sql.Date start = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).withDayOfMonth(1).toDate().getTime());
        java.sql.Date end = new java.sql.Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).plusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime());
        Double vatPercentage = 1.0 + (Integer.parseInt(Setup.getParameter(Setup.getModule("sales"),
                PayrollManagementModule.PARAMETER_SALES_VAT_PERCENT)) * 1.0 / 100);

        //if we are getting paid MV maids for Audit then don't take into consideration MOL Condition
        Boolean withMol = forAudit ? null : monthlyPaymentRule.withOrWithoutMOL();
        List<Housemaid> allMaidVisaMaids;
        if(monthlyPaymentRule != null && monthlyPaymentRule.getId() != null && PayrollType.SECONDARY.equals(monthlyPaymentRule.getPayrollType()) && transferredMaidsIds.size() > 0)
            allMaidVisaMaids = Setup.getRepository(HousemaidRepository.class).getTargetMVMaidsWithoutTransferred(HousemaidStatus.EMPLOYEMENT_TERMINATED, monthlyPaymentRule.getPayrollMonth(), withMol, transferredMaidsIds);
        else
            allMaidVisaMaids = Setup.getRepository(HousemaidRepository.class).getTargetMVMaids(HousemaidStatus.EMPLOYEMENT_TERMINATED, monthlyPaymentRule.getPayrollMonth(), withMol);
        List<Long> normallyPaidHousemaidIds = new ArrayList<>();
        List<Long> cancelledContractsPaidHousemaidIds = new ArrayList<>();
//        DebugHelper.sendMail("<EMAIL>", "start getPaidMVMaidsIdsNew: for " + allMaidVisaMaids.size());
        Long monthlyPaymentPickListItemId = PicklistHelper.getItem("TypeOfPayment", "Monthly Payment").getId();
        for(Housemaid housemaid : allMaidVisaMaids){

            Contract contract = housemaid.getActiveContract();

            //A- Active Contracts
            if (contract != null){
                List<BigInteger> paidIdsObj = Setup.getRepository(PaymentRepository.class)
                        .findPaidMaidVisaByHousemaid(ContractStatus.ACTIVE.toString(),
                                monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), vatPercentage, housemaid.getId());

                if (paidIdsObj != null && paidIdsObj.size()>0){
                    normallyPaidHousemaidIds.add(housemaid.getId());
                }
            }
            //B- Cancelled & Expired Contracts
            else {
                Double basicSalary = housemaid.getBasicSalary();
                basicSalary = basicSalary == null ? 0.0 : basicSalary;

                HistorySelectQuery<Contract> historySelectQuery = new HistorySelectQuery<>(Contract.class);
                historySelectQuery.filterBy("housemaid", "=", housemaid);
                historySelectQuery.filterBy("dateOfTermination", ">=", start);
                historySelectQuery.filterBy("status", "IN", Arrays.asList(ContractStatus.EXPIRED, ContractStatus.CANCELLED));
                historySelectQuery.sortBy("lastModificationDate",false);
                historySelectQuery.setLimit(1);
                List<Contract> contracts = historySelectQuery.execute();
                if (contracts != null && contracts.size() > 0) {
                    contract = Setup.getRepository(ContractRepository.class).findOne(contracts.get(0).getId());
                    if (contract != null) {
                        List<BigInteger> receivedPaymentsObj = Setup.getRepository(PaymentRepository.class).findPaidMaidVisaForCancelledContractsNew(monthlyPaymentPickListItemId,
                                start, end, PaymentStatus.RECEIVED.toString(), contract.getId(), basicSalary, vatPercentage);
                        if (receivedPaymentsObj!= null && receivedPaymentsObj.size() > 0)
                            cancelledContractsPaidHousemaidIds.add(housemaid.getId());
                    }
                }
            }

        }

//        DebugHelper.sendMail("<EMAIL>", "after getPaidMVMaidsIdsNew: normallyPaidHousemaidIds --> " + normallyPaidHousemaidIds + ", cancelledContractsPaidHousemaidIds --> " + cancelledContractsPaidHousemaidIds);
        normallyPaidHousemaidIds.addAll(cancelledContractsPaidHousemaidIds);
        return normallyPaidHousemaidIds;
    }

    public java.sql.Date getPayrollStartLockDate(MonthlyPaymentRule rule) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(rule.getPayrollMonth(), -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for(MonthlyPaymentRule monthlyPaymentRule: rules) {
            if(monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return  monthlyPaymentRule.getLockDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    /**
     * get the list of housemaid statuses that we need to generate a payroll log for it in Primary Payroll
     *
     * @param monthlyPaymentRule
     * @return
     */
    public static List<HousemaidStatus> getFinalHousemaidsStatusesMustBeGenerated(MonthlyPaymentRule monthlyPaymentRule) {
        List<HousemaidStatus> wpsHousemaidsStatuses = Arrays.asList(HousemaidStatus.WITH_CLIENT);
        List<HousemaidStatus> payCashHousemaidsStatuses = Arrays.asList(
                HousemaidStatus.VIP_RESERVATIONS, HousemaidStatus.AVAILABLE,
                HousemaidStatus.RESERVED_FOR_PROSPECT, HousemaidStatus.RESERVED_FOR_REPLACEMENT,
                HousemaidStatus.SICK_WITHOUT_CLIENT, HousemaidStatus.NO_SHOW,
                HousemaidStatus.ON_VACATION, HousemaidStatus.LANDED_IN_DUBAI,
                HousemaidStatus.PENDING_FOR_VIDEOSHOOT, HousemaidStatus.RESERVED_HOME_VISIT,
                HousemaidStatus.ASSIGNED_OFFICE_WORK, HousemaidStatus.PENDING_FOR_DISCIPLINE);
        if (monthlyPaymentRule.isTargetingWithClientOnly())
            return wpsHousemaidsStatuses;
        else if (monthlyPaymentRule.isTargetingInAccommodationOnly())
            return payCashHousemaidsStatuses;
        else {
            List<HousemaidStatus> all = new ArrayList<>(wpsHousemaidsStatuses);
            all.addAll(payCashHousemaidsStatuses);
            return all;
        }
    }

}
