package com.magnamedia.service.payroll.revamp;

import com.magnamedia.controller.PayrollAuditTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.EmployeeListRBATrigger;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.PayrollRosterApproveRequest;
import com.magnamedia.entity.projection.payrollAudit.PayrollAuditMissingField;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HazardousCondition;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import com.magnamedia.service.Auditor.Housemaids.HousemaidCheckListService;
import com.magnamedia.service.Auditor.Housemaids.HousemaidsMissingFields;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.PayrollAuditTodoService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.module.type.HazardousCondition.*;
import static com.magnamedia.module.type.MaidType.MAIDS_CC;
import static com.magnamedia.module.type.MaidType.MAID_VISA;

@Service
public class PayrollRevampService {

    private static final Logger logger =
            Logger.getLogger(PayrollRevampService.class.getName());

    @Autowired
    private PayrollRosterApproveRequestRepository payrollRosterApproveRequestRepository;

    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    private RepaymentRepository repaymentRepository;

    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    @Autowired
    EmployeeLoanRepository employeeLoanRepository;

    @Autowired
    PayrollRevampHousemaidTargetListService payrollRevampHousemaidTargetListService;

    @Autowired
    MonthlyPaymentRuleRepository monthlyPaymentRuleRepository;

    @Autowired
    PayrollAuditTodoService payrollAuditTodoService;

    @Autowired
    EmployeeListRBATriggerRepository employeeListRBATriggerRepository;

    public Boolean checkPayrollRosterIsApproved(MonthlyPaymentRule rule){ //true = ok, false = there is issue
        // Step#1 of final payroll generation conditions checks on the Payment Date

        if(!rule.isTargetingOfficeStaff()) return true;

        Boolean isEmarati = false;
        if(rule.isTargetingEmiratis())
            isEmarati = true;

        List<PayrollRosterApproveRequest> pendingRequests = payrollRosterApproveRequestRepository.findByPayrollMonthAndApprovedFalseAndForEmarati(rule.getPayrollMonth(), isEmarati);

        if(pendingRequests.isEmpty()) return true;

        //send email
        String templateName = "Pending_Payroll_Roster_Approval_Alert_Template";
        List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_RECIPIENT));
        String templateSubject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PENDING_PAYROLL_ROSTER_APPROVAL_REMINDER_TEMPLATE_SUBJECT);
        String employeeType = "";

        if(rule.getEmployeeTypeList() != null && !rule.getEmployeeTypeList().isEmpty()){
            for(PaymentRuleEmployeeType type : rule.getEmployeeTypeList()){
                if(!type.equals(PaymentRuleEmployeeType.HOUSEMAIDS)){
                    employeeType = type.getLabel();
                    break;
                }
            }
        }

        templateSubject = templateSubject
                .replace("@employee_type@", employeeType) // employee_type is either Overseas, Expat, or Emirates
                .replace("@payroll_month@", DateUtil.formatFullMonthFullYear(rule.getPayrollMonth()));

        Map<String, String> parameters = new HashMap<>();
        StringBuilder managersNames = new StringBuilder();

        for (PayrollRosterApproveRequest request : pendingRequests) {
            managersNames.append("   -   ")
                    .append(request.getManager().getName())
                    .append("<br>");
        }
        parameters.put("managers_names", managersNames.toString());

        Setup.getApplicationContext().getBean(MessagingService.class)
                .send(recipients, null, templateName, templateSubject,
                        parameters, null, null);

        return false;
    }

    //this function won't be triggered in case of OVERSEAS payroll release
    public Boolean checkLatestEmployeeList(Boolean checkAndTriggerRBA) { //true = ok, false = there is issue
        // Step#2 of final payroll generation conditions checks on the Payment Date

        Attachment latestEmployeeList = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(
                "/visa/employeeListFile/lastFile", Attachment.class);


        Integer maxEmployeeListAge = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.MAX_EMPLOYEE_LIST_AGE_IN_HOUR_PARAM));
        if (latestEmployeeList != null && latestEmployeeList.getCreationDate() != null
                && DateUtil.getHoursBetween(latestEmployeeList.getCreationDate(), new Date()) <= maxEmployeeListAge) { //if latestEmployeeList is new
            if (!checkAndTriggerRBA) {
                /* Check if all previous EmployeeListRBATriggers are done
                if done -> return true
                    else -> return false */
                List<EmployeeListRBATrigger> pendingTrigger = employeeListRBATriggerRepository.findByPending(true);
                if(pendingTrigger != null && !pendingTrigger.isEmpty()) {
                    return false;
                } else return true;

            }
            return true;
        } else { // If latestEmployeeList is older than 48 hours
            if (checkAndTriggerRBA) {
                // Create new pending EmployeeListRBATrigger
                EmployeeListRBATrigger employeeListRBATrigger = new EmployeeListRBATrigger(true);
                employeeListRBATrigger = employeeListRBATriggerRepository.save(employeeListRBATrigger);

                //send email
                String templateName = "Payroll_Employee_List_RBA_Check_Template";
                List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_RECIPIENT));
                String templateSubject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_EMPLOYEE_LIST_RBA_CHECK_TEMPLATE_SUBJECT);

                Setup.getApplicationContext().getBean(MessagingService.class)
                        .send(recipients, null, templateName, templateSubject,
                                null, null, null);

                return false;
            } else {
                //send email
                String templateName = "Payroll_Mol_List_Upload_Failure_Alert_Template";
                List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_RECIPIENT));
                String templateSubject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_MOL_LIST_UPLOAD_FAILURE_ALERT_TEMPLATE_SUBJECT);

                Map<String, String> parameters = new HashMap<>();
                parameters.put("last_uploaded_mol_list_date", latestEmployeeList != null && latestEmployeeList.getCreationDate() != null ? DateUtil.formatFullDate(latestEmployeeList.getCreationDate()) : "UNKNOWN");

                Setup.getApplicationContext().getBean(MessagingService.class)
                        .send(recipients, null, templateName, templateSubject,
                                parameters, null, null);

                return true;
            }
        }
    }

    public Boolean checkAccountingConditions(MonthlyPaymentRule rule) { //true = ok, false = there is issue
        // Step#3 of final payroll generation conditions checks on the Payment Date

        if(!rule.isTargetingHousemaid() || !rule.getPayrollType().equals(PayrollType.PRIMARY)) return true;

        Map<String, Object> bankStatementProgress = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(
                "/accounting/bankStatementFile/getBankStatementProgress", Map.class);

        // Check if No bank statement is under process
        if(bankStatementProgress != null && bankStatementProgress.get("bankStatementInProgress") != null){
            Boolean bankStatementInProgress = (Boolean) bankStatementProgress.get("bankStatementInProgress");
            if (bankStatementInProgress) return false;
        }

        return true;
    }

    public Boolean checkHazardousConditions(MonthlyPaymentRule rule) { //true = ok, false = there is issue
        // Step#4 of final payroll generation conditions checks on the Payment Date

        if(!rule.isTargetingHousemaid() || !rule.getPayrollType().equals(PayrollType.PRIMARY)) return true;

        Boolean hazardousConditionsIsOk = true; //true = ok, false = there is issue

        //Create Included Maids Lists

        // CC Maids not "with_client" List
        List<Housemaid> ccMaidsWithoutClientList = housemaidPayrollPaymentServiceV2.getIncludedTargetListBasedOnHousemaidTypeAndHousemaidStatus(rule, false, false);
        // All CC Maids List
        List<Housemaid> allCcMaidsList = housemaidPayrollPaymentServiceV2.getIncludedTargetListBasedOnHousemaidTypeAndHousemaidStatus(rule, false, null);
        // MV Maids List
        List<Housemaid> mVMaidsList = housemaidPayrollPaymentServiceV2.getIncludedTargetListBasedOnHousemaidTypeAndHousemaidStatus(rule, true, null); // withClient field is not required in case of maid_visa

        //Check Hazardous Conditions:
        List<HazardousCondition> hazardousConditionsList = new ArrayList<>();

        //1- CC_MAID_CLIENT_COUNT
        if(!checkHazardousCondition1CcMaidClientCount(rule, ccMaidsWithoutClientList, allCcMaidsList)) {
            hazardousConditionsIsOk = false;
            hazardousConditionsList.add(CC_MAID_CLIENT_COUNT);
        }

        //2- CC_MAID_SALARY_COMPARISON
        if(!checkHazardousCondition2CcMaidSalaryComparison(rule, allCcMaidsList)) {
            hazardousConditionsIsOk = false;
            hazardousConditionsList.add(CC_MAID_SALARY_COMPARISON);
        }

        //3- LOAN_REPAYMENT_BALANCE_COMPARISON
        if(!checkHazardousCondition3LoanRepaymentBalanceComparison(rule)) {
            hazardousConditionsIsOk = false;
            hazardousConditionsList.add(LOAN_REPAYMENT_BALANCE_COMPARISON);
        }

        //4- MV_MAID_SALARY_CLIENT_PAYMENT
        if(!checkHazardousCondition4MvMaidSalaryClientPayment(rule, mVMaidsList)) {
            hazardousConditionsIsOk = false;
            hazardousConditionsList.add(MV_MAID_SALARY_CLIENT_PAYMENT);
        }

        //5- GRP_EARNINGS_COMPARISON
        if(!checkHazardousCondition5GrpEarningsComparison(rule, allCcMaidsList)) {
            hazardousConditionsIsOk = false;
            hazardousConditionsList.add(GRP_EARNINGS_COMPARISON);
        }

        if(!hazardousConditionsIsOk){
            //1- send email
            String templateName = "Payroll_Hazardous_Conditions_Alert_Template";
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_RECIPIENT));
            String templateSubject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_HAZARDOUS_CONDITIONS_ALERT_TEMPLATE_SUBJECT);

            Map<String, String> parameters = new HashMap<>();
            List<String> conditions = new ArrayList<>();
            if(! hazardousConditionsList.isEmpty()) {
                for (HazardousCondition condition : rule.getHazardousConditionsList()) {
                    conditions.add("   -   ");
                    conditions.add(condition.getLabel());
                    conditions.add("<br>");
                }
            }
            parameters.put("conditions", conditions.toString());
            parameters.put("payroll_month", DateUtil.formatFullMonthFullYear(rule.getPayrollMonth()));

            Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(recipients, null, templateName, templateSubject,
                            parameters, null, null);
            return false;
        }

        return true;
    }

    private Boolean checkHazardousCondition1CcMaidClientCount(MonthlyPaymentRule rule, List<Housemaid> ccMaidsWithoutClientList, List<Housemaid> allCcMaidsList) { //true = ok, false = hazardous condition happened
        //1- CC_MAID_CLIENT_COUNT Condition
        double percentage = Double.parseDouble(Setup.getParameter(
                Setup.getCurrentModule(),
                PayrollManagementModule.RATIO_CC_MAIDS_WITHOUT_CLIENT_PARAM));

        // Check if the count of CC maids with status = "not with client" equal to or greater than 10% of total CC maids
        if (ccMaidsWithoutClientList.size() >= (percentage / 100.0) * allCcMaidsList.size()) {
            return false;
        }

        return true;
    }

    private Boolean checkHazardousCondition2CcMaidSalaryComparison(MonthlyPaymentRule rule, List<Housemaid> allCcMaidsList) { //true = ok, false = hazardous condition happened
        //2- CC_MAID_SALARY_COMPARISON Condition
        Double currentTotalSalary = housemaidPayrollLogRepository.sumOfCcMaidsTotalSalaryByPayrollMonth(rule.getPayrollMonth(), allCcMaidsList);
        Double previousTotalSalary = housemaidPayrollLogRepository.sumOfCcMaidsTransferredSalaryByPayrollMonth(DateUtil.getPreviousMonths(rule.getPayrollMonth(), 1));

        currentTotalSalary = (currentTotalSalary != null) ? currentTotalSalary : 0.0;
        previousTotalSalary = (previousTotalSalary != null) ? previousTotalSalary : 0.0;

        // Check if the salary difference is equal to or greater than the value specified in the parameter
        if (Math.abs(currentTotalSalary - previousTotalSalary) >= Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.CC_TOTAL_NET_SALARY_DIFFERENCE_FROM_PREVIOUS_MONTH_PARAM))) {
            return false;
        } else
            return true;
    }

    private Boolean checkHazardousCondition3LoanRepaymentBalanceComparison(MonthlyPaymentRule rule) { //true = ok, false = hazardous condition happened
        //3- LOAN_REPAYMENT_BALANCE_COMPARISON Condition
        Double currentMonthRepayment = repaymentRepository.sumByRepaymentDate(new LocalDate(rule.getPayrollMonth()).withDayOfMonth(1).toDate(), new LocalDate(rule.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate());
        Double loanBalance = employeeLoanRepository.sumLoanBalanceForAllMaids((List<HousemaidStatus>) Housemaid.rejectedStatuses);

        currentMonthRepayment = (currentMonthRepayment != null) ? currentMonthRepayment : 0.0;
        loanBalance = (loanBalance != null) ? loanBalance : 0.0;

        int percentage = Integer.parseInt(Setup.getParameter(
                Setup.getCurrentModule(),
                PayrollManagementModule.TOTAL_LOAN_REPAYMENT_PERCENTAGE_FROM_TOTAL_LOAN_BALANCE_PARAM
        ));

        if (currentMonthRepayment < (loanBalance * percentage) / 100.0) {
            // Loan repayment is less than 20% of the total loan balance
            return false;
        } else
            return true;
    }

    private Boolean checkHazardousCondition4MvMaidSalaryClientPayment(MonthlyPaymentRule rule, List<Housemaid> mVMaidsList) { //true = ok, false = hazardous condition happened
        //4- MV_MAID_SALARY_CLIENT_PAYMENT Condition
        Map<String, Double> sumOfMvMonthlyPayments = Setup.getApplicationContext().getBean(InterModuleConnector.class).get(
                "/accounting/payments/getSumOfMvMonthlyPayments", Map.class);

        Double sumOfMvMonthlyPaymentsAmount =0.0;
        if(sumOfMvMonthlyPayments != null && sumOfMvMonthlyPayments.get("amount") != null)
            sumOfMvMonthlyPaymentsAmount =  sumOfMvMonthlyPayments.get("amount");

        Double mvMaidsTotalSalary = housemaidPayrollLogRepository.sumOfMvMaidsTotalSalaryByPayrollMonth(rule.getPayrollMonth(), mVMaidsList);
        mvMaidsTotalSalary = (mvMaidsTotalSalary != null) ? mvMaidsTotalSalary : 0.0;

        int percentage = Integer.parseInt(Setup.getParameter(
                Setup.getCurrentModule(),
                PayrollManagementModule.MV_MAIDS_SALARY_PERCENTAGE_FROM_TOTAL_MV_PAYMENT_PARAM
        ));

        // Check if MV maids' salary exceeds percentage% of total received payments
        if (mvMaidsTotalSalary > (percentage / 100.0) * sumOfMvMonthlyPaymentsAmount) {
            // If the total salary exceeds percentage% of total MV client payments
            return false;
        } else
            return true;
    }

    private Boolean checkHazardousCondition5GrpEarningsComparison(MonthlyPaymentRule rule, List<Housemaid> allCcMaidsList) { //true = ok, false = hazardous condition happened
        //5- GRP_EARNINGS_COMPARISON Condition

        Double grp1TotalEarnings = housemaidPayrollLogRepository.sumOfTotalProratedSalaryByPayrollMonth(rule.getPayrollMonth(), allCcMaidsList);
        Double grp2TotalEarnings = housemaidPayrollLogRepository.sumMohreProratedSalaryByPayrollMonth(rule.getPayrollMonth(), allCcMaidsList);
        Double grp5TotalEarnings = housemaidPayrollLogRepository.sumOfTotalLiveOutProratedSalaryByPayrollMonth(rule.getPayrollMonth(), allCcMaidsList);
        Double grp6TotalEarnings = housemaidPayrollLogRepository.sumMohreLiveOutProratedSalaryByPayrollMonth(rule.getPayrollMonth(), allCcMaidsList);

        grp1TotalEarnings = (grp1TotalEarnings != null) ? grp1TotalEarnings : 0.0;
        grp2TotalEarnings = (grp2TotalEarnings != null) ? grp2TotalEarnings : 0.0;
        grp5TotalEarnings = (grp5TotalEarnings != null) ? grp5TotalEarnings : 0.0;
        grp6TotalEarnings = (grp6TotalEarnings != null) ? grp6TotalEarnings : 0.0;

        double percentage = Double.parseDouble(Setup.getParameter(
                Setup.getCurrentModule(),
                PayrollManagementModule.GROUP_2_OR_6_EARNING_PERCENTAGE_FROM_GROUP_1_OR_5_EARNING_PARAM
        ));

        double grp1EarningsPercentage = (percentage / 100.0) * grp1TotalEarnings;

        // Check if GRP 2 earnings are less than or equal to 0.7% of GRP 1 earnings
        if (grp2TotalEarnings <= grp1EarningsPercentage) {
            return false;
        }

        double grp5EarningsPercentage = (percentage / 100.0) * grp5TotalEarnings;

        // Check if GRP 6 earnings are less than or equal to 0.7% of GRP 5 earnings
        if (grp6TotalEarnings <= grp5EarningsPercentage) {
            return false;
        }

        return true;
    }

    public MonthlyPaymentRule addExcludedMaidsToMonthlyPaymentRule(MonthlyPaymentRule monthlyPaymentRule) {
        List<MonthlyPaymentRule> listOfRelatedRules = monthlyPaymentRuleRepository.findByPayrollMonthAndEmployeeTypeAndPayrollTypeAndLockDate(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS, monthlyPaymentRule.getPayrollType(), monthlyPaymentRule.getLockDate());

        Logger.getLogger(PayrollAuditTodoService.class.getName()).log(Level.SEVERE, "monthlyPaymentRule #" + monthlyPaymentRule.getId());

        List<Housemaid> targetList = payrollRevampHousemaidTargetListService.getAuditingHousemaidTargetList(monthlyPaymentRule);

        List<PayrollAuditMissingField> missingFields = Setup.getApplicationContext().getBean(HousemaidsMissingFields.class).getHousemaidMissingFields(targetList);
        if (missingFields != null && missingFields.size() > 0) {
            Set<Housemaid> excludedMaidsDueToMissingFields = new HashSet<>();
            for (PayrollAuditMissingField missingField : missingFields) {
                excludedMaidsDueToMissingFields.add(Setup.getRepository(HousemaidRepository.class).getOne(missingField.getHousemaidId()));
            }

            //delete generate audit log in case we are in secondary payroll and the maid is excluded due to her missing details
            //in order to prevent duplicated logs
            if (monthlyPaymentRule.isSecondaryMonthlyRule())
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).clearDataForMaids(new ArrayList<>(excludedMaidsDueToMissingFields), monthlyPaymentRule, null, null, listOfRelatedRules);

            monthlyPaymentRule.setExcludedMaidsDueToMissingFields(excludedMaidsDueToMissingFields);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
        }

        List<Housemaid> excludedMedicalTestIsNotDoneMaids = Setup.getApplicationContext().getBean(PayrollAuditTodoController.class).
                getExcludedMaidsDueToMedicalTestIsNotDone(targetList);
        if (excludedMedicalTestIsNotDoneMaids != null) {
            Set<Housemaid> excludedMaidsDueToMedicalTestIsNotDone = new HashSet<>(excludedMedicalTestIsNotDoneMaids);

            //delete generate audit log in case we are in secondary payroll and the maid is excluded due to medical test is not done
            //in order to prevent duplicated logs
            if (monthlyPaymentRule.isSecondaryMonthlyRule())
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).clearDataForMaids(new ArrayList<>(excludedMaidsDueToMedicalTestIsNotDone), monthlyPaymentRule, null, null, listOfRelatedRules);

            monthlyPaymentRule.setExcludedDueToMedicalTest(excludedMaidsDueToMedicalTestIsNotDone);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
        }

        int overLapDays = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_EXCLUDING_OVERLAP_MAIDS_DAYS_COUNT));
        Set<Housemaid> overLapMaids = Setup.getApplicationContext().getBean(HousemaidCheckListService.class).getOverlapMaidsForMoreThan(targetList, overLapDays);
        if (overLapMaids != null && overLapMaids.size() > 0) {

            //delete generate audit log in case we are in secondary payroll and the maid is excluded due to overlap for more than X days
            //in order to prevent duplicated logs
            if (monthlyPaymentRule.isSecondaryMonthlyRule())
                Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).clearDataForMaids(new ArrayList<>(overLapMaids), monthlyPaymentRule, null, null, listOfRelatedRules);

            monthlyPaymentRule.setExcludedMaidsDueToOverLap(overLapMaids);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
        }

        List<Housemaid> excludedDueToMissingMolNumberMaids = Setup.getRepository(HousemaidRepository.class).getWithoutMolNumber(targetList);
        if (excludedDueToMissingMolNumberMaids != null) {
            Set<Housemaid> excludedDueToMolNumberIsMissing = new HashSet<>(excludedDueToMissingMolNumberMaids);

            monthlyPaymentRule.setExcludedMaidsDueToMissingMolNumber(excludedDueToMolNumberIsMissing);
            monthlyPaymentRule = monthlyPaymentRuleRepository.save(monthlyPaymentRule);
        }

        return monthlyPaymentRule;
    }
}
