package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.TransferDestination;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.NumberFormatter;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.TransferDestinationRepository;
import com.magnamedia.service.message.MessagingService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class SendingTransactionService {

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private TransferDestinationRepository transferDestinationRepository;

    @Autowired
    private OfficeStaffRepository officeStaffRepository;



    public void bankTransferIsDone(OfficeStaffPayrollLog officeStaffPayrollLog) {
        OfficeStaff officeStaff = officeStaffPayrollLog.getOfficeStaff();

//        if (officeStaff.getPhoneNumber() != null && !isSyrianNumber(officeStaff.getPhoneNumber())) {
//            //Sending messages
//            Map<String, String> paramValues = new HashMap<>();
//
//            paramValues.put("receiver_name", officeStaff.getName());
//            paramValues.put("amount", NumberFormatter.formatNumber(officeStaffPayrollLog.getTotalSalary())
//                    + " " + officeStaffPayrollLog.getCurrency());
//
//            SmsResponse smsResponse = smsService.send("Bank transfer completed for office staff salaries",
//                    normalizePhoneNumber(officeStaff.getPhoneNumber()),
//                    SmsReceiverType.Prosepect,
//                    officeStaff.getId(),
//                    officeStaff.getName(),
//                    "Payroll_Bank_Transfer_Completed",
//                    paramValues,
//                    null);
//
//        } else
        if (officeStaff.getEmail() != null) {
            String subject = "Salary Transfer - " + DateUtil.formatSimpleMonthYear(officeStaffPayrollLog.getPayrollMonth());

            if (officeStaff.getEmail() != null) {
                List<EmailRecipient> recipients = Recipient.parseEmailsString(officeStaff.getEmail());
                Map<String, String> params = new HashMap<>();
                params.put("employeeName", officeStaff.getName());
                params.put("salary", NumberFormatter.formatNumber(officeStaffPayrollLog.getTotalSalary())
                        + " " + officeStaffPayrollLog.getCurrency());
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Salary_Transfer_Notification", params);

                Attachment proofOfTransfer = officeStaffPayrollLog.getAttachment("proofOfTransfer");

                List<Attachment> attachments = null;
                if (proofOfTransfer != null) {
//                    templateEmail.addAttachement(proofOfTransfer);
                    attachments = Collections.singletonList(proofOfTransfer);
                }
                Setup.getApplicationContext().getBean(MessagingService.class)
                        .send(recipients, null, "Payroll_Salary_Transfer_Notification", subject, params, attachments, null);
//                Setup.getMailService().sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                        .recipients(recipients)
//                        .html()
//                        .secure()
//                        .build());
            }
        }
    }


    private boolean isSyrianNumber(String number) {
        return number != null && (
                number.startsWith("+963") || number.startsWith("00963") || number.startsWith("963")
        );
    }




    public void internationalTransferIsDone(String receiverName, String ceNumber, String amount, String currency, String country, Map<String, List<String>> receiverToCENumbersAndAmounts, Map<String, Set<String>> receiverToAffectedEmployees) {

        //Sending messages
        Map<String, String> paramValues = new HashMap<>();

        paramValues.put("receiver_name", receiverName);
        paramValues.put("ce_number", ceNumber);

        String email = null;

        //phoneNumber = normalizePhoneNumber(phoneNumber);
        OfficeStaff officeStaff = new OfficeStaff();
        officeStaff.setId(0L);

        // First try: Match with office staff
        List<OfficeStaff> officeStaffs;

        if(country != null && country.equals("SYRIA"))
            officeStaffs = officeStaffRepository.findByFullNameInArabic(receiverName);
        else
            officeStaffs = officeStaffRepository.findByName(receiverName);

        if (officeStaffs != null && !officeStaffs.isEmpty() && officeStaffs.get(0).getEmail() != null) {
            email = officeStaffs.get(0).getEmail();
            officeStaff.setEmail(email);
            messagingService.send("Payroll_Sending_Transaction_Number",
                    "Sending Transaction Number",
                    null, officeStaff, paramValues, null, null, null);
            return;
        }

        // Second try: Match with money receiver name
        List<TransferDestination> destinations;

        if(country != null && country.equals("SYRIA"))
            destinations = transferDestinationRepository.findByFullNameInArabicAndSelfReceiverFalseAndOfficeStaffIsActive(receiverName);
        else
            destinations = transferDestinationRepository.findByNameAndSelfReceiverFalseAndOfficeStaffIsActive(receiverName);

        if (destinations != null && !destinations.isEmpty()) {
            // Check if money receiver has email
            if (destinations.get(0).getEmail() != null) {
                email = destinations.get(0).getEmail();
                officeStaff.setEmail(email);
                messagingService.send("Payroll_Sending_Transaction_Number",
                        "Sending Transaction Number",
                        null, officeStaff, paramValues, null, null, null);
                return;
            }
            // Check if multiple profiles exist
            Set<OfficeStaff> staffsForDestinations = destinations.stream()
                    .map(TransferDestination::getOfficeStaff)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (staffsForDestinations.size() == 1) {
                // Single profile case
                OfficeStaff staff = staffsForDestinations.iterator().next();
                if (staff.getEmail() != null) {
                    officeStaff.setEmail(staff.getEmail());
                    messagingService.send("Payroll_Sending_Transaction_Number",
                            "Sending Transaction Number",
                            null, officeStaff, paramValues, null, null, null);
                }
            } else if (staffsForDestinations.size() > 1) {
                // Multiple profiles case - collect for combined email
                // Format the CE number and amount as a single string (e.g., "CE123 - 1000.00 USD")
                String ceNumberWithAmount = String.format("%s - %s %s", ceNumber, amount, currency);

                // Initialize the list for this receiver if it doesn't exist
                if (!receiverToCENumbersAndAmounts.containsKey(receiverName)) {
                    receiverToCENumbersAndAmounts.put(receiverName, new ArrayList<>());
                }
                // Add the formatted CE number and amount to the receiver's list
                receiverToCENumbersAndAmounts.get(receiverName).add(ceNumberWithAmount);

                // Extract names of all affected employees from their staff profiles
                List<String> affectedEmployees = staffsForDestinations.stream()
                        .map(OfficeStaff::getName)
                        .collect(Collectors.toList());

                // Initialize the affected employees list for this receiver if it doesn't exist
                if (!receiverToAffectedEmployees.containsKey(receiverName)) {
                    receiverToAffectedEmployees.put(receiverName, new HashSet<>());
                }
                // Add all affected employee names to the receiver's list
                receiverToAffectedEmployees.get(receiverName).addAll(affectedEmployees);

            }
        }
    }

    // New method to send the combined multiple profiles email
    public void sendMultipleProfilesEmail(Map<String, List<String>> receiverToCENumbersAndAmounts, Map<String, Set<String>> receiverToAffectedEmployees) {
        if (receiverToCENumbersAndAmounts.isEmpty()) {
            return;
        }

        String templateName = "Payroll_Multiple_Profiles_Summary_Template";
        String subject = "Action Required: Multiple Profiles Linked to The Same Money Receiver Name.";

        List<EmailRecipient> recipients = Recipient.parseEmailsString(
                Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_PAYROLL_MULTIPLE_PROFILES_EMAIL_RECIPIENTS)
        );

        try {
            StringBuilder emailBody = new StringBuilder();
            for (String receiverName : receiverToCENumbersAndAmounts.keySet()) {
                emailBody
                        .append("<b>Money Receiver Name:</b> ")
                        .append(receiverName).append("<br><br>");
                emailBody.append("<b>CE Numbers - amount:</b><br>")
                        .append(String.join("<br>", receiverToCENumbersAndAmounts.get(receiverName)))
                        .append("<br><br>");
                emailBody.append("<b>Affected Employees:</b> ")
                        .append(String.join(", ", new ArrayList<>(receiverToAffectedEmployees.get(receiverName))))
                        .append("<br><br>");
            }

            Map<String, String> params = new HashMap<>();
            params.put("info", emailBody.toString());

            Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(recipients, null, templateName, subject, params, null, null);

        } finally {
            // Clear the collections regardless of success or failure
            receiverToCENumbersAndAmounts.clear();
            receiverToAffectedEmployees.clear();
        }
    }



        public Cell findFirstCell(Row row) {
        Iterator<Cell> cells = row.iterator();
        int c = 0;
        while (cells.hasNext()) {
            Cell cell = cells.next();
            if (cell.getStringCellValue().equalsIgnoreCase("Serial Number")) {
                return cell;
            }
            c++;
        }
        return null;
    }

    public int findColumnIndexByHeaderName(Row row, String header) {
        Iterator<Cell> cells = row.iterator();
        int c = 0;
        while (cells.hasNext()) {
            Cell cell = cells.next();
            if (cell.getStringCellValue().equalsIgnoreCase(header.trim())) {
                return cell.getColumnIndex();
            }
            c++;
        }
        return -1;
    }



    public void extractTransactionNumbers(Attachment attachment) throws IOException {
        try {

            // fields for accumulating multiple profile cases
            Map<String, List<String>> receiverToCENumbersAndAmounts = new HashMap<>();
            Map<String, Set<String>> receiverToAffectedEmployees = new HashMap<>();

//            DebugHelper.sendMail("<EMAIL>", "Start extracting transactions");
            attachment = Setup.getRepository(AttachementRepository.class)
                    .findOne(attachment.getId());

            try (InputStream inputStream = Storage.getStream(attachment)) {

                Workbook workbook = new XSSFWorkbook(inputStream);

                Sheet firstSheet = workbook.getSheetAt(0);

                Iterator<Row> iterator = firstSheet.iterator();

                //handle headers to determine the columns indexes
                int ceNumberIndex = -1;
                int receiverNameIndex = -1;
                int amountIndex = -1;
                int currencyIndex = -1;
                int countryIndex = -1;
                if (iterator.hasNext()) {
                    Row row = iterator.next();
                    ceNumberIndex = findColumnIndexByHeaderName(row, "CE Number");
                    receiverNameIndex = findColumnIndexByHeaderName(row, "Receiver Name");
                    amountIndex = findColumnIndexByHeaderName(row, "Amount(In Fcy)");
                    currencyIndex = findColumnIndexByHeaderName(row, "Destination Currency");
                    countryIndex = findColumnIndexByHeaderName(row, "Receiving Country");

                    if (ceNumberIndex == -1)
                        throw new RuntimeException("Couldn't find a column with name 'CE Number'");
                    if (receiverNameIndex == -1)
                        throw new RuntimeException("Couldn't find a column with name 'Receiver Name'");
                    if (amountIndex == -1)
                        throw new RuntimeException("Couldn't find a column with name 'Amount(In Fcy)'");
                    if (currencyIndex == -1)
                        throw new RuntimeException("Couldn't find a column with name 'Destination Currency'");
                    if (countryIndex == -1)
                        throw new RuntimeException("Couldn't find a column with name 'Receiving Country'");
                }

                while (iterator.hasNext()) {
                    Row row = iterator.next();

                    Cell ceNumberCell = row.getCell(ceNumberIndex);
                    if (ceNumberCell != null)
                        ceNumberCell.setCellType(CellType.STRING);
                    else
                        break;
                    Cell amountCell = row.getCell(amountIndex);
                    if (amountCell != null)
                        amountCell.setCellType(CellType.STRING);
                    Cell receiverNameCell = row.getCell(receiverNameIndex);
                    if (receiverNameCell != null)
                        receiverNameCell.setCellType(CellType.STRING);
                    Cell currencyCell = row.getCell(currencyIndex);
                    if (currencyCell != null)
                        currencyCell.setCellType(CellType.STRING);
                    Cell countryCell = row.getCell(countryIndex);
                    if (countryCell != null)
                        countryCell.setCellType(CellType.STRING);

                    if (!isCellEmpty(ceNumberCell) && !isCellEmpty(receiverNameCell) && !isCellEmpty(amountCell) && !isCellEmpty(currencyCell) && !isCellEmpty(countryCell)) {

                        if (receiverNameCell.getStringCellValue().trim().equals("Receiver Name")) continue;

                        String ceNumber = ceNumberCell.getCellType() == CellType.STRING ?
                                ceNumberCell.getStringCellValue() : String.valueOf(ceNumberCell.getNumericCellValue());

                        String amount = amountCell.getCellType() == CellType.STRING ?
                                amountCell.getStringCellValue() : String.valueOf(amountCell.getNumericCellValue());
                        ;

                        String receiverName = receiverNameCell.getCellType() == CellType.STRING ?
                                receiverNameCell.getStringCellValue() : String.valueOf(receiverNameCell.getNumericCellValue());
                        ;

                        String currency = currencyCell.getCellType() == CellType.STRING ?
                                currencyCell.getStringCellValue() : String.valueOf(currencyCell.getNumericCellValue());

                        String country = countryCell.getCellType() == CellType.STRING ?
                                countryCell.getStringCellValue() : String.valueOf(countryCell.getNumericCellValue());

                        internationalTransferIsDone(receiverName, ceNumber, amount, currency, country, receiverToCENumbersAndAmounts, receiverToAffectedEmployees);

                    } else
                        throw new RuntimeException("Error, Couldn't parse the row #" + row.getRowNum() + 1);
                }

                sendMultipleProfilesEmail(receiverToCENumbersAndAmounts, receiverToAffectedEmployees);
            }
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while extracting transaction numbers", false);
            throw ex;
        }
    }

    public boolean isCellEmpty(final Cell cell) {
        if (cell == null) { // use row.getCell(x, Row.CREATE_NULL_AS_BLANK) to avoid null cells
            return true;
        }

        if (cell.getCellType() == CellType.BLANK) {
            return true;
        }

        if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().isEmpty()) {
            return true;
        }

        if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().equalsIgnoreCase("not available")) {
            return true;
        }

        return false;
    }
}
