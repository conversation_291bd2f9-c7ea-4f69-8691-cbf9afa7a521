package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.AbstractPayrollManagerNote;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.entity.HousemaidPayrollMonthlyGroup;
import com.magnamedia.extra.payroll.init.HousemaidPayrollInitializer;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.repository.PayrollManagerNoteRepository;
import com.magnamedia.service.HousemaidUnpaidDayService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollGroupService;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class ProRatedSalariesService {

    @Transactional
    public List<PayrollManagerNote> processProRatedSalaries(List<Housemaid> housemaids, MonthlyPaymentRule rule, LocalDate payrollStart, LocalDate payrollEnd, boolean finalFile,
                                                            HousemaidPayrollInitializer initializer) {

        PicklistItem salaryHeld =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PicklistItem.getCode("Previously Held Salary"));

        PicklistItem salaryDispute =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        "salary_dispute");


        LocalDate payrollMonth = new LocalDate(rule.getPayrollMonth());
        LocalDate lastPayrollMonth = payrollMonth.minusMonths(1);
        List<PayrollManagerNote> notes = new ArrayList<>();
        for(Housemaid housemaid: housemaids) {
            if(initializer.hasPaidSalary(housemaid.getId())) continue;
            LocalDate startingDate = new LocalDate(housemaid.getStartDate());

            if (!rule.isSecondaryMonthlyRule()) {
                double proratedAmount = 0d;
                if (housemaid.getBasicSalary() != null &&
                        !startingDate.isBefore(lastPayrollMonth.withDayOfMonth(27)) &&
                        startingDate.isBefore(payrollMonth.withDayOfMonth(1))) {
                    proratedAmount = (housemaid.getBasicSalary() / lastPayrollMonth.dayOfMonth().getMaximumValue())
                            * (Math.abs((Days.daysBetween(startingDate, payrollMonth.withDayOfMonth(1))).getDays()));
                }

                if (proratedAmount > 0) {
                    PayrollManagerNote note = new PayrollManagerNote();
                    note.setHousemaid(housemaid);
                    note.setAmount((double) Math.round(proratedAmount));
                    note.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
                    note.setNotFinal(!finalFile);
                    note.setNoteDate(new java.sql.Date(
                            payrollStart.getMonthOfYear() == payrollMonth.getMonthOfYear() ? payrollStart.toDate().getTime()
                                    : payrollMonth.withDayOfMonth(1).toDate().getTime()));

                    note.setAdditionReason(salaryDispute);
                    notes.add(note);
                }
            }

            Double heldSalary = HousemaidPayrollPaymentServiceV2.getHousemaidPayrollInitializer()
                    .getPreviousHeldSalary(housemaid.getId());
            if(heldSalary > 0 && housemaid.getStatus() != HousemaidStatus.ON_VACATION
            && housemaid.getStatus() != HousemaidStatus.NO_SHOW) {
                PayrollManagerNote note = new PayrollManagerNote();
                note.setHousemaid(housemaid);
                note.setAmount((double) Math.round(heldSalary));
                note.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
                note.setNotFinal(!finalFile);
                note.setNoteReasone("She was on vacation and didn't receive her salary.");
                note.setNoteDate(new java.sql.Date(
                        payrollStart.getMonthOfYear() == payrollMonth.getMonthOfYear() ? payrollStart.toDate().getTime()
                                : payrollMonth.withDayOfMonth(1).toDate().getTime()));

                note.setAdditionReason(salaryHeld);
                notes.add(note);
            }
        }

        return Setup.getRepository(PayrollManagerNoteRepository.class)
                .save(notes);
    }


    // from, to, payrollMonth: same month
    // This method used for calculating salary without "Salary Calculation Transactions"
    // not used anymore
    @Deprecated
    public Double getSalary(Housemaid housemaid, LocalDate payrollMonth,
                            java.sql.Date fromDate, java.sql.Date toDate,
                            int groupOne, long previousVacationDays, long vacationDays,
                            Map<Long, Date> lastVacationDates) {

        int groupFour = (int) vacationDays;

        int monthDays = payrollMonth.dayOfMonth().getMaximumValue();

        long toSubtract = Math.min(vacationDays, (previousVacationDays + vacationDays) - 30);
        // Vacation less than 30 days up till now
        if(toSubtract < 0) toSubtract = 0;
        if(housemaid.getHousemaidType() != null &&
                housemaid.getHousemaidType() == HousemaidType.MAID_VISA) {

            double basicSalary = housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0;
            java.sql.Date startDate = new java.sql.Date(housemaid.getStartDate().getTime());

            // check startDate, terminationDate deductions
            LocalDate from = new LocalDate(startDate.after(fromDate) ? startDate : fromDate);
            LocalDate to  = new LocalDate(toDate);
            int numberOfDays = to.getDayOfMonth() - from.getDayOfMonth() + 1;

            numberOfDays = Math.max(0, numberOfDays - (int)toSubtract);

            if(numberOfDays != monthDays) {
                return (basicSalary * numberOfDays) / 30.4d;
            }
            return basicSalary;
        }


        Double basicSalary = 0d;
        if(housemaid.getBasicSalary() != null) {
            basicSalary = housemaid.getBasicSalary();
        }

        Double primarySalary = 0d;
        if(housemaid.getPrimarySalary() != null) {
            primarySalary = housemaid.getPrimarySalary();
        }

        Double mohreSalary = 0d;
        if (housemaid.getPrimarySalary() != null){
            mohreSalary = housemaid.getPrimarySalary();
        }

        int numberOfDays = new LocalDate(toDate).getDayOfMonth() - new LocalDate(fromDate).getDayOfMonth() + 1;

        int minDay = 0;

        groupOne += minDay;

        if (housemaid.getStartDate() != null) {
            LocalDate startDate = new LocalDate(housemaid.getStartDate());
            if (startDate.isAfter(payrollMonth.withDayOfMonth(1))) {
                int amount = startDate.getDayOfMonth() - 1;

                numberOfDays -= amount;
                groupOne -= Math.min(minDay, amount);
            }
        }


        if(groupOne > numberOfDays) {
            groupOne = numberOfDays;
        }

        int groupTwo = numberOfDays - (groupOne + groupFour);

        if(isGroupOneVacation(housemaid.getId(), lastVacationDates)) {
            groupOne = Math.max(0, groupOne - (int) toSubtract);
        } else {
            groupFour = groupFour - (int)toSubtract ;
            if (groupFour < 0){
                groupTwo = groupTwo + groupFour;
                if (groupTwo < 0) groupTwo = 0;
                groupFour = 0;
            }
//            groupTwo = Math.max(0, groupTwo - (int) toSubtract);
        }

        if(groupOne == monthDays) return  1d * Math.round(basicSalary);
        if(groupTwo == monthDays) return 1d * Math.round(primarySalary);
        if(groupFour == monthDays) return 1d * Math.round(mohreSalary);

        double total = (basicSalary * groupOne) / monthDays;
        total += (primarySalary * groupTwo) / monthDays;
        total += (mohreSalary * groupFour) / monthDays;

        return 1d * Math.round(total);
    }


    // from, to, payrollMonth: same month
    // This method used for calculating salary without "Salary Calculation Transactions"
    // For now it's used for:
    // 1- getting housemaid total salary
    public Double getSalaryNew(Housemaid housemaid, LocalDate payrollMonth,
                               java.sql.Date fromDate, java.sql.Date toDate,
                               long previousVacationDays) {



        int monthDays = payrollMonth.dayOfMonth().getMaximumValue();
        if(housemaid.getHousemaidType() != null &&
                housemaid.getHousemaidType() == HousemaidType.MAID_VISA) {

            double basicSalary = housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0;
            java.sql.Date startDate = new java.sql.Date(housemaid.getStartDate().getTime());

            // check startDate, terminationDate deductions
            LocalDate from = new LocalDate(startDate.after(fromDate) ? startDate : fromDate);
            LocalDate to  = new LocalDate(toDate);
            int numberOfDays = to.getDayOfMonth() - from.getDayOfMonth() + 1;

            numberOfDays = Math.max(0, numberOfDays);

            if(numberOfDays != monthDays) {
                return (basicSalary * numberOfDays) / 30.4d;
            }
            return basicSalary;
        }


//        Map<HousemaidSalaryGroup, Double> salaries = Setup.getApplicationContext().getBean(PayrollGroupService.class)
//                .getSalaryForEachGroup(workDays);
        HousemaidPayrollMonthlyGroup groups = Setup.getApplicationContext().getBean(PayrollGroupService.class).getOrCreateMonthlyGroupForMaid(housemaid, new java.sql.Date(payrollMonth.toDate().getTime()));
        int groupOne = groups.getGr1Days().intValue();
        int groupTwo = groups.getGr2Days().intValue();
        int group3Days = groups.getGr3Days().intValue();
        int groupFour = groups.getGr4Days().intValue();
        int groupFive = groups.getGr5Days().intValue();
        int groupSix = groups.getGr6Days().intValue();


        long toSubtract = Math.min(groupFour, (previousVacationDays + groupFour) - 30);
        // Vacation less than 30 days up till now
        if(toSubtract < 0) toSubtract = 0;

        groupFour = groupFour - (int) toSubtract;

        Double basicSalary = groups.getGr1Salary();

        Double accommodationSalary = groups.getGr2Salary();

        Double mohreSalary = groups.getGr4Salary();

        Double liveOutBasicSalary = groups.getGr5Salary();

        Double liveOutAccommodationSalary = groups.getGr6Salary();


//        int numberOfDays = new LocalDate(toDate).getDayOfMonth() - new LocalDate(fromDate).getDayOfMonth() + 1;

//        int minDay = 0;

//        groupOne += minDay;

//        if (housemaid.getStartDate() != null) {
//            LocalDate startDate = new LocalDate(housemaid.getStartDate());
//            if (housemaid.getReplacementSalaryStartDate() != null)
//                startDate = new LocalDate(housemaid.getReplacementSalaryStartDate());
//
//            if (startDate.isAfter(payrollMonth.withDayOfMonth(1))) {
//                int amount = startDate.getDayOfMonth() - 1;
//
//                numberOfDays -= amount;
//                groupOne -= Math.min(minDay, amount);
//            }
//        }


//        if(groupOne > numberOfDays) {
//            groupOne = numberOfDays;
//        }

        // PAY-1524 this case happens when the maid has a vacation days and her replacement salary start date > 1st of payroll month
//        if(groupOne + groupFour > numberOfDays) {
//            groupFour = numberOfDays - groupOne;
//        }

//        int groupTwo = numberOfDays - (groupOne + groupFour);

        // remove the number of group 3 days between these period
//        Integer group3Days = Setup.getApplicationContext().getBean(HousemaidUnpaidDayService.class).getGroup3DaysForHousemaid(housemaid, fromDate, toDate);
//        groupTwo -= group3Days;
//        if(groupTwo < 0) groupTwo = 0;
//
//        groupFour = groupFour - (int) toSubtract;
//        if (groupFour < 0){
//            groupTwo = groupTwo + groupFour;
//            if (groupTwo < 0) groupTwo = 0;
//            groupFour = 0;
//        }

//        if(groupOne + groupTwo + group3Days + groupFour > numberOfDays)
//            group3Days = numberOfDays - (groupOne + groupTwo + groupFour);
//        if(group3Days < 0) group3Days = 0;


        if(groupOne == monthDays) return  1d * Math.round(basicSalary);
        if(groupTwo == monthDays) return 1d * Math.round(accommodationSalary);
        if (groupFour == monthDays) return 1d * Math.round(mohreSalary);
        if (groupFive == monthDays) return 1d * Math.round(liveOutBasicSalary);
        if (groupSix == monthDays) return 1d * Math.round(liveOutAccommodationSalary);


        double total = (basicSalary * groupOne) / monthDays;
        total += (accommodationSalary * groupTwo) / monthDays;
        total += (mohreSalary * groupFour) / monthDays;
        total += (liveOutBasicSalary * groupFive) / monthDays;
        total += (liveOutAccommodationSalary * groupSix) / monthDays;

        return 1d * Math.round(total);
    }

    // from, to, payrollMonth: same month
    // This method used for calculating salary without "Salary Calculation Transactions"
    // not used anymore (just in case we used it again)
    @Deprecated
    public Map<String, Object> getSalaryBreakDown(Housemaid housemaid, LocalDate payrollMonth,
                                                  java.sql.Date fromDate, java.sql.Date toDate,
                                                  int groupOne, long previousVacationDays, long vacationDays,
                                                  Map<Long, Date> lastVacationDates) {

        int groupFour = (int) vacationDays;

        long toSubtract = Math.min(vacationDays, (previousVacationDays + vacationDays) - 30);
        // Vacation less than 30 days up till now
        if(toSubtract < 0) toSubtract = 0;
        int monthDays = payrollMonth.dayOfMonth().getMaximumValue();

        Map<String, Object> salaryBreakDown = new HashMap<>();

        if(housemaid.getHousemaidType() != null &&
                housemaid.getHousemaidType() == HousemaidType.MAID_VISA) {

            double basicSalary = housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0d;
            double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0d;

            java.sql.Date startDate = new java.sql.Date(housemaid.getStartDate().getTime());

            // check startDate, terminationDate deductions
            LocalDate from = new LocalDate(startDate.after(fromDate) ? startDate : fromDate);
            LocalDate to  = new LocalDate(toDate);
            int numberOfDays = to.getDayOfMonth() - from.getDayOfMonth() + 1;

            numberOfDays = Math.max(0, numberOfDays - (int)toSubtract);

            double amount, cashAdvance;

            if(numberOfDays != monthDays) {
                amount = (basicSalary * numberOfDays) / 30.4d;
                cashAdvance = (monthlyLoan * numberOfDays) / 30.4d;
            } else {
                amount = basicSalary;
                cashAdvance = monthlyLoan;
            }

            salaryBreakDown.put("group1Salary", amount);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group1Days", numberOfDays);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("total", amount);
            salaryBreakDown.put("proratedCashAdvance", cashAdvance);
            return salaryBreakDown;

        }


        Double basicSalary = 0d;
        if(housemaid.getBasicSalary() != null) {
            basicSalary = housemaid.getBasicSalary();
        }

        Double accommodationSalary = 0d;
        if(housemaid.getAccommodationSalary() != null) {
            accommodationSalary = housemaid.getAccommodationSalary();
        }

        Double mohreSalary = 0d;
        if (housemaid.getPrimarySalary() != null){
            mohreSalary = housemaid.getPrimarySalary();
        }

        double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0d;


        int numberOfDays = new LocalDate(toDate).getDayOfMonth() - new LocalDate(fromDate).getDayOfMonth() + 1;

        int minDay = 0;

        groupOne += minDay;

        if (housemaid.getStartDate() != null) {
            LocalDate startDate = new LocalDate(housemaid.getStartDate());
            if (startDate.isAfter(payrollMonth.withDayOfMonth(1))) {
                int amount = startDate.getDayOfMonth() - 1;

                numberOfDays -= amount;
                groupOne -= Math.min(minDay, amount);
            }
        }


        if(groupOne > numberOfDays) {
            groupOne = numberOfDays;
        }

        int groupTwo = numberOfDays - (groupOne + groupFour);

        // remove the number of group 3 days between these period
        Integer group3Days = Setup.getApplicationContext().getBean(HousemaidUnpaidDayService.class).getGroup3DaysForHousemaid(housemaid, fromDate, toDate);
        groupTwo -= group3Days;

        if(isGroupOneVacation(housemaid.getId(), lastVacationDates)) {
            groupOne = Math.max(0, groupOne - (int) toSubtract);
        } else {
            groupFour = groupFour - (int)toSubtract ;
            if (groupFour < 0){
                groupTwo = groupTwo + groupFour;
                if (groupTwo < 0) groupTwo = 0;
                groupFour = 0;
            }
//            groupTwo = Math.max(0, groupTwo - (int) toSubtract);
        }

        if(groupOne == monthDays){
            salaryBreakDown.put("group1Salary", 1d * Math.round(basicSalary));
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group1Days", groupOne);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(basicSalary));
            salaryBreakDown.put("proratedCashAdvance", monthlyLoan);
            return salaryBreakDown;
        }
        if(groupTwo == monthDays){
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 1d * Math.round(accommodationSalary));
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", groupTwo);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(accommodationSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            return salaryBreakDown;
        }
        if(groupFour == monthDays){
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 1d * Math.round(mohreSalary));
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", groupFour);
            salaryBreakDown.put("total", 1d * Math.round(mohreSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            return salaryBreakDown;
        }


        double total = (basicSalary * groupOne) / monthDays;
        total += (accommodationSalary * groupTwo) / monthDays;
        total += (mohreSalary * groupFour) / monthDays;

        salaryBreakDown.put("group1Salary", (basicSalary * groupOne) / monthDays);
        salaryBreakDown.put("group2Salary", (accommodationSalary * groupTwo) / monthDays);
        salaryBreakDown.put("group3Salary", (mohreSalary * groupFour) / monthDays);
        salaryBreakDown.put("group1Days", groupOne);
        salaryBreakDown.put("group2Days", groupTwo);
        salaryBreakDown.put("group3Days", group3Days);
        salaryBreakDown.put("group4Days", groupFour);
        salaryBreakDown.put("total", 1d * Math.round(total));
        salaryBreakDown.put("proratedCashAdvance", (monthlyLoan * groupOne) / monthDays);

        return salaryBreakDown;
    }

    // from, to, payrollMonth: same month
    // This method used for calculating salary without "Salary Calculation Transactions"
    // For now it's used for:
    // 1- Calculating housemaids final settlement
    @Deprecated
    public Map<String, Object> getSalaryBreakDownForFinalSettlement(Housemaid housemaid, LocalDate payrollMonth,
                                                                    java.sql.Date fromDate, java.sql.Date toDate,
                                                                    int groupOne, long previousVacationDays, long vacationDays,
                                                                    Map<Long, Date> lastVacationDates) {


        int groupFour = (int) vacationDays;

        long toSubtract = Math.min(vacationDays, (previousVacationDays + vacationDays) - 30);
        // Vacation less than 30 days up till now
        if(toSubtract < 0) toSubtract = 0;
        int monthDays = payrollMonth.dayOfMonth().getMaximumValue();

        Map<String, Object> salaryBreakDown = new HashMap<>();

        if(housemaid.getHousemaidType() != null &&
                housemaid.getHousemaidType() == HousemaidType.MAID_VISA) {

            double basicSalary = housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0d;
            double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0d;

            java.sql.Date startDate = new java.sql.Date(housemaid.getStartDate().getTime());

            // check startDate, terminationDate deductions
            LocalDate from = new LocalDate(startDate.after(fromDate) ? startDate : fromDate);
            LocalDate to  = new LocalDate(toDate);
            int numberOfDays = to.getDayOfMonth() - from.getDayOfMonth() + 1;

            numberOfDays = Math.max(0, numberOfDays - (int)toSubtract);

            double amount, cashAdvance;

            if(numberOfDays != monthDays) {
                amount = (basicSalary * numberOfDays) / 30.4d;
                cashAdvance = (monthlyLoan * numberOfDays) / 30.4d;
            } else {
                amount = basicSalary;
                cashAdvance = monthlyLoan;
            }

            salaryBreakDown.put("group1Salary", amount);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group1Days", numberOfDays);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("total", amount);
            salaryBreakDown.put("proratedCashAdvance", cashAdvance);
            return salaryBreakDown;

        }


        Double basicSalary = 0d;
        if(housemaid.getBasicSalary() != null) {
            basicSalary = housemaid.getBasicSalary();
        }

        Double accommodationSalary = 0d;
        if(housemaid.getAccommodationSalary() != null) {
            accommodationSalary = housemaid.getAccommodationSalary();
        }

        Double mohreSalary = 0d;
        if (housemaid.getPrimarySalary() != null){
            mohreSalary = housemaid.getPrimarySalary();
        }

        double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0d;


        int numberOfDays = new LocalDate(toDate).getDayOfMonth() - new LocalDate(fromDate).getDayOfMonth() + 1;

        if(groupOne > numberOfDays) {
            groupOne = numberOfDays;
        }


        // PAY-1524 this case happens when the maid has a vacation days and her replacement salary start date > 1st of payroll month
        if(groupOne + groupFour > numberOfDays) {
            groupFour = numberOfDays - groupOne;
        }

        int groupTwo = numberOfDays - (groupOne + groupFour);

        // remove the number of group 3 days between these period
        Integer group3Days = Setup.getApplicationContext().getBean(HousemaidUnpaidDayService.class).getGroup3DaysForHousemaid(housemaid, fromDate, toDate);
        groupTwo -= group3Days;
        if(groupTwo < 0) groupTwo = 0;

        groupFour = groupFour - (int) toSubtract;
        if (groupFour < 0){
            groupTwo = groupTwo + groupFour;
            if (groupTwo < 0) groupTwo = 0;
            groupFour = 0;
        }

        if(groupOne + groupTwo + group3Days + groupFour > numberOfDays)
            group3Days = numberOfDays - (groupOne + groupTwo + groupFour);
        if(group3Days < 0) group3Days = 0;


        if(groupOne == monthDays){
            salaryBreakDown.put("group1Salary", 1d * Math.round(basicSalary));
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group1Days", groupOne);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(basicSalary));
            salaryBreakDown.put("proratedCashAdvance", monthlyLoan);
            return salaryBreakDown;
        }
        if(groupTwo == monthDays){
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 1d * Math.round(accommodationSalary));
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", groupTwo);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(accommodationSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            return salaryBreakDown;
        }
        if(groupFour == monthDays){
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 1d * Math.round(mohreSalary));
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", groupFour);
            salaryBreakDown.put("total", 1d * Math.round(mohreSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            return salaryBreakDown;
        }


        double total = (basicSalary * groupOne) / monthDays;
        total += (accommodationSalary * groupTwo) / monthDays;
        total += (mohreSalary * groupFour) / monthDays;

        salaryBreakDown.put("group1Salary", (basicSalary * groupOne) / monthDays);
        salaryBreakDown.put("group2Salary", (accommodationSalary * groupTwo) / monthDays);
        salaryBreakDown.put("group4Salary", (mohreSalary * groupFour) / monthDays);
        salaryBreakDown.put("group1Days", groupOne);
        salaryBreakDown.put("group2Days", groupTwo);
        salaryBreakDown.put("group3Days", group3Days);
        salaryBreakDown.put("group4Days", groupFour);
        salaryBreakDown.put("total", 1d * Math.round(total));
        salaryBreakDown.put("proratedCashAdvance", (monthlyLoan * groupOne) / monthDays);

        return salaryBreakDown;
    }

    // from, to, payrollMonth: same month
    // This method used for calculating salary without "Salary Calculation Transactions"
    // For now it's used for:
    // 1- calculate maid salary
    // 2- used for Final Settlement too
    public Map<String, Object> getSalaryBreakDownNew(Housemaid housemaid, LocalDate payrollMonth,
                                                     java.sql.Date fromDate, java.sql.Date toDate, long previousVacationDays) {



        int monthDays = payrollMonth != null ? payrollMonth.dayOfMonth().getMaximumValue() : (new LocalDate(toDate)).dayOfMonth().getMaximumValue();

        Map<String, Object> salaryBreakDown = new HashMap<>();

        if(housemaid.getHousemaidType() != null &&
                housemaid.getHousemaidType() == HousemaidType.MAID_VISA) {

            double basicSalary = housemaid.getBasicSalary() != null ? housemaid.getBasicSalary() : 0d;
            double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0d;

            java.sql.Date startDate = new java.sql.Date(housemaid.getStartDate().getTime());

            // check startDate, terminationDate deductions
            LocalDate from = new LocalDate(startDate.after(fromDate) ? startDate : fromDate);
            LocalDate to  = new LocalDate(toDate);
            int numberOfDays = to.getDayOfMonth() - from.getDayOfMonth() + 1;

            numberOfDays = Math.max(0, numberOfDays);

            double amount, cashAdvance;

            if(numberOfDays != monthDays) {
                amount = (basicSalary * numberOfDays) / 30.4d;
                cashAdvance = (monthlyLoan * numberOfDays) / 30.4d;
            } else {
                amount = basicSalary;
                cashAdvance = monthlyLoan;
            }

            salaryBreakDown.put("group1Salary", amount);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group5Salary", 0d);
            salaryBreakDown.put("group6Salary", 0d);
            salaryBreakDown.put("group1Days", numberOfDays);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("group5Days", 0);
            salaryBreakDown.put("group6Days", 0);
            salaryBreakDown.put("total", amount);
            salaryBreakDown.put("proratedCashAdvance", cashAdvance);
            salaryBreakDown.put("vacationDays", (long)0);
            return salaryBreakDown;

        }

        HousemaidPayrollMonthlyGroup groups = null;

        // final settlement case
        if (payrollMonth == null) {
            groups = Setup.getApplicationContext().getBean(PayrollGroupService.class).createPayrollMonthlyGroup(housemaid, null, fromDate, toDate, false, false);
            payrollMonth = new LocalDate(toDate);
        } else {
            groups = Setup.getApplicationContext().getBean(PayrollGroupService.class).getOrCreateMonthlyGroupForMaid(housemaid, new java.sql.Date(payrollMonth.toDate().getTime()));
        }

        int groupOne = groups.getGr1Days().intValue();
        int groupTwo = groups.getGr2Days().intValue();
        int group3Days = groups.getGr3Days().intValue();
        int groupFour = groups.getGr4Days().intValue();
        int groupFive = groups.getGr5Days().intValue();
        int groupSix = groups.getGr6Days().intValue();
//        int groupFour = (int) groupFour;
        long toSubtract = Math.min(groupFour, (previousVacationDays + groupFour) - 30);
        // Vacation less than 30 days up till now
        if(toSubtract < 0) toSubtract = 0;



//        Map<HousemaidSalaryGroup, Double> salaries = Setup.getApplicationContext().getBean(PayrollGroupService.class)
//                .getSalaryForEachGroup(workDays);
        Double basicSalary = groups.getGr1Salary();

        Double accommodationSalary = groups.getGr2Salary();

        Double mohreSalary = groups.getGr4Salary();

        Double liveOutBasicSalary = groups.getGr5Salary();

        Double liveOutAccommodationSalary = groups.getGr6Salary();

        double monthlyLoan = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0d;


        int numberOfDays = new LocalDate(toDate).getDayOfMonth() - new LocalDate(fromDate).getDayOfMonth() + 1;

//        int minDay = 0;
//
//        groupOne += minDay;

//        if (housemaid.getStartDate() != null) {
//            LocalDate startDate = new LocalDate(housemaid.getStartDate());
//            if (housemaid.getReplacementSalaryStartDate() != null)
//                startDate = new LocalDate(housemaid.getReplacementSalaryStartDate());
//
//            if (startDate.isAfter(payrollMonth.withDayOfMonth(1))) {
//                int amount = startDate.getDayOfMonth() - 1;
//
//                numberOfDays -= amount;
//                groupOne -= Math.min(minDay, amount);
//            }
//        }


//        if(groupOne > numberOfDays) {
//            groupOne = numberOfDays;
//        }

        // PAY-1524 this case happens when the maid has a vacation days and her replacement salary start date > 1st of payroll month
//        if(groupOne + groupFour > numberOfDays) {
//            groupFour = numberOfDays - groupOne;
//        }

//        groupTwo = numberOfDays - (groupOne + groupFour);

        // remove the number of group 3 days between these period
//        group3Days = Setup.getApplicationContext().getBean(HousemaidUnpaidDayService.class).getGroup3DaysForHousemaid(housemaid, fromDate, toDate);
//        groupTwo -= group3Days;
//        if(groupTwo < 0) groupTwo = 0;

        groupFour = groupFour - (int) toSubtract;
//        if (groupFour < 0) {
//            groupTwo = groupTwo + groupFour;
//            if (groupTwo < 0) {
//                groupSix = groupSix + groupTwo;
//                if (groupSix < 0) {
//                    groupSix = 0;
//                }
//                groupTwo = 0;
//            }
//            groupFour = 0;
//        }

//        if(groupOne + groupTwo + group3Days + groupFour > numberOfDays)
//            group3Days = numberOfDays - (groupOne + groupTwo + groupFour);
//        if(group3Days < 0) group3Days = 0;

//        groupTwo = Math.max(0, groupTwo - (int) toSubtract);


        if(groupOne == monthDays){
            salaryBreakDown.put("group1Salary", 1d * Math.round(basicSalary));
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group5Salary", 0d);
            salaryBreakDown.put("group6Salary", 0d);
            salaryBreakDown.put("group1Days", groupOne);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("group5Days", 0);
            salaryBreakDown.put("group6Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(basicSalary));
            salaryBreakDown.put("proratedCashAdvance", monthlyLoan);
            salaryBreakDown.put("vacationDays", (long)groupFour);
            return salaryBreakDown;
        }
        if(groupTwo == monthDays){
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 1d * Math.round(accommodationSalary));
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group5Salary", 0d);
            salaryBreakDown.put("group6Salary", 0d);
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", groupTwo);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("group5Days", 0);
            salaryBreakDown.put("group6Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(accommodationSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            salaryBreakDown.put("vacationDays", (long)groupFour);
            return salaryBreakDown;
        }

        if(groupFour == monthDays){
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 1d * Math.round(mohreSalary));
            salaryBreakDown.put("group5Salary", 0d);
            salaryBreakDown.put("group6Salary", 0d);
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", groupFour);
            salaryBreakDown.put("group5Days", 0);
            salaryBreakDown.put("group6Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(mohreSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            salaryBreakDown.put("vacationDays", (long)groupFour);
            return salaryBreakDown;
        }

        if(groupFive == monthDays) {
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group5Salary", 1d * Math.round(liveOutBasicSalary));
            salaryBreakDown.put("group6Salary", 0d);
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("group5Days", groupFive);
            salaryBreakDown.put("group6Days", 0);
            salaryBreakDown.put("total", 1d * Math.round(liveOutBasicSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            salaryBreakDown.put("vacationDays", (long)groupFour);
            return salaryBreakDown;
        }

        if(groupSix == monthDays) {
            salaryBreakDown.put("group1Salary", 0d);
            salaryBreakDown.put("group2Salary", 0d);
            salaryBreakDown.put("group4Salary", 0d);
            salaryBreakDown.put("group5Salary", 0d);
            salaryBreakDown.put("group6Salary", 1d * Math.round(liveOutAccommodationSalary));
            salaryBreakDown.put("group1Days", 0);
            salaryBreakDown.put("group2Days", 0);
            salaryBreakDown.put("group3Days", 0);
            salaryBreakDown.put("group4Days", 0);
            salaryBreakDown.put("group5Days", 0);
            salaryBreakDown.put("group6Days", groupSix);
            salaryBreakDown.put("total", 1d * Math.round(liveOutAccommodationSalary));
            salaryBreakDown.put("proratedCashAdvance", 0d);
            salaryBreakDown.put("vacationDays", (long)groupFour);
            return salaryBreakDown;
        }



        double total = (basicSalary * groupOne) / monthDays;
        total += (accommodationSalary * groupTwo) / monthDays;
        total += (mohreSalary * groupFour) / monthDays;
        total += (liveOutBasicSalary * groupFive) / monthDays;
        total += (liveOutAccommodationSalary * groupSix) / monthDays;

        salaryBreakDown.put("group1Salary", (basicSalary * groupOne) / monthDays);
        salaryBreakDown.put("group2Salary", (accommodationSalary * groupTwo) / monthDays);
        salaryBreakDown.put("group4Salary", (mohreSalary * groupFour) / monthDays);
        salaryBreakDown.put("group5Salary", (liveOutBasicSalary * groupFive) / monthDays);
        salaryBreakDown.put("group6Salary", (liveOutAccommodationSalary * groupSix) / monthDays);
        salaryBreakDown.put("group1Days", groupOne);
        salaryBreakDown.put("group2Days", groupTwo);
        salaryBreakDown.put("group3Days", group3Days);
        salaryBreakDown.put("group4Days", groupFour);
        salaryBreakDown.put("group5Days", groupFive);
        salaryBreakDown.put("group6Days", groupSix);
        salaryBreakDown.put("total", 1d * Math.round(total));
        salaryBreakDown.put("proratedCashAdvance", (monthlyLoan * groupOne) / monthDays);
        salaryBreakDown.put("vacationDays", (long)groupFour);

        return salaryBreakDown;
    }

    //remove it later
    @Deprecated
    public Boolean isGroupOneVacation(Long housemaid, Map<Long, Date> lastVacationDates) {
        Date lastVacationDate = lastVacationDates.get(housemaid);
        if (lastVacationDate == null) return false;

        if (new java.sql.Date(lastVacationDate.getTime()).before(java.sql.Date.valueOf("2020-09-01"))) {
            return true;
        }
        return false;
    }
}
