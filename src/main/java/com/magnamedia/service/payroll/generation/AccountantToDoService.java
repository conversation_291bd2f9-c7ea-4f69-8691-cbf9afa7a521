package com.magnamedia.service.payroll.generation;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.OfficeStaffPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollExceptionsReportService;
import com.magnamedia.service.payroll.generation.newversion.AuditFilesService;
import com.magnamedia.service.payroll.generation.newversion.HousemaidPayrollAuditService;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import com.magnamedia.service.payroll.generation.newversion.TransferFilesService;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.awt.*;
import java.awt.font.TextAttribute;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.nio.file.Paths;
import java.text.AttributedString;
import java.util.*;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Saker Ali
 * Creation Date 19/06/2020
 */
@Service
@Lazy
public class AccountantToDoService {

    private static final Logger logger =
            Logger.getLogger(AccountantToDoService.class.getName());

    @Autowired
    @Lazy
    private AccountantToDoService selfReference;

    @Autowired
    private HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2;

    @Autowired
    private OfficeStaffPayrollPaymentServiceV2 officeStaffPayrollPaymentServiceV2;

    @Autowired
    private PayrollNotificationsService notificationsService;

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private PayrollAuditTodoService payrollAuditTodoService;

    @Autowired
    private PayrollAccountantTodoRepository payrollAccountantTodoRepository;

    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

    @Autowired
    private OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository;

    @Autowired
    private OfficeStaffPayrollBeanRepository officeStaffPayrollBeanRepository;

    @Autowired
    private LockDateService lockDateService;

    @Autowired
    private MailService mailService;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private PayrollHousemaidFinalSettlementRepository payrollHousemaidFinalSettlementRepository;

    @Autowired
    private PayrollAuditTodoRepository payrollAuditTodoRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    PayrollExceptionsReportService payrollExceptionsReportService;

    @Autowired
    private UserRepository userRepository;

//    @Transactional(propagation = Propagation.REQUIRES_NEW)
//    public void createAccountantToDoBasedOnPaymentRule(MonthlyPaymentRule rule) {
//
//        //if targeting maid visa and we can create Excluded MV Audit To-do then skip this rule
//        // it will be created after closing the audit to-do
//        if (rule.isTargetingMaidVisa() && payrollAuditTodoService.createExcludedMVAuditTodo(rule)) {
//            DebugHelper.sendMail("<EMAIL>","skip generating accountant todo id type " + rule.getPaymentMethod());
//            return;
//        }
//
//        switch (rule.getPaymentMethod()) {
//            case WPS:
//                selfReference.createAccountantToDo(rule, PayrollAccountantTodoType.WPS);
//                break;
//            case CASH:
//                selfReference.createAccountantToDo(rule, PayrollAccountantTodoType.CASH);
//                break;
//            case LOCAL_TRANSFER:
//                selfReference.createAccountantToDo(rule, PayrollAccountantTodoType.LOCAL_TRANSFER);
//                break;
//            case ACCORDING_TO_EMPLOYEE_PROFILE:
//                if(officeStaffPayrollPaymentServiceV2.countTargetList(rule, PayrollAccountantTodoType.BANK_TRANSFER) > 0) {
//                    selfReference.createAccountantToDo(rule, PayrollAccountantTodoType.BANK_TRANSFER);
//                }
//
//                if(officeStaffPayrollPaymentServiceV2.countTargetList(rule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) > 0) {
//                    selfReference.createAccountantToDo(rule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER);
//                }
//                break;
//        }
//        rule.setFinished(true);
//        Setup.getRepository(MonthlyPaymentRuleRepository.class).save(rule);
//    }


    public void createAccountantToDoBasedOnPaymentRuleNew(MonthlyPaymentRule rule) throws Exception{

        //if targeting maid visa and we can create Excluded MV Audit To-do then skip this rule
        // it will be created after closing the audit to-do
        if (rule.isTargetingMaidVisa() && payrollAuditTodoService.createExcludedMVAuditTodo(rule)) {
            DebugHelper.sendMail("<EMAIL>","createAccountantToDoBasedOnPaymentRuleNew --> skip generating accountant todo id type " + rule.getPaymentMethod());
            return;
        }

        switch (rule.getPaymentMethod()) {
            case WPS:
                selfReference.createAccountantToDoNew(rule, PayrollAccountantTodoType.WPS);
                break;
            case CASH:
                selfReference.createAccountantToDoNew(rule, PayrollAccountantTodoType.CASH);
                break;
            case LOCAL_TRANSFER:
                selfReference.createAccountantToDoNew(rule, PayrollAccountantTodoType.LOCAL_TRANSFER);
                break;
            case ACCORDING_TO_EMPLOYEE_PROFILE:
                if(officeStaffPayrollPaymentServiceV2.countTargetList(rule, PayrollAccountantTodoType.BANK_TRANSFER) > 0) {
                    selfReference.createAccountantToDoNew(rule, PayrollAccountantTodoType.BANK_TRANSFER);
                }

                if(officeStaffPayrollPaymentServiceV2.countTargetList(rule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) > 0) {
                    selfReference.createAccountantToDoNew(rule, PayrollAccountantTodoType.INTERNATIONAL_TRANSFER);
                }
                break;
        }
        rule.setFinished(true);
        Setup.getRepository(MonthlyPaymentRuleRepository.class).save(rule);
    }

//    @Transactional
//    public void createAccountantToDo(MonthlyPaymentRule rule, PayrollAccountantTodoType todoType) {
//        try {
//            List<Housemaid> housemaids = housemaidPayrollPaymentServiceV2.getTargetList(rule);
//            List<Housemaid> includedHousemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(rule);
//            List<OfficeStaff> officeStaffs = officeStaffPayrollPaymentServiceV2.getTargetList(rule, todoType);
//            List<OfficeStaff> includedOfficeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(rule, todoType);
//            List<OfficeStaff> terminatedStaffs = officeStaffPayrollPaymentServiceV2.getTerminatedListForDetailedFile(rule, todoType);
//            List<OfficeStaff> excludedStaffs = officeStaffPayrollPaymentServiceV2.getExcludedListForDetailedFile(rule, todoType);
//
//            DebugHelper.sendMail("<EMAIL>", String.format("createAccountantToDo of type %s for rule %s , housemaids size: %s , and officeStaffs size: %s", todoType.toString(), rule.getId(), housemaids.size(), officeStaffs.size()));
//            if (housemaids.isEmpty() && officeStaffs.isEmpty()) return;
//
//            PayrollAccountantTodo todo = new PayrollAccountantTodo(rule, todoType, housemaids, includedHousemaids, officeStaffs, includedOfficeStaffs, terminatedStaffs, excludedStaffs);
//            todo.setPayrollMonth(rule.getPayrollMonth());
//            Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
//            try {
//                todo.build();
//                Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
//
//                // insert a new background task to send emails
//                Setup.getApplicationContext()
//                        .getBean(BackgroundTaskService.class)
//                        .addDirectCallBackgroundTaskForEntity(
//                                "requestApprovalToProceed", "accountantToDoService", "payroll",
//                                "requestApprovalToProceed",
//                                todo.getEntityType(), todo.getId(), false,
//                                false, new Class[]{Long.class}, new Object[]{todo.getId()});
//            } catch (Exception ex) {
//                DebugHelper.sendExceptionMail("<EMAIL>", ex, String.format("Error while building todo %s for rule %s", todoType.toString(), rule.getId()), false);
//            }
//        } catch (Exception e) {
//            DebugHelper.sendExceptionMail("<EMAIL>", e, String.format("Error while building todo %s for rule %s", todoType.toString(), rule.getId()), false);
//
//        }
//
//    }

    public void createAccountantToDoNew(MonthlyPaymentRule rule, PayrollAccountantTodoType todoType) throws Exception{
        try {
            if (rule.isTargetingHousemaid() && rule.getPaymentMethod().equals(PaymentRulePaymentMethod.WPS) && rule.getPayrollType().equals(PayrollType.PRIMARY))
                selfReference.sendPayrollGenerationEmail(rule, true);
            PayrollAccountantTodo todo = payrollAccountantTodoRepository.findTopByMonthlyPaymentRuleAndTaskName(rule, todoType.toString());
            List<Housemaid> housemaids = housemaidPayrollPaymentServiceV2.getTargetList(rule);
            List<Housemaid> includedHousemaids = housemaidPayrollPaymentServiceV2.getIncludedTargetList(rule);
            List<OfficeStaff> officeStaffs = officeStaffPayrollPaymentServiceV2.getTargetList(rule, todoType);
            List<OfficeStaff> includedOfficeStaffs = officeStaffPayrollPaymentServiceV2.getIncludedTargetList(rule, todoType);
            List<OfficeStaff> terminatedStaffs = officeStaffPayrollPaymentServiceV2.getTerminatedListForDetailedFile(rule, todoType);
            List<OfficeStaff> excludedStaffs = officeStaffPayrollPaymentServiceV2.getExcludedListForDetailedFile(rule, todoType);

//            DebugHelper.sendMail("<EMAIL>", "createAccountantToDoNew --> check for changed and re generate");
            housemaidPayrollPaymentServiceV2.clearHousemaidLogsForChangedAndRecreate(housemaids, rule, null, todo, false, true);
            officeStaffPayrollPaymentServiceV2.clearOfficeStaffLogsForChangedAndRecreate(officeStaffs, rule, null, todo, false);
//            DebugHelper.sendMail("<EMAIL>", String.format("createAccountantToDo of type %s for rule %s , housemaids size: %s , and officeStaffs size: %s", todoType.toString(), rule.getId(), housemaids.size(), officeStaffs.size()));
            if (housemaids.isEmpty() && officeStaffs.isEmpty()) return;

            if( todo == null) {
                todo = new PayrollAccountantTodo(rule, todoType, housemaids, includedHousemaids, officeStaffs, includedOfficeStaffs, terminatedStaffs, excludedStaffs);
                todo.setStopped(true);
            }else{
                todo.setHousemaids(new HashSet<>(housemaids));
                todo.setIncludedHousemaids(new HashSet<>(includedHousemaids));
                todo.setOfficeStaffs(new HashSet<>(officeStaffs));
                todo.setIncludedofficeStaffs(new HashSet<>(includedOfficeStaffs));
                todo.setTerminatedOfficeStaffs(new HashSet<>(terminatedStaffs));
                todo.setExcludedOfficeStaffs(new HashSet<>(excludedStaffs));
            }

            todo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
            try {
                todo.buildNew();

                // insert a new background task to generate files emails
                Setup.getApplicationContext()
                        .getBean(BackgroundTaskService.class)
                        .addDirectCallBackgroundTaskForEntity(
                                "generateFileForAccountantTodo", "accountantToDoService", "payroll",
                                "generateFileForAccountantTodo",
                                todo.getEntityType(), todo.getId(), false,
                                false, new Class[]{Long.class}, new Object[]{todo.getId()});

                if (rule.isTargetingHousemaid() && rule.getPaymentMethod().equals(PaymentRulePaymentMethod.LOCAL_TRANSFER) && rule.getPayrollType().equals(PayrollType.PRIMARY))
                    selfReference.sendPayrollGenerationEmail(rule, false);
            } catch (Exception ex) {
                DebugHelper.sendExceptionMail("<EMAIL>", ex, String.format("Error while building todo %s for rule %s", todoType.toString(), rule.getId()), false);
                throw  ex;
            }
        } catch (Exception e) {
            String mails = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_EXCEPTION_NOTIFICATION_EMAILS);

            DebugHelper.sendExceptionMail(mails, e, String.format("Error while building todo %s for rule %s", todoType.toString(), rule.getId()), false);
            throw e;
        }

    }

    public void sendPayrollGenerationEmail(MonthlyPaymentRule rule, Boolean started) {
        Map<String, String> params = new HashMap<>();
        String payrollMonthYear = DateUtil.formatFullMonthFullYear(rule.getPayrollMonth());
        String payrollMonth = DateUtil.formatMonth(rule.getPayrollMonth());
        params.put("payroll_month", payrollMonth);

        if (started) {
            //send proof of transfer authorization
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_PAYROLL_GENERATION_START_RECEIVERS_EMAIL));
            List<EmailRecipient> CCRecipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_PAYROLL_GENERATION_START_CC_EMAIL));

//            TemplateEmail templateEmail = new TemplateEmail(payrollMonthYear + " Housemaids Primary payroll generation has started", "Payroll_Payroll_Generation_Started_Template", params);

            if (recipients.size() > 0) {
                //mailService.sendEmail(recipients, CCRecipients, templateEmail, null);
                messagingService.send(recipients, CCRecipients, "Payroll_Payroll_Generation_Started_Template",
                        payrollMonthYear + " Housemaids Primary payroll generation has started", null, null, params, null, null, null);
            }
        } else {
            //send proof of transfer authorization
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_PAYROLL_GENERATION_FINISHED_RECEIVERS_EMAIL));
            List<EmailRecipient> CCRecipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                    PayrollManagementModule.PARAMETER_PAYROLL_GENERATION_FINISHED_CC_EMAIL));

//            TemplateEmail templateEmail = new TemplateEmail(payrollMonthYear + " Housemaids Primary payroll generation has finished ", "Payroll_Payroll_Generation_Finished_Template", params);

            if (recipients.size() > 0) {
                messagingService.send(recipients, CCRecipients, "Payroll_Payroll_Generation_Finished_Template", payrollMonthYear + " Housemaids Primary payroll generation has finished ",
                        null, null, params, null, null, null);
//                mailService.sendEmail(recipients, CCRecipients, templateEmail, null);
            }
        }
    }

    public Boolean generateFileForAccountantTodo(Long PayrollAccountantTodo_id) throws Exception {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(PayrollAccountantTodo_id);
        if (todo == null)
            return false;

//        DebugHelper.sendMail("<EMAIL>", "Before generating files for accountant todo");
        AuditFilesService auditFilesService = Setup.getApplicationContext()
                .getBean(AuditFilesService.class);

        //get Included Payroll logs only

        int daysInPeriod = todo.getPayrollMonth().toLocalDate().lengthOfMonth();
        java.util.Date start = new LocalDate(todo.getPayrollMonth()).withDayOfMonth(1).toDate();
        java.util.Date end = new LocalDate(todo.getPayrollMonth()).dayOfMonth().withMaximumValue().toDate();

        PayrollAccountantTodoType todoType = PayrollAccountantTodoType.valueOf(todo.getTaskName());
        MonthlyPaymentRule monthlyPaymentRule = todo.getMonthlyPaymentRule();
        StringBuilder employeeTypes = new StringBuilder();
        for (PaymentRuleEmployeeType paymentRuleEmployeeType : monthlyPaymentRule.getEmployeeTypeList()) {
            if (employeeTypes.length() != 0) {
                employeeTypes.append(" and ");
            }
            employeeTypes.append(paymentRuleEmployeeType.getLabel());
        }

        List<OfficeStaffPayrollLog> includedOfficeStaffPayrollLogs = officeStaffPayrollLogRepository.findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByOfficeStaffName(todo, todo.getPayrollMonth());
        List<OfficeStaffPayrollLog> terminatedOfficeStaffPayrollLogs = officeStaffPayrollLogRepository.findByPayrollAccountantTodoAndTransferredFalseAndPayrollMonthAndOfficeStaffInOrderByOfficeStaffName(todo, todo.getPayrollMonth(), new ArrayList<>(todo.getTerminatedOfficeStaffs()));
        List<OfficeStaffPayrollLog> excludedOfficeStaffPayrollLogs = officeStaffPayrollLogRepository.findByPayrollAccountantTodoAndTransferredFalseAndPayrollMonthAndOfficeStaffInOrderByOfficeStaffName(todo, todo.getPayrollMonth(), new ArrayList<>(todo.getExcludedOfficeStaffs()));
        List<HousemaidPayrollLog> includedHousemaidPayrollLogs = housemaidPayrollLogRepository.findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthOrderByHousemaidName(todo, todo.getPayrollMonth());
        List<HousemaidPayrollLog> maidVisaLogsForPreviousMonths = housemaidPayrollLogRepository.findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrueAndPayrollMonthLessThanAndHousemaidUnpaidStatusOrderByHousemaidName(todo, todo.getPayrollMonth(), HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT);
        Map<Long, List<HousemaidPayrollLog>> maidVisaLogsForPreviousMonthsMap = PayrollGenerationLibrary.convertListToMap(maidVisaLogsForPreviousMonths);

        String ansariInfo = " of " + todo.getMonthlyPaymentRule().getEmployeesTypes()
                + (monthlyPaymentRule.getPayrollType() == null ? "" : " - " + monthlyPaymentRule.getPayrollType().getLabel())
                + " of " + DateUtil.formatSimpleMonth(monthlyPaymentRule.getPayrollMonth());

        if(todoType.equals(PayrollAccountantTodoType.INTERNATIONAL_TRANSFER) && todo.getUniquePaymentCode() != null){
            ansariInfo += " - " + todo.getUniquePaymentCode();
        }

        switch (todoType) {
            case WPS:
                try {
                    // insert a new background task to generate payroll exception file & send it in email to auditors
                    Setup.getApplicationContext()
                            .getBean(BackgroundTaskService.class)
                            .addDirectCallBackgroundTaskForEntity(
                                    "generateExceptionFileForAccountantTodoAndSendEmail", "accountantToDoService", "payroll",
                                    "generateExceptionFileForAccountantTodoAndSendEmail",
                                    todo.getEntityType(), todo.getId(), false,
                                    false,
                                    new Class[]{Long.class},
                                    new Object[]{todo.getId()});

                    // Create the other files:
                    //WPS FILE
                    Attachment wpsFile = todo.getAttachment("WPSTransferFile");
                    if(wpsFile == null) {
                        entityManager.detach(todo);
                        todo = Setup.getApplicationContext().getBean(TransferFilesService.class).
                                generateWPSTransferFile(monthlyPaymentRule, todo, ansariInfo, includedHousemaidPayrollLogs, includedOfficeStaffPayrollLogs, maidVisaLogsForPreviousMonthsMap, todo.getTotalIncludedMaids(), daysInPeriod, start, end);
                    }


                    //OFFICE STAFF DETAILED FILE
                    if(!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = todo.getAttachment("OfficeStaffDetailedPayrollFile");
                        if(staffDetailedFile == null) {
                            entityManager.detach(todo);
                            todo = auditFilesService.generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, todo, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, monthlyPaymentRule, todoType, true);
                        }
                    }

                    // HOUSEMAID DETAILED FILE
                    if(!includedHousemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = todo.getAttachment("HousemaidDetailedPayrollFile");
                        if(housemaidsDetailedFile == null) {
                            entityManager.detach(todo);
                            todo = auditFilesService
                                    .generateHousemaidFinalDetailedPayrollFileNew(includedHousemaidPayrollLogs, todo, monthlyPaymentRule, maidVisaLogsForPreviousMonthsMap, todoType, true);
                        }
                    }

                    entityManager.detach(todo);
                    todo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(todo.getId());

//                    Attachment noEidFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
//                            generateNoEIDFile(this.getMonthlyPaymentRule(), housemaidPayrollLogs, officeStaffPayrollLogs);
//                    if(noEidFile != null) {
//                        attachments.add(noEidFile);
//                    }

                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating files for todo type WPS", false);
                    throw ex;
                }

                todo.setLabel(String.format("Transfer Money to WPS for %s", employeeTypes));
                todo.setCharges((todo.getTotalIncludedStaffs() + todo.getTotalIncludedMaids()) * Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ANSARI_CHARGES_RATE)));
                todo.setVat(todo.getCharges() * 0.05d);

                break;
            case BANK_TRANSFER:
                try {
                    // insert a new background task to generate payroll exception file & send it in email to auditors
                    Setup.getApplicationContext()
                            .getBean(BackgroundTaskService.class)
                            .addDirectCallBackgroundTaskForEntity(
                                    "generateExceptionFileForAccountantTodoAndSendEmail", "accountantToDoService", "payroll",
                                    "generateExceptionFileForAccountantTodoAndSendEmail",
                                    todo.getEntityType(), todo.getId(), false,
                                    false,
                                    new Class[]{Long.class},
                                    new Object[]{todo.getId()});

                    // Create the other files:
                    //BANK TRANSFER FILE
                    Attachment bankTransferFile = todo.getAttachment("BankTransferFile");
                    if(bankTransferFile == null) {
                        entityManager.detach(todo);
                        todo = Setup.getApplicationContext().getBean(TransferFilesService.class).
                                generateBankTransferFile(todo, ansariInfo, includedOfficeStaffPayrollLogs);
                    }

                    //OFFICE STAFF DETAILED FILE
                    if(!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = todo.getAttachment("OfficeStaffDetailedPayrollFile");
                        if(staffDetailedFile == null) {
                            entityManager.detach(todo);
                            todo = auditFilesService.generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, todo, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, monthlyPaymentRule, todoType, true);
                        }
                    }


                    // HOUSEMAID DETAILED FILE
                    if(!includedHousemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = todo.getAttachment("HousemaidDetailedPayrollFile");
                        if(housemaidsDetailedFile == null) {
                            entityManager.detach(todo);
                            todo = auditFilesService
                                    .generateHousemaidFinalDetailedPayrollFileNew(includedHousemaidPayrollLogs, todo, monthlyPaymentRule, maidVisaLogsForPreviousMonthsMap, todoType, true);
                        }
                    }

                    entityManager.detach(todo);
                    todo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(todo.getId());

                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating files for todo type BANK TRANSFER", false);
                }

                todo.setLabel(String.format("Transfer Money to (%s) employees' bank accounts", includedOfficeStaffPayrollLogs.size()));

                break;
            case LOCAL_TRANSFER:

                try {
                    // insert a new background task to generate payroll exception file & send it in email to auditors
                    Setup.getApplicationContext()
                            .getBean(BackgroundTaskService.class)
                            .addDirectCallBackgroundTaskForEntity(
                                    "generateExceptionFileForAccountantTodoAndSendEmail", "accountantToDoService", "payroll",
                                    "generateExceptionFileForAccountantTodoAndSendEmail",
                                    todo.getEntityType(), todo.getId(), false,
                                    false,
                                    new Class[]{Long.class},
                                    new Object[]{todo.getId()});

                    // Create the other files:
                    //LOCAL TRANSFER FILE
                    Attachment localFile = todo.getAttachment("LocalTransferFile");
                    if(localFile == null) {
                        entityManager.detach(todo);
                        todo = Setup.getApplicationContext().getBean(TransferFilesService.class).
                                generateLocalTransferFile(monthlyPaymentRule, todo, ansariInfo, includedHousemaidPayrollLogs, includedOfficeStaffPayrollLogs, maidVisaLogsForPreviousMonthsMap);
                    }

                    //OFFICE STAFF DETAILED FILE
                    if(!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = todo.getAttachment("OfficeStaffDetailedPayrollFile");
                        if(staffDetailedFile == null) {
                            entityManager.detach(todo);
                            todo = auditFilesService.generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, todo, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, monthlyPaymentRule, todoType, true);
                        }
                    }

                    // HOUSEMAID DETAILED FILE
                    if(!includedHousemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = todo.getAttachment("HousemaidDetailedPayrollFile");
                        if(housemaidsDetailedFile == null) {
                            entityManager.detach(todo);
                            todo = auditFilesService
                                    .generateHousemaidFinalDetailedPayrollFileNew(includedHousemaidPayrollLogs, todo, monthlyPaymentRule, maidVisaLogsForPreviousMonthsMap, todoType, true);
                        }
                    }

                    entityManager.detach(todo);
                    todo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(todo.getId());

//                    //send email to Ansari
//                    Map<String, String> paramValues = new HashMap();
//                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
//                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_Local_Exchange_Template",
//                            paramValues, this.getAttachments());


                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating files for todo type LOCAL TRANSFER", false);
                }

                todo.setLabel(String.format("Transfer Money to %s for %s",
                        monthlyPaymentRule.getMoneyExchangeName(), employeeTypes));

                break;
            case INTERNATIONAL_TRANSFER:

                try {
                    // insert a new background task to generate payroll exception file & send it in email to auditors
                    Setup.getApplicationContext()
                            .getBean(BackgroundTaskService.class)
                            .addDirectCallBackgroundTaskForEntity(
                                    "generateExceptionFileForAccountantTodoAndSendEmail", "accountantToDoService", "payroll",
                                    "generateExceptionFileForAccountantTodoAndSendEmail",
                                    todo.getEntityType(), todo.getId(), false,
                                    false,
                                    new Class[]{Long.class},
                                    new Object[]{todo.getId()});

                    // Create the other files:
                    //INTERNATIONAL TRANSFER FILE
                    Attachment internationalFile = todo.getAttachment("InternationalTransferFile");
                    if(internationalFile == null) {
                        entityManager.detach(todo);
                        todo = Setup.getApplicationContext().getBean(TransferFilesService.class).
                                generateInternationalTransferFile(monthlyPaymentRule, todo, ansariInfo, includedOfficeStaffPayrollLogs);
                    }

                    //OFFICE STAFF DETAILED FILE
                    if(!includedOfficeStaffPayrollLogs.isEmpty()) {
                        Attachment staffDetailedFile = todo.getAttachment("OfficeStaffDetailedPayrollFile");
                        if(staffDetailedFile == null){
                            entityManager.detach(todo);
                            todo = auditFilesService.generateOfficeStaffFinalDetailedPayrollFile(includedOfficeStaffPayrollLogs, todo, terminatedOfficeStaffPayrollLogs, excludedOfficeStaffPayrollLogs, monthlyPaymentRule, todoType, true);
                        }
                    }

                    // HOUSEMAID DETAILED FILE
                    if(!includedHousemaidPayrollLogs.isEmpty()) {
                        Attachment housemaidsDetailedFile = todo.getAttachment("HousemaidDetailedPayrollFile");
                        if(housemaidsDetailedFile == null){
                            entityManager.detach(todo);
                            todo = auditFilesService
                                    .generateHousemaidFinalDetailedPayrollFileNew(includedHousemaidPayrollLogs, todo, monthlyPaymentRule, maidVisaLogsForPreviousMonthsMap, todoType, true);
                        }}

                    entityManager.detach(todo);
                    todo = Setup.getRepository(PayrollAccountantTodoRepository.class).getById(todo.getId());

//                    //send email to Ansari
//                    Map<String, String> paramValues = new HashMap();
//                    paramValues.put("payment_date", DateUtil.formatMonthDayYear(this.getPaymentDate()));
//                    Setup.getApplicationContext().getBean(EmailTemplateService.class).sendEmail("PAY_Request_International_Exchange_Template",
//                            paramValues, this.getAttachments());

                } catch (Exception ex) {
                    DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating files for todo type INTERNATIONAL TRANSFER", false);
                }

                todo.setLabel(String.format("Transfer Money to %s for %s",
                        monthlyPaymentRule.getMoneyExchangeName(), employeeTypes));

                break;
            case CASH:
                todo.setLabel("Pay cash salaries");
                break;
            case SENDING_TRANSACTIONS:
                todo.setLabel("Sending Transaction Number of the Transfers Done for Overseas Staff");
                break;
            case PENSION_AUTHORITY:
                todo.setLabel(String.format("Transfer Money to Pension Authority for (%s) Employees", includedOfficeStaffPayrollLogs.size()));
                break;
        }

        todo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);

//        DebugHelper.sendMail("<EMAIL>", "After generating files for accountant todo");

        // insert a new background task to send emails
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "requestApprovalToProceed", "accountantToDoService", "payroll",
                        "requestApprovalToProceed",
                        todo.getEntityType(), todo.getId(), false,
                        false, new Class[]{Long.class}, new Object[]{todo.getId()});

        return true;

    }

    public Boolean requestApprovalToProceed(Long PayrollAccountantTodo_id) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(PayrollAccountantTodo_id);
        if (todo == null)
            return false;
        String subject;
        String templateName = "PayrollFilesStatistics";
        String tokenPart = "PayrollFilesStatistics";
        String transfer_files = "";
        String firstColumn = "";
        String usersIds;
        List<EmailRecipient> recipients = new ArrayList<>();
        if(PayrollAccountantTodoType.BANK_TRANSFER == PayrollAccountantTodoType.valueOf(todo.getTaskName())) {
            subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT_FOR_BANK_TRANSFER);
            tokenPart = "Payroll_Payment_Approve_For_Bank_Transfer";
            usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS_FOR_BANK_TRANSFER);
            transfer_files = "bank transfer";
            firstColumn = "Payment File of bank transfers";
        }else {
            subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PAYMENT_APPROVE_SUBJECT);
            tokenPart = "Payroll_Payment_Approve";
            usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_PAYMENT_APPROVE_USER_ID_RECEIVERS);
            firstColumn = "Payment File to be sent to Ansari";
        }

        List<String> idsList = Arrays.asList(usersIds.split(";"));

        Map<String, Object> params = new HashMap<>();
        params.put("payrollMonth", DateUtil.formatSimpleMonthYear(todo.getPayrollMonth()));
        params.put("title", subject);
        params.put("transfer_files", transfer_files);
        params.put("firstColumn", firstColumn);
        params.putAll(PayrollGenerationLibrary.readFiles(todo.getAttachments()));
        if (params.get("auditTotal") != null)
            params.put("auditTotal", NumberFormatter.formatNumber((Double) params.get("auditTotal")));
        if (params.get("auditAED") != null)
            params.put("auditAED", NumberFormatter.formatNumber((Double) params.get("auditAED")));
        if (params.get("auditUSD") != null)
            params.put("auditUSD", NumberFormatter.formatNumber((Double) params.get("auditUSD")));

        List<Attachment> attachmentsListWithoutExceptionReport = new ArrayList<>();
        for(Attachment attachment: todo.getAttachments()) {
            if(!"payrollExceptionsReport".equals(attachment.getTag())) {
                attachmentsListWithoutExceptionReport.add(attachment);
            }
        }

        for(String userId : idsList) {
            User user = userRepository.findOne(Long.parseLong(userId));

            recipients = new ArrayList<>();
            EmailRecipient recipient = new Recipient(user.getEmail(), user.getName());
            recipients.add(recipient);

            String url = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_MANAGER_APPROVE, todo.getId().toString() + "#" + tokenPart, userId);

            params.put("url", url);

            TemplateEmail templateEmail = new TemplateEmail(subject, templateName, params);

            for (Attachment attachment : attachmentsListWithoutExceptionReport) {
                templateEmail.addAttachement(attachment);
            }
            Setup.getMailService().sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
                    .recipients(recipients)
                    .html()
                    .senderName(MessageTemplateService.getMaidsCcSenderName())
                    .secure()
                    .build());
        }
        // send the files to Payroll Auditors when sending to Ansari
        if(todo.getAttachments() != null && todo.getAttachments().size() > 0){
            recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDITORS_RECIPIENTS_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL));
            String subjectInfo = todo.getMonthlyPaymentRule().getEmployeesTypes() + " - "
                    + ("WPS".equals(todo.getTaskName()) ? "WPS" : StringHelper.enumToCapitalizedFirstLetter(todo.getTaskName()))
                    + " - " + todo.getMonthlyPaymentRule().getPayrollType().getLabel()
                    + " of " + DateUtil.formatSimpleMonthYear(todo.getPayrollMonth());
            subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDITORS_SUBJECT_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL);
            subject = subject.replaceFirst("@payroll_month_and_year@", subjectInfo);

            Map<String, String> params1 = new HashMap<>();
            params1.put("payroll_month_and_year", DateUtil.formatSimpleMonthYear(todo.getPayrollMonth()));

            messagingService.send(recipients, null, "Payroll_Send_Final_Payroll_Files_To_Payroll_Auditors",
                    subject, params1, attachmentsListWithoutExceptionReport, null);

//            templateEmail = new TemplateEmail(subject, "Payroll_Send_Final_Payroll_Files_To_Payroll_Auditors", params);
//            for (Attachment attachment : todo.getAttachments())
//                templateEmail.addAttachement(attachment);
//            Setup.getMailService().sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                    .recipients(recipients)
//                    .html()
//                    .senderName(MessageTemplateService.getMaidsCcSenderName())
//                    .secure()
//                    .build());
        }
        return true;
    }

    @Transactional
    public void createPensionAuthorityToDo(List<OfficeStaff> officeStaffs) {
        if(officeStaffs.isEmpty()) return;
        PayrollAccountantTodo todo = new PayrollAccountantTodo(PayrollAccountantTodoType.PENSION_AUTHORITY, officeStaffs);
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
        try {
            todo.build();
            Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
            messagingService.notifyAccountants(todo);
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while building pension authority todo", false);
        }
    }

    @Transactional
    public void createSendingTransactionsToDo(PayrollAccountantTodo payrollAccountantTodo) {
        PayrollAccountantTodo todo = new PayrollAccountantTodo(PayrollAccountantTodoType.SENDING_TRANSACTIONS,
                payrollAccountantTodo.getPaymentDate(),
                payrollAccountantTodo.getPayrollMonth());
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(todo);
        messagingService.notifyAccountants(todo);
    }

    public MonthlyPaymentRule createMonthlyRuleForSingleOfficeStaff(OfficeStaff officeStaff) {

        LocalDate payrollMonth = LocalDate.now().withDayOfMonth(1);
        MonthlyPaymentRule monthlyPaymentRule = new MonthlyPaymentRule();
        monthlyPaymentRule.setPayrollMonth(new java.sql.Date(payrollMonth.toDate().getTime()));
        monthlyPaymentRule.setPaymentDate(new java.sql.Date(System.currentTimeMillis()));
        monthlyPaymentRule.setFinished(true);
        monthlyPaymentRule.setSingleOfficeStaff(true);
        monthlyPaymentRule.setMoneyExchangeName(Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_MONEY_EXCHANGE_NAME));
        PaymentRuleEmployeeType employeeType;
        if(officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            employeeType = PaymentRuleEmployeeType.OVERSEAS;
            monthlyPaymentRule.setLockDate(lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), 0, PaymentRuleEmployeeType.OVERSEAS));
        } else if (officeStaff.getEmployeeType() == OfficeStaffType.DUBAI_STAFF_EXPAT) {
            employeeType = PaymentRuleEmployeeType.EXPATS;
            monthlyPaymentRule.setLockDate(lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), 0, PaymentRuleEmployeeType.EXPATS));
        } else {
            employeeType = PaymentRuleEmployeeType.EMIRATI;
            monthlyPaymentRule.setLockDate(lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), 0, PaymentRuleEmployeeType.EMIRATI));
        }
        List<PaymentRuleEmployeeType> employeeTypeList = new ArrayList<>();
        employeeTypeList.add(employeeType);
        monthlyPaymentRule.setEmployeeTypeList(employeeTypeList);
        monthlyPaymentRule.setPaymentMethod(officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF ? PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE : PaymentRulePaymentMethod.WPS);

        return monthlyPaymentRule;
    }

    @Transactional
    public PayrollAccountantTodo createAccountantToDoForSingleOfficeStaff(MonthlyPaymentRule rule, OfficeStaff officeStaff) {
        PayrollAccountantTodoType type = null;
        switch (rule.getPaymentMethod()) {
            case WPS:
                type = PayrollAccountantTodoType.WPS;
                break;
            case LOCAL_TRANSFER:
                type = PayrollAccountantTodoType.LOCAL_TRANSFER;
                break;
            case ACCORDING_TO_EMPLOYEE_PROFILE:
                TransferDestination transferDestination = officeStaff.getSelectedTransferDestination();
                if (transferDestination == null) {
                    throw new BusinessException("Please fill the employee's transfer information to complete this operation!");
                }
                if (transferDestination.getReceiveMoneyMethod() == ReceiveMoneyMethod.BANK_TRANSFER && !transferDestinationIsFilipino(officeStaff)) {
                    type = PayrollAccountantTodoType.BANK_TRANSFER;
                }
                if (transferDestination.getReceiveMoneyMethod() != ReceiveMoneyMethod.BANK_TRANSFER || transferDestinationIsFilipino(officeStaff)) {
                    type = PayrollAccountantTodoType.INTERNATIONAL_TRANSFER;
                }
                break;
        }
        if (type == null) {
            throw new RuntimeException("Unable to detect the right payment method!");
        }

        List<OfficeStaff> officeStaffList = new ArrayList<>();
        officeStaffList.add(officeStaff);
        PayrollAccountantTodo todo = new PayrollAccountantTodo(rule, type, new ArrayList<>(), new ArrayList<>(), officeStaffList, officeStaffList, new ArrayList<>(), new ArrayList<>());
        todo.setPayrollMonth(rule.getPayrollMonth());
        return todo;
    }

    private boolean transferDestinationIsFilipino(OfficeStaff officeStaff) {
        PicklistItem philippines = Setup.getItem("countries", "philippines");
        if (officeStaff.getSelectedTransferDestination() == null ||
                (officeStaff.getSelectedTransferDestination().getSelfReceiver() != null && officeStaff.getSelectedTransferDestination().getSelfReceiver())) {
            if (philippines.equals(officeStaff.getCountry())) {
                return true;
            }
        } else {
            if (philippines.equals(officeStaff.getSelectedTransferDestination().getCountry())) {
                return true;
            }
        }
        return false;
    }

    public OfficeStaffPayrollLog createPayrollLogForSingleOfficeStaff(OfficeStaff officeStaff, EmployeeLoan employeeLoan, MonthlyPaymentRule monthlyPaymentRule) {
        OfficeStaffPayrollBean bean = new OfficeStaffPayrollBean();
        bean.setBasicSalary(officeStaff.getBasicSalary());
        bean.setRemainingLoanBalance(String.valueOf(Math.round(officeStaff.getLoanBalance())));
        Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).runTransactions(officeStaff, new LocalDate(employeeLoan.getLoanDate()), new LocalDate(employeeLoan.getLoanDate()), employeeLoan.getLoanDate(), employeeLoan.getLoanDate(), new LocalDate(employeeLoan.getLoanDate()).withDayOfMonth(1), true);
        bean.setTotatIcome(employeeLoan.getAmount());
        bean.setBalance(employeeLoan.getAmount());
        bean.setUnpaidLoans(employeeLoan.getAmount());
        if (bean != null)
            bean = officeStaffPayrollBeanRepository.save(bean);
        OfficeStaffPayrollLog log = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).createPayrollLogBasedOnAll(officeStaff, bean, monthlyPaymentRule, null, null);
        log.setOfficeStaffPayrollBean(bean);
        log.setLogStatus(OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus.FINAL);
        log.setForEmployeeLoan(true);
        if (log.getTotalSalary() > 1) {
            log = officeStaffPayrollLogRepository.save(log);
        }
        return log;
    }

    public OfficeStaffPayrollLog createPayrollLogForSingleOfficeStaffWithCalculatingTransactions(OfficeStaff officeStaff, MonthlyPaymentRule monthlyPaymentRule) {
        LocalDate payrollStart = new LocalDate(officeStaffPayrollPaymentServiceV2.getPayrollStartLockDate(monthlyPaymentRule));
        LocalDate payrollEnd = new LocalDate(officeStaffPayrollPaymentServiceV2.getPayrollEndLockDate(monthlyPaymentRule));
        LocalDate payrollMonth = new LocalDate(monthlyPaymentRule.getPayrollMonth());

        Date payrollStartForManagerNotes = officeStaffPayrollPaymentServiceV2.getPayrollStartLockDateForManagerNotes(monthlyPaymentRule);
        Date payrollEndForManagerNotes = officeStaffPayrollPaymentServiceV2.getPayrollEndLockDateForManagerNotes(monthlyPaymentRule);

        //generate Log
        OfficeStaffPayrollLog log = Setup.getApplicationContext().getBean(OfficeStaffPayrollPaymentServiceV2.class).generateOneOfficeStaffLogBasedOnAll(officeStaff, monthlyPaymentRule, null, null, payrollStart, payrollEnd, payrollStartForManagerNotes, payrollEndForManagerNotes, payrollMonth, true, true);
        log.setLogStatus(OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus.FINAL);
        if (log.getTotalSalary() > 1) {
            log = officeStaffPayrollLogRepository.save(log);
        }
        return log;
    }

    public void createPaymentToDoForSingleOfficeStaff(MonthlyPaymentRule rule, OfficeStaff officeStaff,
                                                      List<OfficeStaffPayrollLog> logs, boolean withAccountantTodo) {
        Setup.getRepository(MonthlyPaymentRuleRepository.class).save(rule);

        PayrollAccountantTodo accountantTodo = createAccountantToDoForSingleOfficeStaff(rule, officeStaff);
        accountantTodo.setSingleOfficeStaff(true);
        accountantTodo.setCharges(Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_ANSARI_CHARGES_RATE)));
        accountantTodo.setVat(accountantTodo.getCharges() * 0.05d);
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);

        if (withAccountantTodo) {
            accountantTodo.buildForSingleOfficeStaff(officeStaff, logs);
            accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);
            messagingService.notifyAccountants(accountantTodo);

            selfReference.sendCelebrationEmailForOneStaff(officeStaff, accountantTodo.getAmount(), new Date());
        } else {
            for (OfficeStaffPayrollLog log : logs) {
                log.setTransferred(true);
                log.setWillBeIncluded(true);
                log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                log.setPayrollAccountantTodo(accountantTodo);
                Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(log);

                //PAY-1293
                //this block to mark all old payroll which was included to the current payroll as paid
                SelectQuery<OfficeStaffPayrollLog> staffQuery = new SelectQuery<>(OfficeStaffPayrollLog.class);
                staffQuery.filterBy("officeStaff.id", "=", officeStaff.getId())
                        .and("transferred", "=", false)
                        .and("willBeIncluded", "=", true)
                        .and("payrollMonth", "<", log.getPayrollMonth())
                        .and("forEmployeeLoan", "=", false);
                List<OfficeStaffPayrollLog> previousLogs = staffQuery.execute();
                for (OfficeStaffPayrollLog previousLog : previousLogs) {
                    previousLog.setTransferred(true);
                    previousLog.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                }
                Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(previousLogs);
            }
            accountantTodo.setCompleted(true);
            accountantTodo.setCeoAction(PayrollAccountantTodoManagerAction.APPROVED);
            accountantTodo.setManagerAction(PayrollAccountantTodoManagerAction.APPROVED);
            Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);
        }
    }

    @Transactional
    public PayrollAccountantTodo createPaymentToDoForFinalSettlements(MonthlyPaymentRule rule,
                                                     List<HousemaidPayrollLog> housemaidPayrollLogs) {
        rule = Setup.getRepository(MonthlyPaymentRuleRepository.class).save(rule);
        List<Housemaid> targetedHousemaids = housemaidPayrollLogs.stream().map(HousemaidPayrollLog::getHousemaid).collect(Collectors.toList());

        PayrollAccountantTodo accountantTodo = new PayrollAccountantTodo(rule, PayrollAccountantTodoType.WPS, new ArrayList<>(targetedHousemaids), new ArrayList<>(targetedHousemaids), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        accountantTodo.setPayrollMonth(rule.getPayrollMonth());
        accountantTodo.setSingleHousemaid(true);
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);

        accountantTodo.buildForHousemaidFinalSettlements(housemaidPayrollLogs);
        accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);
        messagingService.notifyAccountants(accountantTodo);
        return accountantTodo;
    }

    @Transactional
    public Boolean createPaymentToDoForCcSwitchedToMvTodo(Long payrollAuditTodoId) {
        PayrollAuditTodo payrollAuditTodo = payrollAuditTodoRepository.findOne(payrollAuditTodoId);
        HousemaidPayrollAuditService housemaidPayrollAuditService = Setup.getApplicationContext().getBean(HousemaidPayrollAuditService.class);
        List<HousemaidPayrollLog> housemaidPayrollLogs = new ArrayList<>();
        java.sql.Date payrollMonth = payrollAuditTodo.getPayrollMonth();

        int index = 1;

        //this is a special case for the deployment month only to consider starting from the first of previous month not from the audit closing date
        String firstDeploymentDateSt = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_CC_SWITCHING_TO_MV_DEPLOYMENT_PAYROLL_MONTH);
        Date firstDeploymentDate = firstDeploymentDateSt != null && !firstDeploymentDateSt.isEmpty() ? DateUtil.parseDateDashedSql(firstDeploymentDateSt) : null;

        HousemaidPayrollPaymentServiceV2 housemaidPayrollPaymentServiceV2 = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class);
        java.util.Date from = firstDeploymentDate != null && firstDeploymentDate.equals(payrollMonth) ? firstDeploymentDate : housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(DateUtil.addMonthsSql(payrollMonth, -1));
        java.util.Date to = housemaidPayrollPaymentServiceV2.getAuditTodoClosingDate(payrollMonth);
        for (CcMaidSwitchedToMv maidSwitchedToMv : housemaidPayrollAuditService.getCCSwitchedToMvMaidsTargetList(from, to)) {
            if (maidSwitchedToMv.getFinalAmountToBeTransferred().equals(0.0))
                continue;
            Housemaid housemaid = maidSwitchedToMv.getHousemaid();
            HousemaidPayrollLog housemaidPayrollLog = new HousemaidPayrollLog(housemaid, "EDR", housemaid.getName(), maidSwitchedToMv, payrollMonth, housemaid.getName(), "UAE", housemaid.getPhoneNumber());
            housemaidPayrollLog.setSn("H-" + index++);
            housemaidPayrollLogs.add(housemaidPayrollLog);
        }

        //if there are no logs (all records have 0 amount) then skip and don't create Accountant to-do
        if (housemaidPayrollLogs.size() == 0)
            return true;

        MonthlyPaymentRule rule = payrollAuditTodo.getMonthlyPaymentRule();
        List<Housemaid> targetedHousemaids = housemaidPayrollLogs.stream().map(HousemaidPayrollLog::getHousemaid).collect(Collectors.toList());

        PayrollAccountantTodo accountantTodo = new PayrollAccountantTodo(rule, PayrollAccountantTodoType.WPS, new ArrayList<>(targetedHousemaids), new ArrayList<>(targetedHousemaids), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        accountantTodo.setPayrollMonth(payrollMonth);
        accountantTodo.setSingleHousemaid(true);
        Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);

        accountantTodo.buildForCcSwitchedToMv(housemaidPayrollLogs);
        accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).save(accountantTodo);
        messagingService.notifyAccountants(accountantTodo);
        return true;
    }

    @Async
    public void createToDosAsync(List<MonthlyPaymentRule> rules) {
        for(MonthlyPaymentRule rule: rules) {
            try {
                Setup.getApplicationContext().getBean(AccountantToDoService.class)
                        .createAccountantToDoBasedOnPaymentRuleNew(rule);
            } catch (Exception ex) {
                DebugHelper.sendExceptionMail("<EMAIL>", ex, "Audit Case - Generate accountant todo for rule #" + rule.getId(), false);
            }
        }
    }

    public Boolean sendCelebrationEmails (Long PayrollAccountantTodo_id) {
        PayrollAccountantTodo todo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(PayrollAccountantTodo_id);
        if (todo != null) {
            List<OfficeStaffPayrollLog> payrollLogs = officeStaffPayrollLogRepository.findByPayrollAccountantTodoAndNotTerminated(todo, OfficeStaffStatus.TERMINATED);

            for (OfficeStaffPayrollLog log : payrollLogs) {
                OfficeStaff staff = log.getOfficeStaff();
                selfReference.sendCelebrationEmailForOneStaff(staff, log.getTotalSalary(), todo.getPaymentDate());
            }
        }
        return true;
    }

    public void sendCelebrationEmailForOneStaff(OfficeStaff officeStaff, Double salary, Date paymentDate){
        if(officeStaff.getEmail() != null) {
            try {
//                DebugHelper.sendMail("<EMAIL>", "sendCelebrationEmailForOneStaff for " + officeStaff.getId() + "-" + officeStaff.getName());
                logger.log(Level.SEVERE, "sendCelebrationEmailForOneStaff for " + officeStaff.getId() + "-" + officeStaff.getName());
                String templateName = "PaydayCelebrationTemplate";
                List<EmailRecipient> recipients = Recipient.parseEmailsString(officeStaff.getEmail());
                String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYDAY_CELEBRATION_EMAIL_SUBJECT);
                subject = subject.replace("@employee_first_name@", officeStaff.getFirstName());

                String day = DateUtil.formatDay(paymentDate);
                String month = DateUtil.formatMonth(paymentDate).toUpperCase();

                final BufferedImage image = ImageIO.read(new URL(PublicPageHelper.getResourceLink("PayDay.png")));
                Graphics g = image.getGraphics();
                Font font = new Font("Calibri", Font.BOLD, 22);
                AttributedString attributedText = new AttributedString(month);
                attributedText.addAttribute(TextAttribute.FONT, font);
                attributedText.addAttribute(TextAttribute.FOREGROUND, Color.WHITE);

                FontMetrics metrics = g.getFontMetrics(font);
                int positionX = (image.getWidth() * 48 / 100) - (metrics.stringWidth(month) / 2);
                int positionY = (image.getHeight() * 54 / 100) - (metrics.getHeight() / 2) + metrics.getAscent();
                g.drawString(attributedText.getIterator(), positionX, positionY);
                g.dispose();

                g = image.getGraphics();
                font = new Font("Calibri", Font.BOLD, 48);
                attributedText = new AttributedString(day);
                attributedText.addAttribute(TextAttribute.FONT, font);
                attributedText.addAttribute(TextAttribute.FOREGROUND, Color.BLACK);

                metrics = g.getFontMetrics(font);
                positionX = (image.getWidth() * 48 / 100) - (metrics.stringWidth(day) / 2);
                positionY = (image.getHeight() * 72 / 100) - (metrics.getHeight() / 2) + metrics.getAscent();
                g.drawString(attributedText.getIterator(), positionX, positionY);
                g.dispose();

                String fileName = officeStaff.getName() + new Date().getTime() + ".png";
                String imageFilePath = Paths.get(System.getProperty("java.io.tmpdir"),
                        fileName).toString();
                File tempFile = new File(imageFilePath);
                ImageIO.write(image, "png", tempFile);
                Attachment attachment = Storage.storeTemporary(fileName, new FileInputStream(tempFile), "payday_celebration_image", false);

                Map<String, Object> params = new HashMap<>();
                params.put("imgUrl", PublicPageHelper.getResourceLinkFromAttachment(attachment.getUuid()));
                params.put("amount", (officeStaff.getSalaryCurrency() != null ? officeStaff.getSalaryCurrency().name() + " " : "AED ") + NumberFormatter.formatNumber(salary));

                TemplateEmail templateEmail = new TemplateEmail(subject, templateName, params);
                Setup.getMailService().sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
                        .recipients(recipients)
                        .html()
                        .senderName(MessageTemplateService.getMaidsCcSenderName())
                        .secure()
                        .build());
            } catch (Exception e) {
                DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception While sendCelebrationEmailForOneStaff for : " + officeStaff.getId(), false);
            }
        }
    }

    //used now to pay final settlements and CC switched to MV to-do
    public MonthlyPaymentRule createMonthlyRuleForSpecialUse(LocalDate payrollMonth) {

        MonthlyPaymentRule monthlyPaymentRule = new MonthlyPaymentRule();
        monthlyPaymentRule.setPayrollMonth(new java.sql.Date(payrollMonth.toDate().getTime()));
        monthlyPaymentRule.setPaymentDate(new java.sql.Date(System.currentTimeMillis()));
        monthlyPaymentRule.setFinished(true);
        monthlyPaymentRule.setSingleHousemaid(true);
        monthlyPaymentRule.setMoneyExchangeName(Setup.getParameter(Setup.getCurrentModule(),
                PayrollManagementModule.PARAMETER_MONEY_EXCHANGE_NAME));
        monthlyPaymentRule.setLockDate(lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), 0, PaymentRuleEmployeeType.HOUSEMAIDS));
        monthlyPaymentRule.setEmployeeTypeList(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS));
        monthlyPaymentRule.setPaymentMethod(PaymentRulePaymentMethod.WPS);

        return monthlyPaymentRule;
    }

    @Transactional
    public void payAllFinalSettlements() {
        List<PayrollHousemaidFinalSettlement> finalSettlementList = payrollHousemaidFinalSettlementRepository.findByReadyForPayTrueAndRelatedToTodoFalseOrderByHousemaid_Name();

        if (finalSettlementList.size() > 0) {
            List<HousemaidPayrollLog> housemaidPayrollLogs = new ArrayList<>();
            java.sql.Date payrollMonth = new java.sql.Date(new LocalDate(new Date()).withDayOfMonth(1).toDate().getTime());
            int index = 1;
            for (PayrollHousemaidFinalSettlement finalSettlement : finalSettlementList) {
                Housemaid housemaid = finalSettlement.getHousemaid();
                HousemaidPayrollLog housemaidPayrollLog = new HousemaidPayrollLog(housemaid, "EDR", housemaid.getName(), finalSettlement, payrollMonth, housemaid.getName(), "UAE", housemaid.getPhoneNumber());
                housemaidPayrollLog.setSn("H-" + index++);
                housemaidPayrollLogs.add(housemaidPayrollLog);
            }

            PayrollAccountantTodo todo = createPaymentToDoForFinalSettlements(createMonthlyRuleForSpecialUse(LocalDate.now().withDayOfMonth(1)), housemaidPayrollLogs);

            //set payroll accountant to-do for all final settlements
            for (PayrollHousemaidFinalSettlement finalSettlement : finalSettlementList) {
                finalSettlement.setPayrollAccountantTodo(todo);
                finalSettlement.setRelatedToTodo(true);
                payrollHousemaidFinalSettlementRepository.save(finalSettlement);
            }
        }
    }

    // This method is used as background task to generate exception file & send email
    public void generateExceptionFileForAccountantTodoAndSendEmail(Long todoId){

        PayrollAccountantTodo todo = payrollAccountantTodoRepository.getById(todoId);
        String todoType = StringHelper.enumToCapitalizedFirstLetter(todo.getTaskName());
        MonthlyPaymentRule monthlyPaymentRule = todo.getMonthlyPaymentRule();

        List<Housemaid> ccMaids = housemaidRepository.findIncludedHousemaidsByAccountantTodoAndHousemaidTypeNot(todo, HousemaidType.MAID_VISA);
        List<Housemaid> visaMaids = housemaidRepository.findIncludedHousemaidsByAccountantTodoAndHousemaidType(todo, HousemaidType.MAID_VISA);
        List<OfficeStaff> officeStaffs = new ArrayList<>(todo.getIncludedofficeStaffs());


        // 1- call generatePayrollExceptionsReport
        Attachment attachment = payrollExceptionsReportService.generatePayrollExceptionsReport(ccMaids, todo, visaMaids, officeStaffs, monthlyPaymentRule, todoType);

        // 2- send payroll exception file to Payroll Auditors in an email
        if (attachment != null && "payrollExceptionsReport".equals(attachment.getTag())) {
            String templateName = "Payroll_Send_Final_Payroll_Exception_File_To_Payroll_Auditors";

            String subject = "Payroll Exceptions File of "
                    + todo.getMonthlyPaymentRule().getEmployeesTypes() + " - "
                    + ("WPS".equals(todo.getTaskName()) ? "WPS" : StringHelper.enumToCapitalizedFirstLetter(todo.getTaskName()))
                    + " - " + todo.getMonthlyPaymentRule().getPayrollType().getLabel()
                    + " of " + DateUtil.formatSimpleMonthYear(todo.getPayrollMonth());

            List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDITORS_RECIPIENTS_OF_PAYROLL_FILES_AFTER_CFO_APPROVAL));


            Map<String, String> params = new HashMap<>();
            params.put("payroll_month_and_year", DateUtil.formatSimpleMonthYear(monthlyPaymentRule.getPayrollMonth()));

            Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(recipients, null, templateName, subject
                            , params, Arrays.asList(attachment), null);
        }
    }
}
