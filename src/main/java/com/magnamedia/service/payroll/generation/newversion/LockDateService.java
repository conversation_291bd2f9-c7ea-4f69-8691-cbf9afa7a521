package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.MonthlyPaymentRuleRepository;
import com.magnamedia.repository.OfficeStaffPayrollLogRepository;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.Arrays;
import java.util.List;

@Service
public class LockDateService {

    public Date getLockDate(Date month, int before, PaymentRuleEmployeeType employeeType) {
        Date payrollMonth = new Date(DateUtil.addMonths(month, before).getTime());

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(payrollMonth);

        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(employeeType)) {
                return monthlyPaymentRule.getLockDate();
            }
        }

        return new Date(new LocalDate(payrollMonth).withDayOfMonth(27).toDate().getTime());
    }

    public Date getPaymentDateBasedOnMonthlyRule(Date noteDate, Date month, PaymentRuleEmployeeType employeeType) {
        Date payrollMonth = new Date(month.getTime());
        Date prevPayrollMonth = new Date(DateUtil.addMonths(month, -1).getTime());

        //check Previous month first
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(prevPayrollMonth);
        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(employeeType)) {
                if(!monthlyPaymentRule.getPaymentDate().before(noteDate))
                    return monthlyPaymentRule.getPaymentDate();
                else
                    break;
            }
        }

        //check Current month now
        rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(payrollMonth);
        for (MonthlyPaymentRule monthlyPaymentRule : rules) {
            if (monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(employeeType)) {
                if(!monthlyPaymentRule.getPaymentDate().before(noteDate))
                    return monthlyPaymentRule.getPaymentDate();
                else
                    break;
            }
        }

        return null;
    }

    public Date getPaymentDate(Date date, PaymentRuleEmployeeType employeeType, PayrollType payrollType) {
        Date month = new Date(new LocalDate(date).withDayOfMonth(1).toDate().getTime());
        Date minimumLogDate = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findMinimumLogDate();
        if (minimumLogDate == null)
            return null;
        if(minimumLogDate != null && month.after(minimumLogDate))
            return getPaymentDateBasedOnMonthlyRule(date, month, employeeType);
        else {
            return new Date(new LocalDate(month).withDayOfMonth(27).toDate().getTime());
        }
    }

    public Date getPaymentDateBasedDate(java.util.Date date) {
        if (date == null) return null;

        Date payrollMonth = new Date(new LocalDate(date).withDayOfMonth(1).toDate().getTime());

        //check Previous month first
        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("payrollMonth", "=", payrollMonth);
        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.sortBy("paymentDate", true);
        List<MonthlyPaymentRule> rules = selectQuery.execute();
        if (rules.size() > 0)
            return rules.get(0).getPaymentDate();

        return new Date(new LocalDate(payrollMonth).plusMonths(1).withDayOfMonth(3).toDate().getTime());
    }

    public Date getPaymentDateBasedOnEmployeeType(Date date, PaymentRuleEmployeeType employeeType, PayrollType payrollType, List<PaymentRulePaymentMethod> paymentMethods) {
        Date payrollMonth = new Date(new LocalDate(date).withDayOfMonth(1).toDate().getTime());

        //check Previous month first
        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
        selectQuery.filterBy("payrollMonth", "=", payrollMonth);

        if(payrollType != null)
            selectQuery.filterBy("payrollType", "=", payrollType);

        selectQuery.filterBy("employeeTypeList", "MEMBER OF", employeeType);

        if(paymentMethods != null && !paymentMethods.isEmpty())
            selectQuery.filterBy("paymentMethod", "IN", paymentMethods);
        selectQuery.filterBy("singleHousemaid", "=", false);
        selectQuery.filterBy("singleOfficeStaff", "=", false);
        selectQuery.sortBy("paymentDate", true);
        List<MonthlyPaymentRule> rules = selectQuery.execute();
        if (rules.size() > 0)
            return rules.get(0).getPaymentDate();

        return new Date(new LocalDate(payrollMonth).plusMonths(1).withDayOfMonth(3).toDate().getTime());
    }

}
