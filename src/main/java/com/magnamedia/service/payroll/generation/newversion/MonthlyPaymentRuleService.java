package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.AbstractPaymentRule;
import com.magnamedia.entity.MonthlyPaymentRule;
import com.magnamedia.entity.PaymentRuleConfiguration;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.MonthlyPaymentRuleRepository;
import com.magnamedia.repository.PaymentRuleConfigurationRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class MonthlyPaymentRuleService {

    @Transactional
    public void createDefaultPaymentRuleConfigurations() {
        if(Setup.getRepository(PaymentRuleConfigurationRepository.class).count() > 0) return;

        List<PaymentRuleConfiguration> configurations = new ArrayList<>();
        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS, PaymentRuleEmployeeType.EXPATS),
                Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITH_MOL, "Ansari", PaymentRulePaymentMethod.WPS,
                3, 2, false, true, PayrollType.PRIMARY));

//        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
//                Arrays.asList(MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITH_AND_WITHOUT, "Ansari", PaymentRulePaymentMethod.CASH,
//                3, 1, false, true, PayrollType.PRIMARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS, PaymentRuleEmployeeType.EXPATS),
                Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITHOUT_MOL, "Ansari", PaymentRulePaymentMethod.LOCAL_TRANSFER,
                3, 2, false, true, PayrollType.PRIMARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.EMIRATI),
                null, null, AbstractPaymentRule.MolType.WITH_MOL, "Ansari", PaymentRulePaymentMethod.WPS,
                28, 1, true, false, PayrollType.PRIMARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.OVERSEAS),
                null, null, null, "Ansari", PaymentRulePaymentMethod.ACCORDING_TO_EMPLOYEE_PROFILE,
                29, 1, true, false, PayrollType.PRIMARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
                Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITH_MOL, "Ansari", PaymentRulePaymentMethod.WPS,
                7, 1, false, true, PayrollType.SECONDARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
                Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITHOUT_MOL, "Ansari", PaymentRulePaymentMethod.LOCAL_TRANSFER,
                7, 1, false, true, PayrollType.SECONDARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
                Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITH_MOL, "Ansari", PaymentRulePaymentMethod.WPS,
                14, 1, false, true, PayrollType.SECONDARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
                Arrays.asList(MaidType.MAID_VISA, MaidType.MAIDS_CC), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITHOUT_MOL, "Ansari", PaymentRulePaymentMethod.LOCAL_TRANSFER,
                14, 1, false, true, PayrollType.SECONDARY));

//        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
//                Arrays.asList(MaidType.MAID_VISA), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITH_AND_WITHOUT, "Ansari", PaymentRulePaymentMethod.LOCAL_TRANSFER,
//                18, 1, false, true, PayrollType.SECONDARY));

        configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
                Arrays.asList(MaidType.MAIDS_CC, MaidType.MAID_VISA), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITH_MOL, "Ansari", PaymentRulePaymentMethod.WPS,
                21, 1, false, true, PayrollType.SECONDARY));

       configurations.add(new PaymentRuleConfiguration(Arrays.asList(PaymentRuleEmployeeType.HOUSEMAIDS),
                Arrays.asList(MaidType.MAIDS_CC, MaidType.MAID_VISA), Arrays.asList(PaymentRuleMaidStatus.WITH_CLIENT, PaymentRuleMaidStatus.IN_ACCOMMODATION), AbstractPaymentRule.MolType.WITHOUT_MOL, "Ansari", PaymentRulePaymentMethod.LOCAL_TRANSFER,
                21, 1, false, true, PayrollType.SECONDARY));

        Setup.getRepository(PaymentRuleConfigurationRepository.class).save(configurations);
    }

    @Transactional
    public void createMonthlyPaymentRule(PaymentRuleConfiguration configuration) {
        MonthlyPaymentRule monthlyRule = new MonthlyPaymentRule(configuration);
        monthlyRule.setAuditingFinished(false);
        Setup.getRepository(MonthlyPaymentRuleRepository.class).save(monthlyRule);
    }
}
