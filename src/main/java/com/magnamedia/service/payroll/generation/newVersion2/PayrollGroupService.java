package com.magnamedia.service.payroll.generation.newVersion2;

import com.magnamedia.controller.HousemaidController;
import com.magnamedia.controller.SalaryRuleController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.HousemaidSalaryProjection;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.module.type.LogisticsWorkOrderStatus;
import com.magnamedia.module.type.PendingStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.message.MessagingService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class PayrollGroupService {

    @Autowired
    private HousemaidRepository housemaidRepository;

    @Autowired
    private HousemaidUnpaidDayRepository housemaidUnpaidDayRepository;

    @Autowired
    private HousemaidController housemaidController;

    @Autowired
    private HousemaidPayrollAttendanceLogRepository housemaidPayrollAttendanceLogRepository;

    @Autowired
    LogisticsWorkOrderRepository logisticsWorkOrderRepository;

    @Autowired
    ContractRepository contractRepository;

    List<HousemaidStatus> gr1Status = Arrays.asList(HousemaidStatus.WITH_CLIENT, HousemaidStatus.ASSIGNED_OFFICE_WORK);

    private static final Logger logger = Logger.getLogger(PayrollGroupService.class.getName());

    @Deprecated
    public void removeDuplicateDaysFromWorkerDays(List<WorkDays> allGroupDays) {
        Map<Integer, HousemaidSalaryGroup> result = new HashMap<>();
        for (int i = 1; i < allGroupDays.size(); i++) {
            WorkDays prevWorkerDays = allGroupDays.get(i - 1);
            WorkDays currWorkDays = allGroupDays.get(i);
            if (currWorkDays.getFrom() == prevWorkerDays.getTo()) {
                boolean isGroup1 = (currWorkDays.getGroup().equals(HousemaidSalaryGroup.GROUP_1) || prevWorkerDays.getGroup().equals(HousemaidSalaryGroup.GROUP_1));
                boolean isGroup4 = (currWorkDays.getGroup().equals(HousemaidSalaryGroup.GROUP_4) || prevWorkerDays.getGroup().equals(HousemaidSalaryGroup.GROUP_4));
                if (result.get(currWorkDays.getFrom()) == null) {
                    result.put(currWorkDays.getFrom(), isGroup1 ? HousemaidSalaryGroup.GROUP_1 : (isGroup4 ? HousemaidSalaryGroup.GROUP_4 : HousemaidSalaryGroup.GROUP_2));
                } else if (isGroup1 && (result.get(currWorkDays.getFrom()).equals(HousemaidSalaryGroup.GROUP_4) || result.get(currWorkDays.getFrom()).equals(HousemaidSalaryGroup.GROUP_2))) {
                    result.put(currWorkDays.getFrom(), HousemaidSalaryGroup.GROUP_1);
                } else if (isGroup4 && result.get(currWorkDays.getFrom()).equals(HousemaidSalaryGroup.GROUP_2)) {
                    result.put(currWorkDays.getFrom(), HousemaidSalaryGroup.GROUP_4);
                }
            }
        }
        Map<Integer, Boolean> isAdded = new HashMap<>();
        for (Integer key : result.keySet()) {
            isAdded.put(key, false);
        }
        for (WorkDays workDays : allGroupDays) {
            if (workDays.getFrom() == workDays.getTo()) {
                if (result.get(workDays.getFrom()) != null) {
                    if (result.get(workDays.getFrom()).equals(workDays.getGroup())
                            && isAdded.get(workDays.getFrom()) != null && !isAdded.get(workDays.getFrom())) {
                        isAdded.put(workDays.getFrom(), true);
                    } else {
                        workDays.setNumberOfDays(workDays.getNumberOfDays() - 1);
                        workDays.setFrom(workDays.getFrom() - 1);
                    }

                }
            }
            else {
                if (result.get(workDays.getFrom()) != null) {
                    if (result.get(workDays.getFrom()).equals(workDays.getGroup())
                            && isAdded.get(workDays.getFrom()) != null && !isAdded.get(workDays.getFrom())) {
                        isAdded.put(workDays.getFrom(), true);
                    } else {
                        workDays.setNumberOfDays(workDays.getNumberOfDays() - 1);
                        workDays.setFrom(workDays.getFrom() - 1);
                    }

                }
                if (result.get(workDays.getTo()) != null) {
                    if (result.get(workDays.getTo()).equals(workDays.getGroup())
                            && isAdded.get(workDays.getTo()) != null && !isAdded.get(workDays.getTo())) {
                        isAdded.put(workDays.getTo(), true);
                    } else {
                        workDays.setNumberOfDays(workDays.getNumberOfDays() - 1);
                        workDays.setTo(workDays.getTo() + 1);
                    }
                }
            }
        }

        return;
    }

    @Deprecated
    public void createHousemaidPayrollEvent(Housemaid housemaid, Date eventDate) {
        HousemaidPayrollEventRepository housemaidPayrollEventRepository = Setup.getRepository(HousemaidPayrollEventRepository.class);
        if (eventDate == null) {
            eventDate = new Date();
        }
        java.sql.Date endEffectiveDate;
        if (eventDate.getHours() >= 18) {
            endEffectiveDate = new java.sql.Date(DateUtil.getDayStart(eventDate).getTime());
        } else {
            endEffectiveDate = new java.sql.Date(DateUtil.getDayStart(DateUtil.addDays(new Date(), -1)).getTime());
        }
        PayrollSwitchEventType type = null;
        PayrollSwitchEventType maybeWasType = null;
        if (housemaid.getLiveOut()) {
            type = PayrollSwitchEventType.LIVE_IN_TO_LIVE_OUT;
            maybeWasType = PayrollSwitchEventType.LIVE_OUT_TO_LIVE_IN;
        } else {
            type = PayrollSwitchEventType.LIVE_OUT_TO_LIVE_IN;
            maybeWasType = PayrollSwitchEventType.LIVE_IN_TO_LIVE_OUT;
        }
        List<HousemaidPayrollEvent> historyEventsInSameDay = housemaidPayrollEventRepository.findByHousemaidAndEndEffectiveDateAndType(housemaid, endEffectiveDate, maybeWasType);
        if (historyEventsInSameDay != null && !historyEventsInSameDay.isEmpty()) {
            housemaidPayrollEventRepository.delete(historyEventsInSameDay);
        } else {
            HousemaidPayrollEvent newEvent = new HousemaidPayrollEvent();
            newEvent.setHousemaid(housemaid);
            newEvent.setType(type);
            newEvent.setEndEffectiveDate(endEffectiveDate);
            newEvent.setBasicSalary(housemaid.getBasicSalary());
            newEvent.setAccommodationSalary(housemaid.getAccommodationSalary());
            newEvent.setPrimarySalary(housemaid.getPrimarySalary());
            housemaidPayrollEventRepository.save(newEvent);
        }
    }

    @Deprecated
    public Map<HousemaidSalaryGroup, Long> getNumberOfDaysForEachGroup(Housemaid housemaid, List<WorkDays> workerDays, LocalDate payrollMonth) {
        Map<HousemaidSalaryGroup, Long> result = new HashMap<>();
        int forgiveDaysWithClientG1 = 0;
        int forgiveDaysWithClientG5 = 0;
        result.put(HousemaidSalaryGroup.GROUP_1, 0L);
        result.put(HousemaidSalaryGroup.GROUP_2, 0L);
        result.put(HousemaidSalaryGroup.GROUP_3, 0L);
        result.put(HousemaidSalaryGroup.GROUP_4, 0L);
        result.put(HousemaidSalaryGroup.GROUP_5, 0L);
        result.put(HousemaidSalaryGroup.GROUP_6, 0L);
        for (WorkDays workDays : workerDays) {
            if (Arrays.asList(HousemaidSalaryGroup.GROUP_2, HousemaidSalaryGroup.GROUP_6).contains(workDays.getGroup())) {
                java.sql.Date start = new java.sql.Date(payrollMonth.withDayOfMonth(workDays.getTo()).toDate().getTime());
                java.sql.Date end = new java.sql.Date(payrollMonth.withDayOfMonth(workDays.getFrom()).toDate().getTime());
                PicklistItem forgivenessType = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_WITH_CLIENT);
                Integer forgiveDaysCount = housemaidUnpaidDayRepository.findForgivenDaysForHousemaidByType(housemaid, start, end, forgivenessType);
                if (forgiveDaysCount != null && forgiveDaysCount > 0) {
                    workDays.setNumberOfDays(workDays.getNumberOfDays() - forgiveDaysCount);
                    if (workDays.getGroup().equals(HousemaidSalaryGroup.GROUP_2)) {
                        forgiveDaysWithClientG1 += forgiveDaysCount;
                    } else {
                        forgiveDaysWithClientG5 += forgiveDaysCount;
                    }
                }
                Integer g3DaysCount = housemaidUnpaidDayRepository.findGroup3DaysForHousemaid(housemaid, start, end);
                if (g3DaysCount != null && g3DaysCount > 0) {
                    workDays.setNumberOfDays(workDays.getNumberOfDays() - g3DaysCount);
                    result.put(HousemaidSalaryGroup.GROUP_3, result.get(HousemaidSalaryGroup.GROUP_3) + g3DaysCount);
                }
            }
            result.put(workDays.getGroup(), result.get(workDays.getGroup()) + workDays.getNumberOfDays());
        }
        result.put(HousemaidSalaryGroup.GROUP_1, result.get(HousemaidSalaryGroup.GROUP_1) + forgiveDaysWithClientG1);
        result.put(HousemaidSalaryGroup.GROUP_5, result.get(HousemaidSalaryGroup.GROUP_5) + forgiveDaysWithClientG5);

        return result;
    }

    @Deprecated
    public Map<HousemaidSalaryGroup, Double> getSalaryForEachGroup(List<WorkDays> workerDays) {
        Map<HousemaidSalaryGroup, Double> salaries = new HashMap<>();
        for (WorkDays workDays : workerDays) {
            if (salaries.get(workDays.getGroup()) == null) {
                switch (workDays.getGroup()) {
                    case GROUP_1:
                    case GROUP_5:
                        salaries.put(workDays.getGroup(), workDays.getBasicSalary());
                        break;
                    case GROUP_2:
                    case GROUP_6:
                        salaries.put(workDays.getGroup(), workDays.getAccommodationSalary());
                        break;
                    case GROUP_4:
                        salaries.put(workDays.getGroup(), workDays.getPrimarySalary());
                        break;
                }
            }
        }
        return salaries;
    }


    @Transactional
    public void createHousemaidPayrollAttendanceLogAndUnpaidDayForHousemaidList(List<String> housemaidIdsAsString, Date date) {
        if (housemaidIdsAsString == null || housemaidIdsAsString.isEmpty() || date == null) {
            return;
        }
        java.sql.Date lastAttendanceCreated = null;
        Integer found = null;
        int daysBetween = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_NUMBER_OF_DAYS_TO_CREATE_ATTENDANCE_LOG));
        for (String housemaidId : housemaidIdsAsString) {
            Housemaid housemaid = housemaidRepository.findOne(Long.parseLong(housemaidId));
            HousemaidPayrollAttendanceLog lastLog = housemaidPayrollAttendanceLogRepository
                    .findTop1ByHousemaidOrderByAttendanceDayDesc(housemaid);
            if (lastLog != null) {
                lastAttendanceCreated = lastLog.getAttendanceDay();
                daysBetween = DateUtil.getDaysBetween(lastAttendanceCreated, date);
            } else {
                lastAttendanceCreated = new java.sql.Date(DateUtil.addDays(date, -daysBetween).getTime());
            }

            Boolean dateIsYesterday =   new LocalDate(date.getTime()).compareTo(new LocalDate(DateUtil.addDaysSql(new java.sql.Date(System.currentTimeMillis()), -1))) == 0;

            for (int i = 1; i <= daysBetween; i++) {
                Date date1 = DateUtil.addDays(lastAttendanceCreated, i);
                Boolean takeFromMaidDirectly = dateIsYesterday && (i == daysBetween);
                HousemaidPayrollAttendanceLog log = createHousemaidPayrollAttendanceLog(housemaid, date1, takeFromMaidDirectly);
                if (log != null && HousemaidSalaryGroup.GROUP_3.equals(log.getSalaryGroup()) && !HousemaidType.MAID_VISA.equals(log.getHousemaidType())
                        && log.getChangingGroupReason() == null) {
                    found = housemaidUnpaidDayRepository.countByHousemaidAndUnpaidDate(housemaid, new java.sql.Date(date1.getTime()));
                    if (found == null || found == 0) {
                        HousemaidUnpaidDay housemaidUnpaidDay = new HousemaidUnpaidDay(housemaid, new java.sql.Date(date1.getTime()), log.getHousemaidStatus(), housemaid.getPendingStatus(), log.getNoShowTotalHours());
                        housemaidUnpaidDayRepository.save(housemaidUnpaidDay);
                    }

                }
            }
        }
    }


    @Transactional
    public HousemaidPayrollAttendanceLog createHousemaidPayrollAttendanceLog(Housemaid housemaid, Date attendanceDate, Boolean takeFromMaidDirectly) {


        Long yesterdayRevision = housemaidRepository.getLastRevisionsByHousemaid(housemaid.getId(), DateUtil.getStartDayValue(attendanceDate));
        if (yesterdayRevision == null) {
            yesterdayRevision = 0L;
        }
        List<HousemaidSalaryProjection> housemaidHistory = housemaidRepository.getSalaryRevisionsByHousemaid(housemaid.getId(), yesterdayRevision, DateUtil.getEndDayValue(attendanceDate));

        double noShowTotalHours = 0;

        double noShowThreshold;
        try {
            noShowThreshold = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_NO_SHOW_THRESHOLD));
        } catch (Exception e) {
            noShowThreshold = 12; // set noShowThreshold value to 12
        }

        boolean liveOut = false;
        HousemaidPayrollAttendanceLog attendanceLog;
        HousemaidSalaryProjection current;
        Date maidGotHiredDate;

        if (housemaidHistory != null && !housemaidHistory.isEmpty()) {
            attendanceLog = new HousemaidPayrollAttendanceLog();
            attendanceLog.setHousemaid(housemaid);
            for (int i = 0; i < housemaidHistory.size(); i++) {

                current = housemaidHistory.get(i);

                if (HousemaidStatus.NO_SHOW.equals(current.getStatus()) && current.getLastModificationDate() != null) {
                    if(i == 0){ // This is the 1st housemaidHistory for that day : 100% occurred in one of the previous days
                        if(housemaidHistory.size() == 1){ // The maid was NO_HOW for the whole day
                            noShowTotalHours = 24.0;
                        } else { // The maid's status is NO_SHOW since previous days
                            noShowTotalHours = DateUtil.difBet2DateInHOURDouble(DateUtil.getStartDayValue(attendanceDate), housemaidHistory.get(i+1).getLastModificationDate());
                        }
                    } else if(i == housemaidHistory.size() - 1) { // The maid status is set to NO_SHOW at the end of that day
                        noShowTotalHours += DateUtil.difBet2DateInHOURDouble(current.getLastModificationDate(), DateUtil.getDayEnd(current.getLastModificationDate()));
                    } else { // during the day
                        noShowTotalHours += DateUtil.difBet2DateInHOURDouble(current.getLastModificationDate(), housemaidHistory.get(i+1).getLastModificationDate());
                    }
                }

                if (gr1Status.contains(current.getStatus())) {
                    attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_1);
                    attendanceLog.setHousemaidStatus(current.getStatus());

                } else if (!HousemaidSalaryGroup.GROUP_1.equals(attendanceLog.getSalaryGroup()) && HousemaidStatus.ON_VACATION.equals(current.getStatus())) {
                    attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_4);
                    attendanceLog.setHousemaidStatus(current.getStatus());
                } else if (( attendanceLog.getSalaryGroup() == null || HousemaidSalaryGroup.GROUP_3.equals(attendanceLog.getSalaryGroup())) && !HousemaidStatus.NO_SHOW.equals(current.getStatus()) &&
                        (!HousemaidStatus.PENDING_FOR_DISCIPLINE.equals(current.getStatus()) || !PendingStatus.PENDING_FOR_TERMINATION.equals(current.getPendingStatus()))) {
                    attendanceLog.setHousemaidStatus(current.getStatus());
                    attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_2);
                }
                if (current.getLiveOut() != null && current.getLiveOut()) {
                    liveOut = true;
                }
                if (i == 0) {
                    if (HousemaidStatus.NO_SHOW.equals(current.getStatus()) || (HousemaidStatus.PENDING_FOR_DISCIPLINE.equals(current.getStatus()) && PendingStatus.PENDING_FOR_TERMINATION.equals(current.getPendingStatus()))) {
                        attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_3);
                        attendanceLog.setHousemaidStatus(current.getStatus());
                    }
                    attendanceLog.setHousemaidType(current.getHousemaidType());
                }
            }

            // If the maid has noShowTotalHours > noShowThreshold at that day && her attendanceLog.salaryGroup is grp2 (In Accommodation)
            if (noShowTotalHours >= noShowThreshold && (attendanceLog.getSalaryGroup().equals(HousemaidSalaryGroup.GROUP_2) || attendanceLog.getSalaryGroup().equals(HousemaidSalaryGroup.GROUP_3))) {
                attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_3);
                attendanceLog.setHousemaidStatus(HousemaidStatus.NO_SHOW);
                attendanceLog.setNoShowTotalHours(noShowTotalHours);
            }

        } else {
            logger.info("createHousemaidPayrollAttendanceLog No Revision #" + housemaid.getId());
            return null;
        }

        //get the components , (takeFromMaidDirectly = true when we are creating an attendance for yesterday) in this case take the components directly from the maid
        Double basicSalary = null, primarySalary = null, accommodationSalary = null;
        if (!takeFromMaidDirectly || (!housemaid.getLiveOut() && liveOut)) {
            basicSalary = housemaidRepository.getHousemaidBasicSalaryComponent(housemaid.getId(), DateUtil.getEndDayValue(attendanceDate), liveOut);
            primarySalary = housemaidRepository.getHousemaidPrimarySalaryComponent(housemaid.getId(), DateUtil.getEndDayValue(attendanceDate), liveOut);
            accommodationSalary = housemaidRepository.getHousemaidAccommodationSalaryComponent(housemaid.getId(), DateUtil.getEndDayValue(attendanceDate), liveOut);
        }

        //correct the group in case of live-out (just for Grp1 -> Grp5 and Grp2 -> Grp6)
        if (liveOut) {
            if (HousemaidSalaryGroup.GROUP_1.equals(attendanceLog.getSalaryGroup())) {
                attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_5);
            } else if (HousemaidSalaryGroup.GROUP_2.equals(attendanceLog.getSalaryGroup())) {
                attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_6);
            }
        }

        //fill the components
        logger.info("createHousemaidPayrollAttendanceLog get component from housemaid #" + housemaid.getId() + " , live out = " + liveOut  + " , basicSalary = " + basicSalary + " , primarySalary = " + primarySalary + " , accommodationSalary = " + accommodationSalary);
        attendanceLog.setBasicSalary(basicSalary == null ? housemaid.getBasicSalary() : basicSalary);
        attendanceLog.setAccommodationSalary(accommodationSalary == null ? housemaid.getAccommodationSalary() : accommodationSalary);
        attendanceLog.setMohreSalary(primarySalary == null ? housemaid.getPrimarySalary() : primarySalary);

        attendanceLog.setLiveOut(liveOut);
        attendanceLog.setAttendanceDay(new java.sql.Date(attendanceDate.getTime()));
        attendanceLog.setPayrollMonth(new java.sql.Date(new LocalDate(attendanceDate).withDayOfMonth(1).toDate().getTime()));

        if (HousemaidSalaryGroup.GROUP_4.equals(attendanceLog.getSalaryGroup())) {
            HousemaidVacationRepository housemaidVacationRepository = Setup.getRepository(HousemaidVacationRepository.class);
            // Check if the maid is in unpaid vacation, then her salary group is grp3
            if (housemaidVacationRepository.existsByHousemaidAndStartDateLessThanEqualAndEndDateGreaterThanEqualAndUnpaidVacationTrue(attendanceLog.getHousemaid(), attendanceLog.getAttendanceDay(), attendanceLog.getAttendanceDay())) {
                attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_3);
                attendanceLog.setChangingGroupReason(ChangingGroupReason.Unpaid_Vacation);
            }
        }
        attendanceLog = housemaidPayrollAttendanceLogRepository.silentSave(attendanceLog);
        return attendanceLog;
    }

    public HousemaidPayrollMonthlyGroup getOrCreateMonthlyGroupForMaid(Housemaid housemaid, java.sql.Date payrollMonth) {
        HousemaidPayrollMonthlyGroup group = Setup.getRepository(HousemaidPayrollMonthlyGroupRepository.class).findTop1ByHousemaidAndPayrollMonth(housemaid, payrollMonth);
        if (group == null) {
            group = createPayrollMonthlyGroup(housemaid, payrollMonth, null, null, true, false);
        }
        return group;
    }

    @Transactional
    public HousemaidPayrollMonthlyGroup createPayrollMonthlyGroup(Housemaid housemaid, java.sql.Date payrollMonth, java.sql.Date from, java.sql.Date to, Boolean toSave, boolean finalFile) {
        if (payrollMonth != null && from == null && to == null) {
            from = payrollMonth;
            to = new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
            if (housemaid != null) {
                if (housemaid.getStartDate() != null) {
                    LocalDate startDate = new LocalDate(housemaid.getStartDate());
                    if (housemaid.getReplacementSalaryStartDate() != null) {
                        startDate = new LocalDate(housemaid.getReplacementSalaryStartDate());
                    }

                    if (startDate.isAfter(new LocalDate(payrollMonth).withDayOfMonth(1))) {
                        from = new java.sql.Date(startDate.toDate().getTime());
                    }
                }
            }
        }
        HousemaidPayrollMonthlyGroup group = prepareGroup(housemaid, from, to, payrollMonth);
        group.setNotFinal(!finalFile);
        if (toSave != null && toSave && payrollMonth != null) {
            HousemaidPayrollMonthlyGroupRepository payrollMonthlyGroupRepository = Setup.getRepository(HousemaidPayrollMonthlyGroupRepository.class);
            group = payrollMonthlyGroupRepository.silentSave(group);
        }
        return group;
    }

    public HousemaidPayrollMonthlyGroup prepareGroup(Housemaid housemaid, java.sql.Date from, java.sql.Date to, java.sql.Date payrollMonth) {
        HousemaidPayrollMonthlyGroup group = new HousemaidPayrollMonthlyGroup();
        group.setPayrollMonth(payrollMonth);
        group.setFromDate(from);
        group.setToDate(to);
        group.setHousemaid(housemaid);
        checkIfNeedMigration(housemaid, from, to);
        List<Object[]> attendanceLogs = housemaidPayrollAttendanceLogRepository.findAllByHousemaidGroupingBySalaryGroupAndAttendanceDayBetween(housemaid.getId(), from, to);
        for (Object[] log : attendanceLogs) {
            HousemaidSalaryGroup groupGroup = HousemaidSalaryGroup.valueOf(log[1].toString());
            HousemaidPayrollAttendanceLog log1 = null;
            long numberOfDay = Long.parseLong(log[2].toString());
            switch (groupGroup) {
                case GROUP_1:
                    log1 = housemaidPayrollAttendanceLogRepository
                            .findTop1ByHousemaidAndAttendanceDayBetweenAndLiveOutOrderByAttendanceDayDesc(housemaid, from, to, false);
                    group.setGr1Days(numberOfDay);
                    group.setGr1Salary(log1 != null ? log1.getBasicSalary() : 0.0);
                    break;

                case GROUP_2:
                    log1 = housemaidPayrollAttendanceLogRepository
                            .findTop1ByHousemaidAndAttendanceDayBetweenAndLiveOutOrderByAttendanceDayDesc(housemaid, from, to, false);
                    group.setGr2Days(numberOfDay);
                    group.setGr2Salary(log1 != null ? log1.getAccommodationSalary() : 0.0);
                    break;

                case GROUP_3:
                    group.setGr3Days(numberOfDay);
                    break;

                case GROUP_4:
                    log1 = housemaidPayrollAttendanceLogRepository
                            .findTop1ByHousemaidAndAttendanceDayBetweenOrderByAttendanceDayDesc(housemaid, from, to);
                    group.setGr4Days(numberOfDay);
                    group.setGr4Salary(log1 != null ? log1.getMohreSalary() : 0.0);
                    break;

                case GROUP_5:
                    log1 = housemaidPayrollAttendanceLogRepository
                            .findTop1ByHousemaidAndAttendanceDayBetweenAndLiveOutOrderByAttendanceDayDesc(housemaid, from, to, true);
                    group.setGr5Days(numberOfDay);
                    group.setGr5Salary(log1 != null ? log1.getBasicSalary() : 0.0);
                    break;

                case GROUP_6:
                    log1 = housemaidPayrollAttendanceLogRepository
                            .findTop1ByHousemaidAndAttendanceDayBetweenAndLiveOutOrderByAttendanceDayDesc(housemaid, from, to, true);
                    group.setGr6Days(numberOfDay);
                    group.setGr6Salary(log1 != null ? log1.getAccommodationSalary() : 0.0);
                    break;
            }
        }
        return group;

    }

    private void checkIfNeedMigration(Housemaid housemaid, java.sql.Date from, java.sql.Date to) {
        Integer numberOfAttendance = housemaidPayrollAttendanceLogRepository.countByHousemaidAndAttendanceDayBetween(housemaid, from, to);
        Integer numberOfDaysFromTo = DateUtil.getDaysBetween(from, to) + 1;
        logger.info("checkIfNeedMigration from = " + from + ", to = " + to);
        logger.info("numberOfAttendance = " + numberOfAttendance);
        logger.info("numberOfDaysFromTo = " + numberOfDaysFromTo);
        if (numberOfAttendance != null && numberOfAttendance.intValue() != numberOfDaysFromTo.intValue()) {
            for (int i = 0 ; i < numberOfDaysFromTo ; i++) {
                java.sql.Date date = DateUtil.addDaysSql(from, i);
                if (!housemaidPayrollAttendanceLogRepository.existsByHousemaidAndAttendanceDay(housemaid, date)){
                    createHousemaidPayrollAttendanceLog(housemaid, date, false);
                }
            }
        }
    }

    public void applyComponentRule(Housemaid housemaid, Map<String, Double> components, PayrollSwitchEventType switchType) {
        Double basicSalary, accommodationSalary, mohreSalary, overTimeSalary, cashAdvance, holidaySalary, airfareFee;

        accommodationSalary = components.getOrDefault("accommodationsalary", 0.0);
        mohreSalary = components.getOrDefault("primarysalary", 0.0);
        overTimeSalary = components.getOrDefault("overtime", 0.0);
        cashAdvance = components.getOrDefault("monthlyloan", 0.0);
        holidaySalary = components.getOrDefault("holiday", 0.0);
        airfareFee = components.getOrDefault("airfarefee", 0.0);

        // Take the “Basic for Accommodation” component from the salary rule.
        basicSalary = mohreSalary + overTimeSalary + cashAdvance + holidaySalary + airfareFee;

        switch (switchType) {
            case MV_TO_CC_LIVE_OUT:
            case MV_TO_CC_LIVE_IN:
            case LIVE_OUT_TO_LIVE_IN:
                // Keep the values of the “MOHRE Salary” & “Holiday” components as they are.
                mohreSalary = housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0;
                holidaySalary = housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0;

                // Calculate the new value of the “Cash advance” component
                cashAdvance = basicSalary - (mohreSalary + overTimeSalary + holidaySalary);
                break;

            case LIVE_IN_TO_LIVE_OUT:
                //        Keep the values of the “MOHRE Salary”, “Holiday” & “Cash advance” components as they are.
                mohreSalary = housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0;
                holidaySalary = housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0;
                cashAdvance = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0.0;

                // Calculate the “Overtime” component = Salary Rule Total Salary - (MOHRE Salary + Holiday + Cash advance)
                overTimeSalary = basicSalary - (mohreSalary + holidaySalary + cashAdvance);
                break;

        }


        housemaid.setAccommodationSalary(accommodationSalary);
        housemaid.setPrimarySalary(mohreSalary);
        housemaid.setAirfareFee(airfareFee);
        housemaid.setHoliday(holidaySalary);
        housemaid.setMonthlyLoan(cashAdvance);
        housemaid.setOverTime(overTimeSalary);

        if (validateComponent(basicSalary, housemaid)) {
            housemaidRepository.save(housemaid);
        } else {
            housemaidController.zeroingHousemaidSalaryComponents(housemaid.getId());
        }

    }



    public boolean validateComponent(double basicSalary, Housemaid housemaid) {
        double mohreSalary = housemaid.getPrimarySalary() != null ? housemaid.getPrimarySalary() : 0.0;
        double holiday = housemaid.getHoliday() != null ? housemaid.getHoliday() : 0.0;
        double overTime = housemaid.getOverTime() != null ? housemaid.getOverTime() : 0.0;
        double cashAdvance = housemaid.getMonthlyLoan() != null ? housemaid.getMonthlyLoan() : 0.0;
        if (basicSalary - (mohreSalary + holiday) < 0 || cashAdvance < 0.0 || overTime < 0.0) {
            String subject = housemaid.getName() + "’s Total Salary is Not Exceeding Contract Minimum Salary";
            String details = "total salary, which was just updated or entered, is less than her contract minimum salary";
            String component = "salary components now meet the ministry contract and our company policies";
            sendComponentSalaryAlert(housemaid, subject, details, component);
            return false;
        }
        if (!HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())) {
            if (basicSalary - (mohreSalary + holiday + overTime) == 0.0) {
                String subject = housemaid.getName() + "’s Cash advance is Not correct";
                String details = "cash advance, which was just updated or entered, is incorrect";
                String component = "new cash advance value is correct";
                sendComponentSalaryAlert(housemaid, subject, details, component);
                return false;
            }
        }
        return true;
    }

    private void sendComponentSalaryAlert(Housemaid housemaid, String subject, String details, String componentCheck) {

        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_NEGATIVE_SALARY_COMPONENT_RECIPIENTS));
        Map<String, String> param = new HashMap<>();
        param.put("maid_full_name", housemaid.getName());
        param.put("component_details", details);
        param.put("component_check", componentCheck);
        Setup.getApplicationContext().getBean(MessagingService.class).send(recipients, null, "Payroll_Negative_Salary_Component_Email_Template",
                subject, param, null, housemaid);

    }

    public void checkOfficeWorkDaysBeforeStartDate(Housemaid housemaid, Date noteDate) {
        if (housemaid == null || housemaid.getLandedInDubaiDate() == null || housemaid.getStartDate() == null) {
             return;
        }
        List<HousemaidPayrollAttendanceLog> logs = housemaidPayrollAttendanceLogRepository
                .findByHousemaidAndAttendanceDayBetweenAndHousemaidStatus(housemaid, new java.sql.Date(housemaid.getLandedInDubaiDate().getTime()), new java.sql.Date(housemaid.getStartDate().getTime()), HousemaidStatus.ASSIGNED_OFFICE_WORK);
        if (logs == null || logs.isEmpty()) {
            return;
        }
        int numberOfDays = logs.size();

        Map<java.sql.Date, Integer> monthsDays = new HashMap<>();
        for (HousemaidPayrollAttendanceLog log : logs) {
            monthsDays.put(log.getPayrollMonth(), monthsDays.getOrDefault(log.getPayrollMonth(), 0) +1);
        }

        StringBuilder noteReason = new StringBuilder("She has worked for " + numberOfDays + " days as an office worker on " + DateUtil.formatDateDashed(logs.get(0).getAttendanceDay()));
        for (int i = 1 ; i <  numberOfDays; i++) {
            noteReason.append(", ").append(DateUtil.formatDateDashed(logs.get(i).getAttendanceDay()));
        }

        Double basicSalary = housemaid.getBasicSalary();
        if (basicSalary == null) {
            SalaryRule salaryRule = Setup.getApplicationContext().getBean(SalaryRuleController.class)
                    .getRuleRelatedToHousemaid(housemaid.getId());
            if (salaryRule != null) {
                List<SalaryRuleDetails> ruleDetails = salaryRule.getSalaryRuleDetailses();
                Map<String, Double> components = new HashMap<>();
                for (SalaryRuleDetails details : ruleDetails) {
                    if (details.getSalaryComponent() != null && details.getSalaryComponent().getCode() != null) {
                        components.put(details.getSalaryComponent().getCode().toLowerCase(), details.getValue());
                    }
                }
                Double mohreSalary, overTimeSalary, cashAdvance, holidaySalary, airfareFee;

                mohreSalary = components.getOrDefault("primarysalary", 0.0);
                overTimeSalary = components.getOrDefault("overtime", 0.0);
                cashAdvance = components.getOrDefault("monthlyloan", 0.0);
                holidaySalary = components.getOrDefault("holiday", 0.0);
                airfareFee = components.getOrDefault("airfarefee", 0.0);

                basicSalary = mohreSalary + overTimeSalary + cashAdvance + holidaySalary + airfareFee;
            }
        }
        Double amount = 0.0d;
        if (basicSalary != null) {
            for (java.sql.Date payrollMonth : monthsDays.keySet()) {
                amount += ((basicSalary / new LocalDate(payrollMonth).dayOfMonth().getMaximumValue()) * monthsDays.getOrDefault(payrollMonth, 0));
            }
        }
        PayrollManagerNote payrollManagerNote = new PayrollManagerNote();
        payrollManagerNote.setHousemaid(housemaid);
        payrollManagerNote.setNoteDate(noteDate);
        payrollManagerNote.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
        payrollManagerNote.setNoteReasone(noteReason.toString());
        payrollManagerNote.setAdditionReason(PicklistHelper.getItem(
                PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                "office_work_addition"));
        payrollManagerNote.setAmount((double) Math.round(amount));
        payrollManagerNote.setNumberOfDaysWorkedAtOffice(numberOfDays);
        Setup.getRepository(PayrollManagerNoteRepository.class).save(payrollManagerNote);


    }
}
