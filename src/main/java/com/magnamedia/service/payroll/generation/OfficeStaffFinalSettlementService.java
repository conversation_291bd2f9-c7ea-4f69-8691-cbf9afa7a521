package com.magnamedia.service.payroll.generation;

import com.aspose.imaging.internal.cR.S;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.helper.SmsResponse;
import com.magnamedia.core.helper.SmsService;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.core.type.SmsReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.accessmgmt.ExternalAccess;
import com.magnamedia.entity.RevokeAccessRequest;
import com.magnamedia.extra.PayrollGenerationLibrary;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
//import com.magnamedia.service.ManageAccessEmailService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.PayrollNotificationsService;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.PublicPageService;
import com.magnamedia.service.payroll.generation.newversion.PayrollRosterApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Period;
import java.util.*;

import static com.magnamedia.controller.PaySlipsController.normalizePhoneNumber;

/**
 * <AUTHOR> Saker Ali
 * Creation Date 19/05/2020
 */
@Service
public class OfficeStaffFinalSettlementService {

    @Autowired
    public Shortener shortener;
    @Autowired
    private OfficeStaffRepository officeStaffRepository ;

    private final SmsService smsService;

    private final MessageTemplateService messageTemplateService;

    private final PublicPageHelper publicPageHelper;

    @Autowired
    private PayrollNotificationsService notificationsService;

    @Autowired
    OfficeStaffFinalSettlementRepository officeStaffFinalSettlementRepository;

    @Autowired
    private MessagingService messagingService;

    @Autowired
    private MailService mailService;

    @Autowired
    PayrollRosterApprovalService payrollRosterApprovalService;

    @Autowired
    UserRepository userRepository;

    public OfficeStaffFinalSettlementService(SmsService smsService, MessageTemplateService messageTemplateService,
                                             PublicPageHelper publicPageHelper) {
        this.smsService = smsService;
        this.messageTemplateService = messageTemplateService;
        this.publicPageHelper = publicPageHelper;
    }

    public double salaryOfLastMonth(OfficeStaff officeStaff) {
        Double salary = 0d;
        if(officeStaff.getSalary() != null) {
            salary = officeStaff.getSalary();
        }

        if(officeStaff.getTerminationDate().before(officeStaff.getStartingDate())) {
            return 0d;
        }

        int days = Math.min(DateUtil.getDaysBetween(officeStaff.getStartingDate(), officeStaff.getTerminationDate()) + 1,
                DateUtil.toLocalDate(officeStaff.getTerminationDate()).getDayOfMonth() );

        return salary * days / 30.4d;
    }

    public double getOffDays(OfficeStaff officeStaff) {
        boolean isExpat = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT);
        boolean isEmarati = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI);

        if(isEmarati || isExpat) {
            Double basicSalary = 0d;
            if (officeStaff.getBasicSalary() != null) {
                basicSalary = officeStaff.getBasicSalary();
            }

            Double earnedOffDays = officeStaff.getEarnedOffDays();
            Double consumedOffDaysBalance = officeStaff.getConsumedOffDaysBalance();
            if (consumedOffDaysBalance == null) consumedOffDaysBalance = 0D;

            //Jirra ACC-1648
            return basicSalary * (earnedOffDays - consumedOffDaysBalance) / 30.4d;
        } else {
            return 0d;
        }
    }

    public double getGratuity(OfficeStaff officeStaff) {
        boolean isExpat = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT);
        if(!isExpat) return 0d;

        double lessThanFiveYearsGratuity = 21d;
        double moreThanFiveYearsGratuity = 30d;

        LocalDate startWorking = DateUtil.toLocalDate(officeStaff.getStartingDate());
        LocalDate finishWorking = DateUtil.toLocalDate(officeStaff.getTerminationDate());
        Period period = Period.between(startWorking, finishWorking);
        double years = period.getYears() + (period.getMonths() / 12.0) + ((period.getDays() + 1) / 365.0);

        if(years < 1) {
            return 0d;
        }

        VisaContractType contractType = getContractType(officeStaff);
        Double eligibleDays = 0d;
        OfficeStaffTerminationType terminationType = officeStaff.getTerminationType();

        if(terminationType.equals(OfficeStaffTerminationType.FIRED)) {
            eligibleDays = lessThanFiveYearsGratuity * Math.min(5, years) +
                            moreThanFiveYearsGratuity * Math.max(0, years - 5);
        } else if(contractType.equals(VisaContractType.Unlimited)){
            double portion = 1d;
            if(years <= 3) portion = 1.0 / 3.0;
            else if(years <= 5) portion = 2.0 / 3.0;

            eligibleDays = years * 21.0 * portion;
        }
        return officeStaff.getBasicSalary() * eligibleDays / 30.4d;
    }


    public double getAirfareTicket(OfficeStaff officeStaff) {

        boolean isExpat = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT);
        if (!isExpat || officeStaff.getTerminationType().equals(OfficeStaffTerminationType.RESIGNED)) return 0d;

        SelectQuery<AirFareTicket> selectQuery = new SelectQuery(AirFareTicket.class);
        selectQuery.filterBy("travelDays.officeStaff", "=", officeStaff);
        selectQuery.filterBy("travelDays.confirmed", "=", true);
        selectQuery.sortBy("ticketDate", false, true);
        List<AirFareTicket> airFareTicketList = selectQuery.execute();

        Calendar startWorkingCalendar = Calendar.getInstance();
        startWorkingCalendar.setTime(officeStaff.getStartingDate());
        startWorkingCalendar.add(Calendar.DAY_OF_MONTH, 1);

        LocalDate finishWorking = DateUtil.toLocalDate(officeStaff.getTerminationDate());
        LocalDate startWorking = LocalDate.of(startWorkingCalendar.get(Calendar.YEAR), startWorkingCalendar.get(Calendar.MONTH) + 1,
                startWorkingCalendar.get(Calendar.DAY_OF_MONTH));

        Period period = Period.between(startWorking, finishWorking);
        int years = period.getYears();

        int count = years / 2;

        // make sure at least can take one
        if (count < 1) return 0d;

        boolean canTake = false;
        boolean samePayroll = false;
        if (airFareTicketList.size() == 0) {
            canTake = true;
        } else {
            AirFareTicket ticket = airFareTicketList.get(0);
            if (ticket.getTicketDate().before(DateUtil.addYears(new Date(), -2))) {
                canTake = true;
            } else {
                org.joda.time.LocalDate payrollStart = PayrollGenerationLibrary.getPayrollStartDate(org.joda.time.LocalDate.now());
                org.joda.time.LocalDate payrollDueDate = new org.joda.time.LocalDate(ticket.getTravelDays().getCheckDate());
                if (payrollDueDate.equals(payrollStart) || payrollDueDate.isAfter(payrollStart)) {
                    canTake = true;
                    samePayroll = true;
                }
            }
        }

        if (canTake && (count - airFareTicketList.size() > 0 || samePayroll)) {
            AirfareTicketType type = officeStaff.getAirfareTicketType();
            if (type == null) return 2000d;

            return type.getAmount();
        }
        return 0;
    }

    public double getCompensation(OfficeStaff officeStaff) {
        boolean isExpat = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT);
        boolean isEmarati = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI);

        if(!(isEmarati || isExpat)) return 0d;

        Date diffDate = DateUtil.addMonths(officeStaff.getTerminationDate(), 3);

        VisaContractType contractType = getContractType(officeStaff);
        OfficeStaffTerminationType terminationType = officeStaff.getTerminationType();

        if(contractType.equals(VisaContractType.Limited) || isExpat) {
            NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class)
                    .findFirstByOfficeStaffOrderByCreationDateDesc(officeStaff);

            if (isExpat && newRequest != null && newRequest.getLaborCardExpiryDate() != null) {
                if(newRequest.getLaborCardExpiryDate().before(diffDate)) {
                    diffDate = newRequest.getLaborCardExpiryDate();
                }

            } else if (DateUtil.addYears(officeStaff.getStartingDate(), 2).before(diffDate)) {
                diffDate = DateUtil.addYears(officeStaff.getStartingDate(), 2);
            }

            if (diffDate.before(officeStaff.getTerminationDate())) {
                return 0d;
            }

            Period period = Period.between(DateUtil.toLocalDate(officeStaff.getTerminationDate()),
                    DateUtil.toLocalDate(diffDate));

            int months = period.getMonths();
            int days = period.getDays();

            double compensation = officeStaff.getSalary() * months +
                    (officeStaff.getSalary() * days / 30.4d);

            return terminationType.equals(OfficeStaffTerminationType.FIRED) ?
                    compensation : -compensation / 2.0d;
        } else {
            diffDate = DateUtil.addMonths(officeStaff.getNoticePeriodStartDate(), 1);

            int unservedPeriod = DateUtil.getDaysBetween(officeStaff.getNoticePeriodStartDate(), diffDate);
            unservedPeriod -= DateUtil.getDaysBetween(officeStaff.getNoticePeriodStartDate(), officeStaff.getNoticePeriodEndDate());

            unservedPeriod = Math.max(unservedPeriod, 0);

            return terminationType.equals(OfficeStaffTerminationType.FIRED) ?
                    officeStaff.getSalary() * unservedPeriod / 30.4d :
                    -1.0 * officeStaff.getSalary() * unservedPeriod / 30.4d;
        }
    }

    public double getLoanRepayments(OfficeStaff officeStaff) {
        return -1.0 * officeStaff.getLoanBalance();
    }

    public VisaContractType getContractType(OfficeStaff officeStaff) {
        NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class).findFirstByOfficeStaffOrderByCreationDateDesc(officeStaff);
        if(newRequest != null && newRequest.getContractType() != null) {
            return newRequest.getContractType();
        }

        boolean isEmarati = officeStaff.getEmployeeType() != null && officeStaff.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI);
        return isEmarati ? VisaContractType.Unlimited : VisaContractType.Limited;
    }

    public OfficeStaffFinalSettlement createFinalSettlement(OfficeStaff officeStaff) {

        OfficeStaffFinalSettlement finalSettlement = new OfficeStaffFinalSettlement();
        finalSettlement.setSalaryOfLastMonth(this.salaryOfLastMonth(officeStaff));
        finalSettlement.setTerminationNotes(officeStaff.getTerminationNotes());
        finalSettlement.setAirfareTicket(this.getAirfareTicket(officeStaff));
        finalSettlement.setGratuity(this.getGratuity(officeStaff));
        finalSettlement.setOffDays(this.getOffDays(officeStaff));
        finalSettlement.setCompensation(this.getCompensation(officeStaff));
        finalSettlement.setLoanRepayments(this.getLoanRepayments(officeStaff));
        finalSettlement.setOfficeStaff(officeStaff);
        finalSettlement.setPayrollDate(new org.joda.time.LocalDate(officeStaff.getTerminationDate()).withDayOfMonth(1).toDate());

        if(officeStaff.getEmployeeType() == OfficeStaffType.OVERSEAS_STAFF) {
            finalSettlement.setIncludeInPayroll(true);
            finalSettlement.setApprovedByCFO(true);

            Date startDate = (officeStaff.getStartingDate() == null || officeStaff.getStartingDate().compareTo(officeStaff.getTerminationDate())>0) ? officeStaff.getTerminationDate()
                    : officeStaff.getStartingDate().compareTo(DateUtil.getFirstOfMonthDate(officeStaff.getTerminationDate())) > 0 ? officeStaff.getStartingDate()
                    : DateUtil.getFirstOfMonthDate(officeStaff.getTerminationDate());
            finalSettlement.setProratedDays(DateUtil.getDaysBetween(startDate, officeStaff.getTerminationDate()) + 1);
        } else {
            OfficeStaff lastManager = payrollRosterApprovalService.getRosterManager(officeStaff);
            finalSettlement.setFinalManagerName(lastManager != null ? lastManager.getName() : "");
            Long CFO_ID = Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CFO_ID));
            if (Objects.equals(officeStaff.getFinalManager().getId(), CFO_ID)) {
                if (officeStaff.getEmployeeManager() == null || (Objects.equals(officeStaff.getEmployeeManager().getId(), officeStaff.getFinalManager().getId()))) {
                    finalSettlement.setScenarioType(ScenarioType.CFO_IS_THE_SAME_AS_THE_FINAL_MANAGER_AND_HE_IS_THE_DIRECT_MANAGER);
                    // note : for getNotes() it caused some problem
                    //finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_CFO);
                } else {
                    finalSettlement.setScenarioType(ScenarioType.CFO_IS_THE_SAME_AS_THE_FINAL_MANAGER_BUT_NOT_THE_SAME_AS_DIRECT_MANAGER);
                    //finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_FINAL_MANAGER);
                }
            } else {
                finalSettlement.setScenarioType(ScenarioType.CFO_IS_NOT_THE_SAME_AS_THE_FINAL_MANAGER);
                //finalSettlement.setCurrentStatus(OfficeStaffFinalSettlementStatus.TO_BE_REVIEW_BY_FINAL_MANAGER);
            }
        }

        return finalSettlement;
    }

    @Transactional
    public void remindCFOOfFinalSettlement(OfficeStaffFinalSettlement finalSettlement) {
        OfficeStaffTodoRepository officeStaffTodoRepository = Setup.getRepository(OfficeStaffTodoRepository.class);

        OfficeStaff officeStaff = finalSettlement.getOfficeStaff();
        OfficeStaffCandidate candidate = officeStaff.getOfficeStaffCandidate();
        if(candidate == null) {
            candidate = new OfficeStaffCandidate();
            candidate.setEmployeeName(officeStaff.getName());
            candidate.setFirstName(officeStaff.getFirstName());
            candidate.setMiddleName(officeStaff.getMiddleName());
            candidate.setLastName(officeStaff.getLastName());
            candidate.setJobTitle(officeStaff.getJobTitle());
            candidate.setPhoneNumber(officeStaff.getPhoneNumber());
            candidate.setEmail(officeStaff.getEmail());
            candidate.setMockCandidate(true);
            Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
            officeStaff.setOfficeStaffCandidate(candidate);
            Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
        }

        if(officeStaffTodoRepository.countByTaskNameAndCandidate(OfficeStaffTodoType.REMIND_CFO_OF_FINAL_SETTLEMENT.toString(),
                candidate) == 0) {
            OfficeStaffTodo todo = new OfficeStaffTodo();
            todo.setTaskName(OfficeStaffTodoType.REMIND_CFO_OF_FINAL_SETTLEMENT.toString());
            todo.setCandidate(candidate);
            todo.setCompleted(false);
            todo.setStopped(false);
            officeStaffTodoRepository.save(todo);
            messagingService.notifyPayrollTrustee(todo);
        }

    }

    @Transactional
    public void remindFinalManagerOfFinalSettlement(OfficeStaffFinalSettlement finalSettlement) {
        OfficeStaff officeStaff = finalSettlement.getOfficeStaff();
        OfficeStaff lastManager = officeStaff.getLastManagerOtherThanCFO();

        if (finalSettlement.getPreviousStatus() == null)
            sendFSToFinalManagerDirectly(finalSettlement, officeStaff);
        else
            sendUpdatedFSToFinalManager(finalSettlement, officeStaff, lastManager);

        OfficeStaffTodoRepository officeStaffTodoRepository = Setup.getRepository(OfficeStaffTodoRepository.class);
        OfficeStaffCandidate candidate = officeStaff.getOfficeStaffCandidate();
        if(candidate == null) {
            candidate = new OfficeStaffCandidate();
            candidate.setEmployeeName(officeStaff.getName());
            candidate.setFirstName(officeStaff.getFirstName());
            candidate.setMiddleName(officeStaff.getMiddleName());
            candidate.setLastName(officeStaff.getLastName());
            candidate.setJobTitle(officeStaff.getJobTitle());
            candidate.setPhoneNumber(officeStaff.getPhoneNumber());
            candidate.setEmail(officeStaff.getEmail());
            candidate.setMockCandidate(true);
            Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);
            officeStaff.setOfficeStaffCandidate(candidate);
            Setup.getRepository(OfficeStaffRepository.class).save(officeStaff);
        }

        if(officeStaffTodoRepository.countByTaskNameAndCandidate(OfficeStaffTodoType.REMIND_FINAL_MANAGER_OF_FINAL_SETTLEMENT.toString(),
                candidate) == 0) {
            OfficeStaffTodo todo = new OfficeStaffTodo();
            todo.setTaskName(OfficeStaffTodoType.REMIND_FINAL_MANAGER_OF_FINAL_SETTLEMENT.toString());
            todo.setCandidate(candidate);
            todo.setCompleted(false);
            todo.setStopped(false);
            officeStaffTodoRepository.save(todo);
            messagingService.notifyPayrollTrustee(todo);
        }
    }

    @Transactional
    public void revokeAllAccesses(OfficeStaff officeStaff, OfficeStaff whoRequestedTermination) {
        List<ExternalAccess> externalAccesses = Setup.getRepository(OfficeStaffAccessRepository.class)
                .findAccessByEmployee(officeStaff);

        // No external access so terminate staff immediately
        if(externalAccesses.isEmpty()) {
            allAccessesRevoked(officeStaff, whoRequestedTermination);
            return;
        }
        Map<String, Object> request = new HashMap<>();
        request.put("employeeGrantAccess", new HashMap(){{put("id", officeStaff.getId());}});
        List<HashMap> externalAccessesMap = new ArrayList<>();
        for (ExternalAccess access : externalAccesses)
            externalAccessesMap.add(new HashMap(){{put("id",access.getId());}});
        request.put("externalAccesses", externalAccessesMap);
        request.put("lastSentSmsDate", new Date());

        Setup.getApplicationContext()
                .getBean(InterModuleConnector.class).postJsonAsync("/admin/revokeAccess/addRequest", request);

//        RevokeAccessRequest revokeAccessRequest = new RevokeAccessRequest();
//        revokeAccessRequest.setEmployeeGrantAccess(officeStaff);
//        revokeAccessRequest.setExternalAccesses(externalAccesses);
//        revokeAccessRequest.setLastSentSmsDate(new Date());
//        Setup.getRepository(RevokeAccessRepository.class).save(revokeAccessRequest);

//        Setup.getApplicationContext().getBean(ManageAccessEmailService.class)
//                .sendRevokeAccessRequestEmail(revokeAccessRequest);
    }

    @Transactional
    public void allAccessesRevoked(OfficeStaff officeStaff) {
        officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(officeStaff.getId());
        allAccessesRevoked(officeStaff, officeStaff.getWhoRequestedTermination());
    }

    @Transactional
    public void allAccessesRevoked(OfficeStaff officeStaff, OfficeStaff terminationManager) {
        officeStaff = Setup.getRepository(OfficeStaffRepository.class).findOne(officeStaff.getId());
        terminationManager = Setup.getRepository(OfficeStaffRepository.class).findOne(terminationManager.getId());
        User terminationManagerUser = terminationManager.getUser();


        if (terminationManager == null || terminationManager.getPhoneNumber() == null)
            throw new BusinessException("Can't find the manager who requested the termination or the manager doesn't have a phone number!");
        String url = "";
        url = publicPageHelper.generatePublicURL(PublicPageHelper.INFORM_TERMINATED_STAFF, officeStaff.getId().toString() + "#Payroll_Accesses_Are_Revoked", String.valueOf(terminationManagerUser.getId()));

        Map<String, String> paramValues = new HashMap<>();
        paramValues.put("employee_name", officeStaff.getFirstLastName());
        paramValues.put("url", url);

//        messageTemplateService.sendMessageOrEmail(
//                "Inform "+officeStaff.getFirstLastName()+" that they are terminated",
//                terminationManager,
//                "Payroll_Accesses_Are_Revoked",
//                paramValues);

        Setup.getApplicationContext().getBean(MessagingService.class)
                .send("Payroll_Accesses_Are_Revoked", "Inform " + officeStaff.getFirstLastName() + " that they are terminated",
                        null, terminationManagerUser, paramValues, officeStaff, null, terminationManager);

    }

    public void sendUpdatedFSToFinalManager(OfficeStaffFinalSettlement finalSettlement, OfficeStaff officeStaff, OfficeStaff lastManager){
        List<EmailRecipient> recipients = Recipient.parseEmailsString(lastManager.getUser().getEmail());
        String publicPage = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_FINAL_MANAGER, finalSettlement.getId().toString(), String.valueOf(lastManager.getUser().getId()));
        String subject = "Updated Final Settlement of " + officeStaff.getFirstLastName();
        Map<String, String> params = new HashMap<>();
        params.put("firstLastName", officeStaff.getFirstLastName());
        params.put("addInCaseFM","and then assign it to the CFO to proceed");
        params.put("url", publicPage);

        messagingService.send(recipients, null, "Payroll_Updated_Final_Settlement_Of_OfficeStaff",
                subject, params, new ArrayList<>(), null);

//        mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "Payroll_Updated_Final_Settlement_Of_OfficeStaff", params), EmailReceiverType.Office_Staff)
//                .recipients(recipients)
//                .html()
//                .senderName(MessageTemplateService.getMaidsCcSenderName())
//                .secure()
//                .build());
    }

    public void sendFSToFinalManagerDirectly(OfficeStaffFinalSettlement finalSettlement, OfficeStaff officeStaff){
        OfficeStaff manager = payrollRosterApprovalService.getRosterManager(officeStaff);
        List<EmailRecipient> recipient = new ArrayList<>();
        if(manager != null && manager.getUser() != null && manager.getUser().getEmail() != null) {
            recipient = Recipient.parseEmailsString(manager.getUser().getEmail());
        }
        String publicPage = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_FINAL_MANAGER, finalSettlement.getId().toString(), String.valueOf(manager.getUser().getId()));
        String subject = "Final Settlement of " + officeStaff.getFirstLastName();
        Map<String, String> params = new HashMap<>();
        params.put("firstLastName", officeStaff.getFirstLastName());
        params.put("url", publicPage);
        messagingService.send(recipient, null, "Payroll_Final_Settlement_Of_OfficeStaff", subject
                , params, new ArrayList<>(), null);
//        mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "Payroll_Final_Settlement_Of_OfficeStaff", params), EmailReceiverType.Office_Staff)
//                .recipients(recipients)
//                .html()
//                .senderName(MessageTemplateService.getMaidsCcSenderName())
//                .secure()
//                .build());
    }

    public void sendFSToCFODirectly(OfficeStaffFinalSettlement finalSettlement, OfficeStaff officeStaff){
        String usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.CFO_FINAL_SETTLEMENT_USER_ID);
        List<String> idsList = Arrays.asList(usersIds.split(";"));

        for(String userId : idsList) {
            User user = userRepository.findOne(Long.parseLong(userId));

            EmailRecipient recipient = new Recipient(user.getEmail() != null ? user.getEmail() : null, user.getName() != null ? user.getName() : null);
            List<EmailRecipient> recipients = new ArrayList<>();
            recipients.add(recipient);
            String publicPage = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_CFO, finalSettlement.getId().toString(), userId);

            String subject = "Final Settlement of " + officeStaff.getFirstLastName();
            Map<String, String> params = new HashMap<>();
            params.put("firstLastName", officeStaff.getFirstLastName());
            params.put("url", publicPage);

            messagingService.send(recipients, null, "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO_Directly", subject
                    , params, new ArrayList<>(), null);

//        Setup.getMailService().sendEmail(new MailObject.builder(new TemplateEmail(subject, "Payroll_Final_Settlement_Of_OfficeStaff_To_CFO_Directly", params), EmailReceiverType.Office_Staff)
//                .recipients(recipients)
//                .html()
//                .secure()
//                .build());
        }
    }

    public void sendUpdatedFSToCFO(OfficeStaffFinalSettlement finalSettlement, OfficeStaff officeStaff){
        String usersIds = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.CFO_FINAL_SETTLEMENT_USER_ID);
        List<String> idsList = Arrays.asList(usersIds.split(";"));

        for(String userId : idsList) {
            User user = userRepository.findOne(Long.parseLong(userId));

            EmailRecipient recipient = new Recipient(user.getEmail() != null ? user.getEmail() : null, user.getName() != null ? user.getName() : null);
            List<EmailRecipient> recipients = new ArrayList<>();
            recipients.add(recipient);

            String publicPage = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_CFO, finalSettlement.getId().toString(), userId);
            String subject = "Updated Final Settlement of " + officeStaff.getFirstLastName();
            Map<String, Object> params = new HashMap<>();
            params.put("firstLastName", officeStaff.getFirstLastName());
            params.put("addInCaseFM","");
            params.put("url", publicPage);
            mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "Payroll_Updated_Final_Settlement_Of_OfficeStaff", params), EmailReceiverType.Office_Staff)
                    .recipients(recipients)
                    .html()
                    .senderName(MessageTemplateService.getMaidsCcSenderName())
                    .secure()
                    .build());
        }
    }

    public void sendRejectEmail(OfficeStaffFinalSettlement finalSettlement, OfficeStaff officeStaff, String note){
        if(note == null || note.isEmpty()){
            throw new RuntimeException("Note is required");
        }
        List<EmailRecipient> recipients = null ;
        OfficeStaff cfo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
        officeStaff = officeStaffRepository.findOne(officeStaff.getId());
        String payrollManagerEmail = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL);
        if(!ScenarioType.CFO_IS_THE_SAME_AS_THE_FINAL_MANAGER_AND_HE_IS_THE_DIRECT_MANAGER.equals(finalSettlement.getScenarioType())){
            OfficeStaff lastManager = officeStaff.getLastManagerOtherThanCFO();
            recipients = Recipient.parseEmailsString(lastManager.getEmail()+","+payrollManagerEmail);
        }else {
            recipients = Recipient.parseEmailsString(payrollManagerEmail);
        }
        Map<String, String> params = new HashMap<>();
        params.put("firstLastName", officeStaff.getFirstLastName());
        params.put("CFOName", cfo.getFirstLastName());
        params.put("noteFromCFO", note);
        String subject = "Rejected the final settlement for " + officeStaff.getFirstLastName();
        messagingService.send(recipients, null, "CFO_Rejected_Final_Settlement_Of_OfficeStaff",
                subject, params, new ArrayList<>(), null);
//        mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "CFO_Rejected_Final_Settlement_Of_OfficeStaff", params), EmailReceiverType.Office_Staff)
//                .recipients(recipients)
//                .html()
//                .senderName(MessageTemplateService.getMaidsCcSenderName())
//                .secure()
//                .build());


    }
    public void sendCFOQuestionTOUser(FinalSettlementNote question){
        if(question.getFinalSettlement() == null || question.getFinalSettlement().getOfficeStaff() == null){
            throw new RuntimeException("this question unrelated with FinalSettlement");
        }
        if(question.getUser() == null){
            throw new RuntimeException("the user is not found") ;
        }
        List<EmailRecipient> recipients = Recipient.parseEmailsString(question.getUser().getEmail());
        OfficeStaff cfo = officeStaffRepository.findOne(Long.valueOf(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CEO_ID)));
        String publicPageQuestion = publicPageHelper.generatePublicURL(PublicPageHelper.FINAL_SETTLEMENT_QUESTION, question.getId().toString(), String.valueOf(question.getUser().getId()));
        String publicPageOfficeStaffFinalSettlement = shortener.shorten(PublicPageHelper.getModuleBaseAngular13Url() + "office-staff-payroll/" + question.getFinalSettlement().getOfficeStaff().getId() + "/termination");
        String subject = "Question by  " + cfo.getFirstLastName();
        Map<String, String> params = new HashMap<>();
        params.put("CFOName", cfo.getFirstLastName());
        params.put("OfficeStaffTermination",publicPageOfficeStaffFinalSettlement);
        params.put("urlQuestion", publicPageQuestion);
        messagingService.send(recipients, null, "CFO_Ask_User_About_Final_Settlement_Of_OfficeStaff",
                subject, params, new ArrayList<>(), null);

//        mailService.sendEmail(new MailObject.builder(new TemplateEmail(subject, "CFO_Ask_User_About_Final_Settlement_Of_OfficeStaff", params), EmailReceiverType.Office_Staff)
//                .recipients(recipients)
//                .html()
//                .senderName(MessageTemplateService.getMaidsCcSenderName())
//                .secure()
//                .build());
    }

    @Transactional
    public void cancelOfficeStaffVisa() {
        SelectQuery select = new SelectQuery(OfficeStaffFinalSettlement.class);
        select.join("officeStaff");
        select.filterBy("officeStaff.employeeType", "=", OfficeStaffType.DUBAI_STAFF_EXPAT);
        select.filterBy("visaCancellationDate", "=", new Date(System.currentTimeMillis()));
        select.filterBy("currentStatus", "IN", Arrays.asList(OfficeStaffFinalSettlementStatus.FINISHED_AND_PAID_NOW, OfficeStaffFinalSettlementStatus.FINISHED_AND_PAID_AT_THE_END_OF_THE_MONTH, OfficeStaffFinalSettlementStatus.FINISHED_WITHOUT_PAID));
        List<OfficeStaffFinalSettlement> finalSettlements = select.execute();
        for (OfficeStaffFinalSettlement finalSettlement : finalSettlements) {
            OfficeStaff officeStaffVisaCancellation = officeStaffRepository.findOne(finalSettlement.getOfficeStaff().getId());
            //a Business Rule will run to cancel his visa
            officeStaffVisaCancellation.setCreateCancel(true);
            officeStaffRepository.save(officeStaffVisaCancellation);
        }
    }
}
