package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.HousemaidPayrollBean;
import com.magnamedia.extra.payroll.init.HousemaidPayrollInitializer;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HousemaidPayrollAuditService {

    @Autowired
    private LockDateService lockDateService;

    public SelectFilter getTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();


        // MAID CC Filter
        if (monthlyPaymentRule.isTargetingMaidCC()) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            //delayed items
            List<PicklistItem> delayItems = HousemaidPayrollPaymentServiceV2.getDelayedItems();

            SelectFilter wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            SelectFilter payCashHousemaidsFilter = new SelectFilter(
                    new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                            .or("status", "=", HousemaidStatus.AVAILABLE)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                            .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                            //Jirra ACC-1005
                            //.or("status", "=", HousemaidStatus.ON_VACATION)
                            .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                            .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                            .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                            //Jirra ACC-1223
                            .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                            .or(
                                    new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                            //Jirra ACC-1005
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                            .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                            .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                    .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                    .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                            .and("pendingSince", ">=", new LocalDate().toDate())
                                                            .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                    .and("pendingUntil", ">=", payrollStart))))
                            ));

            //filter all housemaids
            maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));



            // exclude pre-paid vacation housemaids
            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);

        }

        Set<Long> excludedHousemaids = new HashSet<>();


        // exclude transferred before housemaids But not those who have important manager notes
        List<PicklistItem> mustBePaidManagerNotes = HousemaidPayrollPaymentServiceV2.getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        // MAID VISA Filter
        if (monthlyPaymentRule.isTargetingMaidVisa()) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);

            List<Long> paidHousemaidsIds = HousemaidPayrollPaymentServiceV2.getPaidMVMaidsIdsNewV2(monthlyPaymentRule, transferredIds, true);

            // add the MV maids who were included by the auditor
            if (monthlyPaymentRule.getMustIncludedMVInfoList().size() > 0)
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream().map(ExcludedMVInfo::getHousemaidId).collect(Collectors.toList()));

            if(paidHousemaidsIds.size() > 0)
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else
                maidVisaFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        //excluded
        excludedHousemaids.addAll(excludedCCHousemaids);

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public SelectFilter getTargetListFilterForHousemaidType(MonthlyPaymentRule monthlyPaymentRule, boolean maidCC) {
        java.sql.Date payrollStart = getPayrollStartLockDate(monthlyPaymentRule);

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;
        Set<Long> excludedCCHousemaids = new HashSet<>();


        // MAID CC Filter
        if (maidCC) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);

            //delayed items
            List<PicklistItem> delayItems = HousemaidPayrollPaymentServiceV2.getDelayedItems();

            SelectFilter wpsHousemaidsFilter = new SelectFilter("status", "=", HousemaidStatus.WITH_CLIENT);

            SelectFilter payCashHousemaidsFilter = new SelectFilter(
                    new SelectFilter("status", "=", HousemaidStatus.VIP_RESERVATIONS)
                            .or("status", "=", HousemaidStatus.AVAILABLE)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_PROSPECT)
                            .or("status", "=", HousemaidStatus.RESERVED_FOR_REPLACEMENT)
                            .or("status", "=", HousemaidStatus.SICK_WITHOUT_CLIENT)
                            //Jirra ACC-1005
                            //.or("status", "=", HousemaidStatus.ON_VACATION)
                            .or("status", "=", HousemaidStatus.LANDED_IN_DUBAI)
                            .or("status", "=", HousemaidStatus.PENDING_FOR_VIDEOSHOOT)
                            .or("status", "=", HousemaidStatus.RESERVED_HOME_VISIT)
                            //Jirra ACC-1223
                            .or("status", "=", HousemaidStatus.ASSIGNED_OFFICE_WORK)
                            .or(
                                    new SelectFilter("status", "=", HousemaidStatus.PENDING_FOR_DISCIPLINE)
                                            //Jirra ACC-1005
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION)
                                            .and("pendingStatus", "<>", PendingStatus.REPEAT_MEDICAL)
                                            .and("pendingStatus", "<>", PendingStatus.PENDING_FOR_TERMINATION_TAWAFUQ)
                                            .and(new SelectFilter("reasonOfPending", "IS NULL", null)
                                                    .or(new SelectFilter("reasonOfPending", "NOT IN", delayItems))
                                                    .or(new SelectFilter("pendingSince", "IS NOT NULL", null)
                                                            .and("pendingSince", ">=", new LocalDate().toDate())
                                                            .or(new SelectFilter("pendingUntil", "IS NOT NULL", null)
                                                                    .and("pendingUntil", ">=", payrollStart))))
                            ));

            //filter all housemaids
            maidCCFilter.and(new SelectFilter(payCashHousemaidsFilter).or(wpsHousemaidsFilter));



            // exclude pre-paid vacation housemaids
            PicklistItem vacationType =
                    PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                            "pre-paid_vacation");
            List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                    .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                            lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
            if (!prePaidVacationMaids.isEmpty())
                excludedCCHousemaids.addAll(prePaidVacationMaids);

        }

        Set<Long> excludedHousemaids = new HashSet<>();


        // exclude transferred before housemaids But not those who have important manager notes
        List<PicklistItem> mustBePaidManagerNotes = HousemaidPayrollPaymentServiceV2.getMustBePaidManagerNotes();

        List<Long> haveMustBePaidManagerNotesMaids = Setup.getRepository(PayrollManagerNoteRepository.class)
                .findHousemaidsWithNonPaidSalaryDisputeAndTaxiNotes(payrollStart, new java.sql.Date(System.currentTimeMillis()),
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION, mustBePaidManagerNotes);

        List<Long> transferredIds;

        if (haveMustBePaidManagerNotesMaids.isEmpty()) {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth());
        } else {
            transferredIds = Setup.getRepository(HousemaidPayrollLogRepository.class).
                    findByPayrollMonthAndTransferredTrue(monthlyPaymentRule.getPayrollMonth(), haveMustBePaidManagerNotesMaids);
        }

        if (!transferredIds.isEmpty())
            excludedHousemaids.addAll(transferredIds);

        // MAID VISA Filter
        if (!maidCC) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);

            List<Long> paidHousemaidsIds = HousemaidPayrollPaymentServiceV2.getPaidMVMaidsIdsNewV2(monthlyPaymentRule, transferredIds, true);

            // add the MV maids who were included by the auditor
            if (monthlyPaymentRule.getMustIncludedMVInfoList().size() > 0)
                paidHousemaidsIds.addAll(monthlyPaymentRule.getMustIncludedMVInfoList().stream().map(ExcludedMVInfo::getHousemaidId).collect(Collectors.toList()));

            if(paidHousemaidsIds.size() > 0)
                maidVisaFilter.and("id", "IN", paidHousemaidsIds);
            else
                maidVisaFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;

        // Not excluded from payroll
        SelectFilter notExcludedFromPayroll = new SelectFilter(new SelectFilter("excludedFromPayroll", "=", false)
                .or("excludedFromPayroll", "IS NULL", null));
        baseFilter.and(notExcludedFromPayroll);

        //excluded
        excludedHousemaids.addAll(excludedCCHousemaids);

        if (!excludedHousemaids.isEmpty())
            baseFilter.and("id", "NOT IN", excludedHousemaids);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public SelectFilter getManuallyExcludedTargetListFilterForHousemaidType(MonthlyPaymentRule monthlyPaymentRule, Boolean maidCC) {

        SelectFilter maidCCFilter = null;
        SelectFilter maidVisaFilter = null;

        // MAID CC Filter
        if (maidCC == null || maidCC) {
            maidCCFilter = new SelectFilter();
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);
            maidCCFilter = new SelectFilter("status", "IN", HousemaidPayrollPaymentServiceV2.getMustBeGeneratedHousemaidsStatuses(monthlyPaymentRule));
        }

        // MAID VISA Filter
        if (maidCC == null || !maidCC) {
            maidVisaFilter = new SelectFilter();
            maidVisaFilter.and("housemaidType", "=", HousemaidType.MAID_VISA);
            maidVisaFilter.and("status", "<>", HousemaidStatus.EMPLOYEMENT_TERMINATED);
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;

        // excluded from payroll and profile
        SelectFilter excludedFromPayroll = new SelectFilter("excludedFromPayroll", "=", true)
                .and("housemaidLastExcludeDetails", "IS NOT NULL", null)
                .and("housemaidLastExcludeDetails.excludedManuallyFromProfile", "=", true);
        baseFilter.and(excludedFromPayroll);

        //start date condition
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public List<Housemaid> getTargetList(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter = getTargetListFilter(monthlyPaymentRule);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();

    }

    public List<Housemaid> getEligibleListForMVMaids(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();
        Date dayOf27th = new Date(new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate().getTime());
        return Setup.getRepository(HousemaidRepository.class).getEligibleListForMVMaids(ContractStatus.ACTIVE, HousemaidStatus.EMPLOYEMENT_TERMINATED, monthlyPaymentRule.getPayrollMonth(), HousemaidType.MAID_VISA, dayOf27th);
    }



    public List<Housemaid> getTargetListForHousemaidType(MonthlyPaymentRule monthlyPaymentRule, boolean maidCC) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter = getTargetListFilterForHousemaidType(monthlyPaymentRule, maidCC);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();

    }

    public List<Housemaid> getManuallyExcludedTargetListForHousemaidType(MonthlyPaymentRule monthlyPaymentRule, Boolean maidCC) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter = getManuallyExcludedTargetListFilterForHousemaidType(monthlyPaymentRule, maidCC);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();

    }

    public List<CcMaidSwitchedToMv> getCCSwitchedToMvMaidsTargetList(java.util.Date from, java.util.Date to) {
        return Setup.getRepository(CcMaidSwitchedToMvRepository.class).getCcMaidSwitchedToMvByPayrollMonth(from, to);
    }

    public List<Housemaid> getTargetListForAllHousemaids(MonthlyPaymentRule monthlyPaymentRule) {
        if (!monthlyPaymentRule.isTargetingHousemaid()) return new ArrayList<>();

        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        SelectFilter selectFilter;
        if(monthlyPaymentRule.isSecondaryMonthlyRule())
            selectFilter = getTargetListFilter(monthlyPaymentRule);
        else
            selectFilter = getPrimaryTargetListFilter(monthlyPaymentRule);

        //if filter is null then there is no maid to give here the salary
        if(selectFilter == null)
            return new ArrayList<>();


        query.filterBy(selectFilter);
        query.sortBy("name", true);
        return query.execute();

    }

    public SelectFilter getPrimaryTargetListFilter(MonthlyPaymentRule monthlyPaymentRule) {

        SelectFilter maidCCFilter = new SelectFilter();
        SelectFilter maidVisaFilter = new SelectFilter();

        // Maid CC Filter
        if(monthlyPaymentRule.isTargetingMaidCC()) {
            //get all Housemaid Statuses that we need to generate payroll log for it
            maidCCFilter = new SelectFilter("status", "IN", HousemaidPayrollPaymentServiceV2.getMustBeGeneratedHousemaidsStatuses(monthlyPaymentRule));
            maidCCFilter.and("housemaidType", "<>", HousemaidType.MAID_VISA);


        }

        // Maid Visa Filter
        if(monthlyPaymentRule.isTargetingMaidVisa()){

            maidVisaFilter = new SelectFilter("housemaidType", "=", HousemaidType.MAID_VISA);
        }

        if (monthlyPaymentRule.isTargetingMaidCCOnly()) {
            maidVisaFilter = null;
        } else if (monthlyPaymentRule.isTargetingMaidVisaOnly()) {
            maidCCFilter = null;
        }

        SelectFilter baseFilter;
        if(maidCCFilter != null && maidVisaFilter != null)
            baseFilter = new SelectFilter(maidCCFilter.or(maidVisaFilter));
        else if (maidCCFilter != null)
            baseFilter = new SelectFilter(maidCCFilter);
        else if (maidVisaFilter != null)
            baseFilter = new SelectFilter(maidVisaFilter);
        else
            return null;


        Set<Long> excludedHousemaids = new HashSet<>();

        // exclude pre-paid vacation housemaids
        PicklistItem vacationType =
                PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        "pre-paid_vacation");
        List<Long> prePaidVacationMaids = Setup.getRepository(ScheduledAnnualVacationRepository.class)
                .findScheduledVacationsOfLastPayroll(vacationType, 0.0, lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -2, PaymentRuleEmployeeType.HOUSEMAIDS),
                        lockDateService.getLockDate(monthlyPaymentRule.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS));
        if (!prePaidVacationMaids.isEmpty()) excludedHousemaids.addAll(prePaidVacationMaids);

        if (!excludedHousemaids.isEmpty()) baseFilter.and("id", "NOT IN", excludedHousemaids);

        //startDate && replacementSalaryStartDate must be before 27 of the month
        baseFilter.and("startDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate());
        baseFilter.and(new SelectFilter("replacementSalaryStartDate", "IS NULL", null).or("replacementSalaryStartDate", "<", new LocalDate(monthlyPaymentRule.getPayrollMonth()).withDayOfMonth(27).toDate()));

        return baseFilter;
    }

    public java.sql.Date getPayrollEndLockDate(MonthlyPaymentRule rule) {
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(rule.getPayrollMonth());

        for(MonthlyPaymentRule monthlyPaymentRule: rules) {
            if(monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return  monthlyPaymentRule.getLockDate();
            }
        }
        throw new BusinessException("Can't find current month lock date for housemaids");
    }

    public java.sql.Date getPayrollStartLockDate(MonthlyPaymentRule rule) {
        java.sql.Date lastMonthPayroll = new java.sql.Date(DateUtil.addMonths(rule.getPayrollMonth(), -1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(lastMonthPayroll);

        for(MonthlyPaymentRule monthlyPaymentRule: rules) {
            if(monthlyPaymentRule.getPaymentMethod() != null && monthlyPaymentRule.getPayrollType() == PayrollType.PRIMARY
                    && monthlyPaymentRule.getEmployeeTypeList().contains(PaymentRuleEmployeeType.HOUSEMAIDS)) {
                return  monthlyPaymentRule.getLockDate();
            }
        }

        LocalDate dt = new LocalDate(lastMonthPayroll);
        return new java.sql.Date(dt.withDayOfMonth(27).toDate().getTime());
    }

    public HousemaidPayrollBean setHousemaidInfo(
            HousemaidPayrollBean bean,
            Housemaid housemaid,
            LocalDate payrollMonth,
            HousemaidPayrollInitializer initializer) {

        Long housemaidId = housemaid.getId();

        bean.setHousemaid(housemaid);
        bean.setHousemaidName(housemaid.getName());

//        if (housemaid.getNationality() != null)
//            bean.setNationality(housemaid.getNationality().getName());
//        else
//            bean.setNationality("");

        bean.setNationality(initializer.getNationality(housemaidId));

//        if (housemaid.getStatus().equals(HousemaidStatus.WITH_CLIENT)) {
//            Client c = housemaid.getCurrentClient();
//            bean.setClientName(c != null ? c.getName() : "");
//        }

        bean.setClientName(initializer.getClientName(housemaidId));

        bean.setStatus(housemaid.getStatus().toString());

        bean.setSource(housemaid.isIsAgency() ? "Agency" : ((housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid()) ? "Freedom Operator" : "Exit"));
//        bean.setFreedomOperatorName(
//                (housemaid.getFreedomMaid() != null && housemaid.getFreedomMaid() && housemaid.getFreedomOperator() != null) ? housemaid.getFreedomOperator().getName() : "");

        bean.setFreedomOperatorName(initializer.getFreedomName(housemaidId));
//        NewRequest visaNewRequest = housemaid.getVisaNewRequest();
//        if (visaNewRequest != null) {
//            if (visaNewRequest.getEmployeeUniqueId() != null) {
//                bean.setEmployeeUniqueId(visaNewRequest.getEmployeeUniqueId());
//            }
//            if (visaNewRequest.getAgentId() != null) {
//                bean.setAgentId(visaNewRequest.getAgentId());
//            }
//            if (visaNewRequest.getEmployeeAccountWithAgent() != null) {
//                bean.setEmployeeAccountWithAgent(visaNewRequest.getEmployeeAccountWithAgent());
//            }
//        }

        bean.setEmployeeUniqueId(initializer.getEmployeeId(housemaidId));
        bean.setEmployeeAccountWithAgent(initializer.getAccountWithAgent(housemaidId));
        bean.setAgentId(initializer.getAgentId(housemaidId));

        //Jirra 1384
//        RenewRequest renewRequest = Setup.getRepository(RenewVisaRequestRepository.class)
//                .findFirstOneByHousemaidAndCompletedOrderByCreationDateDesc(housemaid, true);
//        if (renewRequest != null) {
//            bean.setRenewalDate(new LocalDate(renewRequest.getLastMoveDate()));
//        }

        java.util.Date renewalDate = initializer.getRenewalDate(housemaidId);
        if(renewalDate != null) {
            bean.setRenewalDate(new Date(renewalDate.getTime()));
        }
        //Contract
//        PicklistItem maidVisa = Setup.getItem(PayrollManagementModule.PICKLIST_PROSPECTTYPE, "maidvisa.ae_prospect");
//        List<Contract> contractsList = housemaid.getContracts();
//        if (contractsList != null && contractsList.size() > 0) {
//            List<Contract> contracts = contractsList.stream().filter(x -> ContractStatus.ACTIVE.equals(x.getStatus())).collect(Collectors.toList());
//            if (contracts.size() > 0) {
//                bean.setContractName("Contr-" + contracts.get(0).getId().toString());
//                if (contracts.get(0).getContractProspectType() != null &&
//                        contracts.get(0).getContractProspectType().getId().equals(maidVisa.getId())) {
//                    bean.setMaidVisaAEContract("Yes");
//                } else {
//                    bean.setMaidVisaAEContract("No");
//                }
//            }
//        }
//        //Jirra ACC-278
//        if (bean.getMaidVisaAEContract() == null || bean.getMaidVisaAEContract().isEmpty()) {
//            bean.setMaidVisaAEContract("No");
//        }

        String contractId = initializer.getContractId(housemaidId);
        String contractType = initializer.getContractType(housemaidId);
        if(contractId != null) {
            bean.setContractName("Contr-" + contractId);
        }

        if (contractType != null && contractType.equals("maidvisa.ae_prospect")) {
            bean.setMaidVisaAEContract("Yes");
        } else {
            bean.setMaidVisaAEContract("No");
        }

        //Start Date
        if (housemaid.getStartDate() != null) {
            bean.setStartingDate(new Date(housemaid.getNewStartDate().getTime()));
        }

        //Arrival Date
        if (housemaid.getLandedInDubaiDate() != null) {
            bean.setArrivalDate(new Date(housemaid.getLandedInDubaiDate().getTime()));
        }

        //Living place && Food allowance
        if (housemaid.getLiving() == HousemaidLiveplace.OUT) {
            bean.setLiving(HousemaidLiveplace.OUT);
        } else {
            bean.setLiving(HousemaidLiveplace.IN);
        }

        //Company Accommodate
        if (housemaid.getHousingAllowance() != null && housemaid.getHousingAllowance() > 0) {
            bean.setCompanyAccommodated("No");
        } else {
            bean.setCompanyAccommodated("Yes");
        }

        //Jirra ACC-322
        //for PWO
        bean.setIncomeVariableComponent("0.0");
        bean.setPayStartDate(new Date(payrollMonth.withDayOfMonth(1).toDate().getTime()));
        bean.setPayEndDate(new Date(payrollMonth.dayOfMonth().withMaximumValue().toDate().getTime()));
        bean.setDaysInPeriod(Math.abs(DateUtil.getDaysBetween(bean.getPayEndDate(), bean.getPayStartDate())) + 1);
        bean.setRecordType("EDR");
        return bean;
    }


}
