package com.magnamedia.service.payroll.generation.newversion;

import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.HousemaidDocument;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.OfficeStaffDocument;
import com.magnamedia.helper.DebugHelper;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class NoEidEmployeeService {

    List<String> docTags = Arrays.asList(
            "EMIRATES_ID_FRONT_SIDE", "EMIRATES_ID_BACK_SIDE",
            "EMIRATE_ID", "eid_front_side", "eid_front", "eid_front",
            "eid_back_side", "eid_back");

    List<String> newRequestTags = Arrays.asList(
            "EMIRATES_ID_FRONT_SIDE", "EMIRATES_ID_BACK_SIDE",
            "EMIRATE_ID", "eid_front_side", "eid_front", "eid_back_side",
            "eid_back");

    List<String> newRequestTaskNames = Arrays.asList(
            "Prepare EID application, Prepare medical application, Prepare Tasheel contract",
            "Prepare folder containing E-visa medical application and EID",
            "Apply for Ansari",
            "Waiting for reply of Ansari",
            "Waiting for the maid to go to medical test and EID fingerprinting",
            "Upload tasheel contract to ERP, Receival of EID Card",
            "Upload tasheel contract to ERP",
            "Pending medical certificate approval from DHA",
            "Repeat Medical",
            "Upload Contract to Tasheel",
            "Prepare insurance application",
            "Pending Insurance Policy Issuance",
            "Pending Insurance Policy Issuance",
            "Apply for R-visa",
            "Get Form from GDRFA",
            "Insert Labour Card Expiry Date",
            "Prepare Folder for Zajel",
            "Prepare Folder for Zajel",
            "Prepare Folder for Zajel",
            "With Zajel for Visa Stamping",
            "With Zajel for Visa Stamping",
            "Receival of EID Card",
            "Receival of EID Card",
            "Send EID copy for Insurance Activation",
            "Put EID Passport and Insurance in 1 Package",
            "Pending Aramex to pick up EIDs and Insurance cards",
            "Put EID Passport and Insurance in 1 Package");

    public boolean isNoEIDOfficeStaff(OfficeStaff officeStaff) {
        try {
            if (!officeStaff.getHasEidAttachments()
                    && officeStaff.getVisaNewRequest() != null && !officeStaff.getVisaRequestHasEidAttachments()
                    && officeStaff.getVisaNewRequest().getTaskName() != null) {
                for (String s : officeStaff.getVisaNewRequest().getTaskName().split(","))
                    if (newRequestTaskNames.contains(s)) {
                        return true;
                    }
            }
        }catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating eid object for staff " + officeStaff.getId(), false);
        }

        return false;
    }

    public boolean isNoEIDHousemaid(Housemaid h) {
        try {
            if (!h.getHasEidAttachments()
                    && h.getVisaNewRequest() != null && !h.getVisaRequestHasEidAttachments()
                    && h.getVisaNewRequest().getTaskName() != null) {
                for (String s : h.getVisaNewRequest().getTaskName().split(","))
                    if (newRequestTaskNames.contains(s)) {
                        return true;
                    }
            }
        }catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating eid object for maid " + h.getId(), false);
        }

        return false;
    }
}
