/*
 * *
 *  *
 *  * <AUTHOR> <<EMAIL>>
 *  * Created At ${DATE}
 *  *
 *  *
 *
 */

package com.magnamedia.service;

import com.magnamedia.controller.HousemaidUnpaidDayController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.ForgivenessStatus;
import com.magnamedia.extra.HousemaidSalaryGroup;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.module.type.PendingStatus;
import com.magnamedia.repository.*;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Haj <PERSON> <<EMAIL>>
 * Created At 4/30/2022
 **/

@Service
public class HousemaidUnpaidDayService {
    @Autowired
    private HousemaidUnpaidDayRepository housemaidUnpaidDayRepository;

    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private PayrollManagerNoteRepository payrollManagerNoteRepository;
    @Autowired
    private HousemaidPayrollLogRepository housemaidPayrollLogRepository;

//    @Transactional
//    public void generateHousemaidUnpaidDays(Date date) {
//        List<Housemaid> housemaids = housemaidRepository.findHousemaidsNoShowOrPendingTermination(HousemaidStatus.NO_SHOW, HousemaidStatus.PENDING_FOR_DISCIPLINE, PendingStatus.PENDING_FOR_TERMINATION, HousemaidType.MAID_VISA);
//
//        for (Housemaid housemaid : housemaids) {
//            if (!maidWasInGroup1OrGroup4(housemaid, DateUtil.getDayEnd(date))) {
//                HousemaidUnpaidDay housemaidUnpaidDay = new HousemaidUnpaidDay(housemaid, date, housemaid.getStatus());
//                housemaidUnpaidDayRepository.save(housemaidUnpaidDay);
//            }
//        }
//    }

    @Transactional
    public void dismissForMVMaids() {
        List<HousemaidUnpaidDay> housemaidUnpaidDays = housemaidUnpaidDayRepository.findDismissMVMaids(HousemaidType.MAID_VISA);
        if (housemaidUnpaidDays != null && !housemaidUnpaidDays.isEmpty()) {
            for (HousemaidUnpaidDay housemaidUnpaidDay: housemaidUnpaidDays) {
                takeAction(housemaidUnpaidDay, false, null);
            }
        }
    }

    //get the number of days spent in group 3 in a payroll month
    public Integer getGroup3DaysForHousemaid(Housemaid housemaid, Date payrollMonth){
        Date payrollMonthStart = payrollMonth;
        Date payrollMonthEnd = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());
        return getGroup3DaysForHousemaid(housemaid, payrollMonthStart, payrollMonthEnd);
    }

    //get the number of days spent in group 3 between two dates
    public Integer getGroup3DaysForHousemaid(Housemaid housemaid, Date start, Date end){
        Integer daysCount = housemaidUnpaidDayRepository.findGroup3DaysForHousemaid(housemaid, start, end);
        return daysCount != null ? daysCount : 0;
    }

    //get the number of forgiven days by type (group 1 or 2) (Full or Basic) in a payroll month
    public Integer getForgivenDaysForHousemaidByType(Housemaid housemaid, Date payrollMonth, boolean group1){
        Date payrollMonthStart = payrollMonth;
        Date payrollMonthEnd = new Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime());

        return getForgivenDaysForHousemaidByType(housemaid, payrollMonthStart, payrollMonthEnd, group1);
    }

    //get the number of forgiven days by type (group 1 or 2) (Full or Basic) between two dates
    public Integer getForgivenDaysForHousemaidByType(Housemaid housemaid, Date start, Date end, boolean group1){

        PicklistItem forgivenessType = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES, group1 ? PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_WITH_CLIENT : PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION);
        Integer daysCount = housemaidUnpaidDayRepository.findForgivenDaysForHousemaidByType(housemaid, start, end, forgivenessType);
        return daysCount != null ? daysCount : 0;
    }


    //this is to forgive or dismiss an unpaid day in group 3
    public void takeAction(HousemaidUnpaidDay housemaidUnpaidDay, Boolean action, PicklistItem forgivenessType) {
        HousemaidPayrollAttendanceLogRepository attendanceLogRepo = Setup.getRepository(HousemaidPayrollAttendanceLogRepository.class);
        HousemaidPayrollAttendanceLog attendanceLog = attendanceLogRepo.findTop1ByHousemaidAndAttendanceDay(housemaidUnpaidDay.getHousemaid(), housemaidUnpaidDay.getUnpaidDate());
        housemaidUnpaidDay.setActionTaken(true);
        housemaidUnpaidDay.setForgiven(action);
        if (action) {
            housemaidUnpaidDay.setForgivenessType(forgivenessType);
            PicklistItem withClient = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_WITH_CLIENT);
            PicklistItem available = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION);

            if (available!= null && available.equals(forgivenessType)){
                housemaidUnpaidDay.setForgivenessStatus(ForgivenessStatus.AVAILABLE);
                if (attendanceLog != null && HousemaidSalaryGroup.GROUP_3.equals(attendanceLog.getSalaryGroup())) {
                    if (attendanceLog.getLiveOut() != null && attendanceLog.getLiveOut()) {
                        attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_6);
                    } else {
                        attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_2);
                    }
                }
            }else if (withClient != null && withClient.equals(forgivenessType)){
                housemaidUnpaidDay.setForgivenessStatus(ForgivenessStatus.WITH_CLIENT);
                if (attendanceLog != null && HousemaidSalaryGroup.GROUP_3.equals(attendanceLog.getSalaryGroup())) {
                    if (attendanceLog.getLiveOut() != null && attendanceLog.getLiveOut()) {
                        attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_5);
                    } else {
                        attendanceLog.setSalaryGroup(HousemaidSalaryGroup.GROUP_1);
                    }
                }
            }
            if (attendanceLog != null) {
                attendanceLog = attendanceLogRepo.save(attendanceLog);
            }

            //create a manager note if we passed the payroll else do nothing
            java.sql.Date payrollMonth = DateUtil.getFirstOfMonthDateSQL(housemaidUnpaidDay.getUnpaidDate());
            HousemaidPayrollLog housemaidPayrollLog = housemaidPayrollLogRepository.findFirstByPayrollMonthAndHousemaid(payrollMonth, housemaidUnpaidDay.getHousemaid());
            if (housemaidPayrollLog != null
                    && (housemaidPayrollLog.getTransferred()
                    || HousemaidPayrollLog.HousemaidPayrollLogStatus.FINAL.equals(housemaidPayrollLog.getLogStatus()))) {
                HousemaidPayrollMonthlyGroup housemaidPayrollMonthlyGroup = Setup.getRepository(HousemaidPayrollMonthlyGroupRepository.class)
                        .findTop1ByHousemaidAndPayrollMonth(housemaidUnpaidDay.getHousemaid(), payrollMonth);

                housemaidUnpaidDay.setNotInSamePayrollMonth(true);
                PayrollManagerNote payrollManagerNote = new PayrollManagerNote();
                payrollManagerNote.setHousemaid(housemaidUnpaidDay.getHousemaid());
                payrollManagerNote.setNoteDate(new java.util.Date());
                payrollManagerNote.setNoteType(AbstractPayrollManagerNote.ManagerNoteType.ADDITION);
                payrollManagerNote.setNoteReasone("Forgiveness for " + DateUtil.formatDateDashed(housemaidUnpaidDay.getUnpaidDate()));
                payrollManagerNote.setAdditionReason(PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        "forgive_deduction"));
                Double amount;
                if (forgivenessType.getCode().toLowerCase().contains("full")) {
                    payrollManagerNote.setPurpose(PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_PURPOSE_FOR_FORGIVENESS_DEDUCTION, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_WITH_CLIENT_PURPOSE));
                    if (housemaidPayrollMonthlyGroup != null && attendanceLog != null) {
                        amount = attendanceLog.getLiveOut() ? housemaidPayrollMonthlyGroup.getGr5Salary() : housemaidPayrollMonthlyGroup.getGr1Salary();
                    } else {
                        amount = housemaidPayrollLog.getBasicSalary() != null ? housemaidPayrollLog.getBasicSalary() : housemaidUnpaidDay.getHousemaid().getBasicSalary();
                    }
                    amount /= new LocalDate(payrollMonth).dayOfMonth().getMaximumValue();
                } else {
                    payrollManagerNote.setPurpose(PicklistHelper.getItem(PayrollManagementModule.PICKLIST_HOUSEMAID_PURPOSE_FOR_FORGIVENESS_DEDUCTION, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_IN_ACCOMMODATION_PURPOSE));
                    if (housemaidPayrollMonthlyGroup != null && attendanceLog != null) {
                        amount = attendanceLog.getLiveOut() ? housemaidPayrollMonthlyGroup.getGr6Salary() : housemaidPayrollMonthlyGroup.getGr2Salary();
                    } else {
                        amount = housemaidPayrollLog.getAccommodationSalary() != null ? housemaidPayrollLog.getAccommodationSalary() : (housemaidUnpaidDay.getHousemaid().getAccommodationSalary() != null ? housemaidUnpaidDay.getHousemaid().getAccommodationSalary() : 0.0);
                    }
                    amount /= new LocalDate(payrollMonth).dayOfMonth().getMaximumValue();
                }
                payrollManagerNote.setAmount((double) Math.round(amount));
                payrollManagerNoteRepository.save(payrollManagerNote);
            }
        }else{
            housemaidUnpaidDay.setForgivenessStatus(ForgivenessStatus.DISMISSED);
        }

        housemaidUnpaidDayRepository.save(housemaidUnpaidDay);
    }

    public Boolean maidWasInGroup1OrGroup4(Housemaid housemaid, java.util.Date date){
        java.util.Date startOfTheDay = DateUtil.getStartDayValue(date);
        List<HousemaidStatus> gr1AndGr4Statuses = Arrays.asList(HousemaidStatus.WITH_CLIENT, HousemaidStatus.ASSIGNED_OFFICE_WORK, HousemaidStatus.ON_VACATION);

        HistorySelectQuery<Housemaid> housemaidHistorySelectQuery = new HistorySelectQuery<>(Housemaid.class);
        housemaidHistorySelectQuery.filterBy("id", "=", housemaid.getId());
        housemaidHistorySelectQuery.filterByChanged("status");
        housemaidHistorySelectQuery.filterBy("status", "IN", gr1AndGr4Statuses);
        housemaidHistorySelectQuery.filterBy("lastModificationDate", ">=", startOfTheDay);
        housemaidHistorySelectQuery.filterBy("lastModificationDate", "<=", date);
        housemaidHistorySelectQuery.sortBy("lastModificationDate", false);
        housemaidHistorySelectQuery.setLimit(1);
        List<Housemaid> housemaidStatuses = housemaidHistorySelectQuery.execute();
        Boolean currentDayHasGroup1Revision = housemaidStatuses.size() > 0;
        if (currentDayHasGroup1Revision){
            return currentDayHasGroup1Revision;
        }

        housemaidHistorySelectQuery = new HistorySelectQuery<>(Housemaid.class);
        housemaidHistorySelectQuery.filterBy("id", "=", housemaid.getId());
        housemaidHistorySelectQuery.filterByChanged("status");
        housemaidHistorySelectQuery.filterBy("lastModificationDate", "<", startOfTheDay);
        housemaidHistorySelectQuery.sortBy("lastModificationDate", false);
        housemaidHistorySelectQuery.setLimit(1);

        housemaidStatuses = housemaidHistorySelectQuery.execute();
        Boolean lasRevisionBeforeTheDayWasGroup1Revision = housemaidStatuses.size() > 0 && gr1AndGr4Statuses.contains(housemaidStatuses.get(0).getStatus());
        return lasRevisionBeforeTheDayWasGroup1Revision;
    }

    @Transactional
    public void createOrPaidDaysForHousemaid(Long housemaidId, Date from, Date to, String status, String pendingStatus){
        Housemaid housemaid = housemaidRepository.findOne(housemaidId);
        if (housemaid == null || from == null || to == null || (housemaid.getHousemaidType() != null && housemaid.getHousemaidType().equals(HousemaidType.MAID_VISA))){
            return;
        }
        to = new Date(DateUtil.getStartDayValue(new java.util.Date(to.getTime())).getTime());
        Date date = from;
        PicklistItem withClient = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_WITH_CLIENT);
        List<HousemaidUnpaidDay> housemaidUnpaidDays = null;
        while (date.before(to)){
            housemaidUnpaidDays = housemaidUnpaidDayRepository.findByHousemaidAndUnpaidDate(housemaid, date);
            if (housemaidUnpaidDays == null || housemaidUnpaidDays.isEmpty()){
                createPaidDaysForHousemaid(housemaid, date, status, pendingStatus);
            }else {
                housemaidUnpaidDays.forEach(housemaidUnpaidDay -> {
                    Setup.getApplicationContext().getBean(HousemaidUnpaidDayController.class).takeAction(housemaidUnpaidDay, true, withClient);

                });
            }
            date = DateUtil.addDaysSql(date, 1);

        }
    }

    @Transactional
    public void createPaidDaysForHousemaid(Housemaid housemaid, Date date, String status, String pendingStatusAsString){
        if (maidWasInGroup1OrGroup4(housemaid, date)){
            return;
        }
        PicklistItem withClient = PicklistHelper.getItem(PayrollManagementModule.PICKLIST_UNPAID_DAYS_FORGIVENESS_TYPES, PayrollManagementModule.PICKLIST_ITEM_MAID_WAS_WITH_CLIENT);

        PendingStatus pendingStatus = (pendingStatusAsString != null && !pendingStatusAsString.isEmpty()) ? PendingStatus.valueOf(pendingStatusAsString) : null;
        HousemaidUnpaidDay housemaidUnpaidDay = new HousemaidUnpaidDay(housemaid, date, HousemaidStatus.valueOf(status), pendingStatus, 0.0);
        Setup.getApplicationContext().getBean(HousemaidUnpaidDayController.class).takeAction(housemaidUnpaidDay, true, withClient);
    }

}
