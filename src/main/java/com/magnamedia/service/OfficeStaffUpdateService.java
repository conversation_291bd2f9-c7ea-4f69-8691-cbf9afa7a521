package com.magnamedia.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SmsResponse;
import com.magnamedia.core.helper.SmsService;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.TransferDestination;
import com.magnamedia.entity.UpdateOfficeStaffApprove;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.ReceiveMoneyMethod;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.OfficeStaffRepository;
import com.magnamedia.repository.UpdateOfficeStaffApproveRepository;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newversion.LockDateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class OfficeStaffUpdateService {

    @Autowired
    private PublicPageHelper publicPageHelper;

    @Autowired
    private ChangeBankDetailsService changeBankDetailsService;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private PendingManagerApprovalService pendingManagerApprovalService;

    public Double notNull(Double number) {
        return number != null ? number : 0.0;
    }

    @Transactional
    public void checkSalaryAndSendApproveMessages(OfficeStaff origin, OfficeStaff updated, ObjectNode jsonNode) {

        Double salary = jsonNode.has("salary") ? notNull(updated.getSalary()) : notNull(origin.getSalary());
        Double housing = jsonNode.has("housingAllowance") ? notNull(updated.getHousingAllowance()) : notNull(origin.getHousingAllowance());
        Double transportation = jsonNode.has("trasnportation") ? notNull(updated.getTrasnportation()) : notNull(origin.getTrasnportation());
        Double basic = jsonNode.has("basicSalary") ? notNull(updated.getBasicSalary()) : notNull(origin.getBasicSalary());
        SalaryCurrency currency = jsonNode.has("salaryCurrency") ? updated.getSalaryCurrency() : origin.getSalaryCurrency();
        if (salary < 0.0 || housing < 0.0 || transportation < 0.0
                || basic < 0.0)
            throw new BusinessException("Salary/Salary Components can't be negative!");

        if ( (origin.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EXPAT) || origin.getEmployeeType().equals(OfficeStaffType.DUBAI_STAFF_EMARATI))
                && !salary.equals(basic + housing + transportation))
            throw new BusinessException("Salary must be equals to Basic Salary + Housing + Transportation");

        if(!origin.getSalary().equals(salary) || !origin.getSalaryCurrency().equals(currency)) {
            UpdateOfficeStaffApprove approve = new UpdateOfficeStaffApprove(origin,
                    UpdateOfficeStaffApprove.UpdateType.SALARY, salary, currency, basic, housing, transportation);
            Setup.getRepository(UpdateOfficeStaffApproveRepository.class).save(approve);

            OfficeStaff finalManager = origin.getFinalManager();
            if (finalManager == null || finalManager.getPhoneNumber() == null) {
                throw new BusinessException("Can't find Final Manager or the Manager doesn't have phone number!");
            } else {
                String url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, approve.getId().toString() + "#Payroll_Update_OfficeStaff_Approval", String.valueOf(finalManager.getUser().getId()));
                pendingManagerApprovalService.insertNewPendingApprovalRequest(origin.getName(), DateUtil.formatFullDate(origin.getStartingDate()),origin.getSalaryWithCurrency(), origin.getJobTitle() != null ? origin.getJobTitle().getName() : "", origin.getEmployeeManager(), finalManager, origin.getSalaryWithCurrency(), "Update Office Staff", null, url, approve.getId().toString() + "#Payroll_Update_OfficeStaff_Approval");
//                Map<String, String> paramValues = new HashMap<>();
//                paramValues.put("employee_name", origin.getFirstLastName());
//                paramValues.put("job_title", origin.getJobTitle().getName());
//                paramValues.put("salary", salary + " " + currency);
//                paramValues.put("start_date", DateUtil.formatClientFullDate(origin.getStartingDate()));
//                paramValues.put("url", url);
//
//                SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                        "Changing Salary or Start Date",
//                        normalizePhoneNumber(finalManager.getPhoneNumber()),
//                        finalManager.getEmail(),
//                        SmsReceiverType.Office_Staff,
//                        finalManager.getId(),
//                        finalManager.getName(),
//                        "Payroll_Update_OfficeStaff_Approval",
//                        paramValues,
//                        null,
//                        finalManager.getPreferredCommunicationMethod());
//
//                if (smsResponse == null || !smsResponse.isSuccess())
//                    throw new RuntimeException("Failed to send the Approval Message to the Final Manager");
            }

        }

        removeNeedApproveFields(jsonNode);
    }


    @Transactional
    public void checkStartingDateAndSendApproveMessages(OfficeStaff origin, OfficeStaff updated, ObjectNode jsonNode) {

        Date startingDate = jsonNode.has("startingDate") ? updated.getStartingDate() : origin.getStartingDate();

        if(startingDate == null) {
            throw new BusinessException("Starting date can't be null!");
        }

        if(origin.getStartingDate() == null ||
                DateUtil.getDayStart(startingDate).getTime() != DateUtil.getDayStart(origin.getStartingDate()).getTime()) {

            PaymentRuleEmployeeType employeeType = PaymentRuleEmployeeType.OVERSEAS;
            switch (origin.getEmployeeType()){
                case DUBAI_STAFF_EXPAT:
                    employeeType = PaymentRuleEmployeeType.EXPATS;
                    break;
                case DUBAI_STAFF_EMARATI:
                    employeeType = PaymentRuleEmployeeType.EMIRATI;
                    break;
                case OVERSEAS_STAFF:
                    employeeType = PaymentRuleEmployeeType.OVERSEAS;
                    break;
            }

            Date now = new Date();
            if (!now.before(Setup.getApplicationContext().getBean(LockDateService.class)
                    .getPaymentDateBasedOnEmployeeType(new java.sql.Date(startingDate.getTime()), employeeType, null, null))){
                throw new BusinessException("update Starting Date is invalid");
            }

            UpdateOfficeStaffApprove approve = new UpdateOfficeStaffApprove(origin, UpdateOfficeStaffApprove.UpdateType.START_DATE, startingDate);
            Setup.getRepository(UpdateOfficeStaffApproveRepository.class).save(approve);

            if(updated.getEmployeeManager() != null) {
                origin.setEmployeeManager(Setup.getRepository(OfficeStaffRepository.class)
                .findOne(updated.getEmployeeManager().getId()));
            }

            if(updated.getJobTitle() != null) {
                origin.setJobTitle(Setup.getRepository(PicklistItemRepository.class)
                        .findOne(updated.getJobTitle().getId()));
            }

            OfficeStaff finalManager = origin.getFinalManager();
            if (finalManager == null || finalManager.getPhoneNumber() == null) {
                throw new BusinessException("Can't find Final Manager or the Manager doesn't have phone number!");
            } else {
                String url = publicPageHelper.generatePublicURLWithoutShorten(PublicPageHelper.FINAL_MANAGER_APPROVE, approve.getId().toString() + "#Payroll_Update_OfficeStaff_Approval", String.valueOf(finalManager.getUser().getId()));
                pendingManagerApprovalService.insertNewPendingApprovalRequest(origin.getName(), DateUtil.formatFullDate(origin.getStartingDate()),origin.getSalaryWithCurrency(), origin.getJobTitle() != null ? origin.getJobTitle().getName() : "", origin.getEmployeeManager(), finalManager, origin.getSalaryWithCurrency(), "Update Office Staff", null, url, approve.getId().toString() + "#Payroll_Update_OfficeStaff_Approval");
//                Map<String, String> paramValues = new HashMap<>();
//                paramValues.put("employee_name", origin.getFirstLastName());
//                paramValues.put("job_title", origin.getJobTitle().getName());
//                paramValues.put("salary", origin.getSalary() + " " + origin.getSalaryCurrency());
//                paramValues.put("start_date", DateUtil.formatClientFullDate(startingDate));
//                paramValues.put("url", url);
//
//                SmsResponse smsResponse = messageTemplateService.sendMessageOrEmail(
//                        "Changing Salary or Start Date",
//                        normalizePhoneNumber(finalManager.getPhoneNumber()),
//                        finalManager.getEmail(),
//                        SmsReceiverType.Office_Staff,
//                        finalManager.getId(),
//                        finalManager.getName(),
//                        "Payroll_Update_OfficeStaff_Approval",
//                        paramValues,
//                        null,
//                        finalManager.getPreferredCommunicationMethod());
//
//                if (smsResponse == null || !smsResponse.isSuccess())
//                    throw new RuntimeException("Failed to send the Approval Message to the Final Manager");
            }
//            sendEmailAfterUpdateStartingDate(origin, startingDate);
        }

        removeNeedApproveFields(jsonNode);
    }


    @Transactional
    public void checkBankDetailsAndSendAnsariEmail(OfficeStaff origin, OfficeStaff updated, ObjectNode jsonNode) {

        if(!jsonNode.has("selectedTransferDestination")) {
            return;
        }

        TransferDestination originTransferDestination = origin.getSelectedTransferDestination();
        TransferDestination updatedTransferDestination = updated.getSelectedTransferDestination();

        if(origin.getEmployeeType() != OfficeStaffType.DUBAI_STAFF_EXPAT
                || updatedTransferDestination.getReceiveMoneyMethod() != ReceiveMoneyMethod.BANK_TRANSFER
                || updatedTransferDestination.isNoIban()
                || updatedTransferDestination.getIban() == null
                || updatedTransferDestination.getAccountHolderName() == null
                || updatedTransferDestination.getAccountNumber() == null) {
            return;
        }

        boolean informationChanged = updatedTransferDestination.getId() == null
                || !originTransferDestination.getId().equals(updatedTransferDestination.getId())
                || originTransferDestination.getReceiveMoneyMethod() != ReceiveMoneyMethod.BANK_TRANSFER
                || originTransferDestination.getIban() == null // the old one was I DON'T HAVE AN IBAN
                || !originTransferDestination.getIban().equals(updatedTransferDestination.getIban())
                || originTransferDestination.getAccountHolderName() == null // Old Data - No Account holder name
                || !originTransferDestination.getAccountHolderName().equals(updatedTransferDestination.getAccountHolderName())
                || originTransferDestination.getAccountNumber() == null // Old Data - No Account number
                || !originTransferDestination.getAccountNumber().equals(updatedTransferDestination.getAccountNumber());

        if(informationChanged) {
            try {
                changeBankDetailsService.sendChangeBankDetailsEmail(origin, updatedTransferDestination.getIban(),
                        updatedTransferDestination.getAccountHolderName(),
                        updatedTransferDestination.getBankName(),
                        updatedTransferDestination.getAccountNumber());
            } catch (Throwable e) {
                Logger.getLogger("OfficeStaffUpdateService").log(Level.SEVERE, "Unable to send change bank details email to Ansari.", e);
                throw new BusinessException("Unable to send change bank details email to Ansari. Please check bank name and IBAN");
            }
        }

    }

    public void removeNeedApproveFields(ObjectNode jsonNode) {
        List<String> fields = Arrays.asList(
                "startingDate",
                "salary",
                "housingAllowance",
                "trasnportation",
                "primarySalary",
                "salaryCurrency",
                "basicSalary"
        );

        for(String key: fields) {
            if(jsonNode.has(key)) jsonNode.remove(key);
        }
    }

    public void sendEmailAfterUpdateStartingDate(OfficeStaff officeStaff, Date oldStartingDate) {
        if (officeStaff.getEmployeeManager() == null || officeStaff.getEmployeeManager().getEmail() == null || officeStaff.getEmployeeManager().getEmail().isEmpty()) {
            return;
        }
        String subject = officeStaff.getName() + " joining date has changed";
        Map<String, String> params = new HashMap<>();
        params.put("employee_name", officeStaff.getName());
        params.put("new_date", officeStaff.getStartingDate() != null ? DateUtil.formatClientFullDate(officeStaff.getStartingDate()): "no value");
        params.put("old_date", oldStartingDate != null ? DateUtil.formatClientFullDate(oldStartingDate) : "no value");

        List<EmailRecipient> recipients = Recipient.parseEmailsString((officeStaff.getEmployeeManager() != null &&  officeStaff.getEmployeeManager().getEmail() != null) ? officeStaff.getEmployeeManager().getEmail() : "");
//        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_After_Office_Staff_Starting_Date_Is_Updated", params);
//        Setup.getMailService().sendEmail(recipients, templateEmail, null);
        Setup.getApplicationContext().getBean(MessagingService.class).send(recipients, null, "Payroll_After_Office_Staff_Starting_Date_Is_Updated"
                , subject, params, new ArrayList<>(), null);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void sendDuplicateIBanEmail(OfficeStaff officeStaff, OfficeStaff existStaff) {
        User user = CurrentRequest.getUser();
        String subject = (user != null ? user.getName() : "User") + " Tried to Add a Duplicate IBAN. ";
        List<EmailRecipient> recipients = Recipient.parseEmailsString(
                Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DUPLICATE_IBAN_DETECTION_RECIPIENT));
        Map<String,String > params = new HashMap<>();
        params.put("date_and_time", DateUtil.formatDateDashedWithTime(new Date()));
        params.put("user_name", user != null ? user.getName() : "User");
        params.put("affected_employee", officeStaff.getName());
        params.put("iban", existStaff.getSelectedTransferDestination() != null ? existStaff.getSelectedTransferDestination().getIban() : "iban");
        params.put("duplicate_found_for", existStaff.getName());
        Setup.getApplicationContext().getBean(MessagingService.class)
                .send(recipients, null, "Payroll_Duplicate_IBan_Detection_Template", subject
                        , params, null, null);
    }

    public String getIsoCode(PicklistItem country) {
        if (country != null && country.getId() != null) {
            PicklistItem countryFromDB = Setup.getRepository(PicklistItemRepository.class).findOne(country.getId());
            if (countryFromDB != null) {
                return countryFromDB.getTagValue("iso_code_payroll") != null ? countryFromDB.getTagValue("iso_code_payroll").getValue() : "";
            }
        }
        return "";
    }
}
