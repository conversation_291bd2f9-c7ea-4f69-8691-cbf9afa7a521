package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.entity.OfficeStaffCandidate;
import com.magnamedia.entity.OfficeStaffTodo;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.extra.zohohr.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.OfficeStaffTodoType;
import com.magnamedia.module.type.OfficeStaffType;
import com.magnamedia.module.type.SalaryCurrency;
import com.magnamedia.repository.OfficeStaffCandidateRepository;
import com.magnamedia.repository.OfficeStaffTodoRepository;
import com.magnamedia.service.message.MessagingService;
import org.springframework.http.*;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.text.ParseException;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class ZohoHRService {
    private static final Logger logger = Logger.getLogger(ZohoHRService.class.getName());
    private static final String REFRESH_TOKEN = "**********************************************************************";
    private static final String CLIENT_ID = "1000.4UTK8ZT4LM7ILPJB5YRCOH83UURI2Q";
    private static final String CLIENT_SECRET = "940b7860c158417c30b94d2e6f7e2ef092ed4bc523";
    private static final String GRANT_TYPE = "refresh_token";

    public static String ZOHO_HR_BASE_API;

    public static String ZOHO_HR_API_OAUTH_TOKEN;

    private final RestTemplate restTemplate;

    public ZohoHRService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    private void init() {
        ZOHO_HR_BASE_API = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ZOHO_HR_BASE_API);
        //Request Access token:
        ZOHO_HR_API_OAUTH_TOKEN = requestZohoAccessToken();
    }

    private String requestZohoAccessToken() {
        int callAPIRetriesCount = 3;
        int callAPIRetryIndex = 0;
        boolean successCall = false;
        ResponseEntity<ZohoRefreshTokenResponseDTO> response = null;
        String url = String.format("%s?refresh_token=%s&client_id=%s&client_secret=%s&grant_type=%s", "https://accounts.zoho.com/oauth/v2/token", REFRESH_TOKEN, CLIENT_ID, CLIENT_SECRET, GRANT_TYPE);
        while (callAPIRetryIndex < callAPIRetriesCount) {
            try {
                callAPIRetryIndex++;
                response = restTemplate.postForEntity(url, null, ZohoRefreshTokenResponseDTO.class);
                if (response.getStatusCode().is2xxSuccessful()) {
                    successCall = true;
                    break;
                }
            } catch (Throwable e) {
                logger.log(Level.SEVERE, "Failed to get access token ,call try :" + (callAPIRetryIndex), e);
            }
        }
        if (!successCall) {
            DebugHelper.sendMail("<EMAIL>", "ZOHO HR service --> requestZohoAccessToken Failed three times at " + new Date());
            return null;
        }

        if (response != null && response.getBody() != null && response.getStatusCode() == HttpStatus.OK) {
            ZohoRefreshTokenResponseDTO zohoRefreshTokenResponseDTO = response.getBody();
            if (zohoRefreshTokenResponseDTO != null) {
                return zohoRefreshTokenResponseDTO.getAccessToken();
            }
        }
        return null;
    }

    public List<ZohoHRMetaInfoDTO> fetchHiresApi(boolean isForOverseas) {
        init();
        if(ZOHO_HR_API_OAUTH_TOKEN == null)
            return Collections.emptyList();
        //Initialize fetch new hired overseas:
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + ZOHO_HR_API_OAUTH_TOKEN);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        String url = "";
        if (isForOverseas)
            url = String.format("%s%s/%s?criteria=%s&cvid=%s", ZOHO_HR_BASE_API, "Candidates", "search", "((Location:equals:Overseas)and(Candidate_Stage:equals:Hired))", "755650000013649489");
        else
            url = String.format("%s%s/%s?criteria=%s", ZOHO_HR_BASE_API, "Candidates", "search", "(((Location:equals:Local)or(Location:equals:Expat)) and (Candidate_Stage:equals:Offered))");
        logger.log(Level.SEVERE, "fetchHiresApi URL" + url);
        return fetchHiresApi(url, entity);
    }

    public List<ZohoHRMetaInfoDTO> fetchHiresApi(String url, HttpEntity<?> httpEntity) {
        List<ZohoHRMetaInfoDTO> zohoHRMetaInfoDTOS = new ArrayList<>();
        int page = 1;
        boolean hasMoreRecords = true;
        while (hasMoreRecords) {
            String fetchURL = url + "&page=" + page;
            ResponseEntity<ZohoCandidateResponseDTO> response = null;
            int callAPIRetriesCount = 3;
            int callAPIRetryIndex = 0;
            boolean successCall = false;
            while (callAPIRetryIndex < callAPIRetriesCount) {
                try {
                    callAPIRetryIndex++;
                    response = restTemplate.exchange(fetchURL, HttpMethod.GET, httpEntity, ZohoCandidateResponseDTO.class);
                    if (response.getStatusCode().is2xxSuccessful()) {
                        successCall = true;
                        break;
                    }
                } catch (Throwable e) {
                    logger.log(Level.SEVERE, "Failed to fetch new hires list,call try :" + (callAPIRetryIndex), e);
                }
            }
            if (!successCall) {
                DebugHelper.sendMail("<EMAIL>", "ZOHO HR service --> fetchHiresApi Failed three times with page:" + page + " at " + new Date());
                return zohoHRMetaInfoDTOS;
            }

            if (response != null && response.getBody() != null && response.getStatusCode() == HttpStatus.OK) {
                ZohoCandidateResponseDTO zohoCandidateResponseDTO = response.getBody();
                List<ZohoHRMetaInfoDTO> metaInfoDTOS = zohoCandidateResponseDTO.getData();
                if (metaInfoDTOS != null && !metaInfoDTOS.isEmpty())
                    zohoHRMetaInfoDTOS.addAll(metaInfoDTOS);
                hasMoreRecords = zohoCandidateResponseDTO.getInfo().isMoreRecords();
                page++;
            } else {
                hasMoreRecords = false;
            }
        }
        return zohoHRMetaInfoDTOS;
    }

    public boolean checkApplicantOfferAccepted(String candidateEmail) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + ZOHO_HR_API_OAUTH_TOKEN);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        String url = String.format("%s%s/search?email=%s", ZOHO_HR_BASE_API, "Applications", candidateEmail);
        logger.log(Level.SEVERE, "fetchApplicant URL" + url);
        int callAPIRetriesCount = 3;
        int callAPIRetryIndex = 0;
        boolean successCall = false;
        ResponseEntity<ZohoHRApplicantResponse> response = null;
        while (callAPIRetryIndex < callAPIRetriesCount) {
            try {
                callAPIRetryIndex++;
                response = restTemplate.exchange(url, HttpMethod.GET, entity, ZohoHRApplicantResponse.class);
                if (response.getStatusCode().is2xxSuccessful()) {
                    successCall = true;
                    break;
                }
            } catch (Throwable e) {
                logger.log(Level.SEVERE, "Failed to fetch Applicant record ,call try :" + (callAPIRetryIndex), e);
            }
        }
        if (!successCall) {
            DebugHelper.sendMail("<EMAIL>", "ZOHO HR service --> fetchApplicant Failed three times at " + new Date());
            return false;
        }

        if (response != null && response.getBody() != null && response.getStatusCode() == HttpStatus.OK) {
            List<ZohoHRApplicant> zohoHRApplicants = response.getBody().getData();
            if (zohoHRApplicants != null && !zohoHRApplicants.isEmpty()) {
                return zohoHRApplicants.stream().anyMatch(ap -> "Offer accepted".equalsIgnoreCase(ap.getApplicationStatus()));
            }
        }
        return false;
    }

    public Attachment fetchCandidateOfferLetter(String candidateName, String applicantId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + ZOHO_HR_API_OAUTH_TOKEN);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        String url = String.format("%s%s/%s/Attachments", ZOHO_HR_BASE_API, "Candidates", applicantId);
        logger.log(Level.SEVERE, "fetchCandidateOfferLetter URL" + url);
        int callAPIRetriesCount = 3;
        int callAPIRetryIndex = 0;
        boolean successCall = false;
        ResponseEntity<ZohoCandidateAttachmentResponse> response = null;
        while (callAPIRetryIndex < callAPIRetriesCount) {
            try {
                callAPIRetryIndex++;
                response = restTemplate.exchange(url, HttpMethod.GET, entity, ZohoCandidateAttachmentResponse.class);
                if (response.getStatusCode().is2xxSuccessful()) {
                    successCall = true;
                    break;
                }
            } catch (Throwable e) {
                logger.log(Level.SEVERE, "Failed to fetch Candidate attachments list,call try :" + (callAPIRetryIndex), e);
            }
        }
        if (!successCall) {
            DebugHelper.sendMail("<EMAIL>", "ZOHO HR service --> fetchCandidateOfferLetter Failed three times for Candiate: " + candidateName + "at " + new Date());
            return null;
        }
        if (response != null && response.getBody() != null && response.getStatusCode() == HttpStatus.OK) {
            for (ZohoCandidateAttachment candidateAttachment : response.getBody().getData()) {
                if (candidateAttachment.getCategory() != null && "Offer".equals(candidateAttachment.getCategory().getName())) {
                    // Download Offer letter attachment:
                    String downloadOfferURL = url + "/" + candidateAttachment.getId();
                    HttpHeaders downloadHeaders = new HttpHeaders();
                    downloadHeaders.set("Authorization", "Bearer " + ZOHO_HR_API_OAUTH_TOKEN);
                    downloadHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_PDF));
                    HttpEntity<String> downloadEntity = new HttpEntity<>(downloadHeaders);
                    RestTemplate restTemplateForDownloadFile = new RestTemplate(Collections.singletonList(new ByteArrayHttpMessageConverter()));
                    int callDownloadAPIRetriesCount = 3;
                    int callDownloadAPIRetryIndex = 0;
                    boolean successDownloadCall = false;
                    ResponseEntity<byte[]> offerLetterFile = null;
                    while (callDownloadAPIRetryIndex < callDownloadAPIRetriesCount) {
                        try {
                            callDownloadAPIRetryIndex++;
                            offerLetterFile = restTemplateForDownloadFile.exchange(downloadOfferURL, HttpMethod.GET, downloadEntity, byte[].class);
                            if (offerLetterFile.getStatusCode().is2xxSuccessful()) {
                                successDownloadCall = true;
                                break;
                            }
                        } catch (Throwable e) {
                            logger.log(Level.SEVERE, "Failed to download Candidate's Offer letter,call try :" + (callDownloadAPIRetryIndex), e);
                        }
                    }
                    if (!successDownloadCall) {
                        DebugHelper.sendMail("<EMAIL>", "ZOHO HR service --> download Offer Letter API Failed three times for Candiate: " + candidateName + "at " + new Date());
                        return null;
                    }
                    if (offerLetterFile != null && offerLetterFile.getBody() != null && offerLetterFile.getStatusCode() == HttpStatus.OK) {
                        logger.log(Level.SEVERE, "Download Offer letter: " + offerLetterFile.getBody().length);
                        return Storage.storeTemporary(candidateAttachment.getFile_name(), new ByteArrayInputStream(offerLetterFile.getBody()), "zoho_candidate_offer_letter", true);
                    }
                }

            }
        }
        return null;
    }

    @Transactional
    public void createHireEmployeeToDo(ZohoHRMetaInfoDTO zohoHRApplicant) {
        logger.log(Level.SEVERE, "createHireEmployeeToDo ID" + zohoHRApplicant.getId());
        OfficeStaffCandidate candidate = new OfficeStaffCandidate();
        candidate.setZohoApplicantId(zohoHRApplicant.getId());
        candidate.setFirstName(zohoHRApplicant.getFirst_name());
        candidate.setLastName(zohoHRApplicant.getLast_name());
        candidate.setPhoneNumber(zohoHRApplicant.getMobile());
        candidate.setEmail(zohoHRApplicant.getGmail_email() != null && !zohoHRApplicant.getGmail_email().isEmpty() ? zohoHRApplicant.getGmail_email() : zohoHRApplicant.getEmail());
        //candidate.setEmail(zohoHRApplicant.getEmail());
        candidate.setSource(zohoHRApplicant.getSource());
        candidate.setEmployeeName(zohoHRApplicant.getFirst_name() + " " + zohoHRApplicant.getLast_name());
        candidate.setZohoJobTitle(zohoHRApplicant.getPosting_title());
        candidate.setZohoExactJobTitle(zohoHRApplicant.getExact_posting_title());
        candidate.setZohoProfile(String.format("https://recruit.zoho.com/recruit/maidscc/EntityInfo.do?module=Candidates&id=%s&submodule=Candidates", zohoHRApplicant.getId()));
        if (!StringUtils.isEmpty(zohoHRApplicant.getStarting_date())) {
            try {
                zohoHRApplicant.setParsedStartingDate(DateUtil.parseDateDashed(zohoHRApplicant.getStarting_date()));
            } catch (ParseException exception) {
                logger.log(Level.SEVERE, "Parsing Starting date failed for CandidateId: " + zohoHRApplicant.getId());
                DebugHelper.sendMail("<EMAIL>", "ZOHO HR service --> Parsing Starting date failed for CandidateId: " + zohoHRApplicant.getId());
                return;
            }
        }
        switch (zohoHRApplicant.getLocation()) {
            case "Overseas":
                candidate.setEmployeeType(OfficeStaffType.OVERSEAS_STAFF);
                candidate.setStartingDate(zohoHRApplicant.getParsedStartingDate());
                break;
            case "Local":
                candidate.setEmployeeType(OfficeStaffType.DUBAI_STAFF_EMARATI);
                candidate.setPotentialStartDate(zohoHRApplicant.getParsedStartingDate());
                break;
            case "Expat":
                candidate.setEmployeeType(OfficeStaffType.DUBAI_STAFF_EXPAT);
                candidate.setPotentialStartDate(zohoHRApplicant.getParsedStartingDate());
                break;
        }
        candidate.setSalary(zohoHRApplicant.getSalary() != null ? Double.parseDouble(zohoHRApplicant.getSalary()) : null);
        candidate.setBasicSalary(candidate.getSalary());
        candidate.setHousing(0.0);
        candidate.setTransportation(0.0);
        switch (zohoHRApplicant.getSalary_currency()) {
            case "USD":
                candidate.setSalaryCurrency(SalaryCurrency.USD);
                break;
            case "AED":
                candidate.setSalaryCurrency(SalaryCurrency.AED);
                break;
        }
        logger.log(Level.SEVERE, "Download Offer letter file");
        Attachment offerLetter = fetchCandidateOfferLetter(zohoHRApplicant.getFirst_name() + " " + zohoHRApplicant.getLast_name(), zohoHRApplicant.getId());
        if (offerLetter != null) {
            logger.log(Level.SEVERE, "Download Offer letter attachment name: " + offerLetter.getName());
            candidate.addAttachment(offerLetter);
        }
        Setup.getRepository(OfficeStaffCandidateRepository.class).save(candidate);

        OfficeStaffTodo officeStaffTodo = new OfficeStaffTodo(OfficeStaffTodoType.HIRE_EMPLOYEE.toString());
        officeStaffTodo.setCandidate(candidate);
        Setup.getRepository(OfficeStaffTodoRepository.class).save(officeStaffTodo);
        //Send hire new employee email:
        HashMap<String, String> parameters = new HashMap<String, String>();
        parameters.put("Employee_FirstName", candidate.getFirstName());
        parameters.put("Employee_LastName", candidate.getLastName());
        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL));
        String subject = String.format("Action Required: New \"Hire Employee\" Task for %s %s", candidate.getFirstName(), candidate.getLastName());

        Setup.getApplicationContext().getBean(MessagingService.class).send(recipients, null, "Payroll_Send_Hire_New_Employee_Email", subject, parameters, new ArrayList<>(), null);
    }

    public void terminateCandidate(String candidateId, Date terminationDate) {
        init();

        // Check if OAuth token is null
        if(ZOHO_HR_API_OAUTH_TOKEN == null) {
            logger.log(Level.SEVERE, "OAuth token is null, cannot proceed.");
            return;
        }

        // Initialize headers for the HTTP request
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + ZOHO_HR_API_OAUTH_TOKEN);


        String requestBody =
                String.format(
                        "{\"data\":[{\"Termination_Status\":\"Terminated\",\"Termination_Date\":\"%s\"}]}",
                        DateUtil.formatDateDashed(terminationDate)
                );

        String url = String.format(ZOHO_HR_BASE_API +"Candidates/%s", candidateId);


        // Create an HttpEntity with the headers and body
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

        // Make the PUT request
        if(Setup.isProduction()) {
            RestTemplate restTemplate = new RestTemplate();
            try {
                ResponseEntity<Object> response = restTemplate.exchange(url, HttpMethod.PUT, entity, Object.class);

                if (response.getStatusCode().is2xxSuccessful()) {
                    logger.log(Level.SEVERE, "Response: " + response.getBody());
                }
            } catch (Exception e) {
                // Log error
                logger.log(Level.SEVERE, "Error terminating candidate: " + candidateId + " ,message: " + e.getMessage());
            }
        }

        return;
    }
}
