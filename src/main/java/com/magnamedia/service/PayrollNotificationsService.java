package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Strings;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.*;
import com.magnamedia.entity.*;
import com.magnamedia.extra.EmployeeType;
import com.magnamedia.extra.PayrollAuditTodoTaskNameType;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.helper.PublicPageHelper;
import com.magnamedia.helper.StringHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.service.message.MessagingService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PayrollNotificationsService {

    String PAYROLL_TRUSTEE_POSITION = "payroll_trustee";
    String PAYROLL_ACCOUNTANT_POSITION = "payroll_accountant";
    String PAYROLL_AUDITOR_POSITION = "payroll_auditor";

    @Autowired
    private Shortener shortener;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private MessagingService messagingService;

    public void replayToAuditorsAboutMaidSalaryQuestion(Housemaid housemaid, String answer) {
        Position position = Setup.getRepository(PositionRepository.class).findByCode(PAYROLL_AUDITOR_POSITION);
        if(position != null) {
            List<User> auditors = Setup.getRepository(UserRepository.class).findByPositionOrderByFullNameAsc(position);
            for(User auditor: auditors) {
                if(!auditor.isEnabled() || Strings.isNullOrEmpty(auditor.getEmail())) {
                    continue;
                }

                String subject = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_REPLAY_QUESTION_ABOUT_SALARY_EMAIL_TITLE);
                subject = subject.replace("@maid_name@", housemaid.getName());

                List<EmailRecipient> recipients = Recipient.parseEmailsString(auditor.getEmail());
                TextEmail mail = new TextEmail(subject, answer);
                Setup.getMailService().sendEmail(recipients, mail, null);
            }
        }
    }

//    public void notifyAuditors(PayrollAuditTodo payrollAuditTodo) {
//        Position position = Setup.getRepository(PositionRepository.class).findByCode(PAYROLL_AUDITOR_POSITION);
//        if(position != null) {
//            String link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + "auditors-to-do");
//
//            String subject;
//            String templateName;
//
//            if ("EXCLUDED_MAID_VISA".equals(payrollAuditTodo.getTaskName())) {
//                subject = Setup.getParameter(Setup.getCurrentModule(),
//                        PayrollManagementModule.PARAMETER_MV_EXCLUDED_AUDIT_TODO_EMAIL_SUBJECT);
//                templateName = "Payroll_Excluded_MV_Audit_TODO_Notification";
//            }else{
//                subject = "Payroll Audit TO-DO Notification";
//                templateName = "Payroll_Audit_TODO_Notification";
//            }
//
//            Map<String, String> params = new HashMap<>();
//            params.put("todoLabel",payrollAuditTodo.getLabel());
//            params.put("url",link);
////            TemplateEmail templateEmail = new TemplateEmail(subject, templateName, params);
//
//            List<User> auditors = Setup.getRepository(UserRepository.class).findByPositionOrderByFullNameAsc(position);
//            for(User auditor: auditors) {
//                if(!auditor.isEnabled() || Strings.isNullOrEmpty(auditor.getEmail())) {
//                    continue;
//                }
//
//                //String testReceiver = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_TEST_EMAIL_RECEIVER);
//                List<EmailRecipient> recipients = Recipient.parseEmailsString(auditor.getEmail());
//                messagingService.send(recipients, null, templateName, subject, params, null);
////                Setup.getMailService().sendEmail(recipients, templateEmail, null);
//            }
//        }
//    }

//    public void notifyAccountants(PayrollAccountantTodo accountantTodo) {
//        String email = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PAYROLL_PAYMENT_TODO_NOTIFICATION_EMAIL);
//        List<EmailRecipient> recipients = Recipient.parseEmailsString(email);
//        String link = shortener.shorten(PublicPageHelper.getOtherModuleBaseUrl("accounting") + "/accountant-todo");
//
//        String subject = "Payroll Payment TO-DO Notification";
//        Map<String, String> params = new HashMap<>();
//        params.put("todoLabel", accountantTodo.getLabel());
//        params.put("url", link);
////        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Payment_TODO_Notification", params);
//
////        MailService mailService = Setup.getMailService();
//
//        messagingService.send(recipients, null, "Payroll_Payment_TODO_Notification", subject, params, null);
////        for (EmailRecipient recipient : recipients)
////            mailService.sendEmail(recipient, templateEmail, null);
//
//    }

//    public void notifyPayrollTrustee(OfficeStaffTodo officeStaffTodo) {
//        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL));
//        String link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + "office-staff-onboarding");
//
//        String subject = "Payroll Manager TO-DO Notification";
//
//        Map<String, String> params = new HashMap<>();
//        params.put("todoLabel", StringHelper.enumToCapitalizedFirstLetter(officeStaffTodo.getTaskName()));
//        params.put("url", link);
////        TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Payroll_Manager_TODO_Notification", params);
////        MailService mailService = Setup.getMailService();
//        messagingService.send(recipients, null, "Payroll_Payroll_Manager_TODO_Notification", subject, params, null);
////        for (EmailRecipient recipient : recipients) {
////            mailService.sendEmail(recipient, templateEmail, null);
////        }
//    }

//    public void notifyPayrollTrusteeAboutManagerNote(AbstractPayrollManagerNote managerNote, OfficeStaff staff, boolean approved) {
//        Position position = Setup.getRepository(PositionRepository.class).findByCode(PAYROLL_TRUSTEE_POSITION);
//        if(position != null) {
//            List<User> trustees = Setup.getRepository(UserRepository.class).findByPositionOrderByFullNameAsc(position);
//            for(User trustee: trustees) {
//                if(!trustee.isEnabled() || Strings.isNullOrEmpty(trustee.getEmail())) {
//                    continue;
//                }
//
//                String link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + "office-staff-payroll/manager-notes/" + staff.getId());
//
//                String subject = "Payroll Manager Note Notification";
//                String messageTemplate = approved ? "Payroll_Manager_Notes_Trustee_Approval_Notification" : "Payroll_Manager_Notes_Trustee_Rejection_Notification";
//                Map paramValues = new HashMap<>();
//                paramValues.put("employee_name", staff.getName());
//                paramValues.put("manager_name", staff.getEmployeeManager() != null ? staff.getEmployeeManager().getName() : "__");
//                paramValues.put("note_type", StringHelper.enumToCapitalizedFirstLetter(managerNote.getNoteType().name()));
//                paramValues.put("request_date", DateUtil.formatDateSlashed(managerNote.getNoteDate()));
//                paramValues.put("link_of_employee_manager_notes_tab", link);
//                List<EmailRecipient> recipients = Recipient.parseEmailsString(trustee.getEmail());
//                TemplateEmail templateEmail = new TemplateEmail(subject, messageTemplate, paramValues);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
//            }
//        }
//    }

//    public void notifyPayrollTrusteeAboutRequest(OfficeStaff staff, OfficeStaff manager, String requestType, Date requestDate, String notes, String link, boolean approved, String amount) {
//        if (amount != null)
//            requestType += " with amount " + amount;
//        notifyPayrollTrusteeAboutRequest(staff, manager, requestType, requestDate, notes, link, approved);
//    }

//    public void notifyPayrollTrusteeAboutRequest( OfficeStaff staff, OfficeStaff manager, String requestType, Date requestDate, String notes, String link, boolean approved) {
//        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL));
//        String subject = "Payroll Change Approval Notification";
//        String messageTemplate = approved ? "Payroll_Request_Trustee_Approval_Notification" : "Payroll_Request_Trustee_Rejection_Notification";
//        Map paramValues = new HashMap<>();
//        paramValues.put("employee_name", staff.getName());
//        paramValues.put("manager_name", manager != null ? manager.getShortName() : "__");
//        paramValues.put("request_type", requestType);
//        paramValues.put("request_date", DateUtil.formatDateSlashed(requestDate));
//        paramValues.put("link_of_tab", link);
//        if (!approved)
//            paramValues.put("notes", notes);
//        TemplateEmail templateEmail = new TemplateEmail(subject, messageTemplate, paramValues);
//        MailService mailService = Setup.getMailService();

//        for (EmailRecipient recipient : recipients) {
//            mailService.sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                    .recipient(recipient)
//                    .html()
//                    .secure()
//                    .build());
//        }
//    }

//    public void sendAnEmailToTrustee ( TemplateEmail templateEmail) {
//        List<EmailRecipient> recipients = Recipient.parseEmailsString(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_TRUSTEE_EMAIL));
//        for (EmailRecipient recipient : recipients) {
//            Setup.getMailService().sendEmail(Arrays.asList(recipient), templateEmail, MessageTemplateService.getMaidsCcSenderName(), null);
//        }
//    }

//    public void notifyClientAboutMaidSalary(Client client, Housemaid housemaid, boolean received, Date receiveDate){
//        try {
//            String notificationType = "";
//            Map<String, String> params = new HashMap<>();
//            if (received) {
//                notificationType = "Payroll_Maid_Salary_Transferred_Notification";
//                params.put("maid_name", housemaid.getName());
//            } else {
//                notificationType = "Payroll_Maid_Salary_On_Hold_Notification";
//                String reason = HousemaidStatus.ON_VACATION.equals(housemaid.getStatus()) ? "was on vacation" : (HousemaidStatus.NO_SHOW.equals(housemaid.getStatus()) ? "was absent when salaries were released" : "");
//                params.put("reason", reason);
//                params.put("date", DateUtil.formatDateSlashed(receiveDate));
//            }
//            AppAction action1 = new AppAction();
//            action1.setText("Where and how can my maid receive her salary");
//            action1.setType(AppActionType.BUTTON);
//            action1.setFunctionType(FunctionType.NAVIGATE);
//            action1.setNavigationType(NavigationType.INAPP);
//            action1.setAppRouteName("/receive_salary_instructions");
//            action1.setAppRouteArguments(new HashMap<>());
//
//            Map<String, AppAction> context = new HashMap<>();
//            context.put("action1", action1);
//
//            messagingService.send(notificationType, notificationType, null, client, params, client, context, housemaid);
////            notificationService.pushNotification(client, notificationType, null, null, notificationType, params, context, client.getNormalizedMobileNumber(), client.getId(), client.getEntityType());
//        }catch (Exception e){
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyClientAboutMaidSalary for " + housemaid.getName(), false);
//        }
//    }


//    public void notifyMvClientAboutMaidSalary(Client client, Housemaid housemaid){
//        try {
//            String notificationType = "Pay_MV_Sal_notify";
//            Map<String, String> params = new HashMap<>();
//            params.put("maid_name", housemaid.getFirstName());
//            params.put("link", "@link_1@");
//
//
//            AppAction action_1 = new AppAction();
//            action_1.setText("Get " + housemaid.getFirstName() + "'s gov. documents & salary info");
//            action_1.setType(AppActionType.LINK);
//            action_1.setFunctionType(FunctionType.NAVIGATE);
//            action_1.setNavigationType(NavigationType.INAPP);
//            action_1.setAppRouteName("/docs");
//            action_1.setAppRouteArguments(new HashMap());
//
//            Map<String, AppAction> context = new HashMap<>();
//            context.put("link_1", action_1);
//            messagingService.send(notificationType, notificationType, null, client, params, client, context, housemaid);
////            notificationService.pushNotification(client, notificationType, null, null, notificationType, params, context, client.getNormalizedMobileNumber(), client.getId(), client.getEntityType());
//        }catch (Exception e){
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyMvClientAboutMaidSalary for " + housemaid.getName(), false);
//        }
//    }

//    public void notifyMaidAboutHerSalaryOnYaya(Housemaid housemaid, String salary, boolean withClient, String url){
//        try {
//            String notificationType = "";
//            Map<String, String> params = new HashMap<>();
//            params.put("salary_amount", salary);
//            params.put("url", url);
//
//            Map<String, AppAction> context = new HashMap<>();
////            HashMap<String, Object> action1 = new HashMap<>();
//            AppAction action_1 = new AppAction();
//            action_1.setType(AppActionType.BUTTON);
//            action_1.setFunctionType(FunctionType.NAVIGATE);
//            action_1.setNavigationType(NavigationType.INAPP);
//            action_1.setHyperlink(url);
//            action_1.setAppRouteArguments(new HashMap<>());
////            action1.put("type", AppActionType.BUTTON);
////            action1.put("functionType", FunctionType.NAVIGATE);
////            action1.put("navigationType", NavigationType.INAPP);
////            action1.put("hyperlink", url);
////            action1.put("appRouteArguments", new HashMap<>());
//
//            if (withClient) {
//                notificationType = "Payroll_Inform_WithClient_Maid_About_Salary_After_Yaya_Notification";
//                action_1.setText("Go to My Salary Information");
//                action_1.setAppRouteName("/questionAboutSalary");
////                action1.put("text", "Go to My Salary Information");
////                action1.put("appRouteName", "/questionAboutSalary");
//            } else {
//                notificationType = "Payroll_Inform_InAccommodation_Maid_About_Salary_After_Yaya_Notification";
//                action_1.setText("View Payslip");
//                action_1.setAppRouteName("/payslip_route");
////                action1.put("text", "View Payslip");
////                action1.put("appRouteName", "/payslip_route");
//            }
//
//            context.put("@action1@", action_1);
//            messagingService.send(notificationType, notificationType, housemaid.getYayaAppNotificationLang(), housemaid, params, housemaid, context, housemaid);
////            notificationService.pushNotification(housemaid, notificationType, null, null, notificationType, housemaid.getYayaAppNotificationLang(), params, context, housemaid.getNormalizedPhoneNumber(), housemaid.getId(), housemaid.getEntityType());
//        }catch (Exception e){
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyMaidAboutHerSalaryOnYaya for " + housemaid.getName(), false);
//        }
//    }


//    public void notifyAuditorsAboutIncludedMaidsTodo(PayrollAuditTodo payrollAuditTodo) {
//        Position position = Setup.getRepository(PositionRepository.class).findByCode(PAYROLL_AUDITOR_POSITION);
//        if(position != null) {
//            List<User> auditors = Setup.getRepository(UserRepository.class).findByPositionOrderByFullNameAsc(position);
//            for(User auditor: auditors) {
//                if(!auditor.isEnabled() || Strings.isNullOrEmpty(auditor.getEmail())) {
//                    continue;
//                }
//
//                String link = "";
//
//                String subject;
//                String templateName;
//                String managerName = "";
//
//                if (PayrollAuditTodoTaskNameType.CC_HOUSEMAIDS_INCLUDED_BY_MANAGER.toString().equals(payrollAuditTodo.getTaskName())) {
//                    subject = "CC maids that manager included in payroll";
//                    String ccManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CC_MANAGER_USER_ID);
//                    if (ccManagerId != null && !"".equals(ccManagerId)) {
//                        User ccManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(ccManagerId));
//                        managerName = ccManager.getFullName();
//                    }
//                    link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + PublicPageHelper.CC_INCLUDED_MAIDS + "/" + payrollAuditTodo.getId());
//                }else{
//                    subject = "MV maids that manager included in payroll";
//                    String mvManagerId = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MV_MANAGER_USER_ID);
//                    if (mvManagerId != null && !"".equals(mvManagerId)) {
//                        User mvManager = Setup.getRepository(UserRepository.class).findOne(Long.parseLong(mvManagerId));
//                        managerName = mvManager.getFullName();
//                    }
//                    link = shortener.shorten(PublicPageHelper.getModuleBaseUrl() + PublicPageHelper.MV_INCLUDED_MAIDS + "/" + payrollAuditTodo.getId());
//                }
//
//                List<EmailRecipient> recipients = Recipient.parseEmailsString(auditor.getEmail());
//                Map params = new HashMap();
//                params.put("manager_name", managerName);
//                params.put("url",link);
//                TemplateEmail templateEmail = new TemplateEmail(subject, "Payroll_Maids_Included_By_Manager_Todo_Email", params);
//                Setup.getMailService().sendEmail(recipients, templateEmail, null);
//            }
//        }
//    }

//    public void notifyClientMaidSalaryIsDaley(Client client, String  paymentDateWithFormat, String maidName) {
//        Map<String,String> paramValue = new HashMap<>();
//        paramValue.put("maid_name", maidName);
//        paramValue.put("payment_date", paymentDateWithFormat);
//        paramValue.put("greetings", Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
//
//        try {
//            messagingService.send("Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date", "Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date",
//                    null, client, paramValue, client, null, client);
////            PushNotification notification = pushNotification(client,
////                    "Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date",
////                    "Salary_Is_Delayed_Because_Update_Monthly_Payment_Rule_Payment_Date",
////                    null,
////                    paramValue,
////                    new HashMap(),
////                    client.getNormalizedMobileNumber(),
////                    true,
////                    DateUtil.remainingHoursTillNextDayAt(9),
////                    client.getId(),
////                    client.getEntityType()
////            );
//        }catch (Exception e){
//            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while notifyClientMaidSalaryIsDaley for " + client.getName(), false);
//        }
//
//    }

//    public static PushNotification pushNotification(BaseEntity recipient, String type, String contentTemplate, String lang, Map<String, String> templateParameters, Map<String, Object> context, String mobileNumber, boolean sendSmsIfNotReceived, int hoursBeforeSendSms, Long ownerId, String ownerType) throws JsonProcessingException {
//        Template template = TemplateUtil.getTemplate(contentTemplate);
//        String content = TemplateUtil.compileTemplate(template, lang, context, templateParameters);
//        String smsTemplateName = StringUtils.defaultIfBlank(template.getNotificationSmsTemplateName(), contentTemplate);
//        String smsText = smsTemplateName.equals(contentTemplate) ? content : TemplateUtil.compileTemplate(smsTemplateName, lang, context, templateParameters);
//        PushNotification pushNotification = Setup.getApplicationContext().getBean(NotificationService.class).pushNotification(recipient,
//                type,
//                content,
//                context,
//                template.getNotificationLocation(),
//                template.isNotificationCanClosedByUser(),
//                template.getNotificationExpirationHours(),
//                sendSmsIfNotReceived,
//                smsText,
//                mobileNumber,
//                hoursBeforeSendSms,
//                ownerId,
//                ownerType);
//        return pushNotification;
//    }
}
