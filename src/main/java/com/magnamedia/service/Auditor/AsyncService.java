package com.magnamedia.service.Auditor;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.mail.*;
import com.magnamedia.core.master.repository.JobDefinitionRepository;
import com.magnamedia.core.master.repository.JobInstanceRepository;
import com.magnamedia.core.repository.BackgroundTaskRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.core.master.repository.JobDefinitionRepository;
import com.magnamedia.core.master.repository.JobInstanceRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.entity.payroll.logging.OfficeStaffPayrollLog;
import com.magnamedia.entity.projection.BankTransferProjection;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.HousemaidPayrollLogProjection;
import com.magnamedia.helper.*;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.service.message.MessagingService;
import com.magnamedia.service.payroll.generation.newVersion2.HousemaidPayrollPaymentServiceV2;
import com.magnamedia.service.payroll.generation.newVersion2.PayrollExceptionsReportService;
import com.magnamedia.service.payroll.generation.newversion.*;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 7/21/2020
 **/
@Service
public class AsyncService {

    public static boolean HOUSEMAID_REPORT_IS_BEING_GENERATED = false;
    public static boolean OFFICESTAFF_REPORT_IS_BEING_GENERATED = false;
    private static final Logger logger = Logger.getLogger(JazzHRService.class.getName());

    @Async
    public void getPayrollFiles(PayrollAuditTodo payrollAuditTodo, String type) {

        try {
            payrollAuditTodo = Setup.getRepository(PayrollAuditTodoRepository.class).findOne(payrollAuditTodo.getId());
            MonthlyPaymentRule monthlyPaymentRule = Setup.getRepository(MonthlyPaymentRuleRepository.class).getOne(payrollAuditTodo.getMonthlyPaymentRule().getId());
            AuditFilesService auditFilesService = Setup.getApplicationContext().getBean(AuditFilesService.class);
            List<Attachment> attachmentList = new ArrayList<>();
            String subject = StringUtils.capitalize(payrollAuditTodo.getTaskName().toLowerCase());

            if (type != null && type.equals("Payroll Detailed File")) {
                subject += " - " + type + " of " + DateUtil.formatMonth(payrollAuditTodo.getPayrollMonth());
                attachmentList.addAll(Arrays.asList(auditFilesService.generatePayrollAuditDetailFile(monthlyPaymentRule, payrollAuditTodo, PaymentRuleEmployeeType.valueOf(payrollAuditTodo.getTaskName()))));
            } else if (type != null && type.equals("Payroll Pay Order File")) {
                subject += " - " + type + " of " + DateUtil.formatMonth(payrollAuditTodo.getPayrollMonth());
                attachmentList.addAll(auditFilesService.generatePayrollPrePaymentFilesNew(monthlyPaymentRule, payrollAuditTodo, PaymentRuleEmployeeType.valueOf(payrollAuditTodo.getTaskName())));
                //...PAY-879...//
            }else if (type != null && type.equals("Payroll Exceptions Report")) {
                subject += " - " + type + " of " + DateUtil.formatMonth(payrollAuditTodo.getPayrollMonth());
                List<Housemaid> housemaids = Setup.getApplicationContext().getBean(HousemaidPayrollAuditService.class).getTargetList(monthlyPaymentRule);
                List<Housemaid> ccMaids = housemaids.stream().filter(x -> !x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
                List<Housemaid> visaMaids = housemaids.stream().filter(x -> x.getHousemaidType().equals(HousemaidType.MAID_VISA)).collect(Collectors.toList());
                List<OfficeStaff> staffs = Setup.getApplicationContext().getBean(OfficeStaffPayrollAuditService.class).getTargetList(monthlyPaymentRule, PaymentRuleEmployeeType.valueOf(payrollAuditTodo.getTaskName()));
                Attachment payrollExceptionReport = Setup.getApplicationContext().getBean(PayrollExceptionsReportService.class).generatePayrollExceptionsReport(ccMaids, visaMaids, staffs, monthlyPaymentRule, monthlyPaymentRule.getEmployeesTypes());
                if (payrollExceptionReport != null)
                    attachmentList.add(payrollExceptionReport);
            }



//            DebugHelper.sendMail("<EMAIL>", "attachment for audit " + payrollAuditTodo.getTaskName() + " and type " + type + " is " + attachmentList.size());
            if (!attachmentList.isEmpty()) {
                String emails = Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_PAYROLL_EMAILS);
                List<EmailRecipient> recipients = EmailHelper.getMailRecipients(emails);
                TextEmail mail = new TextEmail(subject, "please find attached files");
                for (Attachment attachment : attachmentList)
                    mail.addAttachement(attachment);
                Setup.getMailService().sendEmail(recipients, mail, false, MessageTemplateService.getMaidsCcSenderName(), null);
//                DebugHelper.sendMail("<EMAIL>", "Email with subject: " + subject + " is sent");
            }
        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while payroll files!", false);
        } finally {
            HOUSEMAID_REPORT_IS_BEING_GENERATED = false;
        }
    }


    public void allTransfersAreDone(PayrollAccountantTodo accountantTodo) {
        // insert a new background task to close logs
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "allTransfersAreDoneBT", "asyncService", "payroll",
                        "allTransfersAreDoneBT",
                        accountantTodo.getEntityType(), accountantTodo.getId(), true,
                        false, new Class[]{Long.class}, new Object[]{accountantTodo.getId()});
    }

    @Transactional
    public Boolean allTransfersAreDoneBT(Long payrollAccountantTodoId) {
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(payrollAccountantTodoId);
        PayrollAccountantTodoType accountantTodoType = PayrollAccountantTodoType.valueOf(accountantTodo.getTaskName());

        Date currentDate = new Date();
        
        try {
            BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

            // Office Staff BGT
            BackgroundTask officeStaffTask = new BackgroundTask
                .builder("processOfficeStaff_" + payrollAccountantTodoId,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "processOfficeStaffPayroll")
                    .withRelatedEntity("PayrollAccountantTodo", payrollAccountantTodoId)
                    .withParameters(new Class[]{Long.class}, accountantTodo.getId())
                .build();
            backgroundTaskService.create(officeStaffTask);

            // Housemaid BGT
            BackgroundTask housemaidTask = new BackgroundTask
                .builder("processHousemaid_" + payrollAccountantTodoId,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "processHousemaidPayroll")
                    .withRelatedEntity("PayrollAccountantTodo", payrollAccountantTodoId)
                    .withParameters(new Class[]{Long.class}, accountantTodo.getId())
                .build();
            backgroundTaskService.create(housemaidTask);

            // Create completion checker BGT with 3 minutes delay
            BackgroundTask checkerTask = new BackgroundTask
                .builder("checkProcessesCompletion_" + payrollAccountantTodoId,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "checkProcessesCompletionBT")
                    .withRelatedEntity("PayrollAccountantTodo", payrollAccountantTodoId)
                    .withParameters(new Class[]{Long.class, String.class, java.util.Date.class},
                        accountantTodo.getId(), 
                        accountantTodoType.name(),
                        currentDate)
                .withDelay(3 * 60 * 1000L) // 3 minutes delay
                .build();
            backgroundTaskService.create(checkerTask);
            
            return true;
        } catch (Exception ex) {
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error in allTransfersAreDone: ", false);
            return false;
        }
    }

    /**
     * Called as BGT
     * Processes all office staff related payroll operations including salary transfers,
     * repayments, and log updates
     */
    public void processOfficeStaffPayroll(Long accountantTodoId) {
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(accountantTodoId);
        if(accountantTodo != null) {

            // Get configured chunk size
            int chunkSize = 50;
            try {
                chunkSize = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_PAYROLL_PROCESS_OFFICE_STAFF_PAYROLL_CHUNK_SIZE));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error parsing payroll process office staff payroll chunk size parameter, using default value: " + chunkSize);
            }

            // Process each step

            // 1. Process current month's staff transfers And previous unpaid salaries
            List<Long> logsIds = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findByPayrollAccountantTodoAndTransferredAndWillBeIncluded(accountantTodo, false, true);

            List<List<Long>> batches = ListHelper.divideList(logsIds, chunkSize);
            BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

            for (int i = 0; i < batches.size(); i++) {
                List<Long> batchLogIds = batches.get(i);

                List<String> batchLogIdsAsString = batchLogIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());

                BackgroundTask batchTask = new BackgroundTask
                        .builder("processCurrentMonthTransfersAndPreviousUnpaidSalariesBatch_" + accountantTodo.getId() + "_" + i,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "processCurrentMonthTransfersAndPreviousUnpaidSalariesBatchBT")
                        .withRelatedEntity("PayrollAccountantTodo", accountantTodo.getId())
                        .withParameters(new Class[]{List.class}, batchLogIdsAsString)
                        .build();
                backgroundTaskService.create(batchTask);
            }

            // 2. Reset excluded staff logs that have a previous months salaries included into it - Collect all updates and save in batch

            logsIds = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findByPayrollAccountantTodoAndTransferredAndWillBeIncludedAndPreviouslyUnpaidSalaries(accountantTodo, false, false);

            batches = ListHelper.divideList(logsIds, chunkSize);
            backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

            for (int i = 0; i < batches.size(); i++) {
                List<Long> batchLogIds = batches.get(i);

                List<String> batchLogIdsAsString = batchLogIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());

                BackgroundTask batchTask = new BackgroundTask
                        .builder("processExcludedLogsBatch_" + accountantTodo.getId() + "_" + i,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "processExcludedLogsBatchBT")
                        .withRelatedEntity("PayrollAccountantTodo", accountantTodo.getId())
                        .withParameters(new Class[]{List.class}, batchLogIdsAsString)
                        .build();
                backgroundTaskService.create(batchTask);
            }

            // 3. Process repayments - Collect all repayments and save in batch
            RepaymentRepository repaymentRepository = Setup.getRepository(RepaymentRepository.class);
            List<Long> repayments = repaymentRepository.getCurrentMonthRepaymentsForAll(accountantTodo.getPayrollMonth());

            batches = ListHelper.divideList(repayments, chunkSize);

            for (int i = 0; i < batches.size(); i++) {
                List<Long> batchIds = batches.get(i);

                List<String> batchIdsAsString = batchIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());

                BackgroundTask batchTask = new BackgroundTask
                        .builder("processRepaymentsBatch_" + accountantTodo.getId() + "_" + i,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "processRepaymentsBatchBT")
                        .withRelatedEntity("PayrollAccountantTodo", accountantTodo.getId())
                        .withParameters(new Class[]{List.class}, batchIdsAsString)
                        .build();
                backgroundTaskService.create(batchTask);
            }
        }
    }


    @Transactional
    public Boolean processCurrentMonthTransfersAndPreviousUnpaidSalariesBatchBT(List<String> logIdsAsString) {
        List<Long> logIds = logIdsAsString.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<OfficeStaffPayrollLog> batch = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findByIdIn(logIds);
        List<Long> transferredOfficeStaffIds = new ArrayList<>();

        for (OfficeStaffPayrollLog log : batch) {
            log.setTransferred(true);
            log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
            transferredOfficeStaffIds.add(log.getOfficeStaff().getId());
        }
        Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(batch);

        // 2. Process previous unpaid salaries
        if (transferredOfficeStaffIds.size() > 0) {
            batch = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findByTransferredAndWillBeIncludedAndOfficeStaffList(transferredOfficeStaffIds, false, true, false);
            for (OfficeStaffPayrollLog log : batch) {
                log.setTransferred(true);
                log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
            }
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(batch);
        }
        return true;
    }

    @Transactional
    public Boolean processExcludedLogsBatchBT(List<String> logIdsAsString) {
        List<Long> logIds = logIdsAsString.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<OfficeStaffPayrollLog> batch = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findByIdIn(logIds);

        List<OfficeStaffPayrollLog> logsToUpdate = new ArrayList<>();
        List<OfficeStaffPayrollLog> oldLogsToUpdate = new ArrayList<>();

        for (OfficeStaffPayrollLog log : batch) {

            Double totalSalary = log.getTotalSalary() != null ? log.getTotalSalary() : 0.0;
            Double previouslyUnpaidSalaries = log.getPreviouslyUnpaidSalaries() != null ? log.getPreviouslyUnpaidSalaries() : 0.0;

            log.setTotalSalary(totalSalary - previouslyUnpaidSalaries);
            log.setPreviouslyUnpaidSalaries(0.0);
            logsToUpdate.add(log);

            // Get old included logs
            List<OfficeStaffPayrollLog> oldStaffLogs = Setup.getRepository(OfficeStaffPayrollLogRepository.class)
                    .findByTransferredAndWillBeIncludedAndOfficeStaff(log.getOfficeStaff(), false, true);
            for (OfficeStaffPayrollLog oldLog : oldStaffLogs) {
                oldLog.setWillBeIncluded(false);
                oldLogsToUpdate.add(oldLog);
            }
        }

        // Batch save all updates
        if (!logsToUpdate.isEmpty()) {
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(logsToUpdate);
        }
        if (!oldLogsToUpdate.isEmpty()) {
            Setup.getRepository(OfficeStaffPayrollLogRepository.class).save(oldLogsToUpdate);
        }

        return true;
    }

    @Transactional
    public Boolean processRepaymentsBatchBT(List<String> repaymentIdsAsString) {
        List<Long> repaymentIds = repaymentIdsAsString.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        RepaymentRepository repaymentRepository = Setup.getRepository(RepaymentRepository.class);
        List<Repayment> batch = repaymentRepository.findAll(repaymentIds);

        List<Repayment> repaymentsToUpdate = new ArrayList<>();

        for (Repayment repayment : batch) {
            if (!repayment.getEmployeeLoan().getDoNotDeductFromSalary()) {
                repayment.setPaidRepayment(true);
                repaymentsToUpdate.add(repayment);
            }
        }
        if (!repaymentsToUpdate.isEmpty()) {
            repaymentRepository.save(repaymentsToUpdate);
        }
        return true;
    }


     /**
     * Processes all housemaid related payroll operations including visa payments,
     * manager notes, and important notes
     */
    public void processHousemaidPayroll(Long accountantTodoId) {
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(accountantTodoId);
        if(accountantTodo != null) {
            // Get configured chunk size
            int chunkSize = 20;
            try {
                chunkSize = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                        PayrollManagementModule.PARAMETER_PAYROLL_PROCESS_HOUSEMAID_PAYROLL_CHUNK_SIZE));
            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error parsing payroll process housemaid payroll chunk size parameter, using default value: " + chunkSize);
            }

            // 1. Process unpaid visa maids
            List<HousemaidPayrollLog> oldUnpaidMaidVisaSalaries = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .getOldUnpaidMaidVisaSalaries(accountantTodo, HousemaidUnpaidStatus.UNPAID_VISA_PAYMENT, true, accountantTodo.getPayrollMonth());

            processUnpaidVisaMaids(oldUnpaidMaidVisaSalaries, accountantTodo.getPayrollMonth());

            // 2. Process current month transfers
            // Complete housemaids transfers
            List<Long> housemaidLogs = Setup.getRepository(HousemaidPayrollLogRepository.class)
                    .findByPayrollAccountantTodoAndTransferredFalseAndWillBeIncludedTrue(accountantTodo);

            List<List<Long>> batches = ListHelper.divideList(housemaidLogs, chunkSize);
            BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

            for (int i = 0; i < batches.size(); i++) {
                List<Long> batchLogIds = batches.get(i);

                List<String> batchLogIdsAsString = batchLogIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());

                BackgroundTask batchTask = new BackgroundTask
                        .builder("processCurrentMonthHousemaidsBatch_" + accountantTodo.getId() + "_" + i,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "processCurrentMonthHousemaidsBatchBT")
                        .withRelatedEntity("PayrollAccountantTodo", accountantTodo.getId())
                        .withParameters(new Class[]{List.class, Long.class}, batchLogIdsAsString, accountantTodo.getId())
                        .build();
                backgroundTaskService.create(batchTask);
            }

            // 3. Process post-transfer tasks
            //deleteRemainingPendingLogsBT BGT
            BackgroundTask housemaidTask = new BackgroundTask
                    .builder("deleteRemainingPendingLogsBT_" + accountantTodo.getId(),
                    Setup.getCurrentModule().getCode(),
                    "asyncService",
                    "deleteRemainingPendingLogsBT")
                    .withRelatedEntity("PayrollAccountantTodo", accountantTodo.getId())
                    .withParameters(new Class[]{Long.class}, accountantTodo.getId())
                    .build();
            Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(housemaidTask);

        }
    }

    @Transactional
    public void processUnpaidVisaMaids(List<HousemaidPayrollLog> oldUnpaidMaidVisaSalaries, java.sql.Date payrollMonth) {
        // close previously unpaid salaries for maid visa
        for (HousemaidPayrollLog log : oldUnpaidMaidVisaSalaries) {
            HousemaidPayrollLog currentLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonth(log.getHousemaid(), payrollMonth);
            log.setTransferred(true);
            log.setPaidOnStatus("");
            log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
            log.setMaidParentLog(currentLog);
        }
        Setup.getRepository(HousemaidPayrollLogRepository.class).save(oldUnpaidMaidVisaSalaries);
    }

    @Transactional
    public Boolean processCurrentMonthHousemaidsBatchBT(List<String> logIdsAsString, Long accountantTodoId) {
        List<Long> logIds = logIdsAsString.stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(accountantTodoId);
        List<HousemaidPayrollLog> batch = Setup.getRepository(HousemaidPayrollLogRepository.class).findAll(logIds);

        // PAY-282 and PAY-344
        Map<Long, HousemaidPayrollLog> logs = new HashMap<>();
        for (HousemaidPayrollLog log : Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findLogByPayrollMonthAndTransferredTrue(accountantTodo.getPayrollMonth())) {
            logs.put(log.getHousemaid().getId(), log);
        }

        Map<Long, HousemaidPayrollLog> oldNotTransferredLogs = new HashMap<>();
        for (HousemaidPayrollLog log : Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findLogByPayrollMonthAndTransferredFalseAndMonthlyPaymentRuleNot(accountantTodo.getPayrollMonth(), accountantTodo.getMonthlyPaymentRule())) {
            oldNotTransferredLogs.put(log.getHousemaid().getId(), log);
        }

        List<Long> transferredImportantNotes = new ArrayList<>();
        List<HousemaidPayrollLog> toSave = new ArrayList<>();
        List<HousemaidPayrollLog> toDelete = new ArrayList<>();

        for (HousemaidPayrollLog log : batch) {
            HousemaidPayrollLog oldLog = logs.get(log.getHousemaid().getId());
            HousemaidPayrollLog oldNotTransferredLog = oldNotTransferredLogs.get(log.getHousemaid().getId());
            if (oldLog != null) {
                // Update old log and remove the new one
                double amount = log.getTotalSalary();
                oldLog.setTotalAddition(oldLog.getTotalAddition() + amount);
                oldLog.setManagerAdditions(oldLog.getManagerAdditions() + amount);
                oldLog.setTotalSalary(oldLog.getTotalSalary() + amount);
                oldLog.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                toSave.add(oldLog);
                transferredImportantNotes.add(log.getHousemaid().getId());

                HousemaidBeanInfo beanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(log);
                if (beanInfo != null)
                    Setup.getRepository(HousemaidBeanInfoRepository.class).delete(beanInfo);

                toDelete.add(log);
            } else if (oldNotTransferredLog != null) {
                //update the old months MV logs related to primary log to be related to the secondary log before deleting the primary log in case primary not paid yet
                List<HousemaidPayrollLog> oldRelatedMVLogs = Setup.getRepository(HousemaidPayrollLogRepository.class).getOldRelatedMVLogs(oldNotTransferredLog);
                for (HousemaidPayrollLog oldRelatedLog : oldRelatedMVLogs) {
                    oldRelatedLog.setMaidParentLog(log);
                    Setup.getRepository(HousemaidPayrollLogRepository.class).save(oldRelatedLog);
                }
                // remove old log and save the new one
                log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                log.setPaidOnStatus("");
                log.setTransferred(true);
                toSave.add(log);
                transferredImportantNotes.add(log.getHousemaid().getId());

                // just in case we are handling the secondary payroll (the secondary is being paid now and the primary  was not transferred)
                if (log.getPayrollMonth().equals(oldNotTransferredLog.getPayrollMonth())) {
                    HousemaidBeanInfo beanInfo = Setup.getRepository(HousemaidBeanInfoRepository.class).findTopByHousemaidPayrollLog(oldNotTransferredLog);
                    if (beanInfo != null)
                        Setup.getRepository(HousemaidBeanInfoRepository.class).delete(beanInfo);

                    toDelete.add(oldNotTransferredLog);
                }
            } else {
                transferredImportantNotes.add(log.getHousemaid().getId());
                log.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                log.setPaidOnStatus("");
                log.setTransferred(true);
                toSave.add(log);
            }
        }

        // Save first
        Setup.getRepository(HousemaidPayrollLogRepository.class).save(toSave);

        // Delete after save
        if (!toDelete.isEmpty()) {
            Setup.getRepository(HousemaidPayrollLogRepository.class).delete(toDelete);
        }

        // PAY-229
        LockDateService lockDateService = Setup.getApplicationContext()
                .getBean(LockDateService.class);

        PicklistItem salaryHeld =
                PicklistHelper.getItem(
                        PayrollManagementModule.PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PicklistItem.getCode("Previously Held Salary"));

        java.sql.Date payrollStart = lockDateService.getLockDate(accountantTodo.getPayrollMonth(), -1, PaymentRuleEmployeeType.HOUSEMAIDS);
        java.sql.Date payrollEnd = lockDateService.getLockDate(accountantTodo.getPayrollMonth(), 0, PaymentRuleEmployeeType.HOUSEMAIDS);

        List<Long> housemaidIds = Setup.getRepository(PayrollManagerNoteRepository.class)
                .getHeldSalaryHousemaids(payrollStart,
                        payrollEnd,
                        AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                        salaryHeld);
        List<HousemaidPayrollLog> oldExcludedMaidSalaries = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .getOldExcludedMaidSalaries(housemaidIds, HousemaidUnpaidStatus.ON_VACATION, accountantTodo.getPayrollMonth());

        for (HousemaidPayrollLog oldLog : oldExcludedMaidSalaries) {
            if (transferredImportantNotes.contains(oldLog.getHousemaid().getId())) {
                oldLog.setTransferred(true);
                oldLog.setPaidOnDate(DateUtil.formatFullDate(new Date()));
                oldLog.setPaidOnStatus((oldLog.getPaidOnStatus() != null ? oldLog.getPaidOnStatus() : "") +
                        " - paid by adding it as a manager note to "
                        + DateUtil.formatSimpleMonthYear(accountantTodo.getPayrollMonth())
                        + " salary"
                );
            }
        }
        Setup.getRepository(HousemaidPayrollLogRepository.class)
                .save(oldExcludedMaidSalaries);
        // End of PAY-229

        // Mark important notes as transferred
        if (!transferredImportantNotes.isEmpty()) {
            List<PicklistItem> importantNotesItems = HousemaidPayrollPaymentServiceV2.getMustBePaidManagerNotes();

            List<PayrollManagerNote> importantNotes = Setup.getRepository(PayrollManagerNoteRepository.class)
                    .findByNonPaidManagerNotes(payrollStart,
                            accountantTodo.getCreationDate(),
                            AbstractPayrollManagerNote.ManagerNoteType.ADDITION,
                            importantNotesItems,
                            transferredImportantNotes);

            for (PayrollManagerNote note : importantNotes) {
                note.setPaid(true);
                note.setPaidOnPayrollMonth(accountantTodo.getPayrollMonth());
            }

            Setup.getRepository(PayrollManagerNoteRepository.class).save(importantNotes);
        }
        // End of PAY-282 and PAY-344

        return true;
    }

    @Transactional
    public void deleteRemainingPendingLogsBT(Long payrollAccountantTodoId) {
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(payrollAccountantTodoId);

        if(accountantTodo != null) {
            //delete all remaining Pending logs

            List<HousemaidPayrollLog> pendingHousemaidLogs = Setup.getRepository(HousemaidPayrollLogRepository.class).findByMonthlyPaymentRuleAndLogStatus(accountantTodo.getMonthlyPaymentRule(), HousemaidPayrollLog.HousemaidPayrollLogStatus.PENDING);
            if(pendingHousemaidLogs != null && !pendingHousemaidLogs.isEmpty())
                Setup.getRepository(HousemaidPayrollLogRepository.class).delete(pendingHousemaidLogs);

            List<OfficeStaffPayrollLog> pendingStaffLogs = Setup.getRepository(OfficeStaffPayrollLogRepository.class).findByMonthlyPaymentRuleAndLogStatus(accountantTodo.getMonthlyPaymentRule(), OfficeStaffPayrollLog.OfficeStaffPayrollLogStatus.PENDING);
            if(pendingStaffLogs != null && !pendingStaffLogs.isEmpty())
                Setup.getRepository(OfficeStaffPayrollLogRepository.class).delete(pendingStaffLogs);

        }
    }

    @Transactional
    public Boolean checkProcessesCompletionBT(Long payrollAccountantTodoId, String accountantTodoTypeName, java.util.Date currentDate) {
        try {
            SelectQuery<BackgroundTask> query = new SelectQuery<>(BackgroundTask.class);

            query.filterBy("name", "=", "processOfficeStaff_" + payrollAccountantTodoId)
                    // Office Staff methods
                    .or("name", "LIKE", "processCurrentMonthTransfersAndPreviousUnpaidSalariesBatch_" + payrollAccountantTodoId + "_%")
                    .or("name", "LIKE","processExcludedLogsBatch_" + payrollAccountantTodoId + "_%")
                    .or("name", "LIKE","processRepaymentsBatch_" + payrollAccountantTodoId + "_%")
                    // Housemaid methods
                    .or("name", "=","processHousemaid_" + payrollAccountantTodoId)
                    .or("name", "LIKE","processCurrentMonthHousemaidsBatch_" + payrollAccountantTodoId + "_%")
                    .or("name", "=","deleteRemainingPendingLogsBT_" + payrollAccountantTodoId);

            query.filterBy("status", "<>", BackgroundTaskStatus.Finished);
            query.filterBy("creationDate", ">=", currentDate);

            List<BackgroundTask> activeTask = query.execute();

            if (activeTask != null && !activeTask .isEmpty()) {
                BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

                currentDate = new Date();

                BackgroundTask checkProcessesCompletionBGT = new BackgroundTask
                        .builder("checkProcessesCompletion_" + payrollAccountantTodoId,
                        Setup.getCurrentModule().getCode(),
                        "asyncService",
                        "checkProcessesCompletionBT")
                        .withRelatedEntity("PayrollAccountantTodo", payrollAccountantTodoId)
                        .withParameters(new Class[]{Long.class, String.class, java.util.Date.class},
                                payrollAccountantTodoId,
                                accountantTodoTypeName,
                                currentDate)
                        .withDelay(3 * 60 * 1000L) // 3 minutes delay
                        .build();
                backgroundTaskService.create(checkProcessesCompletionBGT);
                return true;
            }

            // All processes are complete, trigger post-transfer actions
            PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(payrollAccountantTodoId);
            PayrollAccountantTodoType accountantTodoType = PayrollAccountantTodoType.valueOf(accountantTodoTypeName);

            switch (accountantTodoType) {
                case WPS:
                    BackgroundTask backgroundTask = new BackgroundTask
                            .builder("addClientRefundsAccountingBGT_" + accountantTodo.getId(),
                            Setup.getCurrentModule().getCode(),
                            "asyncService",
                            "addClientRefundsAccountingBGT")
                            .withRelatedEntity("PayrollAccountantTodo", payrollAccountantTodoId)
                            .withParameters(new Class[]{Long.class}, accountantTodo.getId())
                            .build();
                    Setup.getApplicationContext().getBean(BackgroundTaskService.class).create(backgroundTask);

                    sendSalaryTransferNotifications(accountantTodo);
                    sendNoEIDFile(accountantTodo);
                    break;
                case LOCAL_TRANSFER:
                    sendSalaryTransferNotifications(accountantTodo);
                    break;
            }

            return true;
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Error in checkProcessesCompletionBT: " + ex.getMessage());
            return false;
        }
    }

    @Transactional
    public void addClientRefundsAccountingBGT(Long accountantTodoId) {
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(accountantTodoId);

        if (accountantTodo == null) {
            return;
        }
        MonthlyPaymentRule monthlyPaymentRule = accountantTodo.getMonthlyPaymentRule();
        if (monthlyPaymentRule == null || (monthlyPaymentRule.getSingleHousemaid() != null && monthlyPaymentRule.getSingleHousemaid())
                || (monthlyPaymentRule.getSingleOfficeStaff() != null && monthlyPaymentRule.getSingleOfficeStaff())) {
            return;
        }
        List<Long> housemaidExcludedId = monthlyPaymentRule.getExcludedDueToMedicalTest().stream().map(BaseEntity::getId).collect(Collectors.toList());
        if (housemaidExcludedId == null || housemaidExcludedId.isEmpty()) {
            return;
        }

        List<HousemaidPayrollLogProjection> logs = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findByHousemaidIds(housemaidExcludedId, accountantTodo.getPayrollMonth());
        if (logs == null || logs.isEmpty()) {
            return;
        }
        List<Map<String, Object>> parameterToBGT = HousemaidPayrollPaymentServiceV2.getPaymentsRelatedToMaid(monthlyPaymentRule, logs);

        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "firstSecondaryPayrollPaidAcc7120_" + new LocalDate().toString("yyyy-MM"), "clientRefundService",
                        Setup.getModule("accounting").getCode(),
                        "firstSecondaryPayrollPaidAcc7120",
                        accountantTodo.getEntityType(), accountantTodo.getId(), true,
                        false, new Class[]{List.class}, new Object[]{parameterToBGT});

    }

    public void sendSalaryTransferNotifications(PayrollAccountantTodo accountantTodo){

        // insert a new background task to send notifications
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "sendSalaryTransferNotificationsBT", "asyncService", "payroll",
                        "sendSalaryTransferNotificationsBT",
                        accountantTodo.getEntityType(), accountantTodo.getId(), true,
                        false, new Class[]{Long.class}, new Object[]{accountantTodo.getId()});
    }

    @Transactional
    public Boolean sendSalaryTransferNotificationsBT(Long payrollAccountantTodoId) {
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(payrollAccountantTodoId);
        SelectQuery<HousemaidPayrollLog> housemaidQuery = new SelectQuery<>(HousemaidPayrollLog.class);
        housemaidQuery.filterBy("payrollAccountantTodo.id", "=", accountantTodo.getId());
        housemaidQuery.filterBy("transferred", "=", true);
        List<HousemaidPayrollLog> housemaidLogs = housemaidQuery.execute();

        int chunkSize = 100;
        try {
            chunkSize = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_CHUNK_SIZE_FOR_BGT));
        }catch (Exception ignored){
        }

        BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

        // *********** send notifications **********//
        // 1- first send for received maids
        if (housemaidLogs != null && !housemaidLogs.isEmpty()){
            List<String> housemaidIds = housemaidLogs.stream().map(housemaidPayrollLog -> housemaidPayrollLog.getHousemaid().getId().toString()).collect(Collectors.toList());
            List<List<String>> batches = ListHelper.divideList(housemaidIds, chunkSize);
            int i = 0;
            for (List<String> batch: batches) {
                BackgroundTask task = new BackgroundTask
                        .builder("notifyClientAboutMaidSalary batch #" + i, Setup.getCurrentModule().getCode(), "messagingService", "notifyClientAboutMaidSalary")
                        .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                        .withParameters(new Class<?>[]{List.class, boolean.class, Date.class}, batch, true, null)
                        .build();

                backgroundTaskService.create(task);
                i++;
            }
        }

//        for (HousemaidPayrollLog log : housemaidLogs) {
//            Housemaid housemaid = log.getHousemaid();
//            Client client = housemaid.getCurrentClient();
//            if (housemaid.getNotifySalaryRelease() && client != null) {
//                Setup.getApplicationContext().getBean(MessagingService.class).notifyClientAboutMaidSalary(client, housemaid, true, null);
//            }
//        }

        // 2- Second: send for those who didn't receive their salaries
        List<Housemaid> onHoldHousemaids = Setup.getApplicationContext().getBean(HousemaidPayrollPaymentServiceV2.class).getOnHoldMaids(accountantTodo);
        if (onHoldHousemaids != null && !onHoldHousemaids.isEmpty()){
            List<String> housemaidIds = onHoldHousemaids.stream().map(housemaid -> housemaid.getId().toString()).collect(Collectors.toList());
            List<List<String>> batches = ListHelper.divideList(housemaidIds, chunkSize);
            int i = 0;
            Date receiveDate = DateUtil.getNextPaymentDate();
            for (List<String> batch: batches) {
                BackgroundTask task = new BackgroundTask
                        .builder("notifyClientAboutMaidSalary (on hold) batch #" + i, Setup.getCurrentModule().getCode(), "messagingService", "notifyClientAboutMaidSalary")
                        .withQueue(BackgroundTaskQueues.HeavyOperationsQueue)
                        .withParameters(new Class<?>[]{List.class, boolean.class, Date.class}, batch, false, receiveDate)
                        .build();

                backgroundTaskService.create(task);
                i++;
            }
        }
//        for (Housemaid housemaid : onHoldHousemaids) {
//            Client client = housemaid.getCurrentClient();
//            if (housemaid.getNotifySalaryRelease() && client != null) {
//                Setup.getApplicationContext().getBean(MessagingService.class).notifyClientAboutMaidSalary(client, housemaid, false, DateUtil.getNextPaymentDate());
//            }
//        }

        return true;
    }


    public void sendNoEIDFile(PayrollAccountantTodo accountantTodo) {
        // insert a new background task to send No Eid file
        Setup.getApplicationContext()
                .getBean(BackgroundTaskService.class)
                .addDirectCallBackgroundTaskForEntity(
                        "sendNoEIDFileBT", "asyncService", "payroll",
                        "sendNoEIDFileBT",
                        accountantTodo.getEntityType(), accountantTodo.getId(), true,
                        false, new Class[]{Long.class}, new Object[]{accountantTodo.getId()});
    }

    @Transactional
    public Boolean sendNoEIDFileBT (Long payrollAccountantTodoId){
        PayrollAccountantTodo accountantTodo = Setup.getRepository(PayrollAccountantTodoRepository.class).findOne(payrollAccountantTodoId);
        HousemaidPayrollLogRepository housemaidPayrollLogRepository = Setup.getRepository(HousemaidPayrollLogRepository.class);
        OfficeStaffPayrollLogRepository officeStaffPayrollLogRepository = Setup.getRepository(OfficeStaffPayrollLogRepository.class);

        try {
            Attachment noEidFile = Setup.getApplicationContext().getBean(TransferFilesService.class).
                    generateNoEIDFile(accountantTodo.getMonthlyPaymentRule(), housemaidPayrollLogRepository.findByPayrollAccountantTodoAndTransferredTrue(accountantTodo),
                            officeStaffPayrollLogRepository.findByPayrollAccountantTodoAndTransferredTrue(accountantTodo));

            Map<String, String> paramValues = new HashMap<>();
            paramValues.put("payment_date", DateUtil.formatMonthDayYear(accountantTodo.getPaymentDate()));
            if (noEidFile != null) {
                Setup.getApplicationContext().getBean(EmailTemplateService.class)
                        .sendEmail("Claiming_Salaries_Email_Template", paramValues, Arrays.asList(noEidFile));
            }
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Exception While sendNoEIDFileBT: " + ex.getMessage());
            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while creating eid file", false);
            return false;
        }
        return true;
    }

    //jira PAY-512 Payroll - Stop sending the payroll audit report
//    @Async
//    public void generateHousemaidAuditReport(PayrollAuditTodo payrollAuditTodo, List<Housemaid> targetList) {
//        if (HOUSEMAID_REPORT_IS_BEING_GENERATED) return;
//
//        try {
//            HOUSEMAID_REPORT_IS_BEING_GENERATED = true;
//            PayrollAuditTodoRepository payrollAuditTodoRepository = Setup.getRepository(PayrollAuditTodoRepository.class);
//            HousemaidsSalaryAuditReport auditReport = new HousemaidsSalaryAuditReport();
//            auditReport.setHousemaids(targetList);
//            auditReport.setPayrollAuditTodo(payrollAuditTodo);
//            String fileName = "Housemaids Salary Audit Report.pdf";
//            File file = auditReport.exportPdf(fileName);
//
//            InputStream auditReportStream = new FileInputStream(file);
//            Attachment auditReportAttachment = Storage.storeTemporary(fileName, auditReportStream, "audit_report", Boolean.TRUE);
//
//            payrollAuditTodo = payrollAuditTodoRepository.findOne(payrollAuditTodo.getId());
//            payrollAuditTodo.addAttachment(auditReportAttachment);
//            payrollAuditTodo = payrollAuditTodoRepository.save(payrollAuditTodo);
//
////            String recipientsParam = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS);
////            String subjectParam = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT);
////            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(recipientsParam);
////            TextEmail email = new TextEmail(subjectParam, "");
////            email.addAttachement(auditReportAttachment);
////            Setup.getMailService().sendEmail(recipients, email, false, MessageTemplateService.getMaidsCcSenderName(),null);
//        } catch (Exception ex) {
//            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating housemaids report", false);
//        } finally {
//            HOUSEMAID_REPORT_IS_BEING_GENERATED = false;
//        }
//    }

    //jira PAY-512 Payroll - Stop sending the payroll audit report
//    @Async
//    public void generateOfficeStaffAuditReport(PayrollAuditTodo payrollAuditTodo) {
//        if (OFFICESTAFF_REPORT_IS_BEING_GENERATED) return;
//
//        OfficeStaffPayrollAuditService officeStaffPayrollAuditService = Setup.getApplicationContext().getBean(OfficeStaffPayrollAuditService.class);
//        try {
//            OFFICESTAFF_REPORT_IS_BEING_GENERATED = true;
//            PayrollAuditTodoRepository payrollAuditTodoRepository = Setup.getRepository(PayrollAuditTodoRepository.class);
//            OfficeStaffSalaryAuditReport auditReport = new OfficeStaffSalaryAuditReport();
//            auditReport.setOfficeStaffs(officeStaffPayrollAuditService.getTargetList(payrollAuditTodo.getMonthlyPaymentRule(), PaymentRuleEmployeeType.valueOf(payrollAuditTodo.getTaskName())));
//            auditReport.setTerminatedStaff(officeStaffPayrollAuditService.getTerminatedList(payrollAuditTodo.getMonthlyPaymentRule(), PaymentRuleEmployeeType.valueOf(payrollAuditTodo.getTaskName())));
//            auditReport.setPayrollAuditTodo(payrollAuditTodo);
//            String fileName = "Office Staff Salary Audit Report.pdf";
//            File file = auditReport.exportPdf(fileName);
//
//            InputStream auditReportStream = new FileInputStream(file);
//            Attachment auditReportAttachment = Storage.storeTemporary(fileName, auditReportStream, "audit_report", Boolean.TRUE);
//
//            payrollAuditTodo = payrollAuditTodoRepository.findOne(payrollAuditTodo.getId());
//            payrollAuditTodo.addAttachment(auditReportAttachment);
//            payrollAuditTodo = payrollAuditTodoRepository.save(payrollAuditTodo);
//
////            String recipientsParam = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_RECIPIENTS);
////            String subjectParam = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_PAYROLL_AUDIT_REPORT_EMAIL_SUBJECT);
////            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(recipientsParam);
////            TextEmail email = new TextEmail(subjectParam, "");
////            email.addAttachement(auditReportAttachment);
////            Setup.getMailService().sendEmail(recipients, email, false, MessageTemplateService.getMaidsCcSenderName(), null);
//        } catch (Exception ex) {
//            DebugHelper.sendExceptionMail("<EMAIL>", ex, "Error while generating office staffs report", false);
//        } finally {
//            OFFICESTAFF_REPORT_IS_BEING_GENERATED = false;
//        }
//    }

    @Transactional
    public void sendBankTransferReport() {
        InputStream is = null;
        BaseControllerHelper baseControllerHelper = Setup.getApplicationContext().getBean(BaseControllerHelper.class);

        List<PayrollAccountantTodo> bankTransferTodos = Setup.getRepository(PayrollAccountantTodoRepository.class).findByTaskNameAndCompletedTrueAndBankTransferReportIsSentFalse(PayrollAccountantTodoType.BANK_TRANSFER.toString());
        if (bankTransferTodos.size() > 0) {
            try {
                List<BankTransferProjection> bankTransferResult = baseControllerHelper.project(Setup.getRepository(OfficeStaffPayrollLogRepository.class).findByPayrollAccountantTodo_IdInAndTransferredTrue(bankTransferTodos.stream().map(BaseEntity::getId).collect(Collectors.toList())), BankTransferProjection.class);
                if (bankTransferResult == null || bankTransferResult.size() == 0) return;

                java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
                java.sql.Date lastWeek = DateUtil.addDaysSql(currentDate, -7);
                String title = String.format("Bank Transfers from %s to %s", DateUtil.formatDateDashedV2(lastWeek), DateUtil.formatDateDashedV2(currentDate));

                String[] headers = new String[]{"employeeName", "beneficiaryName", "currency", "amount", "iban", "accountNumber", "bankName", "swiftCode"};
                BankTransferProjection.Counter.resetCounter();
                is = baseControllerHelper.generateCsv(bankTransferResult, BankTransferProjection.class, headers);

                Attachment attachment = Storage.storeTemporary(title + ".csv", is, "BankTransfersReportFile", false);
//                TemplateEmail templateEmail = new TemplateEmail(title, "Payroll_Bank_Transfer_Report", null);
//                templateEmail.addAttachement(attachment);

                List<EmailRecipient> recipients = Recipient.parseEmailsString(
                        Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_BANK_TRANSFER_REPORT_RECEIVER_EMAIL)
                );

                Setup.getApplicationContext().getBean(MessagingService.class)
                    .send(recipients, null,"Payroll_Bank_Transfer_Report",title,null, Collections.singletonList(attachment), null);
//            Setup.getMailService().sendEmail(new MailObject.builder(templateEmail, EmailReceiverType.Office_Staff)
//                    .recipients(recipients)
    //                    .html()
    //                    .senderName(MessageTemplateService.getMaidsCcSenderName())
    //                    .secure()
    //                    .build());

                for (PayrollAccountantTodo bankTodo : bankTransferTodos)
                    bankTodo.setBankTransferReportIsSent(true);
                Setup.getRepository(PayrollAccountantTodoRepository.class).save(bankTransferTodos);

            } catch (Exception e) {
                DebugHelper.sendExceptionMail("<EMAIL>", e, "Error while generating bank transfers report", false);
            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            }
        }
    }
}
