package com.magnamedia.service.message;


import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.core.type.SmsReceiverType;
import com.magnamedia.core.type.template.NotificationTarget;
import com.magnamedia.entity.*;

import javax.validation.constraints.NotNull;

import java.util.*;

import static com.magnamedia.controller.PaySlipsController.normalizePhoneNumber;

public class MessageTarget implements NotificationTarget {
    private Long id;
    private String entityType;
    private String mobileNumber;
    private SmsReceiverType smsReceiverType;
    private String whatsappNumber;

    private Boolean primaryTarget = Boolean.TRUE;

    private Long ownerId;

    private String ownerType;

    private String email;

    private boolean secure;

    private EmailRecipient emailRecipient;

    private List<EmailRecipient> cc;

    private List<EmailRecipient> bcc;

    private List<Attachment> emailAttachments;

    private String emailSenderName;

    private String emailSubject;

    private boolean appendTrailingSentence = true;

    private List<EmailRecipient> emailRecipients;

    private String notificationTitle;


    private Map<String, String> additionalInfo;

    public MessageTarget() {
    }

    private void fillMessageTarget(Housemaid housemaid) {
        this.id = housemaid.getId();
        this.entityType = housemaid.getEntityType();
        this.mobileNumber = housemaid.getNormalizedPhoneNumber();
        this.smsReceiverType = SmsReceiverType.Housemaid;
        this.whatsappNumber = housemaid.getNormalizedWhatsAppPhoneNumber();
    }
    private void fillMessageTarget(Client client) {
        this.id = client.getId();
        this.entityType = client.getEntityType();
        this.mobileNumber = client.getNormalizedMobileNumber();
        this.smsReceiverType = SmsReceiverType.Client;
        this.whatsappNumber = client.getNormalizedWhatsappNumber();
        this.email = client.getEmail();
    }
    private void fillMessageTarget(User user) {
        this.id = user.getId();
        this.entityType = user.getEntityType();
        this.mobileNumber = user.getMobileNumber();
        this.smsReceiverType = SmsReceiverType.Office_Staff;
        this.whatsappNumber = user.getWhatsappNumber();
        this.email = user.getEmail();
    }

    private void fillMessageTarget(Client client, Boolean forSpouse) {
        if (forSpouse == null || !forSpouse){
            fillMessageTarget(client);
        }else {
            primaryTarget = false;
            this.id = client.getId();
            this.entityType = client.getEntityType();
            this.mobileNumber = client.getNormalizedSpouseMobileNumber();
            this.smsReceiverType = SmsReceiverType.Spouse;
            this.whatsappNumber = client.getNormalizedSpouseWhatsappNumber();
        }

    }
    private void fillMessageTarget(Contract contract) {
        this.id = contract.getId();
        this.entityType = contract.getEntityType();
        this.mobileNumber = contract.getHousemaid() != null ? contract.getHousemaid().getNormalizedPhoneNumber(): "";
        this.smsReceiverType = SmsReceiverType.Housemaid;
        this.whatsappNumber = contract.getHousemaid() != null ? contract.getHousemaid().getNormalizedWhatsAppPhoneNumber(): "";
    }

    private void fillMessageTarget(OfficeStaff officeStaff) {
        this.id = officeStaff.getId();
        this.entityType = officeStaff.getEntityType();
        this.mobileNumber = normalizePhoneNumber(officeStaff.getPhoneNumber());
        this.smsReceiverType = SmsReceiverType.Office_Staff;
        this.whatsappNumber = normalizePhoneNumber(officeStaff.getPhoneNumber());
        this.email = officeStaff.getEmail();
    }

    private void fillMessageTarget(FreedomOperator freedomOperator) {
        this.id = freedomOperator.getId();
        this.entityType = freedomOperator.getEntityType();
        this.mobileNumber = normalizePhoneNumber(freedomOperator.getPhoneNumber());
        this.smsReceiverType = SmsReceiverType.Operator;
        this.whatsappNumber = normalizePhoneNumber(freedomOperator.getPhoneNumber());
        this.email = freedomOperator.getEmail();
    }

    public void fillAdditionalInfo(Housemaid housemaid) {
        if (housemaid != null) {
            Map<String, String> maidInfo = new HashMap<>();
            maidInfo.put("maidId", housemaid.getId().toString());
            this.setAdditionalInfo(maidInfo);
        }
    }

    public MessageTarget(Object context) {
        this();
        if (context instanceof Housemaid)
            this.fillAdditionalInfo((Housemaid) context);
        else if (context instanceof Contract)
            this.fillAdditionalInfo(((Contract) context).getHousemaid());
    }

    public MessageTarget(BaseEntity recipient, BaseEntity owner) {
        this.secure = false;
        if (owner == null){
            owner = recipient;
        }
        this.ownerId = owner.getId();
        this.ownerType = owner.getEntityType();
        if (recipient instanceof Housemaid) {
            fillMessageTarget((Housemaid) recipient);
        }
        if (recipient instanceof Client) {
            fillMessageTarget((Client) recipient, false);
        }
        if (recipient instanceof Contract) {
            fillMessageTarget((Contract) recipient);
        }
        if (recipient instanceof OfficeStaff) {
            fillMessageTarget((OfficeStaff) recipient);
        }
        if (recipient instanceof FreedomOperator) {
            fillMessageTarget((FreedomOperator) recipient);
        }
        if (recipient instanceof User) {
            fillMessageTarget((User) recipient);
        }
    }

    public MessageTarget(BaseEntity recipient, BaseEntity owner, Object context) {
        this(recipient, owner);

        if (recipient instanceof Housemaid)
            this.fillAdditionalInfo((Housemaid) recipient);
        else if (owner instanceof Housemaid)
            this.fillAdditionalInfo((Housemaid) owner);
        else if (context instanceof Housemaid)
            this.fillAdditionalInfo((Housemaid) context);
        else if (context instanceof Contract)
            this.fillAdditionalInfo(((Contract) context).getHousemaid());
    }

    public MessageTarget(EmailRecipient emailRecipient){
        this.emailRecipient = emailRecipient;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public String getEntityType() {
        return entityType;
    }

    @Override
    public String getReceiverName() {
        return null;
    }

    @Override
    public String getMobileNumber() {
        return mobileNumber;
    }

    @Override
    public SmsReceiverType getSmsReceiverType() {
        return smsReceiverType;
    }

    @Override
    public String getWhatsappNumber() {
        return whatsappNumber;
    }

    public Boolean getPrimaryTarget() {
        return primaryTarget;
    }

    public void setPrimaryTarget(Boolean primaryTarget) {
        this.primaryTarget = primaryTarget;
    }

    @Override
    public Long getOwnerId() {
        return ownerId;
    }

    public String getOwnerType() {
        return ownerType;
    }

    @Override
    public boolean isSecure() {
        return secure;
    }



    @Override
    public List<String> getTargetNotLoggedinToAppSecondaryNumbers() {
        return new ArrayList<>();
    }

    @Override
    public List<Tag> getTags() {
        return new ArrayList<>();
    }

    public void setSecure(boolean secure) {
        this.secure = secure;
    }

    @Override
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public EmailRecipient getEmailRecipient() {
        return emailRecipient;
    }

    @Override
    public Properties getMailProperties() {
        return null;
    }

    public void setEmailRecipient(EmailRecipient emailRecipient) {
        this.emailRecipient = emailRecipient;
    }

    @Override
    public String getEmailSenderName() {
        return emailSenderName;
    }

    public void setEmailSenderName(String emailSenderName) {
        this.emailSenderName = emailSenderName;
    }

    @Override
    public List<EmailRecipient> getCc() {
        if (cc == null){
            return new ArrayList<>();
        }
        return cc;
    }

    public void setCc(List<EmailRecipient> cc) {
        this.cc = cc;
    }

    @Override
    public List<EmailRecipient> getBcc() {
        if (bcc == null){
            return new ArrayList<>();
        }
        return bcc;
    }

    public void setBcc(List<EmailRecipient> bcc) {
        this.bcc = bcc;
    }

    @Override
    public List<Attachment> getEmailAttachments() {
        if (emailAttachments == null){
            return new ArrayList<>();
        }
        return emailAttachments;
    }

    @Override
    public boolean isEmailHml() {
        return true;
    }

    @Override
    public String getSmsReceiverName() {
        return null;
    }

    @Override
    public boolean isIgnoreOnFailAction() {
        return false;
    }

    public void setEmailAttachments(List<Attachment> emailAttachments) {
        this.emailAttachments = emailAttachments;
    }

    @Override
    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public boolean isAppendTrailingSentence() {
        return appendTrailingSentence;
    }

    public void setAppendTrailingSentence(boolean appendTrailingSentence) {
        this.appendTrailingSentence = appendTrailingSentence;
    }

    @Override
    public List<EmailRecipient> getEmailRecipients() {
        if (emailRecipients == null){
            return new ArrayList<>();
        }
        return emailRecipients;
    }

    public void setEmailRecipients(List<EmailRecipient> emailRecipients) {
        this.emailRecipients = emailRecipients;
    }

    @Override
    public String getOwnerEntityType(){
        return this.ownerType;
    }

    @Override
    public String getNotificationTitle() {
        return notificationTitle;
    }

    public void setNotificationTitle(String notificationTitle) {
        this.notificationTitle = notificationTitle;
    }

    @Override
    public Map<String, String> getAdditionalInfo() {
        if (additionalInfo == null)
            return new HashMap<>();
        return additionalInfo;
    }

    public void setAdditionalInfo(Map<String, String> additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}
