package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.TemplateParameterType;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Component("ccAppContentHelper")
public class CCAppContentHelper {

    @Autowired
    private ReplacementRepository replacementRepository ;

    @Autowired
    private PayrollAuditTodoRepository payrollAuditTodoRepository;

    @Autowired
    private PaymentRepository paymentRepository ;

    public  String formatDate(Date date){
        if(date==null)
            return "";
        DateFormat dateFormat = new SimpleDateFormat(
                "dd MMM yyyy");
        return  dateFormat.format(date);
    }

    public  String getThisMonth(){
        DateFormat dateFormat = new SimpleDateFormat(
                "MMM yyyy");
        return  dateFormat.format(new Date());
    }

    public  String getNextMonth(){
        DateFormat dateFormat = new SimpleDateFormat(
                "MMM yyyy");
        return  dateFormat.format(DateUtil.addMonths(new Date() , 1));
    }

    public String getCurrentMonthName() {
       return DateUtil.formatMonth(new Date());
    }

    public String getNextMonthName() {
        return DateUtil.formatMonth(DateUtil.addMonths(new Date() , 1));
    }

    public String getHouseMaidTeamNumber(){
        return "00971506715943";
    }



    public String formatDateWithDayOfWeek(Date date)
    {
        if(date==null){
            return  "";
        }
        DateFormat dateFormat = new SimpleDateFormat(
                "EEEE ,MMM dd, yyyy");
        return dateFormat.format(date);
    }

    public String formatTomorrowDateWithDayOfWeekWithoutYear()
    {
        DateFormat dateFormat = new SimpleDateFormat(
                "MMM dd");
        return dateFormat.format(DateUtil.addDays(new Date() , 1));
    }

    public String formatDateWithDayOfWeekWithoutYear(Date date)
    {
        if(date==null){
            return  "";
        }
        DateFormat dateFormat = new SimpleDateFormat(
                "EEEE ,MMM dd");
        return dateFormat.format(date);
    }

    public  String formatTime(Date date){
        if(date==null)
            return "";
        SimpleDateFormat dateFormat = new SimpleDateFormat(
                "EEEE, MMM dd 'at' hh:mm aaa");
        return  dateFormat.format(date);
    }

    //
    public  String getReleasePrimarySalaryDay() {
        LocalDate payrollMonth = LocalDate.now();

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .minusMonths(1).withDayOfMonth(1).toDate().getTime()));

        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY) {
                if (!new java.sql.Date(payrollMonth.toDate().getTime()).after(rule.getPaymentDate())) {
                    int day = new LocalDate(rule.getPaymentDate()).getDayOfMonth() + 2;
                    if (day == 1) return "1st";
                    if (day == 2) return "2nd";
                    if (day == 3) return "3rd";
                    return day + "th";
                }
            }
        }

        rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .withDayOfMonth(1).toDate().getTime()));


        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY) {
                int day = new LocalDate(rule.getPaymentDate()).getDayOfMonth() + 2;
                if (day == 1) return "1st";
                if (day == 2) return "2nd";
                if (day == 3) return "3rd";
                return day + "th";
            }
        }

        return "5th";
    }

    public  String getReleasePrimarySalaryDayPlusOne() {
        LocalDate payrollMonth = LocalDate.now();

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .minusMonths(1).withDayOfMonth(1).toDate().getTime()));

        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY) {
                if (!new java.sql.Date(payrollMonth.toDate().getTime()).after(rule.getPaymentDate())) {
                    int day = new LocalDate(rule.getPaymentDate()).plusDays(1).getDayOfMonth();
                    if (day == 1) return "1st";
                    if (day == 2) return "2nd";
                    if (day == 3) return "3rd";
                    return day + "th";
                }
            }
        }

        rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .withDayOfMonth(1).toDate().getTime()));


        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY) {
                int day = new LocalDate(rule.getPaymentDate()).plusDays(1).getDayOfMonth();
                if (day == 1) return "1st";
                if (day == 2) return "2nd";
                if (day == 3) return "3rd";
                return day + "th";
            }
        }

        return "4th";
    }

    //
    public  String getReleasePrimarySalaryDate() {
        LocalDate payrollMonth = LocalDate.now();

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .minusMonths(1).withDayOfMonth(1).toDate().getTime()));

        DateFormat dateFormat = new SimpleDateFormat(
                "EEEE, MMM dd");

        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY) {
                if (!new java.sql.Date(payrollMonth.toDate().getTime()).after(rule.getPaymentDate())) {
                    // payment date + 2 days
                    return dateFormat.format(new Date(rule.getPaymentDate().getTime() + (2 * 24 * 60 * 60 * 1000)));

                }
            }
        }

        // after 21 of this month
        rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .withDayOfMonth(1).toDate().getTime()));

        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY)
                // payment date + 2 days
                return dateFormat.format(new Date(rule.getPaymentDate().getTime() + (2 * 24 * 60 * 60 * 1000)));
        }


        if (payrollMonth.getDayOfMonth() <= 3) {
            return dateFormat.format(LocalDate.now()
                    .withDayOfMonth(5).toDate());
        } else {
            return dateFormat.format(LocalDate.now()
                    .plusMonths(1).withDayOfMonth(5).toDate());
        }

    }

    public  String getNextReleasePrimarySalaryDate() {
        LocalDate payrollMonth = LocalDate.now();

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .withDayOfMonth(1).toDate().getTime()));

        DateFormat dateFormat = new SimpleDateFormat(
                "EEEE, MMMM dd");

        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY)
                // payment date + 2 days
                return dateFormat.format(new Date(rule.getPaymentDate().getTime() + (2 * 24 * 60 * 60 * 1000)));
        }


        if (payrollMonth.getDayOfMonth() <= 3) {
            return dateFormat.format(LocalDate.now()
                    .withDayOfMonth(5).toDate());
        } else {
            return dateFormat.format(LocalDate.now()
                    .plusMonths(1).withDayOfMonth(5).toDate());
        }

    }

    //
    public String onHoldStatus(Long housemaidId) {

        LocalDate payrollMonth = LocalDate.now();
        if(payrollMonth.getDayOfMonth() < 3) {
            return "";
        }

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                .findByPayrollMonth(new java.sql.Date(payrollMonth
                        .minusMonths(1).withDayOfMonth(1).toDate().getTime()));

        LocalDate paymentDate = null;
        for (MonthlyPaymentRule rule : rules) {
            if (rule.isTargetingHousemaid() && rule.getPayrollType() == PayrollType.PRIMARY) {
                paymentDate = new LocalDate(rule.getPaymentDate());
            }
        }

        if(paymentDate == null) paymentDate = payrollMonth.withDayOfMonth(3);

        boolean wasOnVacation = hasThisStatusOnPrimaryPaymentDate(housemaidId, HousemaidStatus.ON_VACATION, paymentDate);

        boolean wasNoShow = hasThisStatusOnPrimaryPaymentDate(housemaidId, HousemaidStatus.NO_SHOW, paymentDate);


        if(wasNoShow) return "absent";
        if(wasOnVacation) return "on vacation";
        return "";
    }


    //
    public String nextPaymentDay() {
        LocalDate payrollMonth = LocalDate.now();

        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class)
                    .findByPayrollMonth(new java.sql.Date(payrollMonth
                            .minusMonths(1).withDayOfMonth(1).toDate().getTime()));

        for(MonthlyPaymentRule rule: rules) {
            if(rule.isTargetingHousemaid() && new LocalDate(rule.getPaymentDate()).getDayOfMonth() >= payrollMonth.getDayOfMonth()) {
                int day = new LocalDate(rule.getPaymentDate()).getDayOfMonth();
                if(day == 1) return "1st";
                if(day == 2) return "2nd";
                if(day == 3) return "3rd";
                return day + "th";
            }
        }

        return "";
    }


    //
    public String getHousemaidTotalSalaryPlusLoan(Long housemaidId)
    {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);

        LocalDate payrollMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
        HousemaidPayrollLogRepository repository = Setup.getRepository(HousemaidPayrollLogRepository.class);

        HousemaidPayrollLog lastMonthLog = repository.findFirstByPayrollMonthAndHousemaid(new java.sql.Date(payrollMonth.toDate().getTime()), housemaid);
        if(lastMonthLog==null)
            return  "AED 0";
        return "AED " + NumberFormatter.formatNumber((lastMonthLog.getTotalSalary() + lastMonthLog.getLoanRepayment()));
    }

    //
    public String getHousemaidTotalSalary(Long housemaidId)
    {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);

        LocalDate payrollMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
        HousemaidPayrollLogRepository repository = Setup.getRepository(HousemaidPayrollLogRepository.class);

        HousemaidPayrollLog lastMonthLog = repository.findFirstByPayrollMonthAndHousemaid(new java.sql.Date(payrollMonth.toDate().getTime()), housemaid);

        if(lastMonthLog==null)
            return  "AED 0";
        return  "AED " + NumberFormatter.formatNumber(lastMonthLog.getTotalSalary());
    }

    //
    public String getHousemaidLoan(Long housemaidId)
    {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);

        LocalDate payrollMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
        HousemaidPayrollLogRepository repository = Setup.getRepository(HousemaidPayrollLogRepository.class);

        HousemaidPayrollLog lastMonthLog = repository.findFirstByPayrollMonthAndHousemaid(new java.sql.Date(payrollMonth.toDate().getTime()), housemaid);

        if(lastMonthLog==null)
            return  "AED 0";
        return "AED " + NumberFormatter.formatNumber(lastMonthLog.getLoanRepayment());
    }

    //
    public String getHousemaidTotalSalaryPlusStartDateDeduction(Long housemaidId)
    {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);

        LocalDate payrollMonth = LocalDate.now().minusMonths(1).withDayOfMonth(1);
        HousemaidPayrollLogRepository repository = Setup.getRepository(HousemaidPayrollLogRepository.class);

        HousemaidPayrollLog lastMonthLog = repository.findFirstByPayrollMonthAndHousemaid(new java.sql.Date(payrollMonth.toDate().getTime()), housemaid);

        if(lastMonthLog==null)
            return  "AED 0";
        return "AED " + NumberFormatter.formatNumber(housemaid.getBasicSalary());
    }

    public boolean hasThisStatusOnPrimaryPaymentDate(Long housemaidId, HousemaidStatus status, LocalDate paymentDate) {
        HistorySelectQuery<Housemaid> historySelectQuery = new HistorySelectQuery(Housemaid.class);
        historySelectQuery.filterBy("id", "=", housemaidId);
        historySelectQuery.filterByChanged("status");
        historySelectQuery.sortBy("lastModificationDate", false);

        List<Housemaid> housemaids = historySelectQuery.execute();


        for(Housemaid housemaid: housemaids) {

            LocalDate currentDate = new LocalDate(new java.sql.Date(housemaid.getLastModificationDate().getTime()));

            if(housemaid.getStatus() == status && !currentDate.isAfter(paymentDate)) return true;

            if(currentDate.isBefore(paymentDate)) break;

        }
        return false;
    }

    public String getFirstName(Long housemaidId) {

        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);
        if (housemaid != null && housemaid.getFirstName()!= null && housemaid.getFirstName().toLowerCase().contains("unknown")){
            return "your maid";
        }
        if (housemaid != null)
            return housemaid.getFormattedFirstName();
        return "";
    }

    public String getSpecialFirstName(Long housemaidId) {

        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);
        if (housemaid != null && housemaid.getFirstName()!= null && housemaid.getFirstName().toLowerCase().contains("unknown")){
            return "my maid";
        }
        if (housemaid != null)
            return housemaid.getFormattedFirstName();
        return "";
    }

    public String getAnsariLocation() {

        String ansariLocation = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ANSARI_LOCATION);
        return ansariLocation != null ? ansariLocation : "";
    }

    public String getMushrifMallAuhLocation() {

        String mushrifMallAuhLocation = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_MUSHRIF_MALL_AUH_LOCATION);
        return mushrifMallAuhLocation != null ? mushrifMallAuhLocation : "";
    }

    public String getAnsariOrMushrifMallAuhLocation(Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);

        if (housemaid != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())) {
            Client client = housemaid.getCurrentClient();
            if (client != null) {
                if ((client.getCity() != null
                        && client.getCity().getCode().equals("abu_dhabi"))
                        || (client.getCity() == null
                        && (client.getFullAddress().toLowerCase().contains("abudhabi")
                        || client.getFullAddress().toLowerCase().contains("abu dhabi")))) {
                    return getMushrifMallAuhLocation();
                }
            }
        }
        return getAnsariLocation();
    }

    public String getAnsariOrMushrifMallAuhName(Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);

        if (housemaid != null && HousemaidType.MAID_VISA.equals(housemaid.getHousemaidType())) {
            Client client = housemaid.getCurrentClient();
            if (client != null) {
                if ((client.getCity() != null
                        && client.getCity().getCode().equals("abu_dhabi"))
                        || (client.getCity() == null
                        && (client.getFullAddress().toLowerCase().contains("abudhabi")
                        || client.getFullAddress().toLowerCase().contains("abu dhabi")))) {
                    return "Al Ansari Exchange Branch in Mushrif Mall Auh";
                }
            }
        }
        return "Al-Ansari Lulu branch";
    }

    public String getAnsariBranch() {

        String ansariBranch = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_ANSARI_BRANCH);
        return ansariBranch != null ? ansariBranch : "";
    }

    public String getMolNumber(Long housemaidId) {
        String molNumber = "";
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class)
                .findOne(housemaidId);
        if (housemaid != null) {
            NewRequest newRequest = Setup.getRepository(NewVisaRequestRepository.class)
                    .findFirstByHousemaidOrderByCreationDateDesc(housemaid);

            if (newRequest != null) {
                molNumber = newRequest.getEmployeeUniqueId();
            }
        }

        return molNumber != null ? molNumber : "";
    }

    public String getSentenceMaidInAbuDhabi(Long contractId) {
        if (contractId == null) {
            return "";
        }
        Contract contract = Setup.getRepository(ContractRepository.class)
                .findOne(contractId);
        if (contract == null) {
            return "";
        }

        Housemaid housemaid = contract.getHousemaid();
        if (housemaid == null) {
            return "";
        }
        Replacement replacement = replacementRepository
                .findTopByContractAndNewHousemaidOrderByCreationDateDesc(contract, housemaid);
        Date fromDate = replacement != null ? replacement.getCreationDate() : contract.getStartOfContract();
        if (fromDate == null || (checkIfClientInAbuDhabi(contract) && DateUtil.getDaysBetween(fromDate, new Date()) + 1 < 14))
            return "";
        else
            return "If the branch above is too far from your home, you can <a href=\"route://ChangeAnsariBranch\" style=\"text-decoration: none;color:#4267b2;\"> ask to send it to another branch.</a>";
    }

    public boolean checkIfClientInAbuDhabi(Contract contract) {
        if (contract.getClient() != null) {
            Client client = contract.getClient();
            if ((client.getCity() != null && client.getCity().getCode().equals("abu_dhabi")) ||
                    (client.getFullAddress() != null &&
                            (client.getFullAddress().toLowerCase().contains("abudhabi") ||
                                    client.getFullAddress().toLowerCase().contains("abu dhabi")))) {
                return true;
            }
        }
        return false;
    }

    public String getGeneralParameterValue(Long contractId, String parameterType){
        try {
            Contract contract = Setup.getRepository(ContractRepository.class)
                    .findOne(contractId);
            Date now = new Date();
            int month = now.getMonth();
            if (TemplateParameterType.MAID_NAME.getLabel().equals(parameterType)) {
                if (contract.getHousemaid() == null || contract.getHousemaid().getName() == null ||
                        contract.getHousemaid().getName().isEmpty() || contract.getHousemaid().getName().toLowerCase().contains("unknown")) {
                    return "your maid";
                }
                return contract.getHousemaid().getFormattedFirstName();
            }

            if (TemplateParameterType.THIS_MONTH.getLabel().equals(parameterType)) {
                return DateUtil.getMonthForInt(month);
            }

            if (TemplateParameterType.NEXT_MONTH.getLabel().equals(parameterType)) {
                return DateUtil.getMonthForInt((month + 1) % 12);
            }

            if (TemplateParameterType.LAST_MONTH.getLabel().equals(parameterType)) {
                month = month - 1;
                month = month < 0 ? month + 12 : month;
                return DateUtil.getMonthForInt(month);
            }

            if (TemplateParameterType.THIS_MONTH_2.getLabel().equals(parameterType)) {
                month = month - 2;
                month = month < 0 ? month + 12 : month;
                return DateUtil.getMonthForInt(month);
            }

            if (TemplateParameterType.WORKER_SALARY.getLabel().equals(parameterType)) {
                return NumberFormatter.formatNumber(contract.getWorkerSalary());
            }

            if (TemplateParameterType.HER_YOU.getLabel().equals(parameterType)) {
                if (contract.getHousemaid() == null || contract.getHousemaid().getPhoneNumber() == null ||
                        contract.getHousemaid().getPhoneNumber().isEmpty()) {
                    return "you when her";
                }
                return "her when the";
            }

            java.sql.Date currentDateSql = new java.sql.Date(System.currentTimeMillis());
            if (TemplateParameterType.UPCOMING_PRIMARY_PAYROLL_WITH_2DAYS.getLabel().equals(parameterType)) {
                List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                MonthlyPaymentRule monthlyPaymentRule = null;
                if (!nextPayrollMonthlyRules.isEmpty()) {
                    monthlyPaymentRule = nextPayrollMonthlyRules.get(0);
                    if (monthlyPaymentRule.getPaymentDate() != null) {
                        return DateUtil.formatClientWithoutYearDate(new LocalDate(monthlyPaymentRule.getPaymentDate()).plusDays(2).toDate());
                    }
                }
            }

            if (TemplateParameterType.SPECIAL_UPCOMING_PRIMARY_PAYROLL_WITH_2DAYS.getLabel().equals(parameterType)) {
                List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                MonthlyPaymentRule monthlyPaymentRulePrimary = null;
                if (nextPayrollMonthlyRules.size() > 0) {
                    monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
                    if (monthlyPaymentRulePrimary.getPaymentDate().getMonth() != currentDateSql.getMonth()) {
                        //get the rule of the month before
                        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
                        selectQuery.filterBy("payrollMonth", "=", DateUtil.addMonthsSql(monthlyPaymentRulePrimary.getPayrollMonth(), -1));
                        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
                        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
                        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
                        selectQuery.filterBy("singleHousemaid", "=", false);
                        selectQuery.filterBy("singleOfficeStaff", "=", false);
                        selectQuery.sortBy("paymentDate", true);
                        nextPayrollMonthlyRules = selectQuery.execute();
                        if (nextPayrollMonthlyRules.size() > 0)
                            monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
                    }
                }
                if(monthlyPaymentRulePrimary != null && monthlyPaymentRulePrimary.getPaymentDate() != null){
                    return DateUtil.formatClientWithoutYearDate(new LocalDate(monthlyPaymentRulePrimary.getPaymentDate()).plusDays(2).toDate());
                }
            }

            if (TemplateParameterType.UPCOMING_PRIMARY_PAYROLL_WITH_1DAY.getLabel().equals(parameterType)) {
                List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                MonthlyPaymentRule monthlyPaymentRule = null;
                if (!nextPayrollMonthlyRules.isEmpty()) {
                    monthlyPaymentRule = nextPayrollMonthlyRules.get(0);
                    if (monthlyPaymentRule.getPaymentDate() != null) {
                        return DateUtil.formatClientWithoutYearDate(new LocalDate(monthlyPaymentRule.getPaymentDate()).plusDays(1).toDate());
                    }
                }
            }

            if (TemplateParameterType.PAYROLL_TRANSFER_DATE.getLabel().equals(parameterType)) {
                List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                MonthlyPaymentRule monthlyPaymentRulePrimary = null;
                if (nextPayrollMonthlyRules.size() > 0) {
                    monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
                    if (monthlyPaymentRulePrimary.getPaymentDate().getMonth() != currentDateSql.getMonth()) {
                        //get the rule of the month before
                        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
                        selectQuery.filterBy("payrollMonth", "=", DateUtil.addMonthsSql(monthlyPaymentRulePrimary.getPayrollMonth(), -1));
                        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
                        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
                        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
                        selectQuery.filterBy("singleHousemaid", "=", false);
                        selectQuery.filterBy("singleOfficeStaff", "=", false);
                        selectQuery.sortBy("paymentDate", true);
                        nextPayrollMonthlyRules = selectQuery.execute();
                        if (nextPayrollMonthlyRules.size() > 0)
                            monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
                    }
                }
                Date date = new Date();
                LocalDate dt = new LocalDate(date);
                java.sql.Date payrollMonth = new java.sql.Date(dt.minusMonths(1).withDayOfMonth(1).toDate().getTime());
                if (monthlyPaymentRulePrimary != null && monthlyPaymentRulePrimary.getPayrollMonth() != null ){
                    payrollMonth = monthlyPaymentRulePrimary.getPayrollMonth();
                }
                if (contract.getHousemaid() == null) {
                    return "";
                }
                HousemaidPayrollLog housemaidPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class)
                        .findTopByHousemaidAndPayrollMonthAndTransferredTrue(contract.getHousemaid(), payrollMonth);
                if (housemaidPayrollLog != null && housemaidPayrollLog.getPaidOnDate() != null && !housemaidPayrollLog.getPaidOnDate().isEmpty()) {
                    return DateUtil.formatClientWithoutYearDate(DateUtil.parseFullDate(housemaidPayrollLog.getPaidOnDate()));
                }

            }

            if (TemplateParameterType.PREV_PAYROLL_TRANSFER_DATE.getLabel().equals(parameterType)) {
                List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                MonthlyPaymentRule monthlyPaymentRulePrimary = null;
                if (nextPayrollMonthlyRules.size() > 0) {
                    monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
                    if (monthlyPaymentRulePrimary.getPaymentDate().getMonth() != currentDateSql.getMonth()) {
                        //get the rule of the month before
                        SelectQuery<MonthlyPaymentRule> selectQuery = new SelectQuery<>(MonthlyPaymentRule.class);
                        selectQuery.filterBy("payrollMonth", "=", DateUtil.addMonthsSql(monthlyPaymentRulePrimary.getPayrollMonth(), -1));
                        selectQuery.filterBy("payrollType", "=", PayrollType.PRIMARY);
                        selectQuery.filterBy("employeeTypeList", "MEMBER OF", PaymentRuleEmployeeType.HOUSEMAIDS);
                        selectQuery.filterBy("paymentMethod", "IN", Arrays.asList(PaymentRulePaymentMethod.WPS));
                        selectQuery.filterBy("singleHousemaid", "=", false);
                        selectQuery.filterBy("singleOfficeStaff", "=", false);
                        selectQuery.sortBy("paymentDate", true);
                        nextPayrollMonthlyRules = selectQuery.execute();
                        if (nextPayrollMonthlyRules.size() > 0)
                            monthlyPaymentRulePrimary = nextPayrollMonthlyRules.get(0);
                    }
                }
                Date date = new Date();
                LocalDate dt = new LocalDate(date);
                java.sql.Date payrollMonth = new java.sql.Date(dt.minusMonths(1).withDayOfMonth(1).toDate().getTime());
                if (monthlyPaymentRulePrimary != null && monthlyPaymentRulePrimary.getPayrollMonth() != null ){
                    payrollMonth = DateUtil.addMonthsSql(monthlyPaymentRulePrimary.getPayrollMonth(), -1);
                }
                if (contract.getHousemaid() == null) {
                    return "";
                }
                HousemaidPayrollLog housemaidPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class)
                        .findTopByHousemaidAndPayrollMonthAndTransferredTrue(contract.getHousemaid(), payrollMonth);
                if (housemaidPayrollLog != null && housemaidPayrollLog.getPaidOnDate() != null && !housemaidPayrollLog.getPaidOnDate().isEmpty()) {
                    return DateUtil.formatClientWithoutYearDate(DateUtil.parseFullDate(housemaidPayrollLog.getPaidOnDate()));
                }

            }

            if (TemplateParameterType.UPCOMING_SECONDARY_PAYROLL_WITH_1DAY.getLabel().equals(parameterType)) {
                List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.SECONDARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                MonthlyPaymentRule monthlyPaymentRule = null;
                if (!nextPayrollMonthlyRules.isEmpty()) {
                    monthlyPaymentRule = nextPayrollMonthlyRules.get(0);
                    if (monthlyPaymentRule.getPaymentDate() != null) {
                        return DateUtil.formatClientWithoutYearDate(new LocalDate(monthlyPaymentRule.getPaymentDate()).plusDays(1).toDate());
                    }
                }
            }

            if (TemplateParameterType.CONTRACT_MONTH_WITH_2MONTH.getLabel().equals(parameterType)) {
                if (contract.getStartOfContract() != null) {
                    int contractMonthPlus2 = (contract.getStartOfContract().getMonth() + 2) % 12;
                    return DateUtil.getMonthForInt(contractMonthPlus2);
                }

            }

            if (TemplateParameterType.CONTRACT_MONTH.getLabel().equals(parameterType)) {
                if (contract.getStartOfContract() != null) {
                    int contractMonth = (contract.getStartOfContract().getMonth());
                    return DateUtil.getMonthForInt(contractMonth);
                }
            }

            if (TemplateParameterType.NEXT_CONTRACT_MONTH.getLabel().equals(parameterType)) {
                if (contract.getStartOfContract() != null) {
                    int contractMonthPlus1 = (contract.getStartOfContract().getMonth() + 1) % 12;
                    return DateUtil.getMonthForInt(contractMonthPlus1);
                }
            }
        } catch (Exception e) {
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception while getGeneralParameterValue(" + contractId + ", " + parameterType + ")", false);
        }
        return "";
    }

    public boolean isContractStartThisMonth(Contract contract, java.sql.Date paymentDatePlusNDays){
        if(contract != null && contract.getStartOfContract() != null){
            java.sql.Date from = new java.sql.Date(contract.getStartOfContract().getTime());
            java.sql.Date now = new java.sql.Date(System.currentTimeMillis());
            return now.compareTo(from) >= 0 && now.compareTo(paymentDatePlusNDays) < 0;
        }
        return false;
    }

    public boolean isContractStartLastMonth(Contract contract, java.sql.Date paymentDatePlusNDays){
        if(contract != null && contract.getStartOfContract() !=null){
            java.sql.Date to = new java.sql.Date(new LocalDate(contract.getStartOfContract()).plusMonths(2).withDayOfMonth(1).toDate().getTime());
            java.sql.Date now = new java.sql.Date(System.currentTimeMillis());
            return now.compareTo(paymentDatePlusNDays) >= 0 && now.compareTo(to) < 0;
        }
        return false;
    }

    public boolean isMaidFirstSalaryCCApp(Contract contract) {
        if(contract != null && contract.getStartOfContract() !=null){
            LocalDate from = new LocalDate(contract.getStartOfContract()).plusMonths(2).withDayOfMonth(1);
            LocalDate to = from.dayOfMonth().withMaximumValue();
            java.sql.Date now = new java.sql.Date(System.currentTimeMillis());
            return now.compareTo(new java.sql.Date(from.toDate().getTime())) >= 0 && now.compareTo(new java.sql.Date(to.toDate().getTime())) <= 0;
        }
        return false;
    }

    public Map<String,Object> getStatusAndTargetPaymentFromPayments(List<Payment> payments){
        if (payments == null || payments.isEmpty()) return null;

        Map<String,Object> result = new HashMap<>();
        boolean isReceived = false;
        boolean isBounced = false;
        boolean isReplaced = false;
        long id1 =0 , id2 = 0;
        for (Payment payment:payments) {
            switch (payment.getStatus()) {
                case RECEIVED:
                    isReceived = true;
                    id1 =payment.getId();
                    break;
                case BOUNCED:
                    isBounced = true;
                    id2 = payment.getId();
                    if (payment.isReplaced() != null && payment.isReplaced()) {
                        isReplaced = true;
                    }
                    break;
            }
        }

        if(isBounced){
            if(isReplaced){
                result.put("status",PaymentStatus.BOUNCED);
                result.put("isReplaced",Boolean.TRUE);
                result.put("payment",paymentRepository.findTopByStatusAndReplacementForId
                        (PaymentStatus.RECEIVED, id2));
                return result;
            }else {
                result.put("status",PaymentStatus.BOUNCED);
                result.put("isReplaced",Boolean.FALSE);
                result.put("payment",paymentRepository.findOne(id2));
                return result;
            }
        }

        if(isReceived){
            result.put("status",PaymentStatus.RECEIVED);
            result.put("payment",paymentRepository.findOne(id1));
            return result;
        }

        return null ;
    }

    public boolean isDateBeforeThePrimaryPayrollIsPaid(Date date){
        java.sql.Date currentDateSql  = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDateSql, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        if (nextPayrollMonthlyRules.size() > 0) {
            MonthlyPaymentRule monthlyPaymentRule = nextPayrollMonthlyRules.get(0);
            if (monthlyPaymentRule.getPaymentDate().getMonth() != currentDateSql.getMonth()) {
                nextPayrollMonthlyRules = Setup.getApplicationContext().getBean(MonthlyPaymentRuleRepository.class)
                        .findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(DateUtil.addMonthsSql(currentDateSql, -1), PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                monthlyPaymentRule = nextPayrollMonthlyRules.get(0);
            }
            PayrollAuditTodo auditTodo = payrollAuditTodoRepository.getAnotherCreatedTodo(monthlyPaymentRule.getPayrollMonth(), PaymentRuleEmployeeType.HOUSEMAIDS.toString(), monthlyPaymentRule.getLockDate());
            if(auditTodo == null || auditTodo.getPaymentDate() == null || date.compareTo(auditTodo.getLastMoveDate()) <= 0){
                return true ;
            }
            return false;
        }

        return false ;
    }

    public PayrollAccountantTodo getAccountantTodo(Housemaid housemaid){
        Date date = new Date();
        LocalDate dt = new LocalDate(date);
        HousemaidPayrollLog housemaidPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class)
                .findTopByHousemaidAndPayrollMonth(housemaid,
                        new java.sql.Date(dt.withDayOfMonth(1).toDate().getTime()));
        return housemaidPayrollLog.getPayrollAccountantTodo();
    }

    boolean isPaid(HousemaidPayrollLog payrollLog){
        if(payrollLog == null || payrollLog.getPayslipSent()){
            if(payrollLog.getTransferred()){

            }
        }
        return false ;
    }
}
