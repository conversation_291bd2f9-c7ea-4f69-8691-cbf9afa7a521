package com.magnamedia.helper;

import com.magnamedia.controller.PaySlipsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.entity.*;
import com.magnamedia.entity.payroll.logging.HousemaidPayrollLog;
import com.magnamedia.extra.SalaryLibrary;
import com.magnamedia.module.type.HousemaidUnpaidStatus;
import com.magnamedia.module.type.PaymentRuleEmployeeType;
import com.magnamedia.module.type.PaymentRulePaymentMethod;
import com.magnamedia.module.type.PayrollType;
import com.magnamedia.repository.*;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Component;

import javax.swing.text.DateFormatter;
import java.util.*;

@Component("yaYaAppContentHelper")
public class YayaAppContentHelper {

    public String getLastSalaryDate() {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        java.sql.Date paymentDate;
        List<MonthlyPaymentRule> lastPayrollMonthlyRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findByPaymentDateLessThan(currentDate, PaymentRuleEmployeeType.HOUSEMAIDS);
        if(lastPayrollMonthlyRules != null && lastPayrollMonthlyRules.size() > 0) {
            MonthlyPaymentRule lastPayrollMonthlyRule = lastPayrollMonthlyRules.get(0);
            return DateUtil.formatFullDate(lastPayrollMonthlyRule.getPaymentDate());
        }else {
            return DateUtil.formatFullDate(new Date());
        }
    }

    public String getLastPrimarySalaryDate() {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> lastPayrollMonthlyRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).getMonthlyRulesByEmployeeTypeAndPaymentDateBeforeAndPaymentMethodAndPayrollType(PaymentRuleEmployeeType.HOUSEMAIDS,currentDate, PaymentRulePaymentMethod.WPS, PayrollType.PRIMARY);
        if(lastPayrollMonthlyRules != null && lastPayrollMonthlyRules.size() > 0) {
            MonthlyPaymentRule lastPayrollMonthlyRule = lastPayrollMonthlyRules.get(0);
            return DateUtil.formatFullDate(lastPayrollMonthlyRule.getPaymentDate());
        }else {
            return DateUtil.formatFullDate(new Date());
        }
    }

    public String getFirstMissSalaryDate() {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> lastPayrollMonthlyRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).getMonthlyRulesByEmployeeTypeAndPaymentDateBeforeAndPaymentMethodAndPayrollType(PaymentRuleEmployeeType.HOUSEMAIDS,currentDate, PaymentRulePaymentMethod.WPS, PayrollType.PRIMARY);
        if (lastPayrollMonthlyRules != null && lastPayrollMonthlyRules.size() > 1){
            return DateUtil.formatFullDate(lastPayrollMonthlyRules.get(1).getPaymentDate());
        }else
            return DateUtil.formatFullDate(new Date());
    }

    //same is getLastPrimarySalaryDate
//    public String getSecondMissSalaryDate() {
//        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
//        List<MonthlyPaymentRule> lastPayrollMonthlyRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).getMonthlyRulesByEmployeeTypeAndPaymentDateBeforeAndPaymentMethodAndPayrollType(PaymentRuleEmployeeType.HOUSEMAIDS,currentDate, PaymentRulePaymentMethod.WPS, PayrollType.PRIMARY);
//        if (lastPayrollMonthlyRules != null && lastPayrollMonthlyRules.size() > 0){
//            return DateUtil.formatFullDate(lastPayrollMonthlyRules.get(0).getPaymentDate());
//        }else
//            return DateUtil.formatFullDate(new Date());
//    }


    public String getLastSalaryMonth(Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> nextPayrollMonthlyRules = getNextPayrollMonthlyRulesForContent(housemaid, true);
        java.sql.Date prevPayrollMonth;
        if (nextPayrollMonthlyRules == null || nextPayrollMonthlyRules.size() == 0 )
            prevPayrollMonth = new java.sql.Date(new LocalDate(new Date()).withDayOfMonth(1).toDate().getTime());
        else {
            prevPayrollMonth = new java.sql.Date(DateUtil.getPreviousMonths(nextPayrollMonthlyRules.get(0).getPayrollMonth(), 1).getTime());
        }

        return DateUtil.formatMonth(prevPayrollMonth);
    }

    public String getLastPaidSalaryDate(Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).getOne(housemaidId);
        HousemaidPayrollLog log = Setup.getRepository(HousemaidPayrollLogRepository.class).findFirstByHousemaidAndTransferredOrderByIdDesc(housemaid, true);
        return log.getPaidOnDate() != null ? log.getPaidOnDate() : DateUtil.formatFullDate(log.getLastModificationDate());
    }


    public String getNextSalaryDate() {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextMonthlyRuleByHousemaidType(PaymentRuleEmployeeType.HOUSEMAIDS, currentDate, PaymentRulePaymentMethod.WPS);
        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate().plusMonths(1).withDayOfMonth(3).toDate().getTime());
        return DateUtil.formatFullDate(paymentDate);
    }

    public java.sql.Date getNextSalaryDateAsDate() {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextMonthlyRuleByHousemaidType(PaymentRuleEmployeeType.HOUSEMAIDS, currentDate, PaymentRulePaymentMethod.WPS);
        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate().plusMonths(1).withDayOfMonth(3).toDate().getTime());
        return paymentDate;
    }

//    public String getNextSalaryDatePlusTwoDays() {
//        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
//        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextMonthlyRuleByHousemaidType(PaymentRuleEmployeeType.HOUSEMAIDS, currentDate, PaymentRulePaymentMethod.WPS);
//        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate().plusMonths(1).withDayOfMonth(3).toDate().getTime());
//        return DateUtil.formatFullDate(new LocalDate(paymentDate).plusDays(2).toDate());
//    }

    public String getNextSalaryDateAfterVacation(Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
        Date endVacationDate = findEndVacationDateForHousemaid(housemaid);
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextMonthlyRuleByHousemaidType(PaymentRuleEmployeeType.HOUSEMAIDS, new java.sql.Date(endVacationDate.getTime()), PaymentRulePaymentMethod.WPS);
        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate(endVacationDate).plusMonths(1).withDayOfMonth(3).toDate().getTime());
        return DateUtil.formatFullDate(new LocalDate(paymentDate).toDate());
    }

    public String getName(Long housemaidId) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);
        return housemaid != null ? housemaid.getName() : "";
    }

    public String getUpcomingPrimaryPaymentDate(Long housemaidId, Boolean isCon4) {
        Housemaid housemaid = Setup.getRepository(HousemaidRepository.class).findOne(housemaidId);

        List<MonthlyPaymentRule> rules = getNextPayrollMonthlyRulesForContent(housemaid, isCon4);
        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate().plusMonths(1).withDayOfMonth(3).toDate().getTime());

        return DateUtil.formatFullDate(paymentDate);
    }

    public java.sql.Date getUpcomingPrimaryPaymentDateAsDate() {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDate, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate().plusMonths(1).withDayOfMonth(3).toDate().getTime());
        return paymentDate;
    }

    public String getFirstPrimaryPayrollAfterUpcomingPrimary() {
        java.sql.Date nextPrimary = new java.sql.Date(DateUtil.addDays(getUpcomingPrimaryPaymentDateAsDate(), 1).getTime());
        List<MonthlyPaymentRule> rules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(nextPrimary, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        java.sql.Date paymentDate = rules != null && rules.size()>0 ? rules.get(0).getPaymentDate() : new java.sql.Date(new LocalDate(nextPrimary).plusMonths(1).withDayOfMonth(3).toDate().getTime());
        return DateUtil.formatFullDate(paymentDate);
    }

    public Date findEndVacationDateForHousemaid(Housemaid housemaid){
        List<HousemaidVacation> vacations1 = Setup.getRepository(HousemaidVacationRepository.class).findByHousemaidAndStartDateLessThanEqualAndEndDateGreaterThan(housemaid, new java.sql.Date(System.currentTimeMillis()), new java.sql.Date(System.currentTimeMillis()));
        if (vacations1 != null && vacations1.size() > 0)
            return vacations1.get(0).getEndDate();
        return new Date();
    }

    public List<MonthlyPaymentRule> getNextPayrollMonthlyRulesForContent(Housemaid housemaid, boolean isCon4) {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        java.sql.Date nextPayrollMonth;

        List<MonthlyPaymentRule> nextPayrollMonthlyRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(currentDate, PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
        if(nextPayrollMonthlyRules.size() != 0) {
            MonthlyPaymentRule nextPayrollMonthlyRule = nextPayrollMonthlyRules.get(0);
            nextPayrollMonth = nextPayrollMonthlyRule.getPayrollMonth();

            //if the next monthly payment rule is finished then we are on Payment date
            // for Con-2 only if the payroll log is transferred then consider it as previous month
            // for Con-4 if the payroll log is transferred and payslip sent then consider it as previous month
            if (nextPayrollMonthlyRule.getFinished()) {
                HousemaidPayrollLog currentMonthLog = Setup.getRepository(HousemaidPayrollLogRepository.class).findFirstByPayrollMonthAndHousemaid(nextPayrollMonth, housemaid);
                if(currentMonthLog != null && currentMonthLog.getTransferred()){
                    if (!isCon4 || currentMonthLog.getPayslipSent())
                        nextPayrollMonthlyRules = Setup.getRepository(MonthlyPaymentRuleRepository.class).findNextHousemaidRuleByPaymentDateAndPayrollTypeAfterOrderByPaymentDateAsc(new java.sql.Date(DateUtil.addDays(currentDate, 2).getTime()), PayrollType.PRIMARY, PaymentRuleEmployeeType.HOUSEMAIDS);
                }
            }
        }
        return nextPayrollMonthlyRules;
    }

    public String getCon2Or4ForMaid(Housemaid housemaid, java.sql.Date nextPayrollMonth, java.sql.Date nextPrimaryPaymentDate, boolean isCon4){
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        LocalDate now = new LocalDate(currentDate);

        java.sql.Date prevPayrollMonth = new java.sql.Date(DateUtil.getPreviousMonths(nextPayrollMonth,1).getTime());
        java.sql.Date prev2PayrollMonth = new java.sql.Date(DateUtil.getPreviousMonths(nextPayrollMonth,2).getTime());

        HousemaidPayrollLog lastMonthPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findFirstByPayrollMonthAndHousemaid(prevPayrollMonth, housemaid);
        HousemaidPayrollLog lastPreviousMonthPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findFirstByPayrollMonthAndHousemaid(prev2PayrollMonth, housemaid);
        HousemaidPayrollLog lastEverPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findFirstByHousemaidAndTransferredOrderByIdDesc(housemaid, true);

        //==========================================================
        String whenYouWillReceiveSalaryTemplate_con4 = "";
        String payslipTemplate_con2 = "";


        //****************** MANAGE CONTENT-4 ********************//

        // housemaid start date is not filled yet
        if(housemaid.getStartDate() == null) {
            whenYouWillReceiveSalaryTemplate_con4 = "@maid_not_enrolled@";
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";
        }
        //same month then we still before the primary payment date and starting date after 27
        else if(isMaidAfter27th(housemaid, nextPrimaryPaymentDate, prevPayrollMonth, nextPayrollMonth)) {
            whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_after_27th@";
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";
        }
        //housemaid is eligible to be in payroll
        else if (isMaidEligible(housemaid, nextPrimaryPaymentDate, prevPayrollMonth, nextPayrollMonth)){
            boolean maidFirstSalary = isMaidFirstSalary(housemaid, nextPrimaryPaymentDate, prevPayrollMonth);

            //case NO_SHOW
            if (lastMonthPayrollLog != null && lastMonthPayrollLog.getTransferred() != null && !lastMonthPayrollLog.getTransferred()
                    && HousemaidUnpaidStatus.NO_SHOW.equals(lastMonthPayrollLog.getHousemaidUnpaidStatus())){
                whenYouWillReceiveSalaryTemplate_con4 = "@maid_was_now_show@";
                payslipTemplate_con2 = "@maid_was_on_vacation_or_on_hold_payslip@";
            }
            //case the maid was on Vacation and still didn't receive here salary
            else if (lastMonthPayrollLog != null && lastMonthPayrollLog.getTransferred() != null  && !lastMonthPayrollLog.getTransferred()
                    && HousemaidUnpaidStatus.ON_VACATION.equals(lastMonthPayrollLog.getHousemaidUnpaidStatus())){
                //check if more than one payroll
                if (lastPreviousMonthPayrollLog != null
                        && lastPreviousMonthPayrollLog.getTransferred() != null && !lastPreviousMonthPayrollLog.getTransferred()
                        && HousemaidUnpaidStatus.ON_VACATION.equals(lastPreviousMonthPayrollLog.getHousemaidUnpaidStatus())) {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_is_on_vacation_more_than_one_payroll@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_more_than_one_payroll_payslip@";
                }else {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_was_on_vacation@";
                    payslipTemplate_con2 = "@maid_was_on_vacation_or_on_hold_payslip@";
                }
            }
            //same month then we still before the primary payment date or if after 15 then directly talk about next salary
            else if (now.getMonthOfYear() == nextPrimaryPaymentDate.toLocalDate().getMonthValue() || (now.getDayOfMonth()>= 15)
                    || maidFirstSalary) {
                whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_after_15th@";

                if (maidFirstSalary)
                    payslipTemplate_con2 = "@maid_does_not_have_payslip@";
                else
                    payslipTemplate_con2 = "@maid_enrolled_after_15th_payslip@";
            }
            //case paid and before 15
            else if(lastMonthPayrollLog != null && lastMonthPayrollLog.getTransferred() != null  && lastMonthPayrollLog.getTransferred()) {
                whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_before_15th@";
                payslipTemplate_con2 = "@maid_enrolled_before_15th_payslip@";
            }
        }
        // maid is on vacation and have previous salary (transferred or not)
        else if (lastMonthPayrollLog != null && HousemaidStatus.ON_VACATION.equals(housemaid.getStatus())) {

            // payroll is not transferred then talk about here missed salary
            if (lastMonthPayrollLog.getTransferred() != null && !lastMonthPayrollLog.getTransferred()
                    && HousemaidUnpaidStatus.ON_VACATION.equals(lastMonthPayrollLog.getHousemaidUnpaidStatus())) {
                //check if more than one payroll
                if (lastPreviousMonthPayrollLog != null
                        && lastPreviousMonthPayrollLog.getTransferred() != null && !lastPreviousMonthPayrollLog.getTransferred()
                        && HousemaidUnpaidStatus.ON_VACATION.equals(lastPreviousMonthPayrollLog.getHousemaidUnpaidStatus())) {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_was_on_vacation_more_than_one_payroll@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_more_than_one_payroll_payslip@";
                }else {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_is_on_vacation@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_payslip@";
                }
            }
            // she is on vacation but here salary was successfully transferred before (act like normal)
            else if (lastMonthPayrollLog.getTransferred() != null && lastMonthPayrollLog.getTransferred()){
                java.util.Date endVacationDate = findEndVacationDateForHousemaid(housemaid);
                endVacationDate = endVacationDate == null ? currentDate : endVacationDate;

                //maid's end vacation date is before next primary date
                if (endVacationDate.before(nextPrimaryPaymentDate)) {
                    if(now.getMonthOfYear() == nextPrimaryPaymentDate.toLocalDate().getMonthValue() || (now.getDayOfMonth()>= 15)) {
                        whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_after_15th@";
                        payslipTemplate_con2 = "@maid_enrolled_after_15th_payslip@";
                    }else {
                        whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_before_15th@";
                        payslipTemplate_con2 = "@maid_enrolled_before_15th_payslip@";
                    }
                }
                //maid's end vacation date is after next primary date
                else {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_is_on_vacation_and_will_return_after_primary@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_payslip@";
                }
            }
        }

        if (whenYouWillReceiveSalaryTemplate_con4.isEmpty()){
            whenYouWillReceiveSalaryTemplate_con4 = "@maid_special_condition@";
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";
        }

        //****************** MANAGE CONTENT-2 ********************//
        if(lastEverPayrollLog == null)
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";

        //==========================================================


        return isCon4 ? whenYouWillReceiveSalaryTemplate_con4 : payslipTemplate_con2;
    }

    public Map<String, Object> getCon4WithCon2ForMaidNormalCase(Housemaid housemaid, java.sql.Date nextPayrollMonth, java.sql.Date nextPrimaryPaymentDate, String lang){
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        LocalDate now = new LocalDate(currentDate);

        java.sql.Date prevPayrollMonth = new java.sql.Date(DateUtil.getPreviousMonths(nextPayrollMonth,1).getTime());
        java.sql.Date prev2PayrollMonth = new java.sql.Date(DateUtil.getPreviousMonths(nextPayrollMonth,2).getTime());

        HousemaidPayrollLog lastMonthPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findFirstByPayrollMonthAndHousemaid(prevPayrollMonth, housemaid);
        HousemaidPayrollLog lastPreviousMonthPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findFirstByPayrollMonthAndHousemaid(prev2PayrollMonth, housemaid);
        HousemaidPayrollLog lastEverPayrollLog = Setup.getRepository(HousemaidPayrollLogRepository.class).
                findFirstByHousemaidAndTransferredOrderByIdDesc(housemaid, true);

        //==========================================================
        Map<String, Object> details = new HashMap<>();
        String whenYouWillReceiveSalaryTemplate_con4 = "";
        Boolean showInHomePage = false;
        String payslipTemplate_con2 = "";


        //****************** MANAGE CONTENT-4 ********************//

        // housemaid start date is not filled yet
        if(housemaid.getStartDate() == null) {
            whenYouWillReceiveSalaryTemplate_con4 = "@maid_not_enrolled@";
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";
        }
        //same month then we still before the primary payment date and starting date after 27
        else if(isMaidAfter27th(housemaid, nextPrimaryPaymentDate, prevPayrollMonth, nextPayrollMonth)) {
            whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_after_27th@";
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";
        }
        //housemaid is eligible to be in payroll
        else if (isMaidEligible(housemaid, nextPrimaryPaymentDate, prevPayrollMonth, nextPayrollMonth)){
            boolean maidFirstSalary = isMaidFirstSalary(housemaid, nextPrimaryPaymentDate, prevPayrollMonth);

            //case NO_SHOW
            if (lastMonthPayrollLog != null && lastMonthPayrollLog.getTransferred() != null && !lastMonthPayrollLog.getTransferred()
                    && HousemaidUnpaidStatus.NO_SHOW.equals(lastMonthPayrollLog.getHousemaidUnpaidStatus())){
                whenYouWillReceiveSalaryTemplate_con4 = "@maid_was_now_show@";
                payslipTemplate_con2 = "@maid_was_on_vacation_or_on_hold_payslip@";
            }
            //case the maid was on Vacation and still didn't receive here salary
            else if (lastMonthPayrollLog != null && lastMonthPayrollLog.getTransferred() != null  && !lastMonthPayrollLog.getTransferred()
                    && HousemaidUnpaidStatus.ON_VACATION.equals(lastMonthPayrollLog.getHousemaidUnpaidStatus())){
                //check if more than one payroll
                if (lastPreviousMonthPayrollLog != null
                        && lastPreviousMonthPayrollLog.getTransferred() != null && !lastPreviousMonthPayrollLog.getTransferred()
                        && HousemaidUnpaidStatus.ON_VACATION.equals(lastPreviousMonthPayrollLog.getHousemaidUnpaidStatus())) {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_is_on_vacation_more_than_one_payroll@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_more_than_one_payroll_payslip@";
                }else {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_was_on_vacation@";
                    payslipTemplate_con2 = "@maid_was_on_vacation_or_on_hold_payslip@";
                }
            }
            //same month then we still before the primary payment date or if after 15 then directly talk about next salary
            else if (now.getMonthOfYear() == nextPrimaryPaymentDate.toLocalDate().getMonthValue() || (now.getDayOfMonth()>= 15)
                    || maidFirstSalary) {
                whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_after_15th@";

                if (maidFirstSalary)
                    payslipTemplate_con2 = "@maid_does_not_have_payslip@";
                else
                    payslipTemplate_con2 = "@maid_enrolled_after_15th_payslip@";
            }
            //case paid and before 15
            else if(lastMonthPayrollLog != null && lastMonthPayrollLog.getTransferred() != null  && lastMonthPayrollLog.getTransferred()) {
                whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_before_15th@";
                payslipTemplate_con2 = "@maid_enrolled_before_15th_payslip@";
            }
        }
        // maid is on vacation and have previous salary (transferred or not)
        else if (lastMonthPayrollLog != null && HousemaidStatus.ON_VACATION.equals(housemaid.getStatus())) {

            // payroll is not transferred then talk about here missed salary
            if (lastMonthPayrollLog.getTransferred() != null && !lastMonthPayrollLog.getTransferred()
                    && HousemaidUnpaidStatus.ON_VACATION.equals(lastMonthPayrollLog.getHousemaidUnpaidStatus())) {
                //check if more than one payroll
                if (lastPreviousMonthPayrollLog != null
                        && lastPreviousMonthPayrollLog.getTransferred() != null && !lastPreviousMonthPayrollLog.getTransferred()
                        && HousemaidUnpaidStatus.ON_VACATION.equals(lastPreviousMonthPayrollLog.getHousemaidUnpaidStatus())) {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_was_on_vacation_more_than_one_payroll@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_more_than_one_payroll_payslip@";
                }else {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_is_on_vacation@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_payslip@";
                }
            }
            // she is on vacation but here salary was successfully transferred before (act like normal)
            else if (lastMonthPayrollLog.getTransferred() != null && lastMonthPayrollLog.getTransferred()){
                java.util.Date endVacationDate = findEndVacationDateForHousemaid(housemaid);
                endVacationDate = endVacationDate == null ? currentDate : endVacationDate;

                //maid's end vacation date is before next primary date
                if (endVacationDate.before(nextPrimaryPaymentDate)) {
                    if(now.getMonthOfYear() == nextPrimaryPaymentDate.toLocalDate().getMonthValue() || (now.getDayOfMonth()>= 15)) {
                        whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_after_15th@";
                        payslipTemplate_con2 = "@maid_enrolled_after_15th_payslip@";
                    }else {
                        whenYouWillReceiveSalaryTemplate_con4 = "@maid_enrolled_before_15th@";
                        payslipTemplate_con2 = "@maid_enrolled_before_15th_payslip@";
                    }
                }
                //maid's end vacation date is after next primary date
                else {
                    whenYouWillReceiveSalaryTemplate_con4 = "@maid_is_on_vacation_and_will_return_after_primary@";
                    payslipTemplate_con2 = "@maid_is_on_vacation_payslip@";
                }
            }
        }

        if (whenYouWillReceiveSalaryTemplate_con4.isEmpty()){
            whenYouWillReceiveSalaryTemplate_con4 = "@maid_special_condition@";
            showInHomePage = true;
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";
        }

        //****************** MANAGE CONTENT-2 ********************//
        if(lastEverPayrollLog == null)
            payslipTemplate_con2 = "@maid_does_not_have_payslip@";

        //==========================================================

        String payslipsURL ="";
        HousemaidPayrollLog log = Setup.getRepository(HousemaidPayrollLogRepository.class).findTopByHousemaidAndPayrollMonthLessThanEqualAndTransferredTrueOrderByCreationDateDesc(housemaid, prevPayrollMonth);
        if(!"amh".equals(lang) && !"om".equals(lang) && !"tl".equals(lang) && !"hi".equals(lang))
            lang = "en";
        if(log != null)
            payslipsURL = Setup.getApplicationContext().getBean(PaySlipsController.class).generatePayslipPublicURLWithoutShorten(housemaid, log.getPayrollMonth(), lang);
        details.put("payslipURL",payslipsURL);
        details.put("whenYouWillReceiveSalaryTemplate_con4", whenYouWillReceiveSalaryTemplate_con4);
        details.put("payslipTemplate_con2", payslipTemplate_con2);
        details.put("showInHomePage", showInHomePage);

        return details;
    }

    public boolean isMaidEligible (Housemaid housemaid, java.sql.Date nextPrimaryPaymentDate, java.sql.Date prevPayrollMonth, java.sql.Date nextPayrollMonth){
        return housemaid.getExcludedFromPayroll() != null && !housemaid.getExcludedFromPayroll()
                && housemaid.getStartDate() != null
                && !housemaid.getStatus().equals(HousemaidStatus.ON_VACATION) && !housemaid.getStatus().equals(HousemaidStatus.NO_SHOW)
                && !isMaidAfter27th(housemaid, nextPrimaryPaymentDate, prevPayrollMonth, nextPayrollMonth);
    }

    public Boolean isMaidAfter27th (Housemaid housemaid, java.sql.Date nextPrimaryPaymentDate, java.sql.Date prevPayrollMonth, java.sql.Date nextPayrollMonth) {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        LocalDate now = new LocalDate(currentDate);
        LocalDate startDate = new LocalDate(housemaid.getStartDate());
        LocalDate firstAttendanceDate = new LocalDate(housemaid.getLandedInDubaiDate());
        Boolean maidEnrolledAfterNext27th = startDate.isAfter(new LocalDate(nextPayrollMonth).withDayOfMonth(27));
        Boolean firstAttendanceDateInSameMonth = firstAttendanceDate.getMonthOfYear() == new LocalDate(nextPayrollMonth).getMonthOfYear();

        return ((now.getMonthOfYear() == nextPrimaryPaymentDate.toLocalDate().getMonthValue() || now.getDayOfMonth() > 27 ) && maidEnrolledAfterNext27th && firstAttendanceDateInSameMonth);
    }

    public Boolean isMaidFirstSalary(Housemaid housemaid, java.sql.Date nextPrimaryPaymentDate, java.sql.Date prevPayrollMonth) {
        java.sql.Date currentDate = new java.sql.Date(System.currentTimeMillis());
        LocalDate now = new LocalDate(currentDate);
        LocalDate startDate = new LocalDate(housemaid.getStartDate());
        Boolean maidEnrolledAfterPrev27th = startDate.isAfter(new LocalDate(prevPayrollMonth).withDayOfMonth(27));
        return now.getMonthOfYear() != nextPrimaryPaymentDate.toLocalDate().getMonthValue() && now.getDayOfMonth() < 15 && maidEnrolledAfterPrev27th;
    }


    //depend on YayaLanguage if not null , else depend on Nationality language
    public List<String> getLanguageForMaid(Housemaid housemaid) {
        if (housemaid.getYayaLanguage() != null){
            return Arrays.asList(housemaid.getYayaLanguage().getCode());
        }
        PicklistItem nationality = housemaid.getNationality();
        List<String> languages = new ArrayList<>();
        if(nationality == null) {
            languages.add("en");
            return languages;
        }

        if(Arrays.asList(SalaryLibrary.ETHIOPIAN_NATIONALITY_CODES).contains(nationality.getCode())) {
            languages.add("am");
            languages.add("az");
        }

        else if(Arrays.asList(SalaryLibrary.FILIPINO_NATIONALITY_CODES).contains(nationality.getCode())) {
            languages.add("tl");
        }
        else {
            languages.add("en");
        }

        return languages;
    }
}
