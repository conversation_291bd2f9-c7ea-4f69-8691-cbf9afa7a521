package com.magnamedia.salarycalculation.officestaff;

import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.salarycalculation.OfficeStaffSalaryTransaction;
import org.joda.time.LocalDate;

import java.util.Date;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 7, 2019
 * Jirra ACC-677
 */
public class TransportationTransaction
        implements OfficeStaffSalaryTransaction {

    @Override
    public Double calculate(
            OfficeStaff officeStaff,
            LocalDate payrollStart,
            LocalDate payrollEnd,
            LocalDate payrollMonth,
            Boolean finalFile) {

        if(officeStaff.getTerminationDate() != null && officeStaff.getFinalSettlement() != null) {
            return 0.0;
        }

        return officeStaff.getTrasnportation() != null ?
                officeStaff.getTrasnportation() : 0D;
    }

    @Override
    public Double calculate(OfficeStaff officeStaff, Date payrollStart, Date payrollEnd, LocalDate payrollMonth, Boolean finalFile) {
        return 0.0;
    }

    @Override
    public OfficeStaffPayrollBean setInSalaryObject(
            OfficeStaffPayrollBean bean,
            Double value) {
        //Jirra ACC-1127
        if (value != null)
            value = Double.valueOf(Math.round(value));
        bean.setTransportation(value.toString());
        bean.setTotatIcome(bean.getTotatIcome() + value);
        return bean;
    }

    @Override
    public Boolean isPositive() {
        return true;
    }
}
