package com.magnamedia.salarycalculation.officestaff;

import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.salarycalculation.OfficeStaffSalaryTransaction;
import org.joda.time.LocalDate;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 7, 2019
 * Jirra ACC-677
 */
public class BasicSalaryTransaction
        implements OfficeStaffSalaryTransaction {

    @Override
    public Double calculate(
            OfficeStaff officeStaff,
            LocalDate payrollStart,
            LocalDate payrollEnd,
            LocalDate payrollMonth,
            Boolean finalFile) {

        if (officeStaff.getTerminationDate() != null && officeStaff.getFinalSettlement() != null) {
            return 0.0;
        }
        Double basicSalary = this.getStaffBasicSalary(officeStaff, payrollEnd);
        return basicSalary != null ?
                basicSalary : 0D;
    }

    @Override
    public Double calculate(OfficeStaff officeStaff, Date payrollStart, Date payrollEnd, LocalDate payrollMonth, Boolean finalFile) {
        if (officeStaff.getTerminationDate() != null && officeStaff.getFinalSettlement() != null) {
            return 0.0;
        }
        Double basicSalary = this.getStaffBasicSalary(officeStaff, payrollEnd);
        return basicSalary != null ?
                basicSalary : 0D;
    }

    @Override
    public OfficeStaffPayrollBean setInSalaryObject(
            OfficeStaffPayrollBean bean,
            Double value) {
        bean.setBasicSalary(value);
        bean.setTotatIcome(bean.getTotatIcome() + value);
        return bean;
    }

    @Override
    public Boolean isPositive() {
        return true;
    }

    private Double getStaffBasicSalary(
            OfficeStaff officeStaff,
            LocalDate payrollEnd) {

        try {
            //check if the salary is change after payroll end date
            HistorySelectQuery<OfficeStaff> historySelectQuery = new HistorySelectQuery<>(OfficeStaff.class);
            historySelectQuery.filterBy("id", "=", officeStaff.getId());
            historySelectQuery.filterByChanged("basicSalary");
            historySelectQuery.filterBy("lastModificationDate", ">=", payrollEnd.toDate());
            historySelectQuery.sortBy("lastModificationDate", true, true);
            List<OfficeStaff> staffHistory = historySelectQuery.execute();

            if (staffHistory != null && staffHistory.size() > 0) {

                //get the last salary just before the editing date
                Date lastModificationDate = staffHistory.get(0).getLastModificationDate();
                HistorySelectQuery<OfficeStaff> historySelectQuery2 = new HistorySelectQuery<>(OfficeStaff.class);
                historySelectQuery2.filterBy("id", "=", officeStaff.getId());
                historySelectQuery2.filterBy("basicSalary", "IS NOT NULL", null);
                historySelectQuery2.filterBy("lastModificationDate", "<", payrollEnd.toDate());
                historySelectQuery2.sortBy("lastModificationDate", false, true);
                staffHistory = historySelectQuery2.execute();

                if (staffHistory != null && staffHistory.size() > 0) {
                    return staffHistory.get(0).getBasicSalary();
                }
            }
        }catch (Exception e){
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in getBasicSalary for office staff " + officeStaff.getId(), false);
        }

        return officeStaff.getBasicSalary();
    }

    private Double getStaffBasicSalary(
            OfficeStaff officeStaff,
            Date payrollEnd) {

        try {
            //check if the salary is change after payroll end date
            HistorySelectQuery<OfficeStaff> historySelectQuery = new HistorySelectQuery<>(OfficeStaff.class);
            historySelectQuery.filterBy("id", "=", officeStaff.getId());
            historySelectQuery.filterByChanged("basicSalary");
            historySelectQuery.filterBy("lastModificationDate", ">=", payrollEnd);
            historySelectQuery.sortBy("lastModificationDate", true, true);
            List<OfficeStaff> staffHistory = historySelectQuery.execute();

            if (staffHistory != null && staffHistory.size() > 0) {

                //get the last salary just before the editing date
                Date lastModificationDate = staffHistory.get(0).getLastModificationDate();
                HistorySelectQuery<OfficeStaff> historySelectQuery2 = new HistorySelectQuery<>(OfficeStaff.class);
                historySelectQuery2.filterBy("id", "=", officeStaff.getId());
                historySelectQuery2.filterBy("basicSalary", "IS NOT NULL", null);
                historySelectQuery2.filterBy("lastModificationDate", "<", payrollEnd);
                historySelectQuery2.sortBy("lastModificationDate", false, true);
                staffHistory = historySelectQuery2.execute();

                if (staffHistory != null && staffHistory.size() > 0) {
                    return staffHistory.get(0).getBasicSalary();
                }
            }
        }catch (Exception e){
            DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in getBasicSalary for office staff " + officeStaff.getId(), false);
        }

        return officeStaff.getBasicSalary();
    }
}
