package com.magnamedia.salarycalculation.version2NewSecondPhase;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.*;
import com.magnamedia.entity.HousemaidPayrollBean;
import com.magnamedia.entity.OfficeStaffPayrollBean;
import com.magnamedia.helper.DebugHelper;
import com.magnamedia.module.PayrollManagementModule;
import com.magnamedia.repository.*;
import com.magnamedia.salarycalculation.HousemaidSalaryTransactionNewPhaseTwo;
import com.magnamedia.service.payroll.generation.newversion.PayrollGenerationHelperService;
import org.joda.time.LocalDate;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 7, 2019
 * Jirra ACC-677
 */
public class Z_DeductionCapTransaction
        implements HousemaidSalaryTransactionNewPhaseTwo {

    @Override
    public Double calculate(
            Housemaid housemaid,
            LocalDate payrollStart,
            LocalDate payrollEnd,
            LocalDate previousPayrollStart,
            Date payrollMonth,
            Boolean isSecondary,
            Boolean finalFile,
            Boolean firstSalary,
            HousemaidPayrollBean bean) {

        double repaymentValue = 0D;

        if (isSecondary) {
            return 0d;
        }
        PayrollGenerationHelperService payrollGenerationHelperService = Setup.getApplicationContext().getBean(PayrollGenerationHelperService.class);
        LocalDate payrollMonthL = new LocalDate(payrollMonth);
        double defaultCap = payrollGenerationHelperService.getDefaultCap(housemaid);

        Integer groupOneDays = null;
        Integer groupTwoDays = null;
        Integer groupThreeDays = null;
        Integer groupFourDays = null;
        Integer groupFiveDays = null;
        Integer groupSixDays = null;
        Integer workingDays = null;

        try {

            Map<String, Object> breakDown = payrollGenerationHelperService.getSalaryBreakDownForHousemaid(housemaid, payrollMonthL);

            groupOneDays = (Integer) breakDown.get("group1Days");
            groupTwoDays = (Integer) breakDown.get("group2Days");
            groupThreeDays = (Integer) breakDown.get("group3Days");
            groupFourDays = (Integer) breakDown.get("group4Days");
            groupFiveDays = (Integer) breakDown.get("group5Days");
            groupSixDays = (Integer) breakDown.get("group6Days");

            workingDays = groupOneDays + groupTwoDays + groupThreeDays + groupFourDays + groupFiveDays + groupSixDays;
        } catch (Exception e) {
            Logger.getLogger(OfficeStaffPayrollBean.class.getName()).log(Level.SEVERE, String.format("Error while generating payroll log for houemaid %s", housemaid.getName()), e);
            DebugHelper.sendExceptionMail("<EMAIL>", e, String.format("Error while generating payroll bean to process deductions cap for housemaid %s", housemaid.getName()), true);
        }
        groupOneDays = groupOneDays != null ? groupOneDays : 0;
        groupTwoDays = groupTwoDays != null ? groupTwoDays : 0;
        groupThreeDays = groupThreeDays != null ? groupThreeDays : 0;
        groupFourDays = groupFourDays != null ? groupFourDays : 0;
        workingDays = workingDays != null ? workingDays : 0;

        // jira PAY-426 check if we need to calculate the deductions in the cap phase or not
        int isActive = 0;
        String isActiveSt = Setup.getParameter(Setup.getCurrentModule(), PayrollManagementModule.PARAMETER_DEDUCTION_CALCULATION_IS_ACTIVE);
        if (isActiveSt != null) {
            try {
                isActiveSt = isActiveSt.trim();
                isActive = Integer.parseInt(isActiveSt);
            } catch (Exception e) {
                DebugHelper.sendExceptionMail("<EMAIL>", e, "Exception in reading the parameter PARAMETER_DEDUCTION_CALCULATION_IS_ACTIVE", false);
            }
        }

        double loanBalance = payrollGenerationHelperService.getLoanBalance(housemaid, new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime()));
        double unpaidDeductionBalance = (isActive == 1 ? payrollGenerationHelperService.getUnpaidDeductionBalance(housemaid) : 0)
                + bean.getTotalDeduction();
        double sumBalances = loanBalance + unpaidDeductionBalance;

        //update previous month balances (loan balance , unpaid deduction balance)
        HousemaidBalancesHistory housemaidBalancesHistory = new HousemaidBalancesHistory(housemaid, loanBalance, payrollGenerationHelperService.getUnpaidDeductionBalance(housemaid), payrollMonth, !finalFile);
        Setup.getRepository(HousemaidBalancesHistoryRepository.class).silentSave(housemaidBalancesHistory);

        if (sumBalances > 0) {

            Double currentCap = Math.min(sumBalances, defaultCap);

            int daysOfMonth = payrollMonthL.dayOfMonth().withMaximumValue().getDayOfMonth();
            double accommodationSalary = housemaid.getAccommodationSalary() == null ? 0.0 : housemaid.getAccommodationSalary();
            double proratedMOHRE = (daysOfMonth == workingDays) ? accommodationSalary :
                    workingDays * accommodationSalary / daysOfMonth;
            proratedMOHRE = Math.round(proratedMOHRE);

            // calculate new CAP
            if (bean.getTotatIcome() - currentCap < proratedMOHRE) {
                currentCap = bean.getTotatIcome() - proratedMOHRE;

            }

            double newUnpaidDeductionAmount = bean.getTotalDeduction();
            double newUnpaidDeductionRepaymentAmount = Math.min(currentCap, unpaidDeductionBalance);
            double newLoanRepaymentAmount = currentCap - newUnpaidDeductionRepaymentAmount;

            //create new Unpaid Deduction
            if (newUnpaidDeductionAmount > 0.0) {
                java.util.Date unpaidDeductionDate = payrollStart.getMonthOfYear() == payrollMonthL.getMonthOfYear() ? payrollStart.toDate() : payrollMonthL.withDayOfMonth(1).toDate();
                UnpaidDeduction newUnpaidDeduction = new UnpaidDeduction();
                newUnpaidDeduction.setAmount(newUnpaidDeductionAmount);
                newUnpaidDeduction.setUnpaidDeductionDate(unpaidDeductionDate);
                newUnpaidDeduction.setHousemaid(housemaid);
                newUnpaidDeduction.setNotes("Net Salary Calculation");
                newUnpaidDeduction.setNotFinal(!finalFile);
                Setup.getRepository(UnpaidDeductionRepository.class).save(newUnpaidDeduction);
            }

            //create new Unpaid Deduction Repayment
            if (newUnpaidDeductionRepaymentAmount > 0.0) {
                java.util.Date repaymentDate = payrollStart.getMonthOfYear() == payrollMonthL.getMonthOfYear() ? payrollStart.toDate() : payrollMonthL.withDayOfMonth(1).toDate();

                UnpaidDeductionRepayment newUnpaidDeductionRepayment = new UnpaidDeductionRepayment();
                newUnpaidDeductionRepayment.setAmount(newUnpaidDeductionRepaymentAmount);
                newUnpaidDeductionRepayment.setRepaymentDate(repaymentDate);
                newUnpaidDeductionRepayment.setHousemaid(housemaid);
                newUnpaidDeductionRepayment.setDescription("Net Salary Calculation");
                newUnpaidDeductionRepayment.setNotFinal(!finalFile);
                newUnpaidDeductionRepayment.setPaidRepayment(true);
                Setup.getRepository(UnpaidDeductionRepaymentRepository.class).save(newUnpaidDeductionRepayment);
            }

            //create new Loan Repayment
            if (housemaid.getStartDate() != null
                    && newLoanRepaymentAmount > 0.0
                    && (groupOneDays >= 20
                        || (housemaid.getStartDate().before(payrollMonthL.withDayOfMonth(1).toDate()) && (housemaid.getReplacementSalaryStartDate() == null || housemaid.getReplacementSalaryStartDate().before(payrollMonthL.withDayOfMonth(1).toDate()))))) {

                java.util.Date repaymentDate = payrollStart.getMonthOfYear() == payrollMonthL.getMonthOfYear() ? payrollStart.toDate() : payrollMonthL.withDayOfMonth(1).toDate();
                Repayment newRepayment = new Repayment();
                newRepayment.setAmount(newLoanRepaymentAmount);
                newRepayment.setDescription("Net Salary Calculation");
                newRepayment.setHousemaid(housemaid);
                newRepayment.setRepaymentDate(repaymentDate);
                newRepayment.setType(Repayment.RepaymentType.CASH_ADVANCED);
                newRepayment.setExculdedFromPayroll(false);
                newRepayment.setPaidRepayment(true);
                newRepayment.setNotFinal(!finalFile);
                Setup.getRepository(RepaymentRepository.class).save(newRepayment);
                repaymentValue = newLoanRepaymentAmount;

                Double newLoanBalance = payrollGenerationHelperService.getLoanBalance(housemaid, new java.sql.Date(new LocalDate(payrollMonth).dayOfMonth().withMaximumValue().toDate().getTime()));
                if (newLoanBalance != null)
                    bean.setRemainingLoanBalance(newLoanBalance.toString());
                else
                    bean.setRemainingLoanBalance("N/A");

                bean.setLoanRepayment(bean.getLoanRepayment() + newLoanRepaymentAmount);
                bean.setTotalDeduction(bean.getTotalDeduction() + newLoanRepaymentAmount);
            }

            //update the fields
            Double newUnpaidDeductionBalance = payrollGenerationHelperService.getUnpaidDeductionBalance(housemaid);
            if (newUnpaidDeductionBalance != null)
                bean.setRemainingUnpaidDeductionBalance(newUnpaidDeductionBalance.toString());
            else
                bean.setRemainingUnpaidDeductionBalance("N/A");

            Double newUnpaidDeduction = payrollGenerationHelperService.getUnpaidDeductionForCurrentMonthByHousemaid(housemaid, payrollStart, payrollEnd);
            bean.setUnpaidDeduction(newUnpaidDeduction);

            Double newUnpaidDeductionRepayments = payrollGenerationHelperService.getSumOfUnpaidDeductionRepaymentForCurrentMonthByHousemaid(housemaid, payrollStart, payrollEnd);
            bean.setUnpaidDeductionRepayment(newUnpaidDeductionRepayments);
        }

        return repaymentValue;
    }

    @Override
    public HousemaidPayrollBean setInSalaryObject(
            HousemaidPayrollBean bean,
            Double value) {

        return bean;
    }

    @Override
    public Boolean isPositive() {
        return true;
    }
}
